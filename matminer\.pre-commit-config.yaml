exclude: ^(docs|.*test_files|cmd_line|dev_scripts|.*data_files)

ci:
  autoupdate_schedule: monthly
  skip: [flake8, mypy, pylint]

repos:

  - repo: https://github.com/myint/autoflake
    rev: v2.2.1
    hooks:
      - id: autoflake

  - repo: https://github.com/asottile/pyupgrade
    rev: v3.10.1
    hooks:
      - id: pyupgrade
        args: [--py38-plus]

  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: check-yaml
        exclude: pymatgen/analysis/vesta_cutoffs.yaml
      - id: end-of-file-fixer
      - id: trailing-whitespace

  - repo: https://github.com/PyCQA/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: [--profile, black]

  - repo: https://github.com/psf/black
    rev: 23.9.1
    hooks:
      - id: black

  - repo: https://github.com/PyCQA/flake8
    rev: 6.1.0
    hooks:
      - id: flake8

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.5.1
    hooks:
      - id: mypy
        additional_dependencies: [types-all]

#  - repo: local
#    hooks:
#      - id: pylint
#        name: pylint
#        entry: pylint
#        language: python
#        types: [python]
#        args: [-sn, --rcfile=pylintrc]
#        additional_dependencies: [pylint]
