
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

    <title>MatMiner Changelog &#8212; matminer 0.9.0 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="_static/nature.css" />
    <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="_static/doctools.js"></script>
    <script src="_static/sphinx_highlight.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
 
<link href='https://fonts.googleapis.com/css?family=Lato:400,700' rel='stylesheet' type='text/css'>

  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">MatMiner Changelog</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="matminer-changelog">
<h1>matminer Changelog<a class="headerlink" href="#matminer-changelog" title="Permalink to this heading">¶</a></h1>
<div class="admonition caution">
<p class="admonition-title">Caution</p>
<p>Starting v0.6.6 onwards, the changelog is no longer maintained. Please check the Github commit log for a record of changes.</p>
</div>
<p><strong>v0.6.5</strong></p>
<ul class="simple">
<li><p>update BV parameters to 2020 version; the existing 2016 version included errors including Rh4+</p></li>
<li><p>Fix matbench dataset urls</p></li>
</ul>
<p><strong>v0.6.4</strong></p>
<ul class="simple">
<li><p>make BaseFeaturizer an ABC with abstractmethods (J. Riebesell)</p></li>
<li><p>default ewald summation to be per atom; change default feature name to reflect this (A. Jain)</p></li>
<li><p>add feature descriptions for magpie (A. Dunn)</p></li>
<li><p>correct yield strengths being accidentally in GPa instead of MPa (A. Dunn)</p></li>
<li><p>minor fixes / updates (B. Krull, A. Dunn, A. Ganose, A. Jain)</p></li>
</ul>
<p><strong>v0.6.3</strong></p>
<ul class="simple">
<li><p>add IntersticeDistribution featurizer (Q. Wang)</p></li>
<li><p>change Dimensionality featurizer to be more accurate (A. Ganose)</p></li>
<li><p>add CohesiveEnergyMP featurizer that gets MP cohesive energy from MP Rester (A. Jain)</p></li>
<li><p>default mp dataretrieval to decode mp entities by default (A. Dunn)</p></li>
<li><p>update dependencies and tests (A. Ganose)</p></li>
<li><p>misc fixes / documentation updates (Q. Wang, A. Dunn, A. Jain, L. Ward, S.P. Ong, A. Ganose)</p></li>
</ul>
<p><strong>v0.6.2</strong></p>
<ul class="simple">
<li><p>Update forum to Discourse link (A. Ganose)</p></li>
<li><p>Add StructuralComplexity featurizer (K. Muraoka)</p></li>
<li><p>Resolve optional requirements problems, update sklearn requirement (A. Dunn)</p></li>
<li><p>Update references to DScribe (A. Dunn)</p></li>
</ul>
<p><strong>v0.6.1</strong></p>
<p>This version was skipped due to an upload issue</p>
<p><strong>v0.6.0</strong></p>
<ul class="simple">
<li><p>Ensure Yang omega is never NaN in YangSolidSolution featurizer (L. Ward)</p></li>
<li><p>More complete BV sum table, some code cleanups (N. Wagner)</p></li>
</ul>
<p><strong>v0.5.9</strong></p>
<ul class="simple">
<li><p>add Meredig composition featurizer (A. Trewartha)</p></li>
<li><p>update / fix Miedema model parameters (Q. Wang)</p></li>
<li><p>update code for latest pymatgen (A. Dunn)</p></li>
</ul>
<p><strong>v0.5.8</strong></p>
<ul class="simple">
<li><p>optimizations for Global Instability Index featurizer (N. Wagner, L. Ward)</p></li>
</ul>
<p><strong>v0.5.7</strong></p>
<ul class="simple">
<li><p>remove SOAP normalization flag (K. Murakoa)</p></li>
<li><p>fix precheck - Miedema and YangSolidSolution (Q. Wang)</p></li>
<li><p>improvements to Miedema - default structure types, docs (Q. Wang)</p></li>
<li><p>fix CGCNN optimizer (Q. Wang)</p></li>
</ul>
<p><strong>v0.5.6</strong></p>
<ul class="simple">
<li><p>add Global Instability Index featurizer (N. Wagner)</p></li>
<li><p>fix Citrine code in docs (D. Nishikawa)</p></li>
<li><p>fix Bond Valence data (K. Muraoka)</p></li>
<li><p>fix MPDataRetrieval (A. Ganose)</p></li>
<li><p>update citation / implementor list for SOAP (A. Jain)</p></li>
<li><p>misc bug fixes (K. Muraoka, C. Legaspi)</p></li>
</ul>
<p><strong>v0.5.5</strong></p>
<ul class="simple">
<li><p>Add a precheck() and precheck_dataframe() function that can be used to quickly see if a featurizer is likely to give NaN values (A. Dunn)</p></li>
<li><p>Add MEGNET 1Neuron element embeddings (A. Dunn)</p></li>
<li><p>fix inplace setting (S. Cherfaoui, A. Dunn, A. Ganose)</p></li>
<li><p>add a conversion featurizer to get a structure from composition using MP most stable structure (A. Jain)</p></li>
<li><p>misc code cleanups (A. Jain, A. Dunn)</p></li>
</ul>
<p><strong>v0.5.4</strong></p>
<ul class="simple">
<li><p>add elementproperty source name to feature labels (A. Dunn)</p></li>
<li><p>update Citrine API key detection logic (matSciMalcolm + A. Jain)</p></li>
<li><p>misc. fixes (A. Dunn)</p></li>
</ul>
<p><strong>v0.5.3</strong></p>
<ul class="simple">
<li><p>fix typo bug that got introduced in 0.5.2 pypi release</p></li>
</ul>
<p><strong>v0.5.2</strong></p>
<ul class="simple">
<li><p>better flattening for ColoumbMatrix featurizers, making them more usable (A. Dunn)</p></li>
<li><p>SOAP featurizer using the dscribe package (A. Dunn)</p></li>
<li><p>DosAsymmetry featurizer (M. Dylla)</p></li>
</ul>
<p><strong>v0.5.1</strong></p>
<ul class="simple">
<li><p>AFLOW data retrieval (M. Dylla)</p></li>
<li><p>SiteDOS featurizer (M. Dylla)</p></li>
<li><p>fix various testing (A. Dunn, M. Dylla, L. Ward)</p></li>
</ul>
<p><strong>v0.5.0</strong></p>
<ul class="simple">
<li><p>fix for Py3.7 and pytorch (L. Ward)</p></li>
</ul>
<p><strong>v0.4.9</strong></p>
<ul class="simple">
<li><p>fix PIP setup of JARVIS data files (A. Dunn)</p></li>
<li><p>some test configuration fixes (A. Dunn)</p></li>
</ul>
<p><strong>v0.4.8</strong></p>
<ul class="simple">
<li><p>CGCNN featurizer / model (Q. Wang, T. Xie)</p></li>
<li><p>Text-mined element embedding featurizer (A. Dunn)</p></li>
<li><p>add Brgoch data set (D. Dopp)</p></li>
<li><p>add quartile to PropertyStats (A. Dunn)</p></li>
<li><p>minor fixes, improvements (A. Dunn, A. Jain)</p></li>
</ul>
<p><strong>v0.4.6</strong></p>
<ul class="simple">
<li><p>Jarvis CFID descriptors (A. Dunn, K. Choudhary)</p></li>
<li><p>Allow oxi conversion featurizers to return original object (A. Ganose)</p></li>
<li><p>better contribution docs (A. Dunn, A. Jain)</p></li>
</ul>
<p><strong>v0.4.5</strong></p>
<ul class="simple">
<li><p>fix for missing data set loader file (D. Dopp)</p></li>
<li><p>fix MDF unit tests (L. Ward)</p></li>
</ul>
<p><strong>v0.4.4</strong></p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Data set loaders may not work properly due to a missing file in this release</p>
</div>
<ul class="simple">
<li><p>Further revamp data set loaders and management (D. Dopp)</p></li>
<li><p>Better default chunksize for multiprocessing should improve performance (L. Ward)</p></li>
<li><p>Improve oxidation state featurizer (A. Dunn)</p></li>
</ul>
<p><strong>v0.4.3</strong></p>
<ul class="simple">
<li><p>Revamped test / example data loader classes (D. Dopp, A. Ganose, A. Dunn)</p></li>
<li><p>Add chunksize support to improve performance of dataframe featurization (A. Ganose)</p></li>
<li><p>Improve performance of BandCenter with large coefficients (A. Faghaninia)</p></li>
<li><p>Revamp of MultiFeaturizer (A. Ganose)</p></li>
<li><p>Custom progress bar for running in notebook (A. Ganose)</p></li>
<li><p>Improved multi-index for conversion featurizerse (A. Ganose)</p></li>
<li><p>Minor fixes / improvements (D. Dopp, A. Ganose, A. Faghaninia)</p></li>
</ul>
<p><strong>v0.4.2</strong></p>
<ul class="simple">
<li><p>Refactor conversion utils to be featurizers for consistency and parallelism (A. Ganose)</p></li>
<li><p>Average Bond Length and Bond Angle implementations (A. Rui, L. Ward)</p></li>
<li><p>Add ability to serialize dataframes as JSON with MontyEncoder (A. Ganose)</p></li>
<li><p>support added for fractional compositions in AtomicOrbitals (M. Dylla)</p></li>
<li><p>Add ability to flatten OFM (A. Dunn)</p></li>
<li><p>updates to FunctionFeaturizer (J. Montoya)</p></li>
<li><p>Various bugfixes (L. Ward, A. Ganose)</p></li>
</ul>
<p><strong>v0.4.1</strong></p>
<ul class="simple">
<li><p>Better elemental properties for Magpie features (L. Ward)</p></li>
<li><p>Improvements to Seko representation (L. Ward)</p></li>
<li><p>Some bugfixes for multiplefeaturizer and compatibility with progress bars (L. Ward, A. Dunn)</p></li>
<li><p>More intuitive input arguments for featurize_many (L. Ward)</p></li>
<li><p>Bugfixes for BOOP features (L. Ward, A. Thompson)</p></li>
</ul>
<p><strong>v0.4.0</strong></p>
<ul class="simple">
<li><p>Progressbar for featurizers (A. Dunn)</p></li>
<li><p>Add BOOP features (L. Ward)</p></li>
<li><p>Add Seko features, including more lookuip tables for MagpieData and elemental property site features + covariance, skew, kurtosis (L. Ward)</p></li>
<li><p>New scheme for GRDF/AFS bin functions (L. Ward)</p></li>
<li><p>misc fixes (A. Dunn., L. Ward)</p></li>
</ul>
<p><strong>v0.3.9</strong></p>
<ul class="simple">
<li><p>BandEdge renamed to Hybridization, gives smoother featurizations (M. Dylla, A. Faghaninia)</p></li>
<li><p>Add hoverinfo option for many plots (A. Dunn)</p></li>
<li><p>minor fixes (A. Faghaninia)</p></li>
</ul>
<p><strong>v0.3.8</strong></p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>This is an unsupported / aborted release</p>
</div>
<p><strong>v0.3.7</strong></p>
<ul class="simple">
<li><p>faster implementation of GaussianSymmFunc (L. Ward)</p></li>
<li><p>more resilient Yang and AtomicPackingEfficiency (L. Ward)</p></li>
<li><p>some fixes for PRDF featurizer (A. Faghaninia)</p></li>
<li><p>add <a href="#id1"><span class="problematic" id="id2">*</span></a>.tsv files to package_data, should fix Miedema PyPI install (A. Faghaninia)</p></li>
</ul>
<p><strong>v0.3.6</strong></p>
<ul class="simple">
<li><p>Improve MPDataRetrieval to serialize objects (A. Faghaninia)</p></li>
<li><p>Some fixes to GDRF and AFS (L. Williams, M. Dylla)</p></li>
<li><p>Some fixes for Ewald (A. Faghaninia)</p></li>
<li><p>improve error messages (A. Jain)</p></li>
</ul>
<p><strong>v0.3.5</strong></p>
<ul class="simple">
<li><p>some tools for sklearn Pipeline integration (J. Brenneck)</p></li>
<li><p>ability to add a chemical descriptor to CNFingerprint (N. Zimmermann, hat tip to S. Dwaraknath and A. Jain)</p></li>
<li><p>add phase diagram-like “triangle” plot (A. Faghaninia)</p></li>
<li><p>add harmonic mean (holder_mean::-1) to PropertyStats (A. Jain)</p></li>
</ul>
<p><strong>v0.3.4</strong></p>
<ul class="simple">
<li><p>add XRDPowderPattern featurizer (A. Jain)</p></li>
<li><p>add multi-index support for featurizers (A. Dunn)</p></li>
<li><p>add BandEdge featurizer (A. Faghaninia)</p></li>
<li><p>better labels support in xy plots + debugs and cleanups (A. Faghaninia)</p></li>
<li><p>deprecate CrystalSiteFingerprint</p></li>
<li><p>remove  a few old and unused site OP functions/methods (A. Jain)</p></li>
<li><p>doc improvements (A. Faghaninia)</p></li>
<li><p>bug fixes, minor code improvements, etc. (N. Zimmermann, A. Dunn, Q. Wang, A. Faghaninia)</p></li>
</ul>
<p><strong>v0.3.3</strong></p>
<ul class="simple">
<li><p>add StackedFeaturizer (L. Ward)</p></li>
<li><p>changes to reference energies in BranchPointEnergy featurizer (A. Faghaninia)</p></li>
<li><p>doc improvements (A. Dunn)</p></li>
</ul>
<p><strong>v0.3.2</strong></p>
<ul class="simple">
<li><p>Major overhaul / redesign of data retrieval classes for consistency (A. Faghaninia, A. Dunn)</p></li>
<li><p>Updates / redesign of function featurizer (J. Montoya)</p></li>
<li><p>Add Yang’s solid solution features (L. Ward)</p></li>
<li><p>Add cluster packing efficiency features (L. Ward)</p></li>
<li><p>update to MDF data retrieval (L. Ward)</p></li>
<li><p>update to Citrine data retrieval for new pycc (S. Bajaj)</p></li>
<li><p>Branch point energy takes into account symmetry (A. Faghaninia)</p></li>
<li><p>minor code and doc updates (A. Jain, A. Faghaninia)</p></li>
</ul>
<p><strong>v0.3.1</strong></p>
<ul class="simple">
<li><p>add caching for featurizers (L. Ward)</p></li>
<li><p>add CrystalNNFingerprint (A. Jain)</p></li>
<li><p>some x-y plot updates (A. Faghaninia)</p></li>
<li><p>speedup to chemenv featurizer (D. Waroquiers)</p></li>
<li><p>minor code cleanups, bugfixes (A. Dunn, L. Ward, N. Zimmermann, A. Jain)</p></li>
</ul>
<p><strong>v0.3.0</strong></p>
<ul class="simple">
<li><p>add structural heterogeneity features (L. Ward)</p></li>
<li><p>add maximum packing efficiency feature (L. Ward)</p></li>
<li><p>add chemical ordering features (L. Ward)</p></li>
<li><p>New BagofBonds based on original paper, old featurizer now BondFractions (A. Dunn)</p></li>
<li><p>add DopingFermi featurizer (A. Faghaninia, A. Jain)</p></li>
<li><p>shortcut for getting composition features from structure (L. Ward)</p></li>
<li><p>fix static mode output in PlotlyFig (A. Dunn)</p></li>
<li><p>some misc Figrecipes updates (A. Dunn)</p></li>
<li><p>add fit_featurize method to base (A. Dunn)</p></li>
<li><p>minor cleanups, doc updates and new docs (A. Jain, L. Ward, A. Dunn)</p></li>
</ul>
<p><strong>v0.2.9</strong></p>
<ul class="simple">
<li><p>fix pymatgen dep (A. Jain)</p></li>
</ul>
<p><strong>v0.2.8</strong></p>
<ul class="simple">
<li><p>new FunctionFeaturizer to combine features into mini functions (J. Montoya)</p></li>
<li><p>updates to PlotlyFig (A. Dunn)</p></li>
<li><p>Update default n_jobs to cpu_count() (A. Dunn)</p></li>
<li><p>test fixes and updates (A. Dunn, N. Zimmermann, J. Montoya)</p></li>
<li><p>move Jupyter notebooks to matminer_examples repo, separate from matminer (J. Montoya)</p></li>
<li><p>add presets for AFS, GRDF featurizes (M. Dylla)</p></li>
<li><p>update CircleCI testing (A. Dunn)</p></li>
<li><p>code cleanups (A. Dunn, A. Jain, J. Montoya)</p></li>
</ul>
<p><strong>v0.2.6</strong></p>
<ul class="simple">
<li><p>modify ChemicalRSO to use fit() method (Q. Wang)</p></li>
<li><p>more updates to FigRecipes (A. Dunn, A. Faghaninia)</p></li>
<li><p>misc code cleanups (M. Dylla, A. Faghaninia, A. Jain, K. Bostrom, Q. Wang)</p></li>
<li><p>fix missing yaml file from package data (A. Jain)</p></li>
</ul>
<p><strong>v0.2.5</strong></p>
<ul class="simple">
<li><p>Major rework of BaseFeaturizer to subclass BaseEstimator/TransformerMixin of sklearn. Allows for support of fit() function needed by many featurizers (L. Ward)</p></li>
<li><p>BaseFeaturizer can return errors as a new column (A. Dunn)</p></li>
<li><p>Clean up data getter signatures (J. Montoya)</p></li>
<li><p>Re-implement PRDF (L. Ward)</p></li>
<li><p>GaussianSymmFunc featurizer (Q. Wang)</p></li>
<li><p>misc code clean up (L. Ward, A. Jain)</p></li>
</ul>
<p><strong>v0.2.4</strong></p>
<ul class="simple">
<li><p>updates to PlotlyFig (A. Dunn, A. Faghaninia)</p></li>
<li><p>adapt to new OP parameters (N. Zimmermann)</p></li>
<li><p>bugfixes, cleanups, doc updates (A. Faghaninia, A. Dunn, Q. Wang, N. Zimmermann, A. Jain)</p></li>
</ul>
<p><strong>v0.2.3</strong></p>
<ul class="simple">
<li><p>MDF data retrieval (J. Montoya)</p></li>
<li><p>new VoronoiFingerprint descriptors (Q. Wang)</p></li>
<li><p>new ChemicalSRO descriptors (Q. Wang)</p></li>
<li><p>bugfixes to featurize_many (A. Dunn)</p></li>
<li><p>minor bug fixes, cleanups, slighly improved docs, etc.</p></li>
</ul>
<p><strong>v0.2.2</strong></p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Py2 compatibility is officially dropped in this version. Please upgrade to Python 3.x.</p>
</div>
<ul class="simple">
<li><p>multiprocessing for pandas dataframes (A. Dunn, L. Ward)</p></li>
<li><p>new CoordinationNumber site featurizer based on NearNeighbor algos (N. Zimmermann)</p></li>
<li><p>update OP fingerprints for latest pymatgen (N. Zimmermann)</p></li>
<li><p>OPStructureFingerprint -&gt; SiteStatsFingerprint that takes in any site fingerprint function (A. Jain)</p></li>
<li><p>Add BondFractions featurizer (A. Dunn)</p></li>
<li><p>multi-index for pandas dataframes (A. Dunn)</p></li>
<li><p>cleanup of formatting for citations, implementors, feature_labels to always be list (N. Zimmermann)</p></li>
<li><p>minor bug fixes, cleanups, slighly improved docs, etc.</p></li>
</ul>
<p><strong>v0.2.1</strong></p>
<ul class="simple">
<li><p>further improvements to test data sets (K. Bystrom)</p></li>
<li><p>new MultiFeaturizer to combine multiple featurizers (L. Ward)</p></li>
</ul>
<p><strong>v0.2.0</strong></p>
<ul class="simple">
<li><p>improvements to test data sets (K. Bystrom)</p></li>
<li><p>new conversion utility functions (A. Jain)</p></li>
<li><p>updated example and removed outdated examples (A. Jain)</p></li>
<li><p>some featurizer internal fixes (A. Faghaninia, M. Dylla, A. Jain)</p></li>
<li><p>minor bugfixes (L. Ward, A. Jain)</p></li>
</ul>
<p><strong>v0.1.9</strong></p>
<ul class="simple">
<li><p>overhaul of data API classes (L. Ward)</p></li>
<li><p>change to oxidation-state dependent classes, now require oxidation set in advance (L. Ward)</p></li>
<li><p>Ewald site and structure energy featurizers (L. Ward)</p></li>
<li><p>AtomicOrbital featurizer (M. Dylla)</p></li>
<li><p>Updates to OP fingerprints based on new bcc renormalization (N. Zimmermann)</p></li>
<li><p>fix to include sample data sets in pip install (A. Jain, K. Bostrom)</p></li>
<li><p>add several utility functions for turning strings to compositions, dicts/jsons to pymatgen objects, and quickly adding oxidation state to structure (A. Jain)</p></li>
<li><p>code cleanups (L. Ward, A. Jain)</p></li>
</ul>
<p><strong>v0.1.8</strong></p>
<ul class="simple">
<li><p>extend Miedema model to ternaries and higher (Q. Wang, A. Faghaninia)</p></li>
<li><p>cleanups/refactor to DOS featurizer (A. Faghaninia)</p></li>
</ul>
<p><strong>v0.1.7</strong></p>
<ul class="simple">
<li><p>lots of code cleanup / refactoring / review, including trimming of unused / moved packages (A. Jain)</p></li>
<li><p>new Chemenv structure fingerprint (N. Zimmermann)</p></li>
<li><p>various updates to BSFeaturizer (A. Faghaninia)</p></li>
<li><p>cleanup / rework of DOSFeaturizer (A. Faghaninia)</p></li>
<li><p>Updated citation for OFM paper (L. Ward)</p></li>
<li><p>CNSiteFingerprint goes to CN=16 by default, includes two presets (“cn” and “ops”) (A. Jain)</p></li>
<li><p>stats use double colon instead of double underscore for params (A. Jain)</p></li>
<li><p>Various cleanups to Miedema featurizer (Q. Wang, A. Faghaninia, A. Dunn)</p></li>
</ul>
<p><strong>v0.1.6</strong></p>
<ul class="simple">
<li><p>new CrystalSiteFingerprint and CNSiteFingerprint (A. Jain)</p></li>
<li><p>Miedema model (Q. Wang)</p></li>
<li><p>Voronoi index site fingerprint (Q. Wang)</p></li>
<li><p>updates to CitrineDataRetrieval (S. Bajaj)</p></li>
<li><p>updates to BandStructureFeaturizer (A. Faghaninia)</p></li>
<li><p>allow featurize_dataframe() to ignore errors (A. Dunn)</p></li>
<li><p>some patches of DOSFeaturizer (A. Jain)</p></li>
</ul>
<p><strong>v0.1.5</strong></p>
<ul class="simple">
<li><p>new Site and Structure fingerprints based on order parameters (N. Zimmermann)</p></li>
<li><p>DOSFeaturizer (M. Dylla)</p></li>
<li><p>Structure fingerprint can do cations/anions only (A. Jain)</p></li>
<li><p>include the degeneracy of the CBM/VBM in BandFeaturizer (A. Faghaninia)</p></li>
<li><p>fixes / updates to CitrineDataRetrieval (S. Bajaj)</p></li>
<li><p>more property stats (L. Ward)</p></li>
<li><p>fixes to AGNIFingerprint (L. Ward)</p></li>
<li><p>FigRecipes cleanup (A. Dunn)</p></li>
<li><p>updated examples, docs (A. Dunn)</p></li>
<li><p>various bugfixes, code cleanup (A. Jain)</p></li>
</ul>
<p><strong>v0.1.4</strong></p>
<ul class="simple">
<li><p>add a band structure featurizer (A. Faghaninia)</p></li>
<li><p>add global structure featurizer (A. Jain)</p></li>
<li><p>improve CoulombMatrix, SineCoulombMatrix, and OrbitalFieldMatrix featurizers (K. Bostrom)</p></li>
<li><p>fix some code structure / interfaces (A. Faghaninia, A. Jain)</p></li>
<li><p>bug fixes (A. Jain, A. Faghaninia, L. Ward)</p></li>
<li><p>code cleanup (A. Jain)</p></li>
<li><p>doc updates (A. Dunn, A. Jain, K. Bostrom)</p></li>
</ul>
<p><strong>v0.1.3</strong></p>
<ul class="simple">
<li><p>remove git-lfs</p></li>
<li><p>updated CSV data sets (K. Bostrom)</p></li>
<li><p>better oxidation state determination in multiple composition descriptors</p></li>
<li><p>refactor structure descriptors</p></li>
<li><p>multiple fixes to cohesive energy</p></li>
<li><p>fixes to data loaders</p></li>
<li><p>fix complex Mongo retrieval queries, better logic for query projections</p></li>
<li><p>more unit tests</p></li>
<li><p>enforce lower case feature names</p></li>
<li><p>sort data by atomic number not electronegativity in data getters, this will avoid pernicious behavior</p></li>
<li><p>many minor cleanups, bug fixes, and consistency fixes</p></li>
</ul>
<p><strong>v0.1.2</strong></p>
<ul class="simple">
<li><p>Several new structure fingerprint methods (L. Ward, K. Bostrom)</p></li>
<li><p>Refactor structure descriptors into new OOP style (N. Zimmermann)</p></li>
<li><p>move large files to git-lfs (K. Bostrom, A. Jain)</p></li>
<li><p>update example notebooks to new style</p></li>
<li><p>misc. cleanups and bug fixes</p></li>
</ul>
<p><strong>v0.1.1</strong></p>
<ul class="simple">
<li><p>refactor and redesign of codebase to be more OOP (J. Chen, L. Ward)</p></li>
<li><p>Py3 compatibility (K. Mathew)</p></li>
<li><p>Element fraction feature (A. Aggarwal)</p></li>
<li><p>misc fixes / improvements (A. Jain, J. Chen, L. Ward, K. Mathew, J. Frost)</p></li>
</ul>
<p><strong>v0.1.0</strong></p>
<ul class="simple">
<li><p>Add MPDS data retrieval (E. Blokhin)</p></li>
<li><p>Add partial RDF descriptor (L. Ward)</p></li>
<li><p>Add local environment motif descriptors (N. Zimmermann)</p></li>
<li><p>fix misc. bugs and installation issues (A. Dunn, S. Bajaj, L. Ward)</p></li>
</ul>
<p>For changelog before v0.1.0, consult the git history of matminer.</p>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/changelog.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">MatMiner Changelog</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2015, Anubhav Jain.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.3.0.
    </div>

  </body>
</html>