
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

    <title>matminer (Materials Data Mining) &#8212; matminer 0.9.0 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="_static/nature.css" />
    <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="_static/doctools.js"></script>
    <script src="_static/sphinx_highlight.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
 
<link href='https://fonts.googleapis.com/css?family=Lato:400,700' rel='stylesheet' type='text/css'>

  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="#">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer (Materials Data Mining)</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <img alt="matminer logo" src="_images/matminer_logo_small.png" />
<section id="matminer">
<h1>matminer<a class="headerlink" href="#matminer" title="Permalink to this heading">¶</a></h1>
<p>matminer is a Python library for data mining the properties of materials.</p>
<p>Matminer contains routines for:</p>
<ul class="simple">
<li><dl class="simple">
<dt><strong>one-line access to 40+ ready-made datasets</strong> (<code class="code docutils literal notranslate"><span class="pre">matminer.datasets</span></code>)</dt><dd><ul>
<li><p>Spans various domains of materials data</p></li>
<li><p>Full list of datasets here: <a class="reference internal" href="dataset_summary.html"><span class="doc">Table of Datasets</span></a></p></li>
</ul>
</dd>
</dl>
</li>
<li><dl class="simple">
<dt><strong>easily creating your own datasets from online repositories</strong>  (<code class="code docutils literal notranslate"><span class="pre">matminer.data_retrieval</span></code>)</dt><dd><ul>
<li><p>such as <a class="reference external" href="https://materialsproject.org">The Materials Project</a> and <a class="reference external" href="https://citrination.com">Citrination</a>, among others</p></li>
</ul>
</dd>
</dl>
</li>
<li><dl class="simple">
<dt><strong>transforming and featurizing complex materials attributes into numerical descriptors</strong>  (<code class="code docutils literal notranslate"><span class="pre">matminer.featurizers</span></code>)</dt><dd><ul>
<li><p>70+ featurizers adapted from scientific publications</p></li>
<li><dl class="simple">
<dt>Feature generation routines for</dt><dd><ul>
<li><p>chemical compositions</p></li>
<li><p>crystal sites and structures</p></li>
<li><p>electronic bandstructure/DOS</p></li>
<li><p>function-expansions of features</p></li>
</ul>
</dd>
</dl>
</li>
<li><p><strong>tools and utilities for practically handling materials data</strong>, in dataframe format (<code class="code docutils literal notranslate"><span class="pre">matminer.featurizers.conversions</span></code>)</p></li>
<li><p>Full table of featurizers here: <a class="reference internal" href="featurizer_summary.html"><span class="doc">Table of Featurizers.</span></a></p></li>
</ul>
</dd>
</dl>
</li>
<li><p><strong>one-line access to pre-trained deep learning models</strong> for inference (<code class="code docutils literal notranslate"><span class="pre">matminer.models</span></code> - Coming soon!)</p></li>
</ul>
<p>Matminer does not contain machine learning routines itself, but works with the <a class="reference external" href="https://pandas.pydata.org">pandas</a> data format in order to make various downstream machine learning libraries and tools available to materials science applications.</p>
<p>matminer is <a class="reference external" href="https://github.com/hackingmaterials/matminer">open source</a> via a BSD-style license.</p>
<p>A general workflow and overview of matminer’s capabilities is presented below:</p>
<div class="line-block">
<div class="line"><br /></div>
</div>
<a class="reference internal image-reference" href="_images/Flowchart.png"><img alt="Flow chart of matminer features" class="align-center" src="_images/Flowchart.png" style="width: 1000px;" /></a>
<div class="line-block">
<div class="line"><br /></div>
<div class="line"><br /></div>
</div>
<p>Take a tour of matminer’s features by scrolling down!</p>
<section id="related-software">
<h2>Related software<a class="headerlink" href="#related-software" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p><a class="reference external" href="https://github.com/hackingmaterials/matminer_examples">matminer_examples</a> is a repository of example notebooks showing how to use matminer.</p></li>
<li><p><a class="reference external" href="https://github.com/hackingmaterials/automatminer">automatminer</a> is the automatic version of matminer, which automatically fits a machine learning pipeline to your problem using matminer descriptors.</p></li>
<li><p><a class="reference external" href="https://github.com/hackingmaterials/figrecipes">figrecipes</a> is a Plotly-based code for quickly generating interactive plots from dataframes</p></li>
<li><p><a class="reference external" href="https://github.com/hackingmaterials/matbench">matbench</a> is an ImageNet for materials properties - a reproducible benchmarking package and dynamic leaderboard for comparing ML algorithms</p></li>
</ul>
</section>
<section id="quick-links">
<h2>Quick Links<a class="headerlink" href="#quick-links" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="dataset_summary.html"><span class="doc">Table of Datasets</span></a></p></li>
<li><p><a class="reference internal" href="featurizer_summary.html"><span class="doc">Table of Featurizers</span></a></p></li>
</ul>
<p>Autogenerated code documentation:</p>
<ul class="simple">
<li><p><a class="reference internal" href="genindex.html"><span class="std std-ref">Index</span></a></p></li>
<li><p><a class="reference internal" href="py-modindex.html"><span class="std std-ref">Module Index</span></a></p></li>
<li><p><a class="reference internal" href="search.html"><span class="std std-ref">Search Page</span></a></p></li>
</ul>
</section>
<section id="installation">
<h2>Installation<a class="headerlink" href="#installation" title="Permalink to this heading">¶</a></h2>
<p>To install matminer, follow the short <a class="reference internal" href="installation.html"><span class="doc">installation tutorial.</span></a></p>
</section>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Permalink to this heading">¶</a></h2>
<section id="featurizers-generate-descriptors-for-materials">
<h3>Featurizers generate descriptors for materials<a class="headerlink" href="#featurizers-generate-descriptors-for-materials" title="Permalink to this heading">¶</a></h3>
<p>Matminer can turn materials objects - for example, a composition such as “Fe3O4” - into arrays of numbers representing things like average electronegativity or
difference in ionic radii of the substituent elements. Matminer also contains sophisticated crystal structure and site featurizers (e.g.,
obtaining the coordination number or local environment of atoms in the structure) as well as featurizers for complex materials data such as
band structures and density of states.</p>
<p>All of these various featurizers are available under <strong>a consistent interface</strong>, making it easy to
try different types of materials descriptors for an analysis and to transform materials science objects into physically-relevant numbers for data
mining. A full <a class="reference internal" href="featurizer_summary.html"><span class="doc">Table of Featurizers</span></a> is available.</p>
<a class="reference internal image-reference" href="_images/featurizer_diagram.png"><img alt="Diagram of featurizers" class="align-center" src="_images/featurizer_diagram.png" style="width: 1000px;" /></a>
</section>
<section id="data-retrieval-easily-puts-complex-online-data-into-dataframes">
<h3>Data retrieval easily puts complex online data into dataframes<a class="headerlink" href="#data-retrieval-easily-puts-complex-online-data-into-dataframes" title="Permalink to this heading">¶</a></h3>
<p><strong>Retrieve data from the biggest materials databases, such as the Materials Project and Citrine’s databases, in a Pandas dataframe format</strong></p>
<p>The <a class="reference external" href="https://github.com/hackingmaterials/matminer/blob/master/matminer/data_retrieval/retrieve_MP.py">MPDataRetrieval</a> and <a class="reference external" href="https://github.com/hackingmaterials/matminer/blob/master/matminer/data_retrieval/retrieve_Citrine.py">CitrineDataRetrieval</a> classes can be used to retrieve data from the biggest open-source materials database collections of the <a class="reference external" href="https://www.materialsproject.org/">Materials Project</a> and <a class="reference external" href="https://citrination.com/">Citrine Informatics</a>, respectively, in a <a class="reference external" href="http://pandas.pydata.org/">Pandas</a> dataframe format. The data contained in these databases are a variety of material properties, obtained in-house or from other external databases, that are either calculated, measured from experiments, or learned from trained algorithms. The <code class="code docutils literal notranslate"><span class="pre">get_dataframe</span></code> method of these classes executes the data retrieval by searching the respective database using user-specified filters, such as compound/material, property type, etc , extracting the selected data in a JSON/dictionary format through the API, parsing it and output the result to a Pandas dataframe with columns as properties/features measured or calculated and rows as data points.</p>
<p>For example, to compare experimental and computed band gaps of Si, one can employ the following lines of code:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">matminer.data_retrieval.retrieve_Citrine</span> <span class="kn">import</span> <span class="n">CitrineDataRetrieval</span>
<span class="kn">from</span> <span class="nn">matminer.data_retrieval.retrieve_MP</span> <span class="kn">import</span> <span class="n">MPDataRetrieval</span>

<span class="n">df_citrine</span> <span class="o">=</span> <span class="n">CitrineDataRetrieval</span><span class="p">()</span><span class="o">.</span><span class="n">get_dataframe</span><span class="p">(</span><span class="n">criteria</span><span class="o">=</span><span class="s1">&#39;Si&#39;</span><span class="p">,</span> <span class="n">properties</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;band_gap&#39;</span><span class="p">])</span>
<span class="n">df_mp</span> <span class="o">=</span> <span class="n">MPDataRetrieval</span><span class="p">()</span><span class="o">.</span><span class="n">get_dataframe</span><span class="p">(</span><span class="n">criteria</span><span class="o">=</span><span class="s1">&#39;Si&#39;</span><span class="p">,</span> <span class="n">properties</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;band_gap&#39;</span><span class="p">])</span>
</pre></div>
</div>
<p><a class="reference external" href="https://github.com/hackingmaterials/matminer/blob/master/matminer/data_retrieval/retrieve_MongoDB.py">MongoDataRetrieval</a> is another data retrieval tool developed that allows for the parsing of any <a class="reference external" href="https://www.mongodb.com/">MongoDB</a> collection (which follows a flexible JSON schema), into a Pandas dataframe that has a format similar to the output dataframe from the above data retrieval tools. The arguments of the <code class="code docutils literal notranslate"><span class="pre">get_dataframe</span></code> method allow to utilize MongoDB’s rich and powerful query/aggregation syntax structure. More information on customization of queries can be found in the <a class="reference external" href="https://docs.mongodb.com/manual/">MongoDB documentation</a>.</p>
</section>
<section id="access-ready-made-datasets-in-one-line">
<h3>Access ready-made datasets in one line<a class="headerlink" href="#access-ready-made-datasets-in-one-line" title="Permalink to this heading">¶</a></h3>
<p><strong>Explore datasets for analysis, benchmarking, and testing without ever leaving the Python interpreter</strong></p>
<p>The datasets module provides an ever growing collection of materials science datasets that have been collected, formatted as pandas dataframes, and made available through a unified interface.</p>
<p>Loading a dataset as a pandas dataframe is as simple as:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">matminer.datasets</span> <span class="kn">import</span> <span class="n">load_dataset</span>

<span class="n">df</span> <span class="o">=</span> <span class="n">load_dataset</span><span class="p">(</span><span class="s2">&quot;jarvis_dft_3d&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>Or use the dataset specific convenience loader to access operations common to that dataset:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">matminer.datasets.convenience_loaders</span> <span class="kn">import</span> <span class="n">load_jarvis_dft_3d</span>

<span class="n">df</span> <span class="o">=</span> <span class="n">load_jarvis_dft_3d</span><span class="p">(</span><span class="n">drop_nan_columns</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;bulk modulus&quot;</span><span class="p">])</span>
</pre></div>
</div>
<p>Matminer’s consistently-formatted datasets makes analysis, visualization, and prototyping models quick and easy:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">figrecipes</span> <span class="kn">import</span> <span class="n">PlotlyFig</span>
<span class="kn">from</span> <span class="nn">matminer.datasets</span> <span class="kn">import</span> <span class="n">load_dataset</span>
<span class="n">df</span> <span class="o">=</span> <span class="n">load_dataset</span><span class="p">(</span><span class="s2">&quot;elastic_tensor_2015&quot;</span><span class="p">)</span>
<span class="n">pf</span> <span class="o">=</span> <span class="n">PlotlyFig</span><span class="p">(</span><span class="n">df</span><span class="p">,</span> <span class="n">y_title</span><span class="o">=</span><span class="s1">&#39;Bulk Modulus (GPa)&#39;</span><span class="p">,</span> <span class="n">x_title</span><span class="o">=</span><span class="s1">&#39;Shear Modulus (GPa)&#39;</span><span class="p">,</span> <span class="n">filename</span><span class="o">=</span><span class="s1">&#39;bulk_shear_moduli&#39;</span><span class="p">)</span>
<span class="n">pf</span><span class="o">.</span><span class="n">xy</span><span class="p">((</span><span class="s1">&#39;G_VRH&#39;</span><span class="p">,</span> <span class="s1">&#39;K_VRH&#39;</span><span class="p">),</span> <span class="n">labels</span><span class="o">=</span><span class="s1">&#39;material_id&#39;</span><span class="p">,</span> <span class="n">colors</span><span class="o">=</span><span class="s1">&#39;poisson_ratio&#39;</span><span class="p">,</span> <span class="n">colorscale</span><span class="o">=</span><span class="s1">&#39;Picnic&#39;</span><span class="p">,</span> <span class="n">limits</span><span class="o">=</span><span class="p">{</span><span class="s1">&#39;x&#39;</span><span class="p">:</span> <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">300</span><span class="p">)})</span>
</pre></div>
</div>
<iframe src="_static/bulk_shear_moduli.html" height="1000px" width=90%" align="center" frameBorder="0">Browser not compatible.</iframe><p>See <a class="reference internal" href="dataset_summary.html"><span class="doc">the dataset summary page</span></a> for a comprehensive summary of
datasets available within matminer. If you would like to contribute a dataset to matminer’s
repository see <a class="reference internal" href="dataset_addition_guide.html"><span class="doc">the dataset addition guide</span></a>.</p>
</section>
<section id="data-munging-with-conversion-featurizers">
<h3>Data munging with Conversion Featurizers<a class="headerlink" href="#data-munging-with-conversion-featurizers" title="Permalink to this heading">¶</a></h3>
<p>Matminer’s multiprocessing-parallelized and error-tolerant featurizer structure makes transforming materials objects into other formats quick and easy.</p>
<p>For example, here is code that robustly transforms a dataframe of 10k ASE (atomic simulation environment) structures in the “ase atoms” column - some of which contain errors - to Pymatgen structures to use with matminer:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">matminer.featurizer.conversions</span> <span class="kn">import</span> <span class="n">ASEAtomstoStructure</span>

<span class="n">aa2s</span> <span class="o">=</span> <span class="n">ASEAtomstoStructure</span><span class="p">()</span>

<span class="n">df</span> <span class="o">=</span> <span class="n">aa2s</span><span class="o">.</span><span class="n">featurize_dataframe</span><span class="p">(</span><span class="n">df</span><span class="p">,</span> <span class="s2">&quot;ase atoms&quot;</span><span class="p">,</span> <span class="n">ignore_errors</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
</pre></div>
</div>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>                                               ase atoms                        pymatgen structure from ase  log10(K_VRH)
0      (Atom(&#39;Ca&#39;, [0.0, 0.0, 0.0], index=0), Atom(&#39;G...  [[0. 0. 0.] Ca, [1.37728887 1.57871271 3.73949...      1.707570
1      Atoms(symbols=&#39;CO&#39;, pbc=False)                     NaN                                                    -inf
2      (Atom(&#39;Si&#39;, [2.068845188153371, 2.406272406310...  [[ 2.06884519  2.40627241 -0.45891585] Si, [1....      1.908485
3      (Atom(&#39;Pd&#39;, [2.064280815, 0.0, 2.064280815], i...  [[2.06428082 0.         2.06428082] Pd, [0.   ...      2.117271
4      (Atom(&#39;Mg&#39;, [3.0963526175, 1.0689416025, 1.536...  [[3.09635262 1.0689416  1.53602403] Mg, [0.593...      1.690196
                                                  ...                                                ...           ...
10982  Atoms(&#39;N3&#39;, [(0, 0, 0), (1, 0, 0), (0, 0, 1)])     NaN                                                    -inf
10983  (Atom(&#39;Mg&#39;, [-1.5115782146589711, 4.4173924989...  [[-1.51157821  4.4173925   1.21553922] Mg, [3....      1.724276
10984  (Atom(&#39;H&#39;, [4.375467717853649, 4.5112839336581...  [[4.37546772 4.51128393 6.81784473] H, [0.4573...      1.342423
10985  (Atom(&#39;Si&#39;, [0.0, 0.0, 0.0], index=0), Atom(&#39;S...  [[0. 0. 0.] Si, [ 4.55195829  4.55195829 -4.55...      1.770852
10986  (Atom(&#39;Al&#39;, [1.44565668, 0.0, 2.05259079], ind...  [[1.44565668 0.         2.05259079] Al, [1.445...      1.954243
</pre></div>
</div>
<p>Other matminer ConversionFeaturizers include:</p>
<ul class="simple">
<li><p>Adding oxidation states to pymatgen structures and compositions</p></li>
<li><p>converting to/from Composition object/string representations of composition</p></li>
<li><p>converting objects to/from json representation</p></li>
<li><p>Accessing arbitrary methods and attributes of pymatgen classes with <code class="code docutils literal notranslate"><span class="pre">PymatgenFunctionApplicator</span></code></p></li>
</ul>
</section>
</section>
<section id="examples">
<h2>Examples<a class="headerlink" href="#examples" title="Permalink to this heading">¶</a></h2>
<p>Check out some examples of how to use matminer!</p>
<ol class="arabic simple" start="0">
<li><p>Examples index. (<a class="reference external" href="https://nbviewer.jupyter.org/github/hackingmaterials/matminer_examples/blob/main/matminer_examples/index.ipynb">Jupyter Notebook</a>)</p></li>
<li><p>Use matminer and scikit-learn to create a model that predicts bulk modulus of materials. (<a class="reference external" href="https://nbviewer.jupyter.org/github/hackingmaterials/matminer_examples/blob/main/matminer_examples/machine_learning-nb/bulk_modulus.ipynb">Jupyter Notebook</a>)</p></li>
<li><p>Compare and plot experimentally band gaps from Citrine with computed values from the Materials Project (<a class="reference external" href="https://nbviewer.jupyter.org/github/hackingmaterials/matminer_examples/blob/main/matminer_examples/data_retrieval-nb/expt_vs_comp_bandgap.ipynb">Jupyter Notebook</a>)</p></li>
<li><p>Compare and plot U-O bond lengths in various compounds from the MPDS (<a class="reference external" href="https://nbviewer.jupyter.org/github/hackingmaterials/matminer_examples/blob/main/matminer_examples/data_retrieval-nb/mpds.ipynb">Jupyter Notebook</a>)</p></li>
<li><p>Retrieve data from various online materials repositories (<a class="reference external" href="https://nbviewer.jupyter.org/github/hackingmaterials/matminer_examples/blob/main/matminer_examples/data_retrieval-nb/data_retrieval_basics.ipynb">Jupyter Notebook</a>)</p></li>
<li><p>Basic Visualization using FigRecipes (<a class="reference external" href="https://nbviewer.jupyter.org/github/hackingmaterials/matminer_examples/blob/main/matminer_examples/figrecipes-nb/figrecipes_basics.ipynb">Jupyter Notebook</a>)</p></li>
<li><p>Advanced Visualization (<a class="reference external" href="https://nbviewer.jupyter.org/github/hackingmaterials/matminer_examples/blob/main/matminer_examples/figrecipes-nb/figrecipes_advanced.ipynb">Jupyter Notebook</a>)</p></li>
<li><p>Many more examples! See the <a class="reference external" href="https://github.com/hackingmaterials/matminer_examples">matminer_examples</a> repo for details.</p></li>
</ol>
</section>
<section id="citations-and-changelog">
<h2>Citations and Changelog<a class="headerlink" href="#citations-and-changelog" title="Permalink to this heading">¶</a></h2>
<section id="citing-matminer">
<h3>Citing matminer<a class="headerlink" href="#citing-matminer" title="Permalink to this heading">¶</a></h3>
<p>If you find matminer useful, please encourage its development by citing the following paper in your research</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>Ward, L., Dunn, A., Faghaninia, A., Zimmermann, N. E. R., Bajaj, S., Wang, Q.,
Montoya, J. H., Chen, J., Bystrom, K., Dylla, M., Chard, K., Asta, M., Persson,
K., Snyder, G. J., Foster, I., Jain, A., Matminer: An open source toolkit for
materials data mining. Comput. Mater. Sci. 152, 60-69 (2018).
</pre></div>
</div>
<p>Matminer helps users apply methods and data sets developed by the community. Please also cite the original sources, as this will add clarity to your article and credit the original authors:</p>
<ul class="simple">
<li><p>If you use one or more <strong>data retrieval methods</strong>, use the <code class="docutils literal notranslate"><span class="pre">citations()</span></code> method present for every data_retrieval class in matminer. This function will provide a list of BibTeX-formatted citations for that featurizer, making it easy to keep track of and cite the original publications.</p></li>
<li><p>If you use one or more <strong>featurizers</strong>, please take advantage of the <code class="docutils literal notranslate"><span class="pre">citations()</span></code> method present for every featurizer in matminer.</p></li>
<li><p>If you use one or more <strong>datasets</strong>, please check the metadata of the dataset for a comprehensive list of BibTex formatted citations to use.</p></li>
</ul>
</section>
<section id="changelog">
<h3>Changelog<a class="headerlink" href="#changelog" title="Permalink to this heading">¶</a></h3>
<p>Check out our full changelog <a class="reference internal" href="changelog.html"><span class="doc">here.</span></a></p>
</section>
<section id="contributions-and-support">
<h3>Contributions and Support<a class="headerlink" href="#contributions-and-support" title="Permalink to this heading">¶</a></h3>
<p>Want to see something added or changed? Here’s a few ways you can!</p>
<ul class="simple">
<li><p>Help us improve the documentation. Tell us where you got ‘stuck’ and improve the install process for everyone.</p></li>
<li><p>Let us know about areas of the code that are difficult to understand or use.</p></li>
<li><p>Contribute code! Fork our <a class="reference external" href="https://github.com/hackingmaterials/matminer">Github repo</a> and make a pull request.</p></li>
</ul>
<p>Submit all questions and contact to the <a class="reference external" href="https://discuss.matsci.org/c/matminer">Discourse forum</a></p>
<p>A comprehensive guide to contributions can be found <a class="reference external" href="https://github.com/hackingmaterials/matminer/blob/master/CONTRIBUTING.md">here.</a></p>
<p>A full list of contributors can be found <a class="reference internal" href="contributors.html"><span class="doc">here.</span></a></p>
</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="#">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">matminer</a><ul>
<li><a class="reference internal" href="#related-software">Related software</a></li>
<li><a class="reference internal" href="#quick-links">Quick Links</a></li>
<li><a class="reference internal" href="#installation">Installation</a></li>
<li><a class="reference internal" href="#overview">Overview</a><ul>
<li><a class="reference internal" href="#featurizers-generate-descriptors-for-materials">Featurizers generate descriptors for materials</a></li>
<li><a class="reference internal" href="#data-retrieval-easily-puts-complex-online-data-into-dataframes">Data retrieval easily puts complex online data into dataframes</a></li>
<li><a class="reference internal" href="#access-ready-made-datasets-in-one-line">Access ready-made datasets in one line</a></li>
<li><a class="reference internal" href="#data-munging-with-conversion-featurizers">Data munging with Conversion Featurizers</a></li>
</ul>
</li>
<li><a class="reference internal" href="#examples">Examples</a></li>
<li><a class="reference internal" href="#citations-and-changelog">Citations and Changelog</a><ul>
<li><a class="reference internal" href="#citing-matminer">Citing matminer</a></li>
<li><a class="reference internal" href="#changelog">Changelog</a></li>
<li><a class="reference internal" href="#contributions-and-support">Contributions and Support</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/index.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="#">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer (Materials Data Mining)</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2015, Anubhav Jain.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.3.0.
    </div>

  </body>
</html>