##This part of the code refers to https://github.com/atomistic-machine-learning/SchNet

import torch.nn as nn
from symmcd.pl_modules.model import build_mlp_con
import hydra
import torch
import math
import sys
from typing import Dict, Optional, List, Callable, Union, Sequence
from omegaconf import ListConfig
from symmcd.common.data_utils import get_atomic_number
from symmcd.common.data_utils import chemical_symbols

# MAX_ATOMIC_NUM = 94 ## MatterGen
MAX_ATOMIC_NUM = 94

class ConditioningModule(nn.Module):
    def __init__(
        self,
        n_features,
        n_layers,
        condition_embeddings,
        ):

        super(ConditioningModule, self).__init__()
        self.n_features = n_features
        self.condition_embeddings = condition_embeddings
        condition_embModel = []
        # self.condition_embModel = condition_embeddings
        n_in = 0
        for condition_emb in self.condition_embeddings:
            condition_embModel.append(hydra.utils.instantiate(condition_emb))
            n_in = condition_emb.n_features
        self.condition_embModel = nn.ModuleList(condition_embModel)

        self.dense_net = build_mlp_con(
            in_dim=n_in,
            out_dim=self.n_features,
            hidden_dim=self.n_features,
            fc_num_layers=n_layers,
            norm=False,
        )

    def forward(self, inputs: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        # embed all conditions
        is_initialize = False
        
        for emb in self.condition_embModel:
            emb_feature, cond_name = emb(inputs)
            # print('shape_emb_feature:', emb_feature.shape)
            if is_initialize == False:
                emb_features = torch.zeros_like(emb_feature)
                is_initialize = True
            emb_features += emb_feature

        # mix the concatenated features
        conditional_features = self.dense_net(emb_features)
        # print('conditional_features:', conditional_features.shape)
        return conditional_features

class ConditioningModule_like_MatterGen(nn.Module):
    def __init__(
        self,
        n_features,
        n_layers,
        condition_embeddings,
        n_cond,
        ):

        super(ConditioningModule_like_MatterGen, self).__init__()
        self.n_features = n_features
        self.condition_embeddings = condition_embeddings
        condition_embModel = []
        # self.condition_embModel = condition_embeddings
        for condition_emb in self.condition_embeddings:
            condition_embModel.append(hydra.utils.instantiate(condition_emb))
        self.condition_embModel = nn.ModuleList(condition_embModel)


    def forward(self, inputs: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        # embed all conditions
        is_initialize = False
        conditions_adapt_dict = {}

        for emb in self.condition_embModel:
            emb_feature, cond_name = emb(inputs)
            # print('\n')
            # print('cond_name:', cond_name)
            # print('emb_feature_shape:', emb_feature.shape)
            conditions_adapt_dict[cond_name] = emb_feature

        return conditions_adapt_dict


class ConditionEmbedding(nn.Module):

    def __init__(
        self,
        condition_name: str,
        n_features: int,
        required_data_properties: Optional[List[str]] = [],
        condition_type: str = "trajectory",
    ):

        super().__init__()
        if condition_type not in ["trajectory", "step", "atom"]:
            raise ValueError(
                f"`condition_type` is {condition_type} but needs to be `trajectory`, "
                f"`step`, or `atom` for trajectory-wise, step-wise, or atom-wise "
                f"conditions, respectively."
            )
        self.condition_name = condition_name
        self.condition_type = condition_type
        self.n_features = n_features
        self.required_data_properties = required_data_properties

    def forward(
        self,
        inputs: Dict[str, torch.Tensor],
    ) -> torch.Tensor:
        raise NotImplementedError

# class ScalarConditionEmbedding(ConditionEmbedding):
class ScalarConditionEmbedding_classifier(nn.Module):
    def __init__(
        self,
        condition_name: str,
        condition_min: float,
        condition_max: float,
        grid_spacing: float,
        n_features: int,
        n_layers: int,
        required_data_properties: Optional[List[str]] = [],
        condition_type: str = "trajectory",
    ):
        super(ScalarConditionEmbedding_classifier, self).__init__()
        # super().__init__(
        #     condition_name, n_features, required_data_properties, condition_type
        # )
        self.condition_name = condition_name
        self.condition_type = condition_type
        self.n_features = n_features
        self.required_data_properties = required_data_properties
        # compute the number of rbfs
        n_rbf = math.ceil((condition_max - condition_min) / grid_spacing) + 1
        # compute the position of the last rbf
        _max = condition_min + grid_spacing * (n_rbf - 1)
        # initialize Gaussian rbf expansion network
        self.gaussian_expansion = GaussianRBF(
            n_rbf=n_rbf, cutoff=_max, start=condition_min
        )
        # initialize fully connected network
        self.dense_net = build_mlp_con(
            in_dim=n_rbf,
            hidden_dim=n_features,
            fc_num_layers = n_layers,
            out_dim=n_features,
            norm=False,
        )

    def forward(
        self,
        inputs: Dict[str, torch.Tensor],
    ) -> torch.Tensor:
        # get the scalar condition value
        
        # print('inputs:', inputs['prop'])
        # print('self.condition_name:', self.condition_name)
        
        scalar_condition = torch.Tensor(inputs['prop'][self.condition_name]).cuda().float()
        # print('scalar_condition:', scalar_condition)
        # expand the scalar value with Gaussian rbfs
        expanded_condition = self.gaussian_expansion(scalar_condition)
        # print('expanded_condition:', expanded_condition)
        # print('dense_net:', self.dense_net)
        # feed through fully connected network
        embedded_condition = self.dense_net(expanded_condition)
        # return embedded_condition, self.condition_name
        return embedded_condition

# class ScalarConditionEmbedding(ConditionEmbedding):
class ScalarConditionEmbedding(nn.Module):
    def __init__(
        self,
        condition_name: str,
        condition_min: float,
        condition_max: float,
        grid_spacing: float,
        n_features: int,
        n_layers: int,
        required_data_properties: Optional[List[str]] = [],
        condition_type: str = "trajectory",
    ):
        super(ScalarConditionEmbedding, self).__init__()
        # super().__init__(
        #     condition_name, n_features, required_data_properties, condition_type
        # )
        self.condition_name = condition_name
        self.condition_type = condition_type
        self.n_features = n_features
        self.required_data_properties = required_data_properties
        # compute the number of rbfs
        n_rbf = math.ceil((condition_max - condition_min) / grid_spacing) + 1
        # compute the position of the last rbf
        _max = condition_min + grid_spacing * (n_rbf - 1)
        # initialize Gaussian rbf expansion network
        self.gaussian_expansion = GaussianRBF(
            n_rbf=n_rbf, cutoff=_max, start=condition_min
        )
        # initialize fully connected network
        self.dense_net = build_mlp_con(
            in_dim=n_rbf,
            hidden_dim=n_features,
            fc_num_layers = n_layers,
            out_dim=n_features,
            norm=False,
        )

    def forward(
        self,
        inputs: Dict[str, torch.Tensor],
    ) -> torch.Tensor:
        # get the scalar condition value

        scalar_condition = torch.Tensor(inputs[self.condition_name]).cuda().float()
        # print('scalar_condition:', scalar_condition)
        # expand the scalar value with Gaussian rbfs
        expanded_condition = self.gaussian_expansion(scalar_condition)
        # print('expanded_condition:', expanded_condition)
        # feed through fully connected network
        embedded_condition = self.dense_net(expanded_condition)
        #return embedded_condition, self.condition_name
        return embedded_condition

# class ScalarConditionEmbedding(ConditionEmbedding):
class ScalarConditionEmbedding_like_MatterGen(nn.Module):
    def __init__(
        self,
        condition_name: str,
        condition_min: float,
        condition_max: float,
        grid_spacing: float,
        n_features: int,
        n_layers: int,
        required_data_properties: Optional[List[str]] = [],
        condition_type: str = "trajectory",
    ):
        super(ScalarConditionEmbedding_like_MatterGen, self).__init__()
        # super().__init__(
        #     condition_name, n_features, required_data_properties, condition_type
        # )
        self.condition_name = condition_name
        self.condition_type = condition_type
        self.n_features = n_features
        self.required_data_properties = required_data_properties
        # compute the number of rbfs
        n_rbf = math.ceil((condition_max - condition_min) / grid_spacing) + 1
        # compute the position of the last rbf
        _max = condition_min + grid_spacing * (n_rbf - 1)
        # initialize Gaussian rbf expansion network
        self.gaussian_expansion = GaussianRBF(
            n_rbf=n_rbf, cutoff=_max, start=condition_min
        )
        # initialize fully connected network
        self.dense_net = build_mlp_con(
            in_dim=n_rbf,
            hidden_dim=n_features,
            fc_num_layers = n_layers,
            out_dim=n_features,
            norm=False,
        )
        # self.embedding = torch.nn.Linear(n_rbf, n_features)

    def forward(
        self,
        inputs: Dict[str, torch.Tensor],
    ) -> torch.Tensor:
        # get the scalar condition value

        scalar_condition = torch.Tensor(inputs[self.condition_name]).cuda().float()
       
        # expand the scalar value with Gaussian rbfs
        expanded_condition = self.gaussian_expansion(scalar_condition)
        # print('expanded_condition:', expanded_condition)
        # feed through fully connected network
        embedded_condition = self.dense_net(expanded_condition)  
  
        return embedded_condition, self.condition_name
    
class ChemicalSystemMultiHotEmbedding(torch.nn.Module):
    def __init__(
        self, 
        condition_name: str,
        n_features: int,
        ):
        super(ChemicalSystemMultiHotEmbedding, self).__init__()
        self.condition_name = condition_name
        self.n_features = n_features
        self.embedding = torch.nn.Linear(in_features=MAX_ATOMIC_NUM + 1, out_features=n_features)
    
    @property
    def device(self):
        return next(self.parameters()).device

    @staticmethod
    def _sequence_to_multi_hot(x: Sequence[str], device: torch.device) -> torch.Tensor:
        """
        Converts a sequence of unique elements present in a single structure to a multi-hot
        vectors of 1s (present) and 0s (not present) for each unique element.

        Returns
        -------
        torch.Tensor, shape = (1, MAX_ATOMIC_NUM + 1)
        """
        # torch.LongTensor of indices of each element in the sequence
        chemical_system_numbers: torch.LongTensor = torch.tensor(
            [get_atomic_number(symbol=_element) for _element in x], dtype=int, device=device
        )
        # 1-d vectors of 1s and 0s for each unique element
        chemical_system_condition = torch.zeros(MAX_ATOMIC_NUM + 1, device=device)
        # set 1s for elements that are present
        chemical_system_condition[chemical_system_numbers] = 1.0
        return chemical_system_condition.reshape(1, -1)

    @staticmethod
    def sequences_to_multi_hot(x: list[list[str]], device: torch.device) -> torch.Tensor:
        """
        Convert a list of sequences of unique elements present in a list of structures to a multi-hot
        tensor of 1s (present) and 0s (not present) for each unique element.

        Returns
        -------
        torch.Tensor, shape = (n_structures_in_batch, MAX_ATOMIC_NUM + 1)
        """
        return torch.cat(
            [ChemicalSystemMultiHotEmbedding._sequence_to_multi_hot(_x, device=device) for _x in x],
            dim=0,
        )

    @staticmethod
    def convert_to_list_of_str(x: list[str]) -> list[list[str]]:
        """
        Returns
        -------
        list[list[str]] -- a list of length n_structures_in_batch of chemical systems for each structure
            where the chemical system is specified as a list of unique elements in the structure.
        """
        x = [_x.split("-") for _x in x if isinstance(_x, str)]

        return x  # type: ignore
    
    def forward(
        self,
        inputs: Dict[str, torch.Tensor],
    ) -> torch.Tensor:
        """
        Keyword arguments
        -----------------
        x: Union[list[str], list[Sequence[str]]] -- if elements are a string, they are assumed to be
            a '-' delimited list of unique elements. If a sequence of strings, it is assumed to be a list of
            unique elements in the structure.
        """
        # make sure each chemical system is specified as a list of unique elements in the structure
        # list[list[str]]

        inputs_data = inputs[self.condition_name]

        try:
            # alex_mp
            x = self.convert_to_list_of_str(inputs_data)
            multi_hot_representation: torch.Tensor = self.sequences_to_multi_hot(x=x, device=self.device)  # type: ignore
        except:
            # mp_20
            chemical_system_data = []
            for data in inputs_data:
                chemical_system = ''
                data = data.split("'")
                for element in data:
                    if element in chemical_symbols:
                        chemical_system += element + '-'
                chemical_system_data.append(chemical_system[:-1])
            # print('chemical_system_data:', chemical_system_data)
            x = self.convert_to_list_of_str(chemical_system_data)
            # shape=(n_structures_in_batch, MAX_ATOMIC_NUM + 1)
            multi_hot_representation: torch.Tensor = self.sequences_to_multi_hot(x=x, device=self.device)  # type: ignore
        # print( multi_hot_representation)
        # print('multi_hot_representation:', multi_hot_representation)
        # print('multi_hot_representation_shape:', multi_hot_representation.shape)
        return self.embedding(multi_hot_representation), self.condition_name

    
class ClassConditionEmbedding(nn.Module):
    def __init__(
        self,
        condition_name: str,
        n_type: int,
        n_emb: int,
        n_features: int,
        n_layers: int,
        required_data_properties: Optional[List[str]] = [],
        condition_type: str = "trajectory",
    ):
        super(ClassConditionEmbedding, self).__init__()
        self.condition_name = condition_name
        self.n_type = n_type
        self.embedding_layer = nn.Embedding(n_type, n_emb)

        self.dense_net = build_mlp_con(
            in_dim=n_emb,
            hidden_dim=n_features,
            fc_num_layers=n_layers,
            out_dim=n_features,
            norm=False,
        )

    def forward(
            self,
            inputs: Dict[str, torch.Tensor],
    ) -> torch.Tensor:
        #print(torch.Tensor(inputs[self.condition_name]))
        emb_input = torch.Tensor(inputs[self.condition_name]).cuda().int() #emb_input = inputs[self.condition_name].int()
        emb_condition = self.embedding_layer(emb_input)
        embedded_condition = self.dense_net(emb_condition)
        
        #return embedded_condition, self.condition_name
        return embedded_condition


class VectorialConditionEmbedding(nn.Module):
    """
    An embedding network for vectorial conditions (e.g. a fingerprint). The vector is
    mapped to the final embedding with a fully connected neural network.
    """

    def __init__(
        self,
        condition_name: str,
        n_in: int,
        n_features: int,
        n_layers: int,
        required_data_properties: Optional[List[str]] = [],
        condition_type: str = "trajectory",
    ):

        super(VectorialConditionEmbedding, self).__init__()
        self.condition_name = condition_name
        # initialize fully connected network
        self.dense_net = build_mlp_con(
            in_dim=n_in,
            hidden_dim=n_features,
            fc_num_layers=n_layers,
            out_dim=n_features,
            norm=False,
        )

    def forward(
        self,
        inputs: Dict[str, torch.Tensor],
    ) -> torch.Tensor:
        # get the vectorial condition value
        vectorial_condition = inputs[self.condition_name]
        # feed through fully connected network
        embedded_condition = self.dense_net(vectorial_condition)
        return embedded_condition


class GaussianRBF(nn.Module):
    r"""Gaussian radial basis functions."""

    def __init__(
        self, n_rbf: int, cutoff: float, start: float = 0.0, trainable: bool = False
    ):
        super(GaussianRBF, self).__init__()
        self.n_rbf = n_rbf

        # compute offset and width of Gaussian functions
        offset = torch.linspace(start, cutoff, n_rbf)
        widths = torch.FloatTensor(
            torch.abs(offset[1] - offset[0]) * torch.ones_like(offset)
        )
        if trainable:
            self.widths = nn.Parameter(widths)
            self.offsets = nn.Parameter(offset)
        else:
            # self.register_buffer("widths", widths)
            # self.register_buffer("offsets", offset)
            self.widths = nn.Parameter(widths)
            self.offsets = nn.Parameter(offset)
            self.widths.requires_grad = False
            self.offsets.requires_grad = False

    def forward(self, inputs: torch.Tensor):
        return gaussian_rbf(inputs, self.offsets, self.widths)


def gaussian_rbf(inputs: torch.Tensor, offsets: torch.Tensor, widths: torch.Tensor):
    # print('input de:', inputs.device, file=sys.stdout)
    # print('offset de:', offsets.device, file=sys.stdout)
    # print('widths de:', widths.device, file=sys.stdout)
    coeff = -0.5 / torch.pow(widths, 2)
    diff = inputs[..., None] - offsets
    y = torch.exp(coeff * torch.pow(diff, 2))
    return y.float()