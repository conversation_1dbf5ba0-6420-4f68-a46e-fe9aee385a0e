import math, copy

import numpy as np

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.autograd import Variable

from typing import Any, Dict

import hydra
import omegaconf
import pytorch_lightning as pl
from torch_scatter import scatter
from torch_scatter.composite import scatter_softmax
from torch_geometric.utils import to_dense_adj, dense_to_sparse
from tqdm import tqdm

from symmcd.common.utils import PROJECT_ROOT
from symmcd.common.data_utils import (
    EPSILON, cart_to_frac_coords, mard, lengths_angles_to_volume, lattice_params_to_matrix_torch,
    frac_to_cart_coords, min_distance_sqr_pbc)

from symmcd.pl_modules.lattice.crystal_family import CrystalFamily
from symmcd.pl_modules.diff_utils import d_log_p_wrapped_normal

MAX_ATOMIC_NUM=100


class BaseModule(pl.LightningModule):
    def __init__(self, *args, **kwargs) -> None:
        super().__init__()
        # populate self.hparams with args and kwargs automagically!
        self.save_hyperparameters()
        if hasattr(self.hparams, "model"):
            self._hparams = self.hparams.model

    def configure_optimizers(self):
        opt = hydra.utils.instantiate(
            self.hparams.optim.optimizer, params=self.parameters(), _convert_="partial"
        )
        if not self.hparams.optim.use_lr_scheduler:
            return [opt]
        scheduler = hydra.utils.instantiate(
            self.hparams.optim.lr_scheduler, optimizer=opt
        )
        # return {"optimizer": opt, "lr_scheduler": scheduler, "monitor": "val_loss"}
        return {"optimizer": opt, "lr_scheduler": scheduler, "monitor": "train_loss"}

### Model definition

class SinusoidalTimeEmbeddings(nn.Module):
    """ Attention is all you need. """
    def __init__(self, dim):
        super().__init__()
        self.dim = dim

    def forward(self, time):
        device = time.device
        half_dim = self.dim // 2
        embeddings = math.log(10000) / (half_dim - 1)
        embeddings = torch.exp(torch.arange(half_dim, device=device) * -embeddings)
        embeddings = time[:, None] * embeddings[None, :]
        embeddings = torch.cat((embeddings.sin(), embeddings.cos()), dim=-1)
        return embeddings


class CSPDiffusion(BaseModule):
    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)

        self.decoder = hydra.utils.instantiate(self.hparams.decoder, latent_dim = self.hparams.time_dim, _recursive_=False)
        self.condition_model = hydra.utils.instantiate(self.hparams.conditionmodel, _recursive_=False)

    def forward(self, batch, batch_idx = None):
    
        # print(batch)
        conditions_adapt_dict, conditions_adapt_mask_dict = self.condition_model(batch)
        # print('conditions_adapt_dict:', conditions_adapt_dict)
        pred_logits = self.decoder(conditions_adapt_dict, conditions_adapt_mask_dict, batch.batch)
        
        # target_class = torch.ones_like(pred_class)
        target_class = batch.conditions['space_group_array'].float()
        # print(target_class.shape)

        '''mse_loss'''
        # pred_class = self.sigmoid(pred_logits)
        # loss_class = F.mse_loss(pred_class, target_class)
    
        '''BCELoss 多目标二分类任务'''
        loss_function = nn.BCELoss()
        loss_class = loss_function(pred_logits, target_class)

        loss = (
            self.hparams.cost_class * loss_class)
        
        return {
            'loss' : loss,
        }

    @torch.no_grad()
    def sample(self, batch, batch_idx = None):

        conditions_adapt_dict, conditions_adapt_mask_dict = self.condition_model(batch)

        pred_class = self.decoder(conditions_adapt_dict, conditions_adapt_mask_dict, batch.batch)

        return pred_class

    def on_after_backward(self):
        # Compute the 2-norm for each layer
        # If using mixed precision, the gradients are already unscaled here

        total_norm = 0.
        for nm,p in self.decoder.named_parameters():
            try:
                param_norm = p.grad.data.norm(2)
                total_norm = total_norm + param_norm.item() ** 2
            except:
                pass
        total_norm = total_norm ** (1. / 2)

        self.log_dict({
            'grad_norm':total_norm
            },
            on_step=True,
            on_epoch=True,
            prog_bar=True,
        )

    def training_step(self, batch: Any, batch_idx: int) -> torch.Tensor:

        output_dict = self(batch, batch_idx)

        loss = output_dict['loss']


        self.log_dict(
            {'train_loss': loss},
            on_step=True,
            on_epoch=True,
            prog_bar=True,
        )

        if loss.isnan() or loss.isinf():
            print(batch_idx)
            return None

        return loss



    def validation_step(self, batch: Any, batch_idx: int) -> torch.Tensor:

        output_dict = self(batch)

        log_dict, loss = self.compute_stats(output_dict, prefix='val')

        self.log_dict(
            log_dict,
            on_step=False,
            on_epoch=True,
            prog_bar=True,
        )
        return loss

    def test_step(self, batch: Any, batch_idx: int) -> torch.Tensor:

        output_dict = self(batch)

        log_dict, loss = self.compute_stats(output_dict, prefix='test')

        self.log_dict(
            log_dict,
        )
        return loss

    def compute_stats(self, output_dict, prefix):

        loss = output_dict['loss']

        log_dict = {
            f'{prefix}_loss': loss,
        }

        return log_dict, loss

    