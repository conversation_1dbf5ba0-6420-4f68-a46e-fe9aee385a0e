import argparse
import torch
from pymatgen.io.cif import CifWriter
from sample_api import dataset_for_classifier_from_csv, dataset_for_classifier_from_pt, dataset_for_predictor, construct_dataset_from_json, generate_sg_dist_from_dataset, construct_dataset_from_syminfo, generate_structures_from_dataset
import os
import time
import numpy as np

def top_k_pt(outs, pt_file, top_k):
    sample_cov = 0
    sample_acc = 0
    cover = 0
    accur = 0
    max_len_label = 0

    pt_list = torch.load(pt_file)
    for pt_data, out in zip(pt_list, outs):
        sample_cov += 1
        # 取出预测概率最高的top_k个空间群
        # print(np.array(out.sort().indices.cpu()))
        pred = list(np.array(out.sort().indices.cpu())[-top_k:])
        space_group_array = np.array(pt_data['space_group_array'].cpu())[0]
        label = np.where(space_group_array == 1)[0]
        if max_len_label < len(label):
            max_len_label = len(label)
        sample_acc += len(label)
        accur += len(set(label) & set(pred))
        if len(set(label) & set(pred)) == len(label):
            cover += 1
        # print('pred:', pred)
        # print('label:', label)
    print('max length of label is:', max_len_label)
    coverage = cover / sample_cov
    accurary = accur / sample_acc
    return coverage, accurary

def main(args):

    tar_dir = args.save_path
    os.makedirs(tar_dir, exist_ok=True)

    if args.json_file != '':
        dataset = construct_dataset_from_json(args.json_file)
    elif args.pt_file != '':
        dataset = dataset_for_classifier_from_pt(args.pt_file)
    elif args.csv_file != '':
        dataset = dataset_for_classifier_from_csv(args.csv_file)
    else:
        dataset = dataset_for_predictor(args.composition, args.band_gap, args.formation_energy)
    
    start_time = time.time()
    outs = generate_sg_dist_from_dataset(args.model_path, dataset, args.batch_size)
    print('推理时间:', time.time() - start_time)

    sg_dist = []
    print('length_of_outputs:', len(outs))
    for out in outs: 
        sg_dist_tensor = out/(out.sum())
        sg_dist_array = np.array(sg_dist_tensor.cpu())
        sg_dist.append(sg_dist_array)

    sg_info_path = './sg_info/sg_dist.pt'
    torch.save(sg_dist, sg_info_path)

    if args.top_k and args.pt_file:
        coverage, accurary = top_k_pt(outs, args.pt_file, args.top_k)
        print(f'top_{args.top_k}的覆盖率为:', coverage)
        print(f'top_{args.top_k}的精确度为:', accurary)




    # print("Saving structures.")
    # for i,structure in enumerate(structure_list):
    #     tar_file = os.path.join(tar_dir, f"{i+1}.cif")
    #     if structure is not None:
    #         writer = CifWriter(structure)
    #         writer.write_file(tar_file)
    #     else:
    #         print(f"{i+1} Error Structure.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--model_path', required=True)
    parser.add_argument('--save_path', required=True)
    parser.add_argument('--batch_size', default=256, type=int)
    parser.add_argument('--composition', default=None, type=str)
    parser.add_argument('--band_gap', type=float)
    parser.add_argument('--formation_energy', type=float)
    parser.add_argument('--json_file', default='', type=str)
    parser.add_argument('--pt_file', default='', type=str)
    parser.add_argument('--csv_file', default='', type=str)
    parser.add_argument('--top_k', type=int)
    args = parser.parse_args()

    main(args)
