
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

    <title>matminer.data_retrieval.tests package &#8212; matminer 0.9.0 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="_static/nature.css" />
    <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="_static/doctools.js"></script>
    <script src="_static/sphinx_highlight.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
 
<link href='https://fonts.googleapis.com/css?family=Lato:400,700' rel='stylesheet' type='text/css'>

  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer.data_retrieval.tests package</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="matminer-data-retrieval-tests-package">
<h1>matminer.data_retrieval.tests package<a class="headerlink" href="#matminer-data-retrieval-tests-package" title="Permalink to this heading">¶</a></h1>
<section id="submodules">
<h2>Submodules<a class="headerlink" href="#submodules" title="Permalink to this heading">¶</a></h2>
</section>
<section id="module-matminer.data_retrieval.tests.base">
<span id="matminer-data-retrieval-tests-base-module"></span><h2>matminer.data_retrieval.tests.base module<a class="headerlink" href="#module-matminer.data_retrieval.tests.base" title="Permalink to this heading">¶</a></h2>
</section>
<section id="module-matminer.data_retrieval.tests.test_retrieve_AFLOW">
<span id="matminer-data-retrieval-tests-test-retrieve-aflow-module"></span><h2>matminer.data_retrieval.tests.test_retrieve_AFLOW module<a class="headerlink" href="#module-matminer.data_retrieval.tests.test_retrieve_AFLOW" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.data_retrieval.tests.test_retrieve_AFLOW.AFLOWDataRetrievalTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.data_retrieval.tests.test_retrieve_AFLOW.</span></span><span class="sig-name descname"><span class="pre">AFLOWDataRetrievalTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.data_retrieval.tests.test_retrieve_AFLOW.AFLOWDataRetrievalTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.data_retrieval.tests.test_retrieve_AFLOW.AFLOWDataRetrievalTest.setUp">
<span class="sig-name descname"><span class="pre">setUp</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.data_retrieval.tests.test_retrieve_AFLOW.AFLOWDataRetrievalTest.setUp" title="Permalink to this definition">¶</a></dt>
<dd><p>Hook method for setting up the test fixture before exercising it.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.data_retrieval.tests.test_retrieve_AFLOW.AFLOWDataRetrievalTest.test_get_data">
<span class="sig-name descname"><span class="pre">test_get_data</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.data_retrieval.tests.test_retrieve_AFLOW.AFLOWDataRetrievalTest.test_get_data" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.data_retrieval.tests.test_retrieve_Citrine">
<span id="matminer-data-retrieval-tests-test-retrieve-citrine-module"></span><h2>matminer.data_retrieval.tests.test_retrieve_Citrine module<a class="headerlink" href="#module-matminer.data_retrieval.tests.test_retrieve_Citrine" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.data_retrieval.tests.test_retrieve_Citrine.CitrineDataRetrievalTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.data_retrieval.tests.test_retrieve_Citrine.</span></span><span class="sig-name descname"><span class="pre">CitrineDataRetrievalTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.data_retrieval.tests.test_retrieve_Citrine.CitrineDataRetrievalTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.data_retrieval.tests.test_retrieve_Citrine.CitrineDataRetrievalTest.setUp">
<span class="sig-name descname"><span class="pre">setUp</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.data_retrieval.tests.test_retrieve_Citrine.CitrineDataRetrievalTest.setUp" title="Permalink to this definition">¶</a></dt>
<dd><p>Hook method for setting up the test fixture before exercising it.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.data_retrieval.tests.test_retrieve_Citrine.CitrineDataRetrievalTest.test_get_data">
<span class="sig-name descname"><span class="pre">test_get_data</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.data_retrieval.tests.test_retrieve_Citrine.CitrineDataRetrievalTest.test_get_data" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.data_retrieval.tests.test_retrieve_Citrine.CitrineDataRetrievalTest.test_multiple_items_in_list">
<span class="sig-name descname"><span class="pre">test_multiple_items_in_list</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.data_retrieval.tests.test_retrieve_Citrine.CitrineDataRetrievalTest.test_multiple_items_in_list" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.data_retrieval.tests.test_retrieve_MDF">
<span id="matminer-data-retrieval-tests-test-retrieve-mdf-module"></span><h2>matminer.data_retrieval.tests.test_retrieve_MDF module<a class="headerlink" href="#module-matminer.data_retrieval.tests.test_retrieve_MDF" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.data_retrieval.tests.test_retrieve_MDF.MDFDataRetrievalTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.data_retrieval.tests.test_retrieve_MDF.</span></span><span class="sig-name descname"><span class="pre">MDFDataRetrievalTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.data_retrieval.tests.test_retrieve_MDF.MDFDataRetrievalTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.data_retrieval.tests.test_retrieve_MDF.MDFDataRetrievalTest.setUpClass">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">setUpClass</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.11)"><span class="pre">None</span></a></span></span><a class="headerlink" href="#matminer.data_retrieval.tests.test_retrieve_MDF.MDFDataRetrievalTest.setUpClass" title="Permalink to this definition">¶</a></dt>
<dd><p>Hook method for setting up class fixture before running tests in the class.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.data_retrieval.tests.test_retrieve_MDF.MDFDataRetrievalTest.test_get_dataframe">
<span class="sig-name descname"><span class="pre">test_get_dataframe</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.data_retrieval.tests.test_retrieve_MDF.MDFDataRetrievalTest.test_get_dataframe" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.data_retrieval.tests.test_retrieve_MDF.MDFDataRetrievalTest.test_get_dataframe_by_query">
<span class="sig-name descname"><span class="pre">test_get_dataframe_by_query</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.data_retrieval.tests.test_retrieve_MDF.MDFDataRetrievalTest.test_get_dataframe_by_query" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.data_retrieval.tests.test_retrieve_MDF.MDFDataRetrievalTest.test_make_dataframe">
<span class="sig-name descname"><span class="pre">test_make_dataframe</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.data_retrieval.tests.test_retrieve_MDF.MDFDataRetrievalTest.test_make_dataframe" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.data_retrieval.tests.test_retrieve_MP">
<span id="matminer-data-retrieval-tests-test-retrieve-mp-module"></span><h2>matminer.data_retrieval.tests.test_retrieve_MP module<a class="headerlink" href="#module-matminer.data_retrieval.tests.test_retrieve_MP" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.data_retrieval.tests.test_retrieve_MP.MPDataRetrievalTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.data_retrieval.tests.test_retrieve_MP.</span></span><span class="sig-name descname"><span class="pre">MPDataRetrievalTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.data_retrieval.tests.test_retrieve_MP.MPDataRetrievalTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.data_retrieval.tests.test_retrieve_MP.MPDataRetrievalTest.setUp">
<span class="sig-name descname"><span class="pre">setUp</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.data_retrieval.tests.test_retrieve_MP.MPDataRetrievalTest.setUp" title="Permalink to this definition">¶</a></dt>
<dd><p>Hook method for setting up the test fixture before exercising it.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.data_retrieval.tests.test_retrieve_MP.MPDataRetrievalTest.test_get_data">
<span class="sig-name descname"><span class="pre">test_get_data</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.data_retrieval.tests.test_retrieve_MP.MPDataRetrievalTest.test_get_data" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.data_retrieval.tests.test_retrieve_MPDS">
<span id="matminer-data-retrieval-tests-test-retrieve-mpds-module"></span><h2>matminer.data_retrieval.tests.test_retrieve_MPDS module<a class="headerlink" href="#module-matminer.data_retrieval.tests.test_retrieve_MPDS" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.data_retrieval.tests.test_retrieve_MPDS.MPDSDataRetrievalTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.data_retrieval.tests.test_retrieve_MPDS.</span></span><span class="sig-name descname"><span class="pre">MPDSDataRetrievalTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.data_retrieval.tests.test_retrieve_MPDS.MPDSDataRetrievalTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.data_retrieval.tests.test_retrieve_MPDS.MPDSDataRetrievalTest.setUp">
<span class="sig-name descname"><span class="pre">setUp</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.data_retrieval.tests.test_retrieve_MPDS.MPDSDataRetrievalTest.setUp" title="Permalink to this definition">¶</a></dt>
<dd><p>Hook method for setting up the test fixture before exercising it.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.data_retrieval.tests.test_retrieve_MPDS.MPDSDataRetrievalTest.test_valid_answer">
<span class="sig-name descname"><span class="pre">test_valid_answer</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.data_retrieval.tests.test_retrieve_MPDS.MPDSDataRetrievalTest.test_valid_answer" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.data_retrieval.tests.test_retrieve_MongoDB">
<span id="matminer-data-retrieval-tests-test-retrieve-mongodb-module"></span><h2>matminer.data_retrieval.tests.test_retrieve_MongoDB module<a class="headerlink" href="#module-matminer.data_retrieval.tests.test_retrieve_MongoDB" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.data_retrieval.tests.test_retrieve_MongoDB.MongoDataRetrievalTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.data_retrieval.tests.test_retrieve_MongoDB.</span></span><span class="sig-name descname"><span class="pre">MongoDataRetrievalTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.data_retrieval.tests.test_retrieve_MongoDB.MongoDataRetrievalTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">PymatgenTest</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.data_retrieval.tests.test_retrieve_MongoDB.MongoDataRetrievalTest.test_cleaned_projection">
<span class="sig-name descname"><span class="pre">test_cleaned_projection</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.data_retrieval.tests.test_retrieve_MongoDB.MongoDataRetrievalTest.test_cleaned_projection" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.data_retrieval.tests.test_retrieve_MongoDB.MongoDataRetrievalTest.test_get_dataframe">
<span class="sig-name descname"><span class="pre">test_get_dataframe</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.data_retrieval.tests.test_retrieve_MongoDB.MongoDataRetrievalTest.test_get_dataframe" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.data_retrieval.tests.test_retrieve_MongoDB.MongoDataRetrievalTest.test_remove_ints">
<span class="sig-name descname"><span class="pre">test_remove_ints</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.data_retrieval.tests.test_retrieve_MongoDB.MongoDataRetrievalTest.test_remove_ints" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.data_retrieval.tests">
<span id="module-contents"></span><h2>Module contents<a class="headerlink" href="#module-matminer.data_retrieval.tests" title="Permalink to this heading">¶</a></h2>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">matminer.data_retrieval.tests package</a><ul>
<li><a class="reference internal" href="#submodules">Submodules</a></li>
<li><a class="reference internal" href="#module-matminer.data_retrieval.tests.base">matminer.data_retrieval.tests.base module</a></li>
<li><a class="reference internal" href="#module-matminer.data_retrieval.tests.test_retrieve_AFLOW">matminer.data_retrieval.tests.test_retrieve_AFLOW module</a><ul>
<li><a class="reference internal" href="#matminer.data_retrieval.tests.test_retrieve_AFLOW.AFLOWDataRetrievalTest"><code class="docutils literal notranslate"><span class="pre">AFLOWDataRetrievalTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.data_retrieval.tests.test_retrieve_AFLOW.AFLOWDataRetrievalTest.setUp"><code class="docutils literal notranslate"><span class="pre">AFLOWDataRetrievalTest.setUp()</span></code></a></li>
<li><a class="reference internal" href="#matminer.data_retrieval.tests.test_retrieve_AFLOW.AFLOWDataRetrievalTest.test_get_data"><code class="docutils literal notranslate"><span class="pre">AFLOWDataRetrievalTest.test_get_data()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.data_retrieval.tests.test_retrieve_Citrine">matminer.data_retrieval.tests.test_retrieve_Citrine module</a><ul>
<li><a class="reference internal" href="#matminer.data_retrieval.tests.test_retrieve_Citrine.CitrineDataRetrievalTest"><code class="docutils literal notranslate"><span class="pre">CitrineDataRetrievalTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.data_retrieval.tests.test_retrieve_Citrine.CitrineDataRetrievalTest.setUp"><code class="docutils literal notranslate"><span class="pre">CitrineDataRetrievalTest.setUp()</span></code></a></li>
<li><a class="reference internal" href="#matminer.data_retrieval.tests.test_retrieve_Citrine.CitrineDataRetrievalTest.test_get_data"><code class="docutils literal notranslate"><span class="pre">CitrineDataRetrievalTest.test_get_data()</span></code></a></li>
<li><a class="reference internal" href="#matminer.data_retrieval.tests.test_retrieve_Citrine.CitrineDataRetrievalTest.test_multiple_items_in_list"><code class="docutils literal notranslate"><span class="pre">CitrineDataRetrievalTest.test_multiple_items_in_list()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.data_retrieval.tests.test_retrieve_MDF">matminer.data_retrieval.tests.test_retrieve_MDF module</a><ul>
<li><a class="reference internal" href="#matminer.data_retrieval.tests.test_retrieve_MDF.MDFDataRetrievalTest"><code class="docutils literal notranslate"><span class="pre">MDFDataRetrievalTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.data_retrieval.tests.test_retrieve_MDF.MDFDataRetrievalTest.setUpClass"><code class="docutils literal notranslate"><span class="pre">MDFDataRetrievalTest.setUpClass()</span></code></a></li>
<li><a class="reference internal" href="#matminer.data_retrieval.tests.test_retrieve_MDF.MDFDataRetrievalTest.test_get_dataframe"><code class="docutils literal notranslate"><span class="pre">MDFDataRetrievalTest.test_get_dataframe()</span></code></a></li>
<li><a class="reference internal" href="#matminer.data_retrieval.tests.test_retrieve_MDF.MDFDataRetrievalTest.test_get_dataframe_by_query"><code class="docutils literal notranslate"><span class="pre">MDFDataRetrievalTest.test_get_dataframe_by_query()</span></code></a></li>
<li><a class="reference internal" href="#matminer.data_retrieval.tests.test_retrieve_MDF.MDFDataRetrievalTest.test_make_dataframe"><code class="docutils literal notranslate"><span class="pre">MDFDataRetrievalTest.test_make_dataframe()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.data_retrieval.tests.test_retrieve_MP">matminer.data_retrieval.tests.test_retrieve_MP module</a><ul>
<li><a class="reference internal" href="#matminer.data_retrieval.tests.test_retrieve_MP.MPDataRetrievalTest"><code class="docutils literal notranslate"><span class="pre">MPDataRetrievalTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.data_retrieval.tests.test_retrieve_MP.MPDataRetrievalTest.setUp"><code class="docutils literal notranslate"><span class="pre">MPDataRetrievalTest.setUp()</span></code></a></li>
<li><a class="reference internal" href="#matminer.data_retrieval.tests.test_retrieve_MP.MPDataRetrievalTest.test_get_data"><code class="docutils literal notranslate"><span class="pre">MPDataRetrievalTest.test_get_data()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.data_retrieval.tests.test_retrieve_MPDS">matminer.data_retrieval.tests.test_retrieve_MPDS module</a><ul>
<li><a class="reference internal" href="#matminer.data_retrieval.tests.test_retrieve_MPDS.MPDSDataRetrievalTest"><code class="docutils literal notranslate"><span class="pre">MPDSDataRetrievalTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.data_retrieval.tests.test_retrieve_MPDS.MPDSDataRetrievalTest.setUp"><code class="docutils literal notranslate"><span class="pre">MPDSDataRetrievalTest.setUp()</span></code></a></li>
<li><a class="reference internal" href="#matminer.data_retrieval.tests.test_retrieve_MPDS.MPDSDataRetrievalTest.test_valid_answer"><code class="docutils literal notranslate"><span class="pre">MPDSDataRetrievalTest.test_valid_answer()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.data_retrieval.tests.test_retrieve_MongoDB">matminer.data_retrieval.tests.test_retrieve_MongoDB module</a><ul>
<li><a class="reference internal" href="#matminer.data_retrieval.tests.test_retrieve_MongoDB.MongoDataRetrievalTest"><code class="docutils literal notranslate"><span class="pre">MongoDataRetrievalTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.data_retrieval.tests.test_retrieve_MongoDB.MongoDataRetrievalTest.test_cleaned_projection"><code class="docutils literal notranslate"><span class="pre">MongoDataRetrievalTest.test_cleaned_projection()</span></code></a></li>
<li><a class="reference internal" href="#matminer.data_retrieval.tests.test_retrieve_MongoDB.MongoDataRetrievalTest.test_get_dataframe"><code class="docutils literal notranslate"><span class="pre">MongoDataRetrievalTest.test_get_dataframe()</span></code></a></li>
<li><a class="reference internal" href="#matminer.data_retrieval.tests.test_retrieve_MongoDB.MongoDataRetrievalTest.test_remove_ints"><code class="docutils literal notranslate"><span class="pre">MongoDataRetrievalTest.test_remove_ints()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.data_retrieval.tests">Module contents</a></li>
</ul>
</li>
</ul>

  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/matminer.data_retrieval.tests.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer.data_retrieval.tests package</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2015, Anubhav Jain.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.3.0.
    </div>

  </body>
</html>