
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

    <title>matminer.utils package &#8212; matminer 0.9.0 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="_static/nature.css" />
    <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="_static/doctools.js"></script>
    <script src="_static/sphinx_highlight.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
 
<link href='https://fonts.googleapis.com/css?family=Lato:400,700' rel='stylesheet' type='text/css'>

  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer.utils package</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="matminer-utils-package">
<h1>matminer.utils package<a class="headerlink" href="#matminer-utils-package" title="Permalink to this heading">¶</a></h1>
<section id="subpackages">
<h2>Subpackages<a class="headerlink" href="#subpackages" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="matminer.utils.data_files.html">matminer.utils.data_files package</a><ul>
<li class="toctree-l2"><a class="reference internal" href="matminer.utils.data_files.html#submodules">Submodules</a></li>
<li class="toctree-l2"><a class="reference internal" href="matminer.utils.data_files.html#module-matminer.utils.data_files.deml_elementdata">matminer.utils.data_files.deml_elementdata module</a></li>
<li class="toctree-l2"><a class="reference internal" href="matminer.utils.data_files.html#module-matminer.utils.data_files">Module contents</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="matminer.utils.tests.html">matminer.utils.tests package</a><ul>
<li class="toctree-l2"><a class="reference internal" href="matminer.utils.tests.html#submodules">Submodules</a></li>
<li class="toctree-l2"><a class="reference internal" href="matminer.utils.tests.html#module-matminer.utils.tests.test_caching">matminer.utils.tests.test_caching module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_caching.TestCaching"><code class="docutils literal notranslate"><span class="pre">TestCaching</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_caching.TestCaching.test_cache"><code class="docutils literal notranslate"><span class="pre">TestCaching.test_cache()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.utils.tests.html#module-matminer.utils.tests.test_data">matminer.utils.tests.test_data module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_data.TestDemlData"><code class="docutils literal notranslate"><span class="pre">TestDemlData</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_data.TestDemlData.setUp"><code class="docutils literal notranslate"><span class="pre">TestDemlData.setUp()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_data.TestDemlData.test_get_oxidation"><code class="docutils literal notranslate"><span class="pre">TestDemlData.test_get_oxidation()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_data.TestDemlData.test_get_property"><code class="docutils literal notranslate"><span class="pre">TestDemlData.test_get_property()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_data.TestIUCrBondValenceData"><code class="docutils literal notranslate"><span class="pre">TestIUCrBondValenceData</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_data.TestIUCrBondValenceData.setUp"><code class="docutils literal notranslate"><span class="pre">TestIUCrBondValenceData.setUp()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_data.TestIUCrBondValenceData.test_get_data"><code class="docutils literal notranslate"><span class="pre">TestIUCrBondValenceData.test_get_data()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_data.TestMEGNetData"><code class="docutils literal notranslate"><span class="pre">TestMEGNetData</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_data.TestMEGNetData.setUp"><code class="docutils literal notranslate"><span class="pre">TestMEGNetData.setUp()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_data.TestMEGNetData.test_get_property"><code class="docutils literal notranslate"><span class="pre">TestMEGNetData.test_get_property()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_data.TestMagpieData"><code class="docutils literal notranslate"><span class="pre">TestMagpieData</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_data.TestMagpieData.setUp"><code class="docutils literal notranslate"><span class="pre">TestMagpieData.setUp()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_data.TestMagpieData.test_get_oxidation"><code class="docutils literal notranslate"><span class="pre">TestMagpieData.test_get_oxidation()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_data.TestMagpieData.test_get_property"><code class="docutils literal notranslate"><span class="pre">TestMagpieData.test_get_property()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_data.TestMatScholarData"><code class="docutils literal notranslate"><span class="pre">TestMatScholarData</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_data.TestMatScholarData.setUp"><code class="docutils literal notranslate"><span class="pre">TestMatScholarData.setUp()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_data.TestMatScholarData.test_get_property"><code class="docutils literal notranslate"><span class="pre">TestMatScholarData.test_get_property()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_data.TestMixingEnthalpy"><code class="docutils literal notranslate"><span class="pre">TestMixingEnthalpy</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_data.TestMixingEnthalpy.setUp"><code class="docutils literal notranslate"><span class="pre">TestMixingEnthalpy.setUp()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_data.TestMixingEnthalpy.test_get_data"><code class="docutils literal notranslate"><span class="pre">TestMixingEnthalpy.test_get_data()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_data.TestPymatgenData"><code class="docutils literal notranslate"><span class="pre">TestPymatgenData</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_data.TestPymatgenData.setUp"><code class="docutils literal notranslate"><span class="pre">TestPymatgenData.setUp()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_data.TestPymatgenData.test_get_oxidation"><code class="docutils literal notranslate"><span class="pre">TestPymatgenData.test_get_oxidation()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_data.TestPymatgenData.test_get_property"><code class="docutils literal notranslate"><span class="pre">TestPymatgenData.test_get_property()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.utils.tests.html#module-matminer.utils.tests.test_flatten_dict">matminer.utils.tests.test_flatten_dict module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_flatten_dict.FlattenDictTest"><code class="docutils literal notranslate"><span class="pre">FlattenDictTest</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_flatten_dict.FlattenDictTest.test_flatten_nested_dict"><code class="docutils literal notranslate"><span class="pre">FlattenDictTest.test_flatten_nested_dict()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.utils.tests.html#module-matminer.utils.tests.test_io">matminer.utils.tests.test_io module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_io.IOTest"><code class="docutils literal notranslate"><span class="pre">IOTest</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_io.IOTest.setUp"><code class="docutils literal notranslate"><span class="pre">IOTest.setUp()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_io.IOTest.tearDown"><code class="docutils literal notranslate"><span class="pre">IOTest.tearDown()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_io.IOTest.test_load_dataframe_from_json"><code class="docutils literal notranslate"><span class="pre">IOTest.test_load_dataframe_from_json()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_io.IOTest.test_store_dataframe_as_json"><code class="docutils literal notranslate"><span class="pre">IOTest.test_store_dataframe_as_json()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.tests.html#matminer.utils.tests.test_io.generate_json_files"><code class="docutils literal notranslate"><span class="pre">generate_json_files()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.utils.tests.html#module-matminer.utils.tests">Module contents</a></li>
</ul>
</li>
</ul>
</div>
</section>
<section id="submodules">
<h2>Submodules<a class="headerlink" href="#submodules" title="Permalink to this heading">¶</a></h2>
</section>
<section id="module-matminer.utils.caching">
<span id="matminer-utils-caching-module"></span><h2>matminer.utils.caching module<a class="headerlink" href="#module-matminer.utils.caching" title="Permalink to this heading">¶</a></h2>
<p>Provides utility functions for caching the results of expensive operations,
such as determining the nearest neighbors of atoms in a structure</p>
<dl class="py function">
<dt class="sig sig-object py" id="matminer.utils.caching.get_all_nearest_neighbors">
<span class="sig-prename descclassname"><span class="pre">matminer.utils.caching.</span></span><span class="sig-name descname"><span class="pre">get_all_nearest_neighbors</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">method</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">structure</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.caching.get_all_nearest_neighbors" title="Permalink to this definition">¶</a></dt>
<dd><p>Get the nearest neighbor list of a structure</p>
<dl class="simple">
<dt>Args:</dt><dd><p>method (NearNeighbor) - Method used to compute nearest neighbors
structure (IStructure) - Structure to study</p>
</dd>
<dt>Returns:</dt><dd><p>Output of <cite>method.get_all_nn_info(structure)</cite></p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.utils.caching.get_nearest_neighbors">
<span class="sig-prename descclassname"><span class="pre">matminer.utils.caching.</span></span><span class="sig-name descname"><span class="pre">get_nearest_neighbors</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">method</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">structure</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">site_idx</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.caching.get_nearest_neighbors" title="Permalink to this definition">¶</a></dt>
<dd><p>Get the nearest neighbor list of a particular site in a structure</p>
<dl class="simple">
<dt>Args:</dt><dd><p>method (NearNeighbor) - Method used to compute nearest neighbors
structure (Structure) - Structure to study
site_idx (int) - Index of site to study</p>
</dd>
<dt>Returns:</dt><dd><p>Output of <cite>method.get_nn_info(structure, site_idx)</cite></p>
</dd>
</dl>
</dd></dl>

</section>
<section id="module-matminer.utils.data">
<span id="matminer-utils-data-module"></span><h2>matminer.utils.data module<a class="headerlink" href="#module-matminer.utils.data" title="Permalink to this heading">¶</a></h2>
<p>Utility classes for retrieving elemental properties. Provides
a uniform interface to several different elemental property resources
including <code class="docutils literal notranslate"><span class="pre">pymatgen</span></code> and <code class="docutils literal notranslate"><span class="pre">Magpie</span></code>.</p>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.utils.data.AbstractData">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.utils.data.</span></span><span class="sig-name descname"><span class="pre">AbstractData</span></span><a class="headerlink" href="#matminer.utils.data.AbstractData" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference external" href="https://docs.python.org/3/library/functions.html#object" title="(in Python v3.11)"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></p>
<p>Abstract class for retrieving elemental properties</p>
<p>All classes must implement the <cite>get_elemental_property</cite> operation. These operations
should return scalar values (ideally floats) and <cite>nan</cite> if a property does not exist</p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.data.AbstractData.get_elemental_properties">
<span class="sig-name descname"><span class="pre">get_elemental_properties</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">elems</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">property_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.AbstractData.get_elemental_properties" title="Permalink to this definition">¶</a></dt>
<dd><p>Get elemental properties for a list of elements</p>
<dl class="simple">
<dt>Args:</dt><dd><p>elems - ([Element]) list of elements
property_name - (str) property to be retrieved</p>
</dd>
<dt>Returns:</dt><dd><p>[float], properties of elements</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.data.AbstractData.get_elemental_property">
<em class="property"><span class="pre">abstract</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">get_elemental_property</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">elem</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">property_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.AbstractData.get_elemental_property" title="Permalink to this definition">¶</a></dt>
<dd><p>Get a certain elemental property for a certain element.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>elem - (Element) element to be assessed
property_name - (str) property to be retrieved</p>
</dd>
<dt>Returns:</dt><dd><p>float, property of that element</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.utils.data.CohesiveEnergyData">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.utils.data.</span></span><span class="sig-name descname"><span class="pre">CohesiveEnergyData</span></span><a class="headerlink" href="#matminer.utils.data.CohesiveEnergyData" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.utils.data.AbstractData" title="matminer.utils.data.AbstractData"><code class="xref py py-class docutils literal notranslate"><span class="pre">AbstractData</span></code></a></p>
<p>Get the cohesive energy of an element.</p>
<p>Data is extracted from KnowledgeDoor Cohesive Energy Handbook online
(<a class="reference external" href="http://www.knowledgedoor.com/2/elements_handbook/cohesive_energy.html">http://www.knowledgedoor.com/2/elements_handbook/cohesive_energy.html</a>),
which in turn got the data from Introduction to Solid State Physics,
8th Edition, by Charles Kittel (ISBN 978-0-471-41526-8), 2005.</p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.data.CohesiveEnergyData.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.CohesiveEnergyData.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.data.CohesiveEnergyData.get_elemental_property">
<span class="sig-name descname"><span class="pre">get_elemental_property</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">elem</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">property_name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'cohesive</span> <span class="pre">energy'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.CohesiveEnergyData.get_elemental_property" title="Permalink to this definition">¶</a></dt>
<dd><dl class="simple">
<dt>Args:</dt><dd><p>elem: (Element) Element of interest
property_name (str): unused, always returns cohesive energy</p>
</dd>
<dt>Returns:</dt><dd><p>(float): cohesive energy of the element</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.utils.data.DemlData">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.utils.data.</span></span><span class="sig-name descname"><span class="pre">DemlData</span></span><a class="headerlink" href="#matminer.utils.data.DemlData" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.utils.data.OxidationStateDependentData" title="matminer.utils.data.OxidationStateDependentData"><code class="xref py py-class docutils literal notranslate"><span class="pre">OxidationStateDependentData</span></code></a>, <a class="reference internal" href="#matminer.utils.data.OxidationStatesMixin" title="matminer.utils.data.OxidationStatesMixin"><code class="xref py py-class docutils literal notranslate"><span class="pre">OxidationStatesMixin</span></code></a></p>
<p>Class to get data from Deml data file. See also: A.M. Deml,
R. O’Hayre, C. Wolverton, V. Stevanovic, Predicting density functional
theory total energies and enthalpies of formation of metal-nonmetal
compounds by linear regression, Phys. Rev. B - Condens. Matter Mater. Phys.
93 (2016).</p>
<p>The meanings of each feature in the data can be found in
./data_files/deml_elementdata.py</p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.data.DemlData.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.DemlData.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.data.DemlData.get_charge_dependent_property">
<span class="sig-name descname"><span class="pre">get_charge_dependent_property</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">element</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">charge</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">property_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.DemlData.get_charge_dependent_property" title="Permalink to this definition">¶</a></dt>
<dd><p>Retrieve a oxidation-state dependent elemental property</p>
<dl class="simple">
<dt>Args:</dt><dd><p>element - (Element), Target element
charge - (int), Oxidation state
property_name - (string), name of property</p>
</dd>
<dt>Return:</dt><dd><p>(float) - Value of property</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.data.DemlData.get_elemental_property">
<span class="sig-name descname"><span class="pre">get_elemental_property</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">elem</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">property_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.DemlData.get_elemental_property" title="Permalink to this definition">¶</a></dt>
<dd><p>Get a certain elemental property for a certain element.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>elem - (Element) element to be assessed
property_name - (str) property to be retrieved</p>
</dd>
<dt>Returns:</dt><dd><p>float, property of that element</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.data.DemlData.get_oxidation_states">
<span class="sig-name descname"><span class="pre">get_oxidation_states</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">elem</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.DemlData.get_oxidation_states" title="Permalink to this definition">¶</a></dt>
<dd><p>Retrieve the possible oxidation states of an element</p>
<dl class="simple">
<dt>Args:</dt><dd><p>elem - (Element), Target element</p>
</dd>
<dt>Returns:</dt><dd><p>[int] - oxidation states</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.utils.data.IUCrBondValenceData">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.utils.data.</span></span><span class="sig-name descname"><span class="pre">IUCrBondValenceData</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">interpolate_soft</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.IUCrBondValenceData" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference external" href="https://docs.python.org/3/library/functions.html#object" title="(in Python v3.11)"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></p>
<p>Get empirical bond valence parameters.</p>
<p>Data come from International Union of Crystallography 2016 tables.
(<a class="reference external" href="https://www.iucr.org/resources/data/datasets/bond-valence-parameters">https://www.iucr.org/resources/data/datasets/bond-valence-parameters</a>)
Both the raw source CIF and cleaned csv file are made accessible here.
Within the source CIF, there are citations for every set of parameters.</p>
<p>The copyright notice and disclaimer are reproduced below
#***************************************************************
# COPYRIGHT NOTICE
# This table may be used and distributed without fee for
# non-profit purposes providing
# 1) that this copyright notice is included and
# 2) no fee is charged for the table and
# 3) details of any changes made in this list by anyone other than
# the copyright owner are suitably noted in the _audit_update record
# Please consult the copyright owner regarding any other uses.
#
# The copyright is owned by I. David Brown, Brockhouse Institute for
# Materials Research, McMaster University, Hamilton, Ontario Canada.
# <a class="reference external" href="mailto:idbrown&#37;&#52;&#48;mcmaster&#46;ca">idbrown<span>&#64;</span>mcmaster<span>&#46;</span>ca</a>
#
#*****************************DISCLAIMER************************
#
# The values reported here are taken from the literature and
# other sources and the author does not warrant their correctness
# nor accept any responsibility for errors.  Users are advised to
# consult the primary sources.
#
#***************************************************************</p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.data.IUCrBondValenceData.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">interpolate_soft</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.IUCrBondValenceData.__init__" title="Permalink to this definition">¶</a></dt>
<dd><p>Load bond valence parameters as pandas dataframe.</p>
<p>If interpolate_soft is True, fill in some missing values
for anions such as I, Br, N, S, Se, etc. with the assumption
that bond valence parameters of such anions don’t depend on
cation oxidation state. This assumption comes from Brese and O’Keeffe,
(1991), Acta Cryst. B47, 194, which states “with less electronegative
anions, … R is not very different for different oxidation states in
general.” In the original data source file, only one set of parameters
is usually provided for those less electronegative anions in a 9+
oxidation state, indicating they can be used with all oxidation states.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.data.IUCrBondValenceData.get_bv_params">
<span class="sig-name descname"><span class="pre">get_bv_params</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cation</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">anion</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cat_val</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">an_val</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.IUCrBondValenceData.get_bv_params" title="Permalink to this definition">¶</a></dt>
<dd><p>Lookup bond valence parameters from IUPAC table.
Args:</p>
<blockquote>
<div><p>cation (Element): cation element
anion (Element): anion element
cat_val (Integer): cation formal oxidation state
an_val (Integer): anion formal oxidation state</p>
</div></blockquote>
<dl class="simple">
<dt>Returns:</dt><dd><p>bond_val_list: dataframe of bond valence parameters</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.data.IUCrBondValenceData.interpolate_soft_anions">
<span class="sig-name descname"><span class="pre">interpolate_soft_anions</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.IUCrBondValenceData.interpolate_soft_anions" title="Permalink to this definition">¶</a></dt>
<dd><p>Fill in missing parameters for oxidation states of soft anions.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.utils.data.MEGNetElementData">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.utils.data.</span></span><span class="sig-name descname"><span class="pre">MEGNetElementData</span></span><a class="headerlink" href="#matminer.utils.data.MEGNetElementData" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.utils.data.AbstractData" title="matminer.utils.data.AbstractData"><code class="xref py py-class docutils literal notranslate"><span class="pre">AbstractData</span></code></a></p>
<p>Class to get neural network embeddings of elements. These embeddings were
generated using the Materials Graph Network (MEGNet) developed by the
MaterialsVirtualLab at U.C. San Diego and described in the publication:</p>
<p>Graph Networks as a Universal Machine Learning Framework for Molecules and
Crystals. Chi Chen, Weike Ye, Yunxing Zuo, Chen Zheng, and Shyue Ping Ong
Chemistry of Materials 2019 31 (9), 3564-3572,
<a class="reference external" href="https://doi.org/10.1021/acs.chemmater.9b01294">https://doi.org/10.1021/acs.chemmater.9b01294</a></p>
<p>The code for MEGNet can be found at:
<a class="reference external" href="https://github.com/materialsvirtuallab/megnet">https://github.com/materialsvirtuallab/megnet</a></p>
<p>The embeddings were generated by training the MEGNet Graph Network on
60,000 structures from the Materials Project for predicting formation
energy, and may be an effective way of applying transfer learning to
smaller datasets using crystal-graph-based networks.</p>
<p>The representations are learned during training to predict a specific
property, though they may be useful for a range of properties.</p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.data.MEGNetElementData.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.MEGNetElementData.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.data.MEGNetElementData.get_elemental_property">
<span class="sig-name descname"><span class="pre">get_elemental_property</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">elem</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">property_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.MEGNetElementData.get_elemental_property" title="Permalink to this definition">¶</a></dt>
<dd><p>Get a certain elemental property for a certain element.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>elem - (Element) element to be assessed
property_name - (str) property to be retrieved</p>
</dd>
<dt>Returns:</dt><dd><p>float, property of that element</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.utils.data.MagpieData">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.utils.data.</span></span><span class="sig-name descname"><span class="pre">MagpieData</span></span><a class="headerlink" href="#matminer.utils.data.MagpieData" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.utils.data.AbstractData" title="matminer.utils.data.AbstractData"><code class="xref py py-class docutils literal notranslate"><span class="pre">AbstractData</span></code></a>, <a class="reference internal" href="#matminer.utils.data.OxidationStatesMixin" title="matminer.utils.data.OxidationStatesMixin"><code class="xref py py-class docutils literal notranslate"><span class="pre">OxidationStatesMixin</span></code></a></p>
<p>Class to get data from Magpie files. See also:
L. Ward, A. Agrawal, A. Choudhary, C. Wolverton, A general-purpose machine
learning framework for predicting properties of inorganic materials,
Npj Comput. Mater. 2 (2016) 16028.</p>
<p>Finding the exact meaning of each of these features can be quite difficult.
Reproduced in ./data_files/magpie_elementdata_feature_descriptions.txt.</p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.data.MagpieData.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.MagpieData.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.data.MagpieData.get_elemental_property">
<span class="sig-name descname"><span class="pre">get_elemental_property</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">elem</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">property_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.MagpieData.get_elemental_property" title="Permalink to this definition">¶</a></dt>
<dd><p>Get a certain elemental property for a certain element.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>elem - (Element) element to be assessed
property_name - (str) property to be retrieved</p>
</dd>
<dt>Returns:</dt><dd><p>float, property of that element</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.data.MagpieData.get_oxidation_states">
<span class="sig-name descname"><span class="pre">get_oxidation_states</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">elem</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.MagpieData.get_oxidation_states" title="Permalink to this definition">¶</a></dt>
<dd><p>Retrieve the possible oxidation states of an element</p>
<dl class="simple">
<dt>Args:</dt><dd><p>elem - (Element), Target element</p>
</dd>
<dt>Returns:</dt><dd><p>[int] - oxidation states</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.utils.data.MatscholarElementData">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.utils.data.</span></span><span class="sig-name descname"><span class="pre">MatscholarElementData</span></span><a class="headerlink" href="#matminer.utils.data.MatscholarElementData" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.utils.data.AbstractData" title="matminer.utils.data.AbstractData"><code class="xref py py-class docutils literal notranslate"><span class="pre">AbstractData</span></code></a></p>
<p>Class to get word embedding vectors of elements. These word embeddings were
generated using NLP + Neural Network techniques on more than 3 million
scientific abstracts.</p>
<p>The data returned by this class are simply learned representations of the
elements, taken from:</p>
<p>Tshitoyan, V., Dagdelen, J., Weston, L. et al. Unsupervised word embeddings
capture latent knowledge from materials science literature. Nature 571,
95–98 (2019). <a class="reference external" href="https://doi.org/10.1038/s41586-019-1335-8">https://doi.org/10.1038/s41586-019-1335-8</a></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.data.MatscholarElementData.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.MatscholarElementData.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.data.MatscholarElementData.get_elemental_property">
<span class="sig-name descname"><span class="pre">get_elemental_property</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">elem</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">property_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.MatscholarElementData.get_elemental_property" title="Permalink to this definition">¶</a></dt>
<dd><p>Get a certain elemental property for a certain element.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>elem - (Element) element to be assessed
property_name - (str) property to be retrieved</p>
</dd>
<dt>Returns:</dt><dd><p>float, property of that element</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.utils.data.MixingEnthalpy">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.utils.data.</span></span><span class="sig-name descname"><span class="pre">MixingEnthalpy</span></span><a class="headerlink" href="#matminer.utils.data.MixingEnthalpy" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference external" href="https://docs.python.org/3/library/functions.html#object" title="(in Python v3.11)"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></p>
<p>Values of <span class="math">\Delta H^{max}_{AB}</span> for different pairs of elements.</p>
<dl class="simple">
<dt>Based on the Miedema model. Tabulated by:</dt><dd><p>A. Takeuchi, A. Inoue, Classification of Bulk Metallic Glasses by Atomic
Size Difference, Heat of Mixing and Period of Constituent Elements and
Its Application to Characterization of the Main Alloying Element.
Mater. Trans. 46, 2817–2829 (2005).</p>
</dd>
<dt>Attributes:</dt><dd><dl class="simple">
<dt>valid_element_list ([Element]): A list of elements for which the</dt><dd><p>mixing enthalpy parameters are defined (although no guarantees
are provided that all combinations of this list will be available).</p>
</dd>
</dl>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.data.MixingEnthalpy.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.MixingEnthalpy.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.data.MixingEnthalpy.get_mixing_enthalpy">
<span class="sig-name descname"><span class="pre">get_mixing_enthalpy</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">elemA</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">elemB</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.MixingEnthalpy.get_mixing_enthalpy" title="Permalink to this definition">¶</a></dt>
<dd><p>Get the mixing enthalpy between different elements</p>
<dl class="simple">
<dt>Args:</dt><dd><p>elemA (Element): An element
elemB (Element): Second element</p>
</dd>
<dt>Returns:</dt><dd><p>(float) mixing enthalpy, nan if pair is not in a table</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.utils.data.OxidationStateDependentData">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.utils.data.</span></span><span class="sig-name descname"><span class="pre">OxidationStateDependentData</span></span><a class="headerlink" href="#matminer.utils.data.OxidationStateDependentData" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.utils.data.AbstractData" title="matminer.utils.data.AbstractData"><code class="xref py py-class docutils literal notranslate"><span class="pre">AbstractData</span></code></a></p>
<p>Abstract class that also includes oxidation-state-dependent properties</p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.data.OxidationStateDependentData.get_charge_dependent_property">
<em class="property"><span class="pre">abstract</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">get_charge_dependent_property</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">element</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">charge</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">property_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.OxidationStateDependentData.get_charge_dependent_property" title="Permalink to this definition">¶</a></dt>
<dd><p>Retrieve a oxidation-state dependent elemental property</p>
<dl class="simple">
<dt>Args:</dt><dd><p>element - (Element), Target element
charge - (int), Oxidation state
property_name - (string), name of property</p>
</dd>
<dt>Return:</dt><dd><p>(float) - Value of property</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.data.OxidationStateDependentData.get_charge_dependent_property_from_specie">
<span class="sig-name descname"><span class="pre">get_charge_dependent_property_from_specie</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">specie</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">property_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.OxidationStateDependentData.get_charge_dependent_property_from_specie" title="Permalink to this definition">¶</a></dt>
<dd><p>Retrieve a oxidation-state dependent elemental property</p>
<dl class="simple">
<dt>Args:</dt><dd><p>specie - (Specie), Specie of interest
property_name - (string), name of property</p>
</dd>
<dt>Return:</dt><dd><p>(float) - Value of property</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.utils.data.OxidationStatesMixin">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.utils.data.</span></span><span class="sig-name descname"><span class="pre">OxidationStatesMixin</span></span><a class="headerlink" href="#matminer.utils.data.OxidationStatesMixin" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference external" href="https://docs.python.org/3/library/functions.html#object" title="(in Python v3.11)"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></p>
<p>Abstract class interface for retrieving the oxidation states
of each element</p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.data.OxidationStatesMixin.get_oxidation_states">
<em class="property"><span class="pre">abstract</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">get_oxidation_states</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">elem</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.OxidationStatesMixin.get_oxidation_states" title="Permalink to this definition">¶</a></dt>
<dd><p>Retrieve the possible oxidation states of an element</p>
<dl class="simple">
<dt>Args:</dt><dd><p>elem - (Element), Target element</p>
</dd>
<dt>Returns:</dt><dd><p>[int] - oxidation states</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.utils.data.PymatgenData">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.utils.data.</span></span><span class="sig-name descname"><span class="pre">PymatgenData</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">use_common_oxi_states</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.PymatgenData" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.utils.data.OxidationStateDependentData" title="matminer.utils.data.OxidationStateDependentData"><code class="xref py py-class docutils literal notranslate"><span class="pre">OxidationStateDependentData</span></code></a>, <a class="reference internal" href="#matminer.utils.data.OxidationStatesMixin" title="matminer.utils.data.OxidationStatesMixin"><code class="xref py py-class docutils literal notranslate"><span class="pre">OxidationStatesMixin</span></code></a></p>
<p>Class to get data from pymatgen. See also:
S.P. Ong, W.D. Richards, A. Jain, G. Hautier, M. Kocher, S. Cholia, et al.,
Python Materials Genomics (pymatgen): A robust, open-source python library
for materials analysis, Comput. Mater. Sci. 68 (2013) 314-319.</p>
<p>Meanings of each feature can be obtained from the pymatgen.Composition
documentation (attributes).</p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.data.PymatgenData.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">use_common_oxi_states</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.PymatgenData.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.data.PymatgenData.get_charge_dependent_property">
<span class="sig-name descname"><span class="pre">get_charge_dependent_property</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">element</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">charge</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">property_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.PymatgenData.get_charge_dependent_property" title="Permalink to this definition">¶</a></dt>
<dd><p>Retrieve a oxidation-state dependent elemental property</p>
<dl class="simple">
<dt>Args:</dt><dd><p>element - (Element), Target element
charge - (int), Oxidation state
property_name - (string), name of property</p>
</dd>
<dt>Return:</dt><dd><p>(float) - Value of property</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.data.PymatgenData.get_elemental_property">
<span class="sig-name descname"><span class="pre">get_elemental_property</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">elem</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">property_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.PymatgenData.get_elemental_property" title="Permalink to this definition">¶</a></dt>
<dd><p>Get a certain elemental property for a certain element.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>elem - (Element) element to be assessed
property_name - (str) property to be retrieved</p>
</dd>
<dt>Returns:</dt><dd><p>float, property of that element</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.data.PymatgenData.get_oxidation_states">
<span class="sig-name descname"><span class="pre">get_oxidation_states</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">elem</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.data.PymatgenData.get_oxidation_states" title="Permalink to this definition">¶</a></dt>
<dd><p>Get the oxidation states of an element</p>
<dl>
<dt>Args:</dt><dd><p>elem - (Element) target element
common - (boolean), whether to return only the common oxidation states,</p>
<blockquote>
<div><p>or all known oxidation states</p>
</div></blockquote>
</dd>
<dt>Returns:</dt><dd><p>[int] list of oxidation states</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

</section>
<section id="module-matminer.utils.flatten_dict">
<span id="matminer-utils-flatten-dict-module"></span><h2>matminer.utils.flatten_dict module<a class="headerlink" href="#module-matminer.utils.flatten_dict" title="Permalink to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="matminer.utils.flatten_dict.flatten_dict">
<span class="sig-prename descclassname"><span class="pre">matminer.utils.flatten_dict.</span></span><span class="sig-name descname"><span class="pre">flatten_dict</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">nested_dict</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">lead_key</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">unwind_arrays</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.flatten_dict.flatten_dict" title="Permalink to this definition">¶</a></dt>
<dd><p>Helper function to flatten nested dictionary, recursively
walks through nested dictionary to get keys corresponding
to dot-notation keys, e. g. converts
{“a”: {“b”: 1, “c”: 2}} to {“a.b”: 1, “a.c”: 2}</p>
<dl>
<dt>Args:</dt><dd><p>nested_dict ({}): nested dictionary to flatten
unwind_arrays (bool): whether to flatten lists/tuples</p>
<blockquote>
<div><p>with numerically indexed dot notation, defaults to True</p>
</div></blockquote>
<dl class="simple">
<dt>lead_key (str): string to append to front of all keys,</dt><dd><p>used primarily for recursion</p>
</dd>
</dl>
</dd>
<dt>Returns:</dt><dd><p>non-nested dictionary</p>
</dd>
</dl>
</dd></dl>

</section>
<section id="module-matminer.utils.io">
<span id="matminer-utils-io-module"></span><h2>matminer.utils.io module<a class="headerlink" href="#module-matminer.utils.io" title="Permalink to this heading">¶</a></h2>
<p>This module defines functions for writing and reading matminer related objects</p>
<dl class="py function">
<dt class="sig sig-object py" id="matminer.utils.io.load_dataframe_from_json">
<span class="sig-prename descclassname"><span class="pre">matminer.utils.io.</span></span><span class="sig-name descname"><span class="pre">load_dataframe_from_json</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pbar</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">decode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.io.load_dataframe_from_json" title="Permalink to this definition">¶</a></dt>
<dd><p>Load pandas dataframe from a json file.</p>
<p>Automatically decodes and instantiates pymatgen objects in the dataframe.</p>
<dl>
<dt>Args:</dt><dd><dl class="simple">
<dt>filename (str): Path to json file. Can be a compressed file (gz and bz2)</dt><dd><p>are supported.</p>
</dd>
</dl>
<p>pbar (bool): If true, shows an ASCII progress bar for loading data from disk.
decode (bool): If true, will automatically decode objects (slow, convenient).</p>
<blockquote>
<div><p>If false, will return json representations of the objects (fast, inconvenient).</p>
</div></blockquote>
</dd>
<dt>Returns:</dt><dd><p>(Pandas.DataFrame): A pandas dataframe.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.utils.io.store_dataframe_as_json">
<span class="sig-prename descclassname"><span class="pre">matminer.utils.io.</span></span><span class="sig-name descname"><span class="pre">store_dataframe_as_json</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dataframe</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filename</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">compression</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">orient</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'split'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pbar</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.io.store_dataframe_as_json" title="Permalink to this definition">¶</a></dt>
<dd><p>Store pandas dataframe as a json file.</p>
<p>Automatically encodes pymatgen objects as dictionaries.</p>
<dl>
<dt>Args:</dt><dd><p>dataframe (Pandas.Dataframe): A pandas dataframe.
filename (str): Path to json file.
compression (str or None): A compression mode. Valid options are “gz”,</p>
<blockquote>
<div><p>“bz2”, and None. Defaults to None. If the filename does not end
in with the correct suffix it will be added automatically.</p>
</div></blockquote>
<dl class="simple">
<dt>orient (str): Determines the format in which the dictionary data is</dt><dd><p>stored. This takes the same set of arguments as the <cite>orient</cite> option
in <cite>pandas.DataFrame.to_dict()</cite> function. ‘split’ is recommended
as it is relatively space efficient and preserves the dtype
of the index.</p>
</dd>
<dt>pbar (bool): If True, shows a progress bar for encoding objects to</dt><dd><p>compatible json format (normally the rate-limiting step).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</section>
<section id="module-matminer.utils.kernels">
<span id="matminer-utils-kernels-module"></span><h2>matminer.utils.kernels module<a class="headerlink" href="#module-matminer.utils.kernels" title="Permalink to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="matminer.utils.kernels.gaussian_kernel">
<span class="sig-prename descclassname"><span class="pre">matminer.utils.kernels.</span></span><span class="sig-name descname"><span class="pre">gaussian_kernel</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">arr0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">arr1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">SIGMA</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.kernels.gaussian_kernel" title="Permalink to this definition">¶</a></dt>
<dd><p>Returns a Gaussian kernel of the two arrays
for use in KRR or other regressions using the
kernel trick.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.utils.kernels.laplacian_kernel">
<span class="sig-prename descclassname"><span class="pre">matminer.utils.kernels.</span></span><span class="sig-name descname"><span class="pre">laplacian_kernel</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">arr0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">arr1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">SIGMA</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.kernels.laplacian_kernel" title="Permalink to this definition">¶</a></dt>
<dd><p>Returns a Laplacian kernel of the two arrays
for use in KRR or other regressions using the
kernel trick.</p>
</dd></dl>

</section>
<section id="module-matminer.utils.pipeline">
<span id="matminer-utils-pipeline-module"></span><h2>matminer.utils.pipeline module<a class="headerlink" href="#module-matminer.utils.pipeline" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.utils.pipeline.DropExcluded">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.utils.pipeline.</span></span><span class="sig-name descname"><span class="pre">DropExcluded</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">excluded</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.pipeline.DropExcluded" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">BaseEstimator</span></code>, <code class="xref py py-class docutils literal notranslate"><span class="pre">TransformerMixin</span></code></p>
<p>Transformer for removing unwanted columns from a dataframe.
Passes back the remaining columns.</p>
<p>Helper class for making sklearn pipelines with matminer.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>excluded (list of labels): A list of column labels to drop from the dataframe</p>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.pipeline.DropExcluded.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">excluded</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.pipeline.DropExcluded.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.pipeline.DropExcluded.fit">
<span class="sig-name descname"><span class="pre">fit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.pipeline.DropExcluded.fit" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.pipeline.DropExcluded.transform">
<span class="sig-name descname"><span class="pre">transform</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">df</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.pipeline.DropExcluded.transform" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.utils.pipeline.ItemSelector">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.utils.pipeline.</span></span><span class="sig-name descname"><span class="pre">ItemSelector</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">label</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.pipeline.ItemSelector" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">BaseEstimator</span></code>, <code class="xref py py-class docutils literal notranslate"><span class="pre">TransformerMixin</span></code></p>
<p>A utility for extracting a column from a DataFrame in a sklearn pipeline,
for example in a FeatureUnion pipeline to featurize a dataset.</p>
<p>Helper class for making sklearn pipelines with matminer.</p>
<p>See (<a class="reference external" href="http://scikit-learn.org/stable/auto_examples/hetero_feature_union.html">http://scikit-learn.org/stable/auto_examples/hetero_feature_union.html</a>)</p>
<dl class="simple">
<dt>Args:</dt><dd><p>label : The label of the column to select.</p>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.pipeline.ItemSelector.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">label</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.pipeline.ItemSelector.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.pipeline.ItemSelector.fit">
<span class="sig-name descname"><span class="pre">fit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.pipeline.ItemSelector.fit" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.pipeline.ItemSelector.transform">
<span class="sig-name descname"><span class="pre">transform</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dataframe</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.pipeline.ItemSelector.transform" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.utils.utils">
<span id="matminer-utils-utils-module"></span><h2>matminer.utils.utils module<a class="headerlink" href="#module-matminer.utils.utils" title="Permalink to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="matminer.utils.utils.homogenize_multiindex">
<span class="sig-prename descclassname"><span class="pre">matminer.utils.utils.</span></span><span class="sig-name descname"><span class="pre">homogenize_multiindex</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">df</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default_key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">coerce</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.utils.homogenize_multiindex" title="Permalink to this definition">¶</a></dt>
<dd><p>Homogenizes a dataframe column index to a 2-level multiindex.</p>
<dl>
<dt>Args:</dt><dd><p>df (pandas DataFrame): A dataframe
default_key (str): The key to use when a single Index must be converted</p>
<blockquote>
<div><p>to a 2-level index. This key is then used as a parent of all
keys present in the original 1-level index.</p>
</div></blockquote>
<dl class="simple">
<dt>coerce (bool): If True, try to force a 2+ level multiindex to a 2-level</dt><dd><p>multiindex.</p>
</dd>
</dl>
</dd>
<dt>Returns:</dt><dd><p>df (pandas DataFrame): A dataframe with a 2-layer multiindex.</p>
</dd>
</dl>
</dd></dl>

</section>
<section id="module-matminer.utils">
<span id="module-contents"></span><h2>Module contents<a class="headerlink" href="#module-matminer.utils" title="Permalink to this heading">¶</a></h2>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">matminer.utils package</a><ul>
<li><a class="reference internal" href="#subpackages">Subpackages</a></li>
<li><a class="reference internal" href="#submodules">Submodules</a></li>
<li><a class="reference internal" href="#module-matminer.utils.caching">matminer.utils.caching module</a><ul>
<li><a class="reference internal" href="#matminer.utils.caching.get_all_nearest_neighbors"><code class="docutils literal notranslate"><span class="pre">get_all_nearest_neighbors()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.caching.get_nearest_neighbors"><code class="docutils literal notranslate"><span class="pre">get_nearest_neighbors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.utils.data">matminer.utils.data module</a><ul>
<li><a class="reference internal" href="#matminer.utils.data.AbstractData"><code class="docutils literal notranslate"><span class="pre">AbstractData</span></code></a><ul>
<li><a class="reference internal" href="#matminer.utils.data.AbstractData.get_elemental_properties"><code class="docutils literal notranslate"><span class="pre">AbstractData.get_elemental_properties()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.data.AbstractData.get_elemental_property"><code class="docutils literal notranslate"><span class="pre">AbstractData.get_elemental_property()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.utils.data.CohesiveEnergyData"><code class="docutils literal notranslate"><span class="pre">CohesiveEnergyData</span></code></a><ul>
<li><a class="reference internal" href="#matminer.utils.data.CohesiveEnergyData.__init__"><code class="docutils literal notranslate"><span class="pre">CohesiveEnergyData.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.data.CohesiveEnergyData.get_elemental_property"><code class="docutils literal notranslate"><span class="pre">CohesiveEnergyData.get_elemental_property()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.utils.data.DemlData"><code class="docutils literal notranslate"><span class="pre">DemlData</span></code></a><ul>
<li><a class="reference internal" href="#matminer.utils.data.DemlData.__init__"><code class="docutils literal notranslate"><span class="pre">DemlData.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.data.DemlData.get_charge_dependent_property"><code class="docutils literal notranslate"><span class="pre">DemlData.get_charge_dependent_property()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.data.DemlData.get_elemental_property"><code class="docutils literal notranslate"><span class="pre">DemlData.get_elemental_property()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.data.DemlData.get_oxidation_states"><code class="docutils literal notranslate"><span class="pre">DemlData.get_oxidation_states()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.utils.data.IUCrBondValenceData"><code class="docutils literal notranslate"><span class="pre">IUCrBondValenceData</span></code></a><ul>
<li><a class="reference internal" href="#matminer.utils.data.IUCrBondValenceData.__init__"><code class="docutils literal notranslate"><span class="pre">IUCrBondValenceData.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.data.IUCrBondValenceData.get_bv_params"><code class="docutils literal notranslate"><span class="pre">IUCrBondValenceData.get_bv_params()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.data.IUCrBondValenceData.interpolate_soft_anions"><code class="docutils literal notranslate"><span class="pre">IUCrBondValenceData.interpolate_soft_anions()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.utils.data.MEGNetElementData"><code class="docutils literal notranslate"><span class="pre">MEGNetElementData</span></code></a><ul>
<li><a class="reference internal" href="#matminer.utils.data.MEGNetElementData.__init__"><code class="docutils literal notranslate"><span class="pre">MEGNetElementData.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.data.MEGNetElementData.get_elemental_property"><code class="docutils literal notranslate"><span class="pre">MEGNetElementData.get_elemental_property()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.utils.data.MagpieData"><code class="docutils literal notranslate"><span class="pre">MagpieData</span></code></a><ul>
<li><a class="reference internal" href="#matminer.utils.data.MagpieData.__init__"><code class="docutils literal notranslate"><span class="pre">MagpieData.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.data.MagpieData.get_elemental_property"><code class="docutils literal notranslate"><span class="pre">MagpieData.get_elemental_property()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.data.MagpieData.get_oxidation_states"><code class="docutils literal notranslate"><span class="pre">MagpieData.get_oxidation_states()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.utils.data.MatscholarElementData"><code class="docutils literal notranslate"><span class="pre">MatscholarElementData</span></code></a><ul>
<li><a class="reference internal" href="#matminer.utils.data.MatscholarElementData.__init__"><code class="docutils literal notranslate"><span class="pre">MatscholarElementData.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.data.MatscholarElementData.get_elemental_property"><code class="docutils literal notranslate"><span class="pre">MatscholarElementData.get_elemental_property()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.utils.data.MixingEnthalpy"><code class="docutils literal notranslate"><span class="pre">MixingEnthalpy</span></code></a><ul>
<li><a class="reference internal" href="#matminer.utils.data.MixingEnthalpy.__init__"><code class="docutils literal notranslate"><span class="pre">MixingEnthalpy.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.data.MixingEnthalpy.get_mixing_enthalpy"><code class="docutils literal notranslate"><span class="pre">MixingEnthalpy.get_mixing_enthalpy()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.utils.data.OxidationStateDependentData"><code class="docutils literal notranslate"><span class="pre">OxidationStateDependentData</span></code></a><ul>
<li><a class="reference internal" href="#matminer.utils.data.OxidationStateDependentData.get_charge_dependent_property"><code class="docutils literal notranslate"><span class="pre">OxidationStateDependentData.get_charge_dependent_property()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.data.OxidationStateDependentData.get_charge_dependent_property_from_specie"><code class="docutils literal notranslate"><span class="pre">OxidationStateDependentData.get_charge_dependent_property_from_specie()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.utils.data.OxidationStatesMixin"><code class="docutils literal notranslate"><span class="pre">OxidationStatesMixin</span></code></a><ul>
<li><a class="reference internal" href="#matminer.utils.data.OxidationStatesMixin.get_oxidation_states"><code class="docutils literal notranslate"><span class="pre">OxidationStatesMixin.get_oxidation_states()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.utils.data.PymatgenData"><code class="docutils literal notranslate"><span class="pre">PymatgenData</span></code></a><ul>
<li><a class="reference internal" href="#matminer.utils.data.PymatgenData.__init__"><code class="docutils literal notranslate"><span class="pre">PymatgenData.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.data.PymatgenData.get_charge_dependent_property"><code class="docutils literal notranslate"><span class="pre">PymatgenData.get_charge_dependent_property()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.data.PymatgenData.get_elemental_property"><code class="docutils literal notranslate"><span class="pre">PymatgenData.get_elemental_property()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.data.PymatgenData.get_oxidation_states"><code class="docutils literal notranslate"><span class="pre">PymatgenData.get_oxidation_states()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.utils.flatten_dict">matminer.utils.flatten_dict module</a><ul>
<li><a class="reference internal" href="#matminer.utils.flatten_dict.flatten_dict"><code class="docutils literal notranslate"><span class="pre">flatten_dict()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.utils.io">matminer.utils.io module</a><ul>
<li><a class="reference internal" href="#matminer.utils.io.load_dataframe_from_json"><code class="docutils literal notranslate"><span class="pre">load_dataframe_from_json()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.io.store_dataframe_as_json"><code class="docutils literal notranslate"><span class="pre">store_dataframe_as_json()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.utils.kernels">matminer.utils.kernels module</a><ul>
<li><a class="reference internal" href="#matminer.utils.kernels.gaussian_kernel"><code class="docutils literal notranslate"><span class="pre">gaussian_kernel()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.kernels.laplacian_kernel"><code class="docutils literal notranslate"><span class="pre">laplacian_kernel()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.utils.pipeline">matminer.utils.pipeline module</a><ul>
<li><a class="reference internal" href="#matminer.utils.pipeline.DropExcluded"><code class="docutils literal notranslate"><span class="pre">DropExcluded</span></code></a><ul>
<li><a class="reference internal" href="#matminer.utils.pipeline.DropExcluded.__init__"><code class="docutils literal notranslate"><span class="pre">DropExcluded.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.pipeline.DropExcluded.fit"><code class="docutils literal notranslate"><span class="pre">DropExcluded.fit()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.pipeline.DropExcluded.transform"><code class="docutils literal notranslate"><span class="pre">DropExcluded.transform()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.utils.pipeline.ItemSelector"><code class="docutils literal notranslate"><span class="pre">ItemSelector</span></code></a><ul>
<li><a class="reference internal" href="#matminer.utils.pipeline.ItemSelector.__init__"><code class="docutils literal notranslate"><span class="pre">ItemSelector.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.pipeline.ItemSelector.fit"><code class="docutils literal notranslate"><span class="pre">ItemSelector.fit()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.pipeline.ItemSelector.transform"><code class="docutils literal notranslate"><span class="pre">ItemSelector.transform()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.utils.utils">matminer.utils.utils module</a><ul>
<li><a class="reference internal" href="#matminer.utils.utils.homogenize_multiindex"><code class="docutils literal notranslate"><span class="pre">homogenize_multiindex()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.utils">Module contents</a></li>
</ul>
</li>
</ul>

  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/matminer.utils.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer.utils package</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2015, Anubhav Jain.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.3.0.
    </div>

  </body>
</html>