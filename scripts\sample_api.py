import sys
sys.path.append('.')
import torch
import pyxtal
from scripts.eval_utils import load_model_classifier, get_crystals_list, lattices_to_params_shape
from pathlib import Path
from pyxtal.symmetry import Group
from torch_geometric.data import Data, Batch, DataLoader
from torch.utils.data import Dataset
from tqdm import tqdm
from p_tqdm import p_map
from pymatgen.core.structure import Structure
from pymatgen.core.lattice import Lattice
import json
import pandas as pd

chemical_symbols = [
    # 0
    'X',
    # 1
    'H', 'He',
    # 2
    'Li', 'Be', 'B', 'C', 'N', 'O', 'F', 'Ne',
    # 3
    'Na', 'Mg', 'Al', 'Si', 'P', 'S', 'Cl', 'Ar',
    # 4
    'K', 'Ca', 'Sc', 'Ti', 'V', 'Cr', 'Mn', 'Fe', 'Co', 'Ni', 'Cu', 'Zn',
    'Ga', 'Ge', 'As', 'Se', 'Br', 'Kr',
    # 5
    'Rb', 'Sr', 'Y', 'Zr', 'Nb', 'Mo', 'Tc', 'Ru', 'Rh', 'Pd', 'Ag', 'Cd',
    'In', 'Sn', 'Sb', 'Te', 'I', 'Xe',
    # 6
    'Cs', 'Ba', 'La', 'Ce', 'Pr', 'Nd', 'Pm', 'Sm', 'Eu', 'Gd', 'Tb', 'Dy',
    'Ho', 'Er', 'Tm', 'Yb', 'Lu',
    'Hf', 'Ta', 'W', 'Re', 'Os', 'Ir', 'Pt', 'Au', 'Hg', 'Tl', 'Pb', 'Bi',
    'Po', 'At', 'Rn',
    # 7
    'Fr', 'Ra', 'Ac', 'Th', 'Pa', 'U', 'Np', 'Pu', 'Am', 'Cm', 'Bk',
    'Cf', 'Es', 'Fm', 'Md', 'No', 'Lr',
    'Rf', 'Db', 'Sg', 'Bh', 'Hs', 'Mt', 'Ds', 'Rg', 'Cn', 'Nh', 'Fl', 'Mc',
    'Lv', 'Ts', 'Og']

rev_chemical_symbols = {ch:i for i,ch in enumerate(chemical_symbols)}

def get_data_from_syminfo(spacegroup_number, wyckoff_letters, atom_types = None):
    
    g = Group(spacegroup_number)
    ops_tot = []
    anchor_index = []
    num_atoms = 0
    if atom_types is not None:
        assert len(wyckoff_letters) == len(atom_types)
        atom_numbers = []
    for idx in range(len(wyckoff_letters)):
        letter = wyckoff_letters[idx][-1] # 'a' for '1a'
        ops = g[letter].ops
        for op in ops:
            ops_tot.append(op.affine_matrix)
            anchor_index.append(num_atoms)
            if atom_types is not None:
                atom_numbers.append(rev_chemical_symbols[atom_types[idx]])
        num_atoms += len(ops)
    data = Data(
        spacegroup = torch.LongTensor([spacegroup_number]),
        ops = torch.FloatTensor(ops_tot),
        anchor_index = torch.LongTensor(anchor_index),
        num_nodes = num_atoms,
        num_atoms = num_atoms,
    )
    data.ops_inv = torch.linalg.pinv(data.ops[:,:3,:3])
    if atom_types is not None:
        data.atom_types = torch.LongTensor(atom_numbers)
    else:
        data.atom_types = torch.zeros(num_atoms)
    return data

class CustomDataset(Dataset):

    def __init__(self, data_list):
        super().__init__()
        self.data_list = data_list

    def __len__(self) -> int:
        return len(self.data_list)

    def __getitem__(self, index):
        return self.data_list[index]

def diffusion(loader, model, step_lr):

    frac_coords = []
    num_atoms = []
    atom_types = []
    lattices = []
    input_data_list = []
    for idx, batch in enumerate(loader):

        if torch.cuda.is_available():
            batch.cuda()
        outputs = model.sample(batch)

    return (
        outputs
    )

def get_pymatgen(crystal_array):
    frac_coords = crystal_array['frac_coords']
    atom_types = crystal_array['atom_types']
    lengths = crystal_array['lengths']
    angles = crystal_array['angles']
    try:
        structure = Structure(
            lattice=Lattice.from_parameters(
                *(lengths.tolist() + angles.tolist())),
            species=atom_types, coords=frac_coords, coords_are_cartesian=False)
        return structure
    except:
        return None

def element2onehot(element):
    "['Co', 'Nd', 'O', 'Sr']"
    'Co-Nd-O-Sr'
    # print(element)
    element_one_hot = torch.zeros(1,118).long()
    element_list = element.split('[')[1].split(']')[0].split(', ')
    # print(element_list)
    for num in range(len(element_list)):
        element = chemical_symbols.index(element_list[num].replace("'",""))
        element_one_hot[0, element - 1] = 1
    return element_one_hot
    # return composition

def dataset_for_classifier_from_csv(csv_file):
    data_list = []
    csv_list = pd.read_csv(csv_file)
    # print(len(csv_list))
    for index in range(len(csv_list)):
        data_dict = {}
        csv_data = csv_list.iloc[index]
        # print(csv_data[7])
        data_dict['composition'] = element2onehot(csv_data[7])
        data_dict['band_gap'] = csv_data[4]
        data_dict['formation_energy'] = csv_data[3]
        data = Data(
            conditions = data_dict
            )
        data_list.append(data)
        # print(data_dict)

    print(f"Collected {len(data_list)} queries.")

    dataset = CustomDataset(data_list)
    return dataset

def dataset_for_classifier_from_pt(pt_file):
    # print(pt_file)
    pt_list = torch.load(pt_file)
    data_list = []

    for pt_data in pt_list:
        data_dict = {}
        data_dict['composition'] = pt_data['composition']
        # print(data_dict['composition'].shape)
        data_dict['band_gap'] = pt_data['band_gap']
        data_dict['formation_energy_per_atom'] = pt_data['formation_energy_per_atom']
        data = Data(
            conditions = data_dict
            )
        data_list.append(data)

    print(f"Collected {len(data_list)} queries.")

    dataset = CustomDataset(data_list)
    return dataset
 
def dataset_for_predictor_from_json(json_file):

    with open(json_file,'r',encoding='UTF-8') as f:
        json_data = json.load(f)
    data_list = []
    gen_list = []
    for data in json_data:
        data_dict = {}
        # data_dict['composition'] = data['composition']
        data_dict['band_gap'] = data['band_gap']
        data_dict['formation_energy_per_atom'] = data['formation_energy_per_atom']
        data_dict['composition'] = torch.zeros(1,118).long()
        data_ = Data(
            conditions = data_dict
            )
        for i in range(data['sample_nums']):    
            data_list.append(data_)
            gen_list.append(data_dict)

    print(f"Collected {len(data_list)} queries.")

    dataset = CustomDataset(data_list)
    return dataset, gen_list

def dataset_for_predictor(composition = None, band_gap = None, formation_energy = None, total_num = None):
    data_list = []
    data_dict = {}
    if composition is not None:
        element_list = composition.split('-')
        element_one_hot = torch.zeros(1,118).long()
        for num in range(len(element_list)):
            element = chemical_symbols.index(element_list[num])
            # print(element)
            element_one_hot[0, element - 1] = 1

        data_dict['composition'] = element_one_hot
    else:
        data_dict['composition'] = torch.zeros(1,118).long()
    # print('data_dict[composition]:', data_dict['composition'])
    if formation_energy is not None:
        data_dict['formation_energy_per_atom'] = float(formation_energy)
    else: 
        data_dict['formation_energy_per_atom'] = float(-10)
    # print('data_dict[formation_energy]:', data_dict['formation_energy'])
    if band_gap is not None:
        data_dict['band_gap'] = float(band_gap)
    else: 
        data_dict['band_gap'] = float(-10)
    # print('data_dict[band_gap]:', data_dict['band_gap'])
    data = Data(
        conditions = data_dict
    )
    
    for i in range(total_num):
        data_list.append(data)
    dataset = CustomDataset(data_list)
    return dataset

def construct_dataset_from_syminfo(spacegroup_number, wyckoff_letters, atom_types = None):

    data = get_data_from_syminfo(spacegroup_number, wyckoff_letters, atom_types)
    dataset = CustomDataset([data])
    return dataset

def construct_dataset_from_json(json_file):

    with open(json_file, 'r') as f:
        json_list = json.load(f)

    data_list = []

    for idx, json_data in tqdm(enumerate(json_list)):
        try:
            data = get_data_from_syminfo(**json_data)
            data_list.append(data)
        except:
            print(f"Parsing Json with index {idx} failed. Skipped.")

    print(f"Collected {len(data_list)} queries.")

    dataset = CustomDataset(data_list)

    return dataset

def generate_sg_dist_from_dataset(model_path, dataset, batch_size = 128, step_lr = 1e-5):

    model_path = Path(model_path)
    model, _, cfg = load_model_classifier(
        model_path, load_data=False)

    if torch.cuda.is_available():
        model.to('cuda')

    loader = DataLoader(dataset, batch_size = min(batch_size, len(dataset)))
    # for load in loader:
    #     print(len(load))
    dataset_outs = []
    for loader_bs in loader:
        outs = diffusion(DataLoader(loader_bs, batch_size = min(batch_size, len(dataset))), model, step_lr)
        for out in outs:
            dataset_outs.append(out)
    # outs = diffusion(loader, model, step_lr)

    # crystal_list = get_crystals_list(frac_coords, atom_types, lengths, angles, num_atoms)
    # structure_list = p_map(get_pymatgen, crystal_list)
    # return structure_list

    return dataset_outs

def generate_structures_from_dataset(model_path, dataset, batch_size = 128, step_lr = 1e-5):

    model_path = Path(model_path)
    model, _, cfg = load_model_classifier(
        model_path, load_data=False)

    if torch.cuda.is_available():
        model.to('cuda')
    loader = DataLoader(dataset, batch_size = min(batch_size, len(dataset)))
    outs = diffusion(loader, model, step_lr)
    # print(outs.shape)
    # print(outs)
    # crystal_list = get_crystals_list(frac_coords, atom_types, lengths, angles, num_atoms)
    # structure_list = p_map(get_pymatgen, crystal_list)
    # return structure_list

    return outs


    