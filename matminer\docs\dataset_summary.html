
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

    <title>Table of Datasets &#8212; matminer 0.9.0 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="_static/nature.css" />
    <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="_static/doctools.js"></script>
    <script src="_static/sphinx_highlight.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
 
<link href='https://fonts.googleapis.com/css?family=Lato:400,700' rel='stylesheet' type='text/css'>

  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Table of Datasets</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="table-of-datasets">
<h1>Table of Datasets<a class="headerlink" href="#table-of-datasets" title="Permalink to this heading">¶</a></h1>
<p>Find a table of all 45 datasets available in matminer here.</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 70.0%" />
<col style="width: 10.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Entries</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">boltztrap_mp</span></code></p></td>
<td><p>Effective mass and thermoelectric properties of 8924 compounds in The  Materials Project database that are calculated by the BoltzTraP software package run on the GGA-PBE or GGA+U density functional theory calculation results</p></td>
<td><p>8924</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">brgoch_superhard_training</span></code></p></td>
<td><p>2574 materials used for training regressors that predict shear and bulk modulus.</p></td>
<td><p>2574</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">castelli_perovskites</span></code></p></td>
<td><p>18,928 perovskites generated with ABX combinatorics, calculating gllbsc band gap and pbe structure, and also reporting absolute band edge positions and heat of formation.</p></td>
<td><p>18928</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">citrine_thermal_conductivity</span></code></p></td>
<td><p>Thermal conductivity of 872 compounds measured experimentally and retrieved from Citrine database from various references</p></td>
<td><p>872</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">dielectric_constant</span></code></p></td>
<td><p>1,056 structures with dielectric properties, calculated with DFPT-PBE.</p></td>
<td><p>1056</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">double_perovskites_gap</span></code></p></td>
<td><p>Band gap of 1306 double perovskites (a_1-b_1-a_2-b_2-O6) calculated using ﻿Gritsenko, van Leeuwen, van Lenthe and Baerends potential (gllbsc) in GPAW.</p></td>
<td><p>1306</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">double_perovskites_gap_lumo</span></code></p></td>
<td><p>Supplementary lumo data of 55 atoms for the double_perovskites_gap dataset.</p></td>
<td><p>55</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">elastic_tensor_2015</span></code></p></td>
<td><p>1,181 structures with elastic properties calculated with DFT-PBE.</p></td>
<td><p>1181</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">expt_formation_enthalpy</span></code></p></td>
<td><p>Experimental formation enthalpies for inorganic compounds, collected from years of calorimetric experiments</p></td>
<td><p>1276</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">expt_formation_enthalpy_kingsbury</span></code></p></td>
<td><p>Dataset containing experimental standard formation enthalpies for solids</p></td>
<td><p>2135</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">expt_gap</span></code></p></td>
<td><p>Experimental band gap of 6354 inorganic semiconductors.</p></td>
<td><p>6354</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">expt_gap_kingsbury</span></code></p></td>
<td><p>Identical to the matbench_expt_gap dataset, except that Materials Project database IDs (mp-ids) have been associated with each material using the same method as described for the expt_formation_enthalpy_kingsbury dataset</p></td>
<td><p>4604</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">flla</span></code></p></td>
<td><p>3938 structures and computed formation energies from “Crystal Structure Representations for Machine Learning Models of Formation Energies.”</p></td>
<td><p>3938</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">glass_binary</span></code></p></td>
<td><p>Metallic glass formation data for binary alloys, collected from various experimental techniques such as melt-spinning or mechanical alloying</p></td>
<td><p>5959</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">glass_binary_v2</span></code></p></td>
<td><p>Identical to glass_binary dataset, but with duplicate entries merged</p></td>
<td><p>5483</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">glass_ternary_hipt</span></code></p></td>
<td><p>Metallic glass formation dataset for ternary alloys, collected from the high-throughput sputtering experiments measuring whether it is possible to form a glass using sputtering</p></td>
<td><p>5170</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">glass_ternary_landolt</span></code></p></td>
<td><p>Metallic glass formation dataset for ternary alloys, collected from the “Nonequilibrium Phase Diagrams of Ternary Amorphous Alloys,’ a volume of the Landolt– Börnstein collection</p></td>
<td><p>7191</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">heusler_magnetic</span></code></p></td>
<td><p>1153 Heusler alloys with DFT-calculated magnetic and electronic properties</p></td>
<td><p>1153</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">jarvis_dft_2d</span></code></p></td>
<td><p>Various properties of 636 2D materials computed with the OptB88vdW and TBmBJ functionals taken from the JARVIS DFT database.</p></td>
<td><p>636</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">jarvis_dft_3d</span></code></p></td>
<td><p>Various properties of 25,923 bulk materials computed with the OptB88vdW and TBmBJ functionals taken from the JARVIS DFT database.</p></td>
<td><p>25923</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">jarvis_ml_dft_training</span></code></p></td>
<td><p>Various properties of 24,759 bulk and 2D materials computed with the OptB88vdW and TBmBJ functionals taken from the JARVIS DFT database.</p></td>
<td><p>24759</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">m2ax</span></code></p></td>
<td><p>Elastic properties of 223 stable M2AX compounds from “A comprehensive survey of M2AX phase elastic properties” by Cover et al</p></td>
<td><p>223</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">matbench_dielectric</span></code></p></td>
<td><p>Matbench v0.1 test dataset for predicting refractive index from structure</p></td>
<td><p>4764</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">matbench_expt_gap</span></code></p></td>
<td><p>Matbench v0.1 test dataset for predicting experimental band gap from composition alone</p></td>
<td><p>4604</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">matbench_expt_is_metal</span></code></p></td>
<td><p>Matbench v0.1 test dataset for classifying metallicity from composition alone</p></td>
<td><p>4921</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">matbench_glass</span></code></p></td>
<td><p>Matbench v0.1 test dataset for predicting full bulk metallic glass formation ability from chemical formula</p></td>
<td><p>5680</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">matbench_jdft2d</span></code></p></td>
<td><p>Matbench v0.1 test dataset for predicting exfoliation energies from crystal structure (computed with the OptB88vdW and TBmBJ functionals)</p></td>
<td><p>636</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">matbench_log_gvrh</span></code></p></td>
<td><p>Matbench v0.1 test dataset for predicting DFT log10 VRH-average shear modulus from structure</p></td>
<td><p>10987</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">matbench_log_kvrh</span></code></p></td>
<td><p>Matbench v0.1 test dataset for predicting DFT log10 VRH-average bulk modulus from structure</p></td>
<td><p>10987</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">matbench_mp_e_form</span></code></p></td>
<td><p>Matbench v0.1 test dataset for predicting DFT formation energy from structure</p></td>
<td><p>132752</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">matbench_mp_gap</span></code></p></td>
<td><p>Matbench v0.1 test dataset for predicting DFT PBE band gap from structure</p></td>
<td><p>106113</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">matbench_mp_is_metal</span></code></p></td>
<td><p>Matbench v0.1 test dataset for predicting DFT metallicity from structure</p></td>
<td><p>106113</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">matbench_perovskites</span></code></p></td>
<td><p>Matbench v0.1 test dataset for predicting formation energy from crystal structure</p></td>
<td><p>18928</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">matbench_phonons</span></code></p></td>
<td><p>Matbench v0.1 test dataset for predicting vibration properties from crystal structure</p></td>
<td><p>1265</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">matbench_steels</span></code></p></td>
<td><p>Matbench v0.1 test dataset for predicting steel yield strengths from chemical composition alone</p></td>
<td><p>312</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">mp_all_20181018</span></code></p></td>
<td><p>A complete copy of the Materials Project database as of 10/18/2018</p></td>
<td><p>83989</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">mp_nostruct_20181018</span></code></p></td>
<td><p>A complete copy of the Materials Project database as of 10/18/2018</p></td>
<td><p>83989</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">phonon_dielectric_mp</span></code></p></td>
<td><p>Phonon (lattice/atoms vibrations) and dielectric properties of 1296 compounds computed via ABINIT software package in the harmonic approximation based on density functional perturbation theory.</p></td>
<td><p>1296</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">piezoelectric_tensor</span></code></p></td>
<td><p>941 structures with piezoelectric properties, calculated with DFT-PBE.</p></td>
<td><p>941</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">ricci_boltztrap_mp_tabular</span></code></p></td>
<td><p>Ab-initio electronic transport database for inorganic materials</p></td>
<td><p>47737</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">steel_strength</span></code></p></td>
<td><p>312 steels with experimental yield strength and ultimate tensile strength, extracted and cleaned (including de-duplicating) from Citrine.</p></td>
<td><p>312</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">superconductivity2018</span></code></p></td>
<td><p>Dataset of ~16,000 experimental superconductivity records (critical temperatures) from Stanev et al., originally from the Japanese National Institute for Materials Science</p></td>
<td><p>16414</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">tholander_nitrides</span></code></p></td>
<td><p>A challenging data set for quantum machine learning containing a diverse set of 12.8k polymorphs in the Zn-Ti-N, Zn-Zr-N and Zn-Hf-N chemical systems</p></td>
<td><p>12815</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">ucsb_thermoelectrics</span></code></p></td>
<td><p>Database of ~1,100 experimental thermoelectric materials from UCSB aggregated from 108 source publications and personal communications</p></td>
<td><p>1093</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">wolverton_oxides</span></code></p></td>
<td><p>4,914 perovskite oxides containing composition data, lattice constants, and formation + vacancy formation energies</p></td>
<td><p>4914</p></td>
</tr>
</tbody>
</table>
</section>
<section id="dataset-info">
<h1>Dataset info<a class="headerlink" href="#dataset-info" title="Permalink to this heading">¶</a></h1>
<section id="boltztrap-mp">
<h2>boltztrap_mp<a class="headerlink" href="#boltztrap-mp" title="Permalink to this heading">¶</a></h2>
<p>Effective mass and thermoelectric properties of 8924 compounds in The  Materials Project database that are calculated by the BoltzTraP software package run on the GGA-PBE or GGA+U density functional theory calculation results. The properties are reported at the temperature of 300 Kelvin and the carrier concentration of 1e18 1/cm3.</p>
<p><strong>Number of entries:</strong> 8924</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">formula</span></code></p></td>
<td><p>Chemical formula of the entry</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">m_n</span></code></p></td>
<td><p>n-type/conduction band effective mass. Units: m_e where m_e is the mass of an electron; i.e. m_n is a unitless ratio</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">m_p</span></code></p></td>
<td><p>p-type/valence band effective mass.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">mpid</span></code></p></td>
<td><p>Materials Project identifier</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">pf_n</span></code></p></td>
<td><p>n-type thermoelectric power factor in uW/cm2.K where uW is microwatts and a constant relaxation time of 1e-14 assumed.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">pf_p</span></code></p></td>
<td><p>p-type power factor in uW/cm2.K</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">s_n</span></code></p></td>
<td><p>n-type Seebeck coefficient in micro Volts per Kelvin</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">s_p</span></code></p></td>
<td><p>p-type Seebeck coefficient in micro Volts per Kelvin</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">structure</span></code></p></td>
<td><p>pymatgen Structure object describing the crystal structure of the material</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>Ricci, F. et al. An ab initio electronic transport database for inorganic materials. Sci. Data 4:170085 doi: 10.1038/sdata.2017.85 (2017).
Ricci F, Chen W, Aydemir U, Snyder J, Rignanese G, Jain A, Hautier G (2017) Data from: An ab initio electronic transport database for inorganic materials. Dryad Digital Repository. <a class="reference external" href="https://doi.org/10.5061/dryad.gn001">https://doi.org/10.5061/dryad.gn001</a></p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@Article{Ricci2017, author={Ricci, Francesco and Chen, Wei and Aydemir, Umut and Snyder, G. Jeffrey and Rignanese, Gian-Marco and Jain, Anubhav and Hautier, Geoffroy}, title={An ab initio electronic transport database for inorganic materials}, journal={Scientific Data}, year={2017}, month={Jul}, day={04}, publisher={The Author(s)}, volume={4}, pages={170085}, note={Data Descriptor}, url={http://dx.doi.org/10.1038/sdata.2017.85} }

@misc{dryad_gn001, title = {Data from: An ab initio electronic transport database for inorganic materials}, author = {Ricci, F and Chen, W and Aydemir, U and Snyder, J and Rignanese, G and Jain, A and Hautier, G}, year = {2017}, journal = {Scientific Data}, URL = {https://doi.org/10.5061/dryad.gn001}, doi = {doi:10.5061/dryad.gn001}, publisher = {Dryad Digital Repository} }
</pre></div>
</div>
</section>
<section id="brgoch-superhard-training">
<h2>brgoch_superhard_training<a class="headerlink" href="#brgoch-superhard-training" title="Permalink to this heading">¶</a></h2>
<p>2574 materials used for training regressors that predict shear and bulk modulus.</p>
<p><strong>Number of entries:</strong> 2574</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">brgoch_feats</span></code></p></td>
<td><p>features used in brgoch study compressed to a dictionary</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">bulk_modulus</span></code></p></td>
<td><p>VRH bulk modulus</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">composition</span></code></p></td>
<td><p>pymatgen composition object</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">formula</span></code></p></td>
<td><p>Chemical formula as a string</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">material_id</span></code></p></td>
<td><p>materials project id</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">structure</span></code></p></td>
<td><p>pymatgen structure object</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">shear_modulus</span></code></p></td>
<td><p>VRH shear modulus</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">suspect_value</span></code></p></td>
<td><p>True if bulk or shear value did not closely match (within 5%/1GPa of MP) materials project value at time of cross reference or if no material could be found</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>Machine Learning Directed Search for Ultraincompressible, Superhard Materials
Aria Mansouri Tehrani, Anton O. Oliynyk, Marcus Parry, Zeshan Rizvi, Samantha Couper, Feng Lin, Lowell Miyagi, Taylor D. Sparks, and Jakoah Brgoch
Journal of the American Chemical Society 2018 140 (31), 9844-9853
DOI: 10.1021/jacs.8b02717</p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@article{doi:10.1021/jacs.8b02717, author = {Mansouri Tehrani, Aria and Oliynyk, Anton O. and Parry, Marcus and Rizvi, Zeshan and Couper, Samantha and Lin, Feng and Miyagi, Lowell and Sparks, Taylor D. and Brgoch, Jakoah}, title = {Machine Learning Directed Search for Ultraincompressible, Superhard Materials}, journal = {Journal of the American Chemical Society}, volume = {140}, number = {31}, pages = {9844-9853}, year = {2018}, doi = {10.1021/jacs.8b02717}, note ={PMID: 30010335}, URL = { https://doi.org/10.1021/jacs.8b02717 }, eprint = { https://doi.org/10.1021/jacs.8b02717 } }
</pre></div>
</div>
</section>
<section id="castelli-perovskites">
<h2>castelli_perovskites<a class="headerlink" href="#castelli-perovskites" title="Permalink to this heading">¶</a></h2>
<p>18,928 perovskites generated with ABX combinatorics, calculating gllbsc band gap and pbe structure, and also reporting absolute band edge positions and heat of formation.</p>
<p><strong>Number of entries:</strong> 18928</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">cbm</span></code></p></td>
<td><p>similar to vbm but for conduction band</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">e_form</span></code></p></td>
<td><p>heat of formation in eV, Note the reference state for oxygen was computed from oxygen’s chemical potential in water vapor, not as oxygen molecules, to reflect the application which these perovskites were studied for.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">fermi</span> <span class="pre">level</span></code></p></td>
<td><p>the thermodynamic work required to add one electron to the body in eV</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">fermi</span> <span class="pre">width</span></code></p></td>
<td><p>fermi bandwidth</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">formula</span></code></p></td>
<td><p>Chemical formula of the material</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">gap</span> <span class="pre">gllbsc</span></code></p></td>
<td><p>electronic band gap in eV calculated via gllbsc functional</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">gap</span> <span class="pre">is</span> <span class="pre">direct</span></code></p></td>
<td><p>boolean indicator for direct gap</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">mu_b</span></code></p></td>
<td><p>magnetic moment in terms of Bohr magneton</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">structure</span></code></p></td>
<td><p>crystal structure represented by pymatgen Structure object</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">vbm</span></code></p></td>
<td><p>absolute value of valence band edge calculated via gllbsc</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>Ivano E. Castelli, David D. Landis, Kristian S. Thygesen, Søren Dahl, Ib Chorkendorff, Thomas F. Jaramillo and Karsten W. Jacobsen (2012) New cubic perovskites for one- and two-photon water splitting using the computational materials repository. Energy Environ. Sci., 2012,5, 9034-9043 <a class="reference external" href="https://doi.org/10.1039/C2EE22341D">https://doi.org/10.1039/C2EE22341D</a></p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@Article{C2EE22341D, author =&quot;Castelli, Ivano E. and Landis, David D. and Thygesen, Kristian S. and Dahl, Søren and Chorkendorff, Ib and Jaramillo, Thomas F. and Jacobsen, Karsten W.&quot;, title  =&quot;New cubic perovskites for one- and two-photon water splitting using the computational materials repository&quot;, journal  =&quot;Energy Environ. Sci.&quot;, year  =&quot;2012&quot;, volume  =&quot;5&quot;, issue  =&quot;10&quot;, pages  =&quot;9034-9043&quot;, publisher  =&quot;The Royal Society of Chemistry&quot;, doi  =&quot;10.1039/C2EE22341D&quot;, url  =&quot;http://dx.doi.org/10.1039/C2EE22341D&quot;, abstract  =&quot;A new efficient photoelectrochemical cell (PEC) is one of the possible solutions to the energy and climate problems of our time. Such a device requires development of new semiconducting materials with tailored properties with respect to stability and light absorption. Here we perform computational screening of around 19 000 oxides{,} oxynitrides{,} oxysulfides{,} oxyfluorides{,} and oxyfluoronitrides in the cubic perovskite structure with PEC applications in mind. We address three main applications: light absorbers for one- and two-photon water splitting and high-stability transparent shields to protect against corrosion. We end up with 20{,} 12{,} and 15 different combinations of oxides{,} oxynitrides and oxyfluorides{,} respectively{,} inviting further experimental investigation.&quot;}
</pre></div>
</div>
</section>
<section id="citrine-thermal-conductivity">
<h2>citrine_thermal_conductivity<a class="headerlink" href="#citrine-thermal-conductivity" title="Permalink to this heading">¶</a></h2>
<p>Thermal conductivity of 872 compounds measured experimentally and retrieved from Citrine database from various references. The reported values are measured at various temperatures of which 295 are at room temperature.</p>
<p><strong>Number of entries:</strong> 872</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">formula</span></code></p></td>
<td><p>Chemical formula of the dataset entry</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">k-units</span></code></p></td>
<td><p>units of thermal conductivity</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">k_condition</span></code></p></td>
<td><p>Temperature description of testing conditions</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">k_condition_units</span></code></p></td>
<td><p>units of testing condition temperature representation</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">k_expt</span></code></p></td>
<td><p>the experimentally measured thermal conductivity in SI units of W/m.K</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p><a class="reference external" href="https://www.citrination.com">https://www.citrination.com</a></p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@misc{Citrine Informatics, title = {Citrination}, howpublished = {\url{https://www.citrination.com/}}, }
</pre></div>
</div>
</section>
<section id="dielectric-constant">
<h2>dielectric_constant<a class="headerlink" href="#dielectric-constant" title="Permalink to this heading">¶</a></h2>
<p>1,056 structures with dielectric properties, calculated with DFPT-PBE.</p>
<p><strong>Number of entries:</strong> 1056</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">band_gap</span></code></p></td>
<td><p>Measure of the conductivity of a material</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">cif</span></code></p></td>
<td><p>optional: Description string for structure</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">e_electronic</span></code></p></td>
<td><p>electronic contribution to dielectric tensor</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">e_total</span></code></p></td>
<td><p>Total dielectric tensor incorporating both electronic and ionic contributions</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">formula</span></code></p></td>
<td><p>Chemical formula of the material</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">material_id</span></code></p></td>
<td><p>Materials Project ID of the material</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">meta</span></code></p></td>
<td><p>optional, metadata descriptor of the datapoint</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">n</span></code></p></td>
<td><p>Refractive Index</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">nsites</span></code></p></td>
<td><p>The # of atoms in the unit cell of the calculation.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">poly_electronic</span></code></p></td>
<td><p>the average of the eigenvalues of the electronic contribution to the dielectric tensor</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">poly_total</span></code></p></td>
<td><p>the average of the eigenvalues of the total (electronic and ionic) contributions to the dielectric tensor</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">poscar</span></code></p></td>
<td><p>optional: Poscar metadata</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">pot_ferroelectric</span></code></p></td>
<td><p>Whether the material is potentially ferroelectric</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">space_group</span></code></p></td>
<td><p>Integer specifying the crystallographic structure of the material</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">structure</span></code></p></td>
<td><p>pandas Series defining the structure of the material</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">volume</span></code></p></td>
<td><p>Volume of the unit cell in cubic angstroms, For supercell calculations, this quantity refers to the volume of the full supercell.</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>Petousis, I., Mrdjenovich, D., Ballouz, E., Liu, M., Winston, D.,
Chen, W., Graf, T., Schladt, T. D., Persson, K. A. &amp; Prinz, F. B.
High-throughput screening of inorganic compounds for the discovery
of novel dielectric and optical materials. Sci. Data 4, 160134 (2017).</p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@Article{Petousis2017, author={Petousis, Ioannis and Mrdjenovich, David and Ballouz, Eric and Liu, Miao and Winston, Donald and Chen, Wei and Graf, Tanja and Schladt, Thomas D. and Persson, Kristin A. and Prinz, Fritz B.}, title={High-throughput screening of inorganic compounds for the discovery of novel dielectric and optical materials}, journal={Scientific Data}, year={2017}, month={Jan}, day={31}, publisher={The Author(s)}, volume={4}, pages={160134}, note={Data Descriptor}, url={http://dx.doi.org/10.1038/sdata.2016.134} }
</pre></div>
</div>
</section>
<section id="double-perovskites-gap">
<h2>double_perovskites_gap<a class="headerlink" href="#double-perovskites-gap" title="Permalink to this heading">¶</a></h2>
<p>Band gap of 1306 double perovskites (a_1-b_1-a_2-b_2-O6) calculated using ﻿Gritsenko, van Leeuwen, van Lenthe and Baerends potential (gllbsc) in GPAW.</p>
<p><strong>Number of entries:</strong> 1306</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">a_1</span></code></p></td>
<td><p>Species occupying the a1 perovskite site</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">a_2</span></code></p></td>
<td><p>Species occupying the a2 site</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">b_1</span></code></p></td>
<td><p>Species occupying the b1 site</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">b_2</span></code></p></td>
<td><p>Species occupying the b2 site</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">formula</span></code></p></td>
<td><p>Chemical formula of the entry</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">gap</span> <span class="pre">gllbsc</span></code></p></td>
<td><p>electronic band gap (in eV) calculated via gllbsc</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>Dataset discussed in:
Pilania, G. et al. Machine learning bandgaps of double perovskites. Sci. Rep. 6, 19375; doi: 10.1038/srep19375 (2016).
Dataset sourced from:
<a class="reference external" href="https://cmr.fysik.dtu.dk/">https://cmr.fysik.dtu.dk/</a></p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@Article{Pilania2016, author={Pilania, G. and Mannodi-Kanakkithodi, A. and Uberuaga, B. P. and Ramprasad, R. and Gubernatis, J. E. and Lookman, T.}, title={Machine learning bandgaps of double perovskites}, journal={Scientific Reports}, year={2016}, month={Jan}, day={19}, publisher={The Author(s)}, volume={6}, pages={19375}, note={Article}, url={http://dx.doi.org/10.1038/srep19375} }

@misc{Computational Materials Repository, title = {Computational Materials Repository}, howpublished = {\url{https://cmr.fysik.dtu.dk/}}, }
</pre></div>
</div>
</section>
<section id="double-perovskites-gap-lumo">
<h2>double_perovskites_gap_lumo<a class="headerlink" href="#double-perovskites-gap-lumo" title="Permalink to this heading">¶</a></h2>
<p>Supplementary lumo data of 55 atoms for the double_perovskites_gap dataset.</p>
<p><strong>Number of entries:</strong> 55</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">atom</span></code></p></td>
<td><p>Name of the atom whos lumo is listed</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">lumo</span></code></p></td>
<td><p>Lowest unoccupied molecular obital energy level (in eV)</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>Dataset discussed in:
Pilania, G. et al. Machine learning bandgaps of double perovskites. Sci. Rep. 6, 19375; doi: 10.1038/srep19375 (2016).
Dataset sourced from:
<a class="reference external" href="https://cmr.fysik.dtu.dk/">https://cmr.fysik.dtu.dk/</a></p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@Article{Pilania2016, author={Pilania, G. and Mannodi-Kanakkithodi, A. and Uberuaga, B. P. and Ramprasad, R. and Gubernatis, J. E. and Lookman, T.}, title={Machine learning bandgaps of double perovskites}, journal={Scientific Reports}, year={2016}, month={Jan}, day={19}, publisher={The Author(s)}, volume={6}, pages={19375}, note={Article}, url={http://dx.doi.org/10.1038/srep19375} }

@misc{Computational Materials Repository, title = {Computational Materials Repository}, howpublished = {\url{https://cmr.fysik.dtu.dk/}}, }
</pre></div>
</div>
</section>
<section id="elastic-tensor-2015">
<h2>elastic_tensor_2015<a class="headerlink" href="#elastic-tensor-2015" title="Permalink to this heading">¶</a></h2>
<p>1,181 structures with elastic properties calculated with DFT-PBE.</p>
<p><strong>Number of entries:</strong> 1181</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">G_Reuss</span></code></p></td>
<td><p>Lower bound on shear modulus for polycrystalline material</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">G_VRH</span></code></p></td>
<td><p>Average of G_Reuss and G_Voigt</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">G_Voigt</span></code></p></td>
<td><p>Upper bound on shear modulus for polycrystalline material</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">K_Reuss</span></code></p></td>
<td><p>Lower bound on bulk modulus for polycrystalline material</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">K_VRH</span></code></p></td>
<td><p>Average of K_Reuss and K_Voigt</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">K_Voigt</span></code></p></td>
<td><p>Upper bound on bulk modulus for polycrystalline material</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">cif</span></code></p></td>
<td><p>optional: Description string for structure</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">compliance_tensor</span></code></p></td>
<td><p>Tensor describing elastic behavior</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">elastic_anisotropy</span></code></p></td>
<td><p>measure of directional dependence of the materials elasticity, metric is always &gt;= 0</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">elastic_tensor</span></code></p></td>
<td><p>Tensor describing elastic behavior corresponding to IEEE orientation, symmetrized to crystal structure</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">elastic_tensor_original</span></code></p></td>
<td><p>Tensor describing elastic behavior, unsymmetrized, corresponding to POSCAR conventional standard cell orientation</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">formula</span></code></p></td>
<td><p>Chemical formula of the material</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">kpoint_density</span></code></p></td>
<td><p>optional: Sampling parameter from calculation</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">material_id</span></code></p></td>
<td><p>Materials Project ID of the material</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">nsites</span></code></p></td>
<td><p>The # of atoms in the unit cell of the calculation.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">poisson_ratio</span></code></p></td>
<td><p>Describes lateral response to loading</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">poscar</span></code></p></td>
<td><p>optional: Poscar metadata</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">space_group</span></code></p></td>
<td><p>Integer specifying the crystallographic structure of the material</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">structure</span></code></p></td>
<td><p>pandas Series defining the structure of the material</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">volume</span></code></p></td>
<td><p>Volume of the unit cell in cubic angstroms, For supercell calculations, this quantity refers to the volume of the full supercell.</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>Jong, M. De, Chen, W., Angsten, T., Jain, A., Notestine, R., Gamst,
A., Sluiter, M., Ande, C. K., Zwaag, S. Van Der, Plata, J. J., Toher,
C., Curtarolo, S., Ceder, G., Persson, K. and Asta, M., “Charting
the complete elastic properties of inorganic crystalline compounds”,
Scientific Data volume 2, Article number: 150009 (2015)</p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@Article{deJong2015, author={de Jong, Maarten and Chen, Wei and Angsten, Thomas and Jain, Anubhav and Notestine, Randy and Gamst, Anthony and Sluiter, Marcel and Krishna Ande, Chaitanya and van der Zwaag, Sybrand and Plata, Jose J. and Toher, Cormac and Curtarolo, Stefano and Ceder, Gerbrand and Persson, Kristin A. and Asta, Mark}, title={Charting the complete elastic properties of inorganic crystalline compounds}, journal={Scientific Data}, year={2015}, month={Mar}, day={17}, publisher={The Author(s)}, volume={2}, pages={150009}, note={Data Descriptor}, url={http://dx.doi.org/10.1038/sdata.2015.9} }
</pre></div>
</div>
</section>
<section id="expt-formation-enthalpy">
<h2>expt_formation_enthalpy<a class="headerlink" href="#expt-formation-enthalpy" title="Permalink to this heading">¶</a></h2>
<p>Experimental formation enthalpies for inorganic compounds, collected from years of calorimetric experiments. There are 1,276 entries in this dataset, mostly binary compounds. Matching mpids or oqmdids as well as the DFT-computed formation energies are also added (if any).</p>
<p><strong>Number of entries:</strong> 1276</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">e_form</span> <span class="pre">expt</span></code></p></td>
<td><p>experimental formation enthalpy (in eV/atom)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">e_form</span> <span class="pre">mp</span></code></p></td>
<td><p>formation enthalpy from Materials Project (in eV/atom)</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">e_form</span> <span class="pre">oqmd</span></code></p></td>
<td><p>formation enthalpy from OQMD (in eV/atom)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">formula</span></code></p></td>
<td><p>chemical formula</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">mpid</span></code></p></td>
<td><p>materials project id</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">oqmdid</span></code></p></td>
<td><p>OQMD id</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">pearson</span> <span class="pre">symbol</span></code></p></td>
<td><p>pearson symbol of the structure</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">space</span> <span class="pre">group</span></code></p></td>
<td><p>space group of the structure</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p><a class="reference external" href="https://www.nature.com/articles/sdata2017162">https://www.nature.com/articles/sdata2017162</a></p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@Article{Kim2017, author={Kim, George and Meschel, S. V. and Nash, Philip and Chen, Wei}, title={Experimental formation enthalpies for intermetallic phases and other inorganic compounds}, journal={Scientific Data}, year={2017}, month={Oct}, day={24}, publisher={The Author(s)}, volume={4}, pages={170162}, note={Data Descriptor}, url={https://doi.org/10.1038/sdata.2017.162}}

 @misc{kim_meschel_nash_chen_2017, title={Experimental formation enthalpies for intermetallic phases and other inorganic compounds}, url={https://figshare.com/collections/Experimental_formation_enthalpies_for_intermetallic_phases_and_other_inorganic_compounds/3822835/1}, DOI={10.6084/m9.figshare.c.3822835.v1}, abstractNote={The standard enthalpy of formation of a compound is the energy associated with the reaction to form the compound from its component elements. The standard enthalpy of formation is a fundamental thermodynamic property that determines its phase stability, which can be coupled with other thermodynamic data to calculate phase diagrams. Calorimetry provides the only direct method by which the standard enthalpy of formation is experimentally measured. However, the measurement is often a time and energy intensive process. We present a dataset of enthalpies of formation measured by high-temperature calorimetry. The phases measured in this dataset include intermetallic compounds with transition metal and rare-earth elements, metal borides, metal carbides, and metallic silicides. These measurements were collected from over 50 years of calorimetric experiments. The dataset contains 1,276 entries on experimental enthalpy of formation values and structural information. Most of the entries are for binary compounds but ternary and quaternary compounds are being added as they become available. The dataset also contains predictions of enthalpy of formation from first-principles calculations for comparison.}, publisher={figshare}, author={Kim, George and Meschel, Susan and Nash, Philip and Chen, Wei}, year={2017}, month={Oct}}
</pre></div>
</div>
</section>
<section id="expt-formation-enthalpy-kingsbury">
<h2>expt_formation_enthalpy_kingsbury<a class="headerlink" href="#expt-formation-enthalpy-kingsbury" title="Permalink to this heading">¶</a></h2>
<p>Dataset containing experimental standard formation enthalpies for solids. Formation enthalpies were compiled primarily from Kim et al., Kubaschewski, and the NIST JANAF tables (see references). Elements, liquids, and gases were excluded. Data were deduplicated such that each material is associated with a single formation enthalpy value. Refer to Wang et al. (see references) for a complete desciption of the methods used. Materials Project database IDs (mp-ids) were assigned to materials from among computed materials in the Materials Project database (version 2021.03.22) that were 1) not marked ‘theoretical’, 2) had structures matching at least one ICSD material, and 3) were within 200 meV of the DFT-computed stable energy hull (e_above_hull &lt; 0.2 eV). Among these candidates, we chose the mp-id with the lowest e_above_hull that matched the reported spacegroup (where available).</p>
<p><strong>Number of entries:</strong> 2135</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">formula</span></code></p></td>
<td><p>Chemical formula.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">expt_form_e</span></code></p></td>
<td><p>Experimental standard formation enthalpy (298 K), in eV/atom.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">uncertainty</span></code></p></td>
<td><p>Uncertainty reported in the experimental formation energy, in eV/atom.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">phaseinfo</span></code></p></td>
<td><p>Description of the material’s crystal structure or space group.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">reference</span></code></p></td>
<td><p>Reference to the original data source.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">likely_mpid</span></code></p></td>
<td><p>Materials Project database ID (mp-id) most likely associated with each material.</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>Wang, A., Kingsbury, R., McDermott, M., Horton, M., Jain. A., Ong, S.P., Dwaraknath, S., Persson, K. A framework for quantifying uncertainty in DFT energy corrections. ChemRxiv. Preprint. <a class="reference external" href="https://doi.org/10.26434/chemrxiv.14593476.v1">https://doi.org/10.26434/chemrxiv.14593476.v1</a></p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@article{Kim2017,doi={10.1038/sdata.2017.162},url={https://doi.org/10.1038/sdata.2017.162},year={2017},month=oct,publisher={Springer Science and Business Media {LLC}}, volume = {4},  number = {1},  author = {George Kim and S. V. Meschel and Philip Nash and Wei Chen},title ={Experimental formation enthalpies for intermetallic phases and other inorganic compounds},journal={Scientific Data}}

@misc{kim_meschel_nash_chen_2017, title={Experimental formation enthalpies for intermetallic phases and other inorganic compounds}, url={https://springernature.figshare.com/collections/Experimental_formation_enthalpies_for_intermetallic_phases_and_other_inorganic_compounds/3822835/1}, DOI={10.6084/m9.figshare.c.3822835.v1}, publisher={figshare},author={Kim, George and Meschel, Susan and Nash, Philip and Chen, Wei}, year={2017}, month={Oct} }

@article{Kim2017, doi = {10.1038/sdata.2017.162}, url = {https://doi.org/10.1038/sdata.2017.162}, year = {2017}, month = oct, publisher = {Springer Science and Business Media LLC}}, volume = {4}, number = {1},author = {George Kim and S. V. Meschel and Philip Nash and Wei Chen},title = {Experimental formation enthalpies for intermetallic phases and other inorganic compounds},journal = {Scientific Data}}

@book{Kubaschewski1993,author={Kubaschewski, O. and Alcock, C.B. and Spencer, P.J.},edition={6th},isbn={0080418880},publisher={Pergamon Press},title={{Materials Thermochemistry}},year = {1993}}

@misc{NIST,doi = {10.18434/T42S31},url = {http://kinetics.nist.gov/janaf/},author = {Malcolm W. Chase}, title = {NIST-JANAF Thermochemical Tables}, publisher = {National Institute of Standards and Technology},  year = {1998},  url={https://janaf.nist.org}}

@article{RZYMAN2000309,title = {Enthalpies of formation of AlFe: Experiment versus theory},journal = {Calphad},volume = {24},number = {3},pages = {309-318},year = {2000},      issn = {0364-5916},doi = {https://doi.org/10.1016/S0364-5916(01)00007-4}, url = {https://www.sciencedirect.com/science/article/pii/S0364591601000074}, author = {K. Rzyman and Z. Moser and A.P. Miodownik and L. Kaufman and R.E. Watson and M. Weinert}}

@book{CRC2007,asin = {0849304881},author = {{CRC Handbook}},dewey = {530},ean = {9780849304880},edition = 88,interhash = {da6394e1a9c5f450ed705c32ec82bb08},intrahash = {5ff8f541915536461697300e8727f265},isbn = {0849304881},keywords = {crc_handbook},publisher = {CRC Press},title = {CRC Handbook of Chemistry and Physics, 88th Edition},        year = 2007}

@article{Grindy2013,author = {Grindy, Scott and Meredig, Bryce and Kirklin, Scott and Saal, James E. and Wolverton, C.},doi = {10.1103/PhysRevB.87.075150},issn = {10980121},journal = {Physical Review B - Condensed Matter and Materials Physics},number = {7},pages = {1--8},title = {{Approaching chemical accuracy with density functional calculations: Diatomic energy corrections}},volume = {87},year = {2013}}
</pre></div>
</div>
</section>
<section id="expt-gap">
<h2>expt_gap<a class="headerlink" href="#expt-gap" title="Permalink to this heading">¶</a></h2>
<p>Experimental band gap of 6354 inorganic semiconductors.</p>
<p><strong>Number of entries:</strong> 6354</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">formula</span></code></p></td>
<td><p>chemical formula</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">gap</span> <span class="pre">expt</span></code></p></td>
<td><p>band gap (in eV) measured experimentally</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p><a class="reference external" href="https://pubs.acs.org/doi/suppl/10.1021/acs.jpclett.8b00124">https://pubs.acs.org/doi/suppl/10.1021/acs.jpclett.8b00124</a></p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@article{doi:10.1021/acs.jpclett.8b00124, author = {Zhuo, Ya and Mansouri Tehrani, Aria and Brgoch, Jakoah}, title = {Predicting the Band Gaps of Inorganic Solids by Machine Learning}, journal = {The Journal of Physical Chemistry Letters}, volume = {9}, number = {7}, pages = {1668-1673}, year = {2018}, doi = {10.1021/acs.jpclett.8b00124}, note ={PMID: 29532658}, eprint = { https://doi.org/10.1021/acs.jpclett.8b00124  }}
</pre></div>
</div>
</section>
<section id="expt-gap-kingsbury">
<h2>expt_gap_kingsbury<a class="headerlink" href="#expt-gap-kingsbury" title="Permalink to this heading">¶</a></h2>
<p>Identical to the matbench_expt_gap dataset, except that Materials Project database IDs (mp-ids) have been associated with each material using the same method as described for the expt_formation_enthalpy_kingsbury dataset. Columns have also been renamed for consistency with the formation enthalpy data.</p>
<p><strong>Number of entries:</strong> 4604</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">formula</span></code></p></td>
<td><p>Chemical formula.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">expt_gap</span></code></p></td>
<td><p>Experimentally measured bandgap, in eV.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">likely_mpid</span></code></p></td>
<td><p>Materials Project database ID (mp-id) most likely associated with each material.</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>Kingsbury, R., Bartel., C., Dwaraknath, S., Gupta, A., Horton, M., Munro, J., Jain. A., Ong, S.P., Persson, K. Comparison of r$^2$SCAN and SCAN metaGGA functionals via an automated, high-throughput computational workflow. In preparation.</p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@Article{Dunn2020, author={Dunn, Alexander and Wang, Qi and Ganose, Alex and Dopp, Daniel and Jain, Anubhav}, title={Benchmarking materials property prediction methods: the Matbench test set and Automatminer reference algorithm}, journal={npj Computational Materials}, year={2020}, month={Sep}, day={15}, volume={6}, number={1}, pages={138}, abstract={We present a benchmark test suite and an automated machine learning procedure for evaluating supervised machine learning (ML) models for predicting properties of inorganic bulk materials. The test suite, Matbench, is a set of 13{\thinspace}ML tasks that range in size from 312 to 132k samples and contain data from 10 density functional theory-derived and experimental sources. Tasks include predicting optical, thermal, electronic, thermodynamic, tensile, and elastic properties given a material&#39;s composition and/or crystal structure. The reference algorithm, Automatminer, is a highly-extensible, fully automated ML pipeline for predicting materials properties from materials primitives (such as composition and crystal structure) without user intervention or hyperparameter tuning. We test Automatminer on the Matbench test suite and compare its predictive power with state-of-the-art crystal graph neural networks and a traditional descriptor-based Random Forest model. We find Automatminer achieves the best performance on 8 of 13 tasks in the benchmark. We also show our test suite is capable of exposing predictive advantages of each algorithm---namely, that crystal graph methods appear to outperform traditional machine learning methods given {\textasciitilde}104 or greater data points. We encourage evaluating materials ML algorithms on the Matbench benchmark and comparing them against the latest version of Automatminer.}, issn={2057-3960}, doi={10.1038/s41524-020-00406-3}, url={https://doi.org/10.1038/s41524-020-00406-3} }

@article{doi:10.1021/acs.jpclett.8b00124, author = {Zhuo, Ya and Mansouri Tehrani, Aria and Brgoch, Jakoah}, title = {Predicting the Band Gaps of Inorganic Solids by Machine Learning}, journal = {The Journal of Physical Chemistry Letters}, volume = {9}, number = {7}, pages = {1668-1673}, year = {2018}, doi = {10.1021/acs.jpclett.8b00124}, note ={PMID: 29532658}, eprint = { https://doi.org/10.1021/acs.jpclett.8b00124  }}
</pre></div>
</div>
</section>
<section id="flla">
<h2>flla<a class="headerlink" href="#flla" title="Permalink to this heading">¶</a></h2>
<p>3938 structures and computed formation energies from “Crystal Structure Representations for Machine Learning Models of Formation Energies.”</p>
<p><strong>Number of entries:</strong> 3938</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">e_above_hull</span></code></p></td>
<td><p>The energy of decomposition of this material into the set of most stable materials at this chemical composition, in eV/atom.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">formation_energy</span></code></p></td>
<td><p>Computed formation energy at 0K, 0atm using a reference state of zero for the pure elements.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">formation_energy_per_atom</span></code></p></td>
<td><p>See formation_energy</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">formula</span></code></p></td>
<td><p>Chemical formula of the material</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">material_id</span></code></p></td>
<td><p>Materials Project ID of the material</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">nsites</span></code></p></td>
<td><p>The # of atoms in the unit cell of the calculation.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">structure</span></code></p></td>
<td><p>pandas Series defining the structure of the material</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>1) F. Faber, A. Lindmaa, O.A. von Lilienfeld, R. Armiento,
“Crystal structure representations for machine learning models of
formation energies”, Int. J. Quantum Chem. 115 (2015) 1094–1101.
doi:10.1002/qua.24917.</p>
<p>(raw data)
2) Jain, A., Ong, S. P., Hautier, G., Chen, W., Richards, W. D.,
Dacek, S., Cholia, S., Gunter, D., Skinner, D., Ceder, G. &amp; Persson,
K. A. Commentary: The Materials Project: A materials genome approach
to accelerating materials innovation. APL Mater. 1, 11002 (2013).</p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@article{doi:10.1002/qua.24917, author = {Faber, Felix and Lindmaa, Alexander and von Lilienfeld, O. Anatole and Armiento, Rickard}, title = {Crystal structure representations for machine learning models of formation energies}, journal = {International Journal of Quantum Chemistry}, volume = {115}, number = {16}, pages = {1094-1101}, keywords = {machine learning, formation energies, representations, crystal structure, periodic systems}, doi = {10.1002/qua.24917}, url = {https://onlinelibrary.wiley.com/doi/abs/10.1002/qua.24917}, eprint = {https://onlinelibrary.wiley.com/doi/pdf/10.1002/qua.24917}, abstract = {We introduce and evaluate a set of feature vector representations of crystal structures for machine learning (ML) models of formation energies of solids. ML models of atomization energies of organic molecules have been successful using a Coulomb matrix representation of the molecule. We consider three ways to generalize such representations to periodic systems: (i) a matrix where each element is related to the Ewald sum of the electrostatic interaction between two different atoms in the unit cell repeated over the lattice; (ii) an extended Coulomb-like matrix that takes into account a number of neighboring unit cells; and (iii) an ansatz that mimics the periodicity and the basic features of the elements in the Ewald sum matrix using a sine function of the crystal coordinates of the atoms. The representations are compared for a Laplacian kernel with Manhattan norm, trained to reproduce formation energies using a dataset of 3938 crystal structures obtained from the Materials Project. For training sets consisting of 3000 crystals, the generalization error in predicting formation energies of new structures corresponds to (i) 0.49, (ii) 0.64, and (iii) for the respective representations. © 2015 Wiley Periodicals, Inc.} }

@article{doi:10.1063/1.4812323, author = {Jain,Anubhav  and Ong,Shyue Ping  and Hautier,Geoffroy and Chen,Wei  and Richards,William Davidson  and Dacek,Stephen and Cholia,Shreyas  and Gunter,Dan  and Skinner,David and Ceder,Gerbrand  and Persson,Kristin A. }, title = {Commentary: The Materials Project: A materials genome approach to accelerating materials innovation}, journal = {APL Materials}, volume = {1}, number = {1}, pages = {011002}, year = {2013}, doi = {10.1063/1.4812323}, URL = {https://doi.org/10.1063/1.4812323}, eprint = {https://doi.org/10.1063/1.4812323} }
</pre></div>
</div>
</section>
<section id="glass-binary">
<h2>glass_binary<a class="headerlink" href="#glass-binary" title="Permalink to this heading">¶</a></h2>
<p>Metallic glass formation data for binary alloys, collected from various experimental techniques such as melt-spinning or mechanical alloying. This dataset covers all compositions with an interval of 5 at. % in 59 binary systems, containing a total of 5959 alloys in the dataset. The target property of this dataset is the glass forming ability (GFA), i.e. whether the composition can form monolithic glass or not, which is either 1 for glass forming or 0 for non-full glass forming.</p>
<p><strong>Number of entries:</strong> 5959</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">formula</span></code></p></td>
<td><p>chemical formula</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">gfa</span></code></p></td>
<td><p>glass forming ability, correlated with the phase column, designating whether the composition can form monolithic glass or not, 1: glass forming (“AM”), 0: non-full-forming(“CR”)</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p><a class="reference external" href="https://pubs.acs.org/doi/10.1021/acs.jpclett.7b01046">https://pubs.acs.org/doi/10.1021/acs.jpclett.7b01046</a></p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@article{doi:10.1021/acs.jpclett.7b01046, author = {Sun, Y. T. and Bai, H. Y. and Li, M. Z. and Wang, W. H.}, title = {Machine Learning Approach for Prediction and Understanding of Glass-Forming Ability}, journal = {The Journal of Physical Chemistry Letters}, volume = {8}, number = {14}, pages = {3434-3439}, year = {2017}, doi = {10.1021/acs.jpclett.7b01046}, note ={PMID: 28697303}, eprint = { https://doi.org/10.1021/acs.jpclett.7b01046  }}
</pre></div>
</div>
</section>
<section id="glass-binary-v2">
<h2>glass_binary_v2<a class="headerlink" href="#glass-binary-v2" title="Permalink to this heading">¶</a></h2>
<p>Identical to glass_binary dataset, but with duplicate entries merged. If there was a disagreement in gfa when merging the class was defaulted to 1.</p>
<p><strong>Number of entries:</strong> 5483</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">formula</span></code></p></td>
<td><p>chemical formula</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">gfa</span></code></p></td>
<td><p>glass forming ability, correlated with the phase column, designating whether the composition can form monolithic glass or not, 1: glass forming (“AM”), 0: non-full-forming(“CR”)</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p><a class="reference external" href="https://pubs.acs.org/doi/10.1021/acs.jpclett.7b01046">https://pubs.acs.org/doi/10.1021/acs.jpclett.7b01046</a></p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@article{doi:10.1021/acs.jpclett.7b01046, author = {Sun, Y. T. and Bai, H. Y. and Li, M. Z. and Wang, W. H.}, title = {Machine Learning Approach for Prediction and Understanding of Glass-Forming Ability}, journal = {The Journal of Physical Chemistry Letters}, volume = {8}, number = {14}, pages = {3434-3439}, year = {2017}, doi = {10.1021/acs.jpclett.7b01046}, note ={PMID: 28697303}, eprint = { https://doi.org/10.1021/acs.jpclett.7b01046  }}
</pre></div>
</div>
</section>
<section id="glass-ternary-hipt">
<h2>glass_ternary_hipt<a class="headerlink" href="#glass-ternary-hipt" title="Permalink to this heading">¶</a></h2>
<p>Metallic glass formation dataset for ternary alloys, collected from the high-throughput sputtering experiments measuring whether it is possible to form a glass using sputtering. The hipt experimental data are of the Co-Fe-Zr, Co-Ti-Zr, Co-V-Zr and Fe-Ti-Nb ternary systems.</p>
<p><strong>Number of entries:</strong> 5170</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">formula</span></code></p></td>
<td><p>Chemical formula of the entry</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">gfa</span></code></p></td>
<td><p>Glass forming ability: 1 means glass forming and coresponds to AM, 0 means non glass forming and corresponds to CR</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">phase</span></code></p></td>
<td><p>AM: amorphous phase or CR: crystalline phase</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">processing</span></code></p></td>
<td><p>How the point was processed, always sputtering for this dataset</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">system</span></code></p></td>
<td><p>System of dataset experiment, one of: CoFeZr, CoTiZr, CoVZr, or FeTiNb</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>Accelerated discovery of metallic glasses through iteration of machine learning and high-throughput experiments
By Fang Ren, Logan Ward, Travis Williams, Kevin J. Laws, Christopher Wolverton, Jason Hattrick-Simpers, Apurva Mehta
Science Advances 13 Apr 2018 : eaaq1566</p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@article {Reneaaq1566, author = {Ren, Fang and Ward, Logan and Williams, Travis and Laws, Kevin J. and Wolverton, Christopher and Hattrick-Simpers, Jason and Mehta, Apurva}, title = {Accelerated discovery of metallic glasses through iteration of machine learning and high-throughput experiments}, volume = {4}, number = {4}, year = {2018}, doi = {10.1126/sciadv.aaq1566}, publisher = {American Association for the Advancement of Science}, abstract = {With more than a hundred elements in the periodic table, a large number of potential new materials exist to address the technological and societal challenges we face today; however, without some guidance, searching through this vast combinatorial space is frustratingly slow and expensive, especially for materials strongly influenced by processing. We train a machine learning (ML) model on previously reported observations, parameters from physiochemical theories, and make it synthesis method{\textendash}dependent to guide high-throughput (HiTp) experiments to find a new system of metallic glasses in the Co-V-Zr ternary. Experimental observations are in good agreement with the predictions of the model, but there are quantitative discrepancies in the precise compositions predicted. We use these discrepancies to retrain the ML model. The refined model has significantly improved accuracy not only for the Co-V-Zr system but also across all other available validation data. We then use the refined model to guide the discovery of metallic glasses in two additional previously unreported ternaries. Although our approach of iterative use of ML and HiTp experiments has guided us to rapid discovery of three new glass-forming systems, it has also provided us with a quantitatively accurate, synthesis method{\textendash}sensitive predictor for metallic glasses that improves performance with use and thus promises to greatly accelerate discovery of many new metallic glasses. We believe that this discovery paradigm is applicable to a wider range of materials and should prove equally powerful for other materials and properties that are synthesis path{\textendash}dependent and that current physiochemical theories find challenging to predict.}, URL = {http://advances.sciencemag.org/content/4/4/eaaq1566}, eprint = {http://advances.sciencemag.org/content/4/4/eaaq1566.full.pdf}, journal = {Science Advances} }
</pre></div>
</div>
</section>
<section id="glass-ternary-landolt">
<h2>glass_ternary_landolt<a class="headerlink" href="#glass-ternary-landolt" title="Permalink to this heading">¶</a></h2>
<p>Metallic glass formation dataset for ternary alloys, collected from the “Nonequilibrium Phase Diagrams of Ternary Amorphous Alloys,’ a volume of the Landolt– Börnstein collection. This dataset contains experimental measurements of whether it is possible to form a glass using a variety of processing techniques at thousands of compositions from hundreds of ternary systems. The processing techniques are designated in the “processing” column. There are originally 7191 experiments in this dataset, will be reduced to 6203 after deduplicated, and will be further reduced to 6118 if combining multiple data for one composition. There are originally 6780 melt-spinning experiments in this dataset, will be reduced to 5800 if deduplicated, and will be further reduced to 5736 if combining multiple experimental data for one composition.</p>
<p><strong>Number of entries:</strong> 7191</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">formula</span></code></p></td>
<td><p>Chemical formula of the entry</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">gfa</span></code></p></td>
<td><p>Glass forming ability: 1 means glass forming and corresponds to AM, 0 means non full glass forming and corresponds to CR AC or QC</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">phase</span></code></p></td>
<td><p>“AM”: amorphous phase. “CR”: crystalline phase. “AC”: amorphous-crystalline composite phase. “QC”: quasi-crystalline phase. Phases obtained from glass producing experiments</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">processing</span></code></p></td>
<td><p>processing method, meltspin or sputtering</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>Y. Kawazoe, T. Masumoto, A.-P. Tsai, J.-Z. Yu, T. Aihara Jr. (1997) Y. Kawazoe, J.-Z. Yu, A.-P. Tsai, T. Masumoto (ed.) SpringerMaterials
Nonequilibrium Phase Diagrams of Ternary Amorphous Alloys · 1 Introduction Landolt-Börnstein - Group III Condensed Matter 37A (Nonequilibrium Phase Diagrams of Ternary Amorphous Alloys) <a class="reference external" href="https://www.springer.com/gp/book/9783540605072">https://www.springer.com/gp/book/9783540605072</a> (Springer-Verlag Berlin Heidelberg © 1997) Accessed: 03-09-2019</p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@Misc{LandoltBornstein1997:sm_lbs_978-3-540-47679-5_2, author=&quot;Kawazoe, Y. and Masumoto, T. and Tsai, A.-P. and Yu, J.-Z. and Aihara Jr., T.&quot;, editor=&quot;Kawazoe, Y. and Yu, J.-Z. and Tsai, A.-P. and Masumoto, T.&quot;, title=&quot;Nonequilibrium Phase Diagrams of Ternary Amorphous Alloys {\textperiodcentered} 1 Introduction: Datasheet from Landolt-B{\&quot;o}rnstein - Group III Condensed Matter {\textperiodcentered} Volume 37A: ``Nonequilibrium Phase Diagrams of Ternary Amorphous Alloys&#39;&#39; in SpringerMaterials (https://dx.doi.org/10.1007/10510374{\_}2)&quot;, publisher=&quot;Springer-Verlag Berlin Heidelberg&quot;, note=&quot;Copyright 1997 Springer-Verlag Berlin Heidelberg&quot;, note=&quot;Part of SpringerMaterials&quot;, note=&quot;accessed 2018-10-23&quot;, doi=&quot;10.1007/10510374_2&quot;, url=&quot;https://materials.springer.com/lb/docs/sm_lbs_978-3-540-47679-5_2&quot; }

@Article{Ward2016, author={Ward, Logan and Agrawal, Ankit and Choudhary, Alok and Wolverton, Christopher}, title={A general-purpose machine learning framework for predicting properties of inorganic materials}, journal={Npj Computational Materials}, year={2016}, month={Aug}, day={26}, publisher={The Author(s)}, volume={2}, pages={16028}, note={Article}, url={http://dx.doi.org/10.1038/npjcompumats.2016.28} }
</pre></div>
</div>
</section>
<section id="heusler-magnetic">
<h2>heusler_magnetic<a class="headerlink" href="#heusler-magnetic" title="Permalink to this heading">¶</a></h2>
<p>1153 Heusler alloys with DFT-calculated magnetic and electronic properties. The 1153 alloys include 576 full, 449 half and 128 inverse Heusler alloys. The data are extracted and cleaned (including de-duplicating) from Citrine.</p>
<p><strong>Number of entries:</strong> 1153</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">e_form</span></code></p></td>
<td><p>Formation energy in eV/atom</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">formula</span></code></p></td>
<td><p>Chemical formula of the entry</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">heusler</span> <span class="pre">type</span></code></p></td>
<td><p>Full, Half, or Inverse Heusler</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">latt</span> <span class="pre">const</span></code></p></td>
<td><p>Lattice constant</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">mu_b</span></code></p></td>
<td><p>Magnetic moment</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">mu_b</span> <span class="pre">saturation</span></code></p></td>
<td><p>Saturation magnetization in emu/cc</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">num_electron</span></code></p></td>
<td><p>Number of electrons per formula unit</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">pol</span> <span class="pre">fermi</span></code></p></td>
<td><p>Polarization at Fermi level in %</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">struct</span> <span class="pre">type</span></code></p></td>
<td><p>Structure type</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">tetragonality</span></code></p></td>
<td><p>Tetragonality, i.e. c/a</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p><a class="reference external" href="https://citrination.com/datasets/150561/">https://citrination.com/datasets/150561/</a></p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@misc{Citrine Informatics, title = {University of Alabama Heusler database}, howpublished = {\url{https://citrination.com/datasets/150561/}}, }
</pre></div>
</div>
</section>
<section id="jarvis-dft-2d">
<h2>jarvis_dft_2d<a class="headerlink" href="#jarvis-dft-2d" title="Permalink to this heading">¶</a></h2>
<p>Various properties of 636 2D materials computed with the OptB88vdW and TBmBJ functionals taken from the JARVIS DFT database.</p>
<p><strong>Number of entries:</strong> 636</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">composition</span></code></p></td>
<td><p>A Pymatgen Composition descriptor of the composition of the material</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">e_form</span></code></p></td>
<td><p>formation energy per atom, in eV/atom</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">epsilon_x</span> <span class="pre">opt</span></code></p></td>
<td><p>Static dielectric function in x direction calculated with OptB88vDW functional.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">epsilon_x</span> <span class="pre">tbmbj</span></code></p></td>
<td><p>Static dielectric function in x direction calculuated with TBMBJ functional.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">epsilon_y</span> <span class="pre">opt</span></code></p></td>
<td><p>Static dielectric function in y direction calculated with OptB88vDW functional.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">epsilon_y</span> <span class="pre">tbmbj</span></code></p></td>
<td><p>Static dielectric function in y direction calculuated with TBMBJ functional.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">epsilon_z</span> <span class="pre">opt</span></code></p></td>
<td><p>Static dielectric function in z direction calculated with OptB88vDW functional.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">epsilon_z</span> <span class="pre">tbmbj</span></code></p></td>
<td><p>Static dielectric function in z direction calculuated with TBMBJ functional.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">exfoliation_en</span></code></p></td>
<td><p>Exfoliation energy (monolayer formation E) in meV/atom</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">gap</span> <span class="pre">opt</span></code></p></td>
<td><p>Band gap calculated with OptB88vDW functional, in eV</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">gap</span> <span class="pre">tbmbj</span></code></p></td>
<td><p>Band gap calculated with TBMBJ functional, in eV</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">jid</span></code></p></td>
<td><p>JARVIS ID</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">mpid</span></code></p></td>
<td><p>Materials Project ID</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">structure</span></code></p></td>
<td><p>A description of the crystal structure of the material</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">structure</span> <span class="pre">initial</span></code></p></td>
<td><p>Initial structure description of the crystal structure of the material</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>2D Dataset discussed in:
High-throughput Identification and Characterization of Two dimensional Materials using Density functional theory Kamal Choudhary, Irina Kalish, Ryan Beams &amp; Francesca Tavazza Scientific Reports volume 7, Article number: 5179 (2017)
Original 2D Data file sourced from:
choudhary, kamal; <a class="reference external" href="https://orcid.org/0000-0001-9737-8074">https://orcid.org/0000-0001-9737-8074</a> (2018): jdft_2d-7-7-2018.json. figshare. Dataset.</p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@Article{Choudhary2017, author={Choudhary, Kamal and Kalish, Irina and Beams, Ryan and Tavazza, Francesca}, title={High-throughput Identification and Characterization of Two-dimensional Materials using Density functional theory}, journal={Scientific Reports}, year={2017}, volume={7}, number={1}, pages={5179}, abstract={We introduce a simple criterion to identify two-dimensional (2D) materials based on the comparison between experimental lattice constants and lattice constants mainly obtained from Materials-Project (MP) density functional theory (DFT) calculation repository. Specifically, if the relative difference between the two lattice constants for a specific material is greater than or equal to 5%, we predict them to be good candidates for 2D materials. We have predicted at least 1356 such 2D materials. For all the systems satisfying our criterion, we manually create single layer systems and calculate their energetics, structural, electronic, and elastic properties for both the bulk and the single layer cases. Currently the database consists of 1012 bulk and 430 single layer materials, of which 371 systems are common to bulk and single layer. The rest of calculations are underway. To validate our criterion, we calculated the exfoliation energy of the suggested layered materials, and we found that in 88.9% of the cases the currently accepted criterion for exfoliation was satisfied. Also, using molybdenum telluride as a test case, we performed X-ray diffraction and Raman scattering experiments to benchmark our calculations and understand their applicability and limitations. The data is publicly available at the website http://www.ctcms.nist.gov/{    extasciitilde}knc6/JVASP.html.}, issn={2045-2322}, doi={10.1038/s41598-017-05402-0}, url={https://doi.org/10.1038/s41598-017-05402-0} }

@misc{choudhary__2018, title={jdft_2d-7-7-2018.json}, url={https://figshare.com/articles/jdft_2d-7-7-2018_json/6815705/1}, DOI={10.6084/m9.figshare.6815705.v1}, abstractNote={2D materials}, publisher={figshare}, author={choudhary, kamal and https://orcid.org/0000-0001-9737-8074}, year={2018}, month={Jul}}
</pre></div>
</div>
</section>
<section id="jarvis-dft-3d">
<h2>jarvis_dft_3d<a class="headerlink" href="#jarvis-dft-3d" title="Permalink to this heading">¶</a></h2>
<p>Various properties of 25,923 bulk materials computed with the OptB88vdW and TBmBJ functionals taken from the JARVIS DFT database.</p>
<p><strong>Number of entries:</strong> 25923</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">bulk</span> <span class="pre">modulus</span></code></p></td>
<td><p>VRH average calculation of bulk modulus</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">composition</span></code></p></td>
<td><p>A Pymatgen Composition descriptor of the composition of the material</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">e_form</span></code></p></td>
<td><p>formation energy per atom, in eV/atom</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">epsilon_x</span> <span class="pre">opt</span></code></p></td>
<td><p>Static dielectric function in x direction calculated with OptB88vDW functional.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">epsilon_x</span> <span class="pre">tbmbj</span></code></p></td>
<td><p>Static dielectric function in x direction calculuated with TBMBJ functional.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">epsilon_y</span> <span class="pre">opt</span></code></p></td>
<td><p>Static dielectric function in y direction calculated with OptB88vDW functional.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">epsilon_y</span> <span class="pre">tbmbj</span></code></p></td>
<td><p>Static dielectric function in y direction calculuated with TBMBJ functional.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">epsilon_z</span> <span class="pre">opt</span></code></p></td>
<td><p>Static dielectric function in z direction calculated with OptB88vDW functional.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">epsilon_z</span> <span class="pre">tbmbj</span></code></p></td>
<td><p>Static dielectric function in z direction calculuated with TBMBJ functional.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">gap</span> <span class="pre">opt</span></code></p></td>
<td><p>Band gap calculated with OptB88vDW functional, in eV</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">gap</span> <span class="pre">tbmbj</span></code></p></td>
<td><p>Band gap calculated with TBMBJ functional, in eV</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">jid</span></code></p></td>
<td><p>JARVIS ID</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">mpid</span></code></p></td>
<td><p>Materials Project ID</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">shear</span> <span class="pre">modulus</span></code></p></td>
<td><p>VRH average calculation of shear modulus</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">structure</span></code></p></td>
<td><p>A description of the crystal structure of the material</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">structure</span> <span class="pre">initial</span></code></p></td>
<td><p>Initial structure description of the crystal structure of the material</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>3D Dataset discussed in:
Elastic properties of bulk and low-dimensional materials using van der Waals density functional Kamal Choudhary, Gowoon Cheon, Evan Reed, and Francesca Tavazza Phys. Rev. B 98, 014107
Original 3D Data file sourced from:
choudhary, kamal; <a class="reference external" href="https://orcid.org/0000-0001-9737-8074">https://orcid.org/0000-0001-9737-8074</a> (2018): jdft_3d.json. figshare. Dataset.</p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@article{PhysRevB.98.014107, title = {Elastic properties of bulk and low-dimensional materials using van der Waals density functional}, author = {Choudhary, Kamal and Cheon, Gowoon and Reed, Evan and Tavazza, Francesca}, journal = {Phys. Rev. B}, volume = {98}, issue = {1}, pages = {014107}, numpages = {12}, year = {2018}, month = {Jul}, publisher = {American Physical Society}, doi = {10.1103/PhysRevB.98.014107}, url = {https://link.aps.org/doi/10.1103/PhysRevB.98.014107} }

@misc{choudhary__2018, title={jdft_3d.json}, url={https://figshare.com/articles/jdft_3d-7-7-2018_json/6815699/2}, DOI={10.6084/m9.figshare.6815699.v2}, abstractNote={https://jarvis.nist.gov/ The Density functional theory section of JARVIS (JARVIS-DFT) consists of thousands of VASP based calculations for 3D-bulk, single layer (2D), nanowire (1D) and molecular (0D) systems. Most of the calculations are carried out with optB88vDW functional. JARVIS-DFT includes materials data such as: energetics, diffraction pattern, radial distribution function, band-structure, density of states, carrier effective mass, temperature and carrier concentration dependent thermoelectric properties, elastic constants and gamma-point phonons.}, publisher={figshare}, author={choudhary, kamal and https://orcid.org/0000-0001-9737-8074}, year={2018}, month={Jul}}
</pre></div>
</div>
</section>
<section id="jarvis-ml-dft-training">
<h2>jarvis_ml_dft_training<a class="headerlink" href="#jarvis-ml-dft-training" title="Permalink to this heading">¶</a></h2>
<p>Various properties of 24,759 bulk and 2D materials computed with the OptB88vdW and TBmBJ functionals taken from the JARVIS DFT database.</p>
<p><strong>Number of entries:</strong> 24759</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">bulk</span> <span class="pre">modulus</span></code></p></td>
<td><p>VRH average calculation of bulk modulus</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">composition</span></code></p></td>
<td><p>A descriptor of the composition of the material</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">e</span> <span class="pre">mass_x</span></code></p></td>
<td><p>Effective electron mass in x direction (BoltzTraP)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">e</span> <span class="pre">mass_y</span></code></p></td>
<td><p>Effective electron mass in y direction (BoltzTraP)</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">e</span> <span class="pre">mass_z</span></code></p></td>
<td><p>Effective electron mass in z direction (BoltzTraP)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">e_exfol</span></code></p></td>
<td><p>exfoliation energy per atom in eV/atom</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">e_form</span></code></p></td>
<td><p>formation energy per atom, in eV/atom</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">epsilon_x</span> <span class="pre">opt</span></code></p></td>
<td><p>Static dielectric function in x direction calculated with OptB88vDW functional.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">epsilon_x</span> <span class="pre">tbmbj</span></code></p></td>
<td><p>Static dielectric function in x direction calculated with TBMBJ functional.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">epsilon_y</span> <span class="pre">opt</span></code></p></td>
<td><p>Static dielectric function in y direction calculated with OptB88vDW functional.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">epsilon_y</span> <span class="pre">tbmbj</span></code></p></td>
<td><p>Static dielectric function in y direction calculated with TBMBJ functional.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">epsilon_z</span> <span class="pre">opt</span></code></p></td>
<td><p>Static dielectric function in z direction calculated with OptB88vDW functional.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">epsilon_z</span> <span class="pre">tbmbj</span></code></p></td>
<td><p>Static dielectric function in z direction calculated with TBMBJ functional.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">gap</span> <span class="pre">opt</span></code></p></td>
<td><p>Band gap calculated with OptB88vDW functional, in eV</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">gap</span> <span class="pre">tbmbj</span></code></p></td>
<td><p>Band gap calculated with TBMBJ functional, in eV</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">hole</span> <span class="pre">mass_x</span></code></p></td>
<td><p>Effective hole mass in x direction (BoltzTraP)</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">hole</span> <span class="pre">mass_y</span></code></p></td>
<td><p>Effective hole mass in y direction (BoltzTraP)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">hole</span> <span class="pre">mass_z</span></code></p></td>
<td><p>Effective hole mass in z direction (BoltzTraP)</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">jid</span></code></p></td>
<td><p>JARVIS ID</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">mpid</span></code></p></td>
<td><p>Materials Project ID</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">mu_b</span></code></p></td>
<td><p>Magnetic moment, in Bohr Magneton</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">shear</span> <span class="pre">modulus</span></code></p></td>
<td><p>VRH average calculation of shear modulus</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">structure</span></code></p></td>
<td><p>A Pymatgen Structure object describing the crystal structure of the material</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>Dataset discussed in:
Machine learning with force-field-inspired descriptors for materials: Fast screening and mapping energy landscape Kamal Choudhary, Brian DeCost, and Francesca Tavazza Phys. Rev. Materials 2, 083801</p>
<p>Original Data file sourced from:
choudhary, kamal (2018): JARVIS-ML-CFID-descriptors and material properties. figshare. Dataset.</p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@article{PhysRevMaterials.2.083801, title = {Machine learning with force-field-inspired descriptors for materials: Fast screening and mapping energy landscape}, author = {Choudhary, Kamal and DeCost, Brian and Tavazza, Francesca}, journal = {Phys. Rev. Materials}, volume = {2}, issue = {8}, pages = {083801}, numpages = {8}, year = {2018}, month = {Aug}, publisher = {American Physical Society}, doi = {10.1103/PhysRevMaterials.2.083801}, url = {https://link.aps.org/doi/10.1103/PhysRevMaterials.2.083801} }

@misc{choudhary_2018, title={JARVIS-ML-CFID-descriptors and material properties}, url={https://figshare.com/articles/JARVIS-ML-CFID-descriptors_and_material_properties/6870101/1}, DOI={10.6084/m9.figshare.6870101.v1}, abstractNote={Classical force-field inspired descriptors (CFID) for more than 25000 materials and their material properties such as bandgap, formation energies, modulus of elasticity etc. See JARVIS-ML: https://jarvis.nist.gov/}, publisher={figshare}, author={choudhary, kamal}, year={2018}, month={Jul}}
</pre></div>
</div>
</section>
<section id="m2ax">
<h2>m2ax<a class="headerlink" href="#m2ax" title="Permalink to this heading">¶</a></h2>
<p>Elastic properties of 223 stable M2AX compounds from “A comprehensive survey of M2AX phase elastic properties” by Cover et al. Calculations are PAW PW91.</p>
<p><strong>Number of entries:</strong> 223</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">a</span></code></p></td>
<td><p>Lattice parameter a, in A (angstrom)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">bulk</span> <span class="pre">modulus</span></code></p></td>
<td><p>In GPa</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">c</span></code></p></td>
<td><p>lattice parameter c, in A (angstrom)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">c11</span></code></p></td>
<td><p>Elastic constants of the M2AX material. These are specific to hexagonal materials.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">c12</span></code></p></td>
<td><p>Elastic constants of the M2AX material. These are specific to hexagonal materials.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">c13</span></code></p></td>
<td><p>Elastic constants of the M2AX material. These are specific to hexagonal materials.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">c33</span></code></p></td>
<td><p>Elastic constants of the M2AX material. These are specific to hexagonal materials.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">c44</span></code></p></td>
<td><p>Elastic constants of the M2AX material. These are specific to hexagonal materials.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">d_ma</span></code></p></td>
<td><p>distance from the M atom to the A atom</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">d_mx</span></code></p></td>
<td><p>distance from the M atom to the X atom</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">elastic</span> <span class="pre">modulus</span></code></p></td>
<td><p>In GPa</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">formula</span></code></p></td>
<td><p>chemical formula</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">shear</span> <span class="pre">modulus</span></code></p></td>
<td><p>In GPa</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p><a class="reference external" href="http://iopscience.iop.org/article/10.1088/0953-8984/21/30/305403/meta">http://iopscience.iop.org/article/10.1088/0953-8984/21/30/305403/meta</a></p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@article{M F Cover, author={M F Cover and O Warschkow and M M M Bilek and D R McKenzie}, title={A comprehensive survey of M 2 AX phase elastic properties}, journal={Journal of Physics: Condensed Matter}, volume={21}, number={30}, pages={305403}, url={http://stacks.iop.org/0953-8984/21/i=30/a=305403}, year={2009}, abstract={M 2 AX phases are a family of nanolaminate, ternary alloys that are composed of slabs of transition metal carbide or nitride (M 2 X) separated by single atomic layers of a main group element. In this combination, they manifest many of the beneficial properties of both ceramic and metallic compounds, making them attractive for many technological applications. We report here the results of a large scale computational survey of the elastic properties of all 240 elemental combinations using first-principles density functional theory calculations. We found correlations revealing the governing role of the A element and its interaction with the M element on the c axis compressibility and shearability of the material. The role of the X element is relatively minor, with the strongest effect seen in the in-plane constants C 11 and C 12 . We identify several elemental compositions with extremal properties such as W 2 SnC, which has by far the lowest value of C 44 , suggesting potential applications as a...}}
</pre></div>
</div>
</section>
<section id="matbench-dielectric">
<h2>matbench_dielectric<a class="headerlink" href="#matbench-dielectric" title="Permalink to this heading">¶</a></h2>
<p>Matbench v0.1 test dataset for predicting refractive index from structure. Adapted from Materials Project database. Removed entries having a formation energy (or energy above the convex hull) more than 150meV and those having refractive indices less than 1 and those containing noble gases. Retrieved April 2, 2019. For benchmarking w/ nested cross validation, the order of the dataset must be identical to the retrieved data; refer to the Automatminer/Matbench publication for more details.</p>
<p><strong>Number of entries:</strong> 4764</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">n</span></code></p></td>
<td><p>Target variable. Refractive index (unitless).</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">structure</span></code></p></td>
<td><p>Pymatgen Structure of the material.</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>Petousis, I., Mrdjenovich, D., Ballouz, E., Liu, M., Winston, D.,
Chen, W., Graf, T., Schladt, T. D., Persson, K. A. &amp; Prinz, F. B.
High-throughput screening of inorganic compounds for the discovery
of novel dielectric and optical materials. Sci. Data 4, 160134 (2017).</p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@Article{Dunn2020, author={Dunn, Alexander and Wang, Qi and Ganose, Alex and Dopp, Daniel and Jain, Anubhav}, title={Benchmarking materials property prediction methods: the Matbench test set and Automatminer reference algorithm}, journal={npj Computational Materials}, year={2020}, month={Sep}, day={15}, volume={6}, number={1}, pages={138}, abstract={We present a benchmark test suite and an automated machine learning procedure for evaluating supervised machine learning (ML) models for predicting properties of inorganic bulk materials. The test suite, Matbench, is a set of 13{\thinspace}ML tasks that range in size from 312 to 132k samples and contain data from 10 density functional theory-derived and experimental sources. Tasks include predicting optical, thermal, electronic, thermodynamic, tensile, and elastic properties given a material&#39;s composition and/or crystal structure. The reference algorithm, Automatminer, is a highly-extensible, fully automated ML pipeline for predicting materials properties from materials primitives (such as composition and crystal structure) without user intervention or hyperparameter tuning. We test Automatminer on the Matbench test suite and compare its predictive power with state-of-the-art crystal graph neural networks and a traditional descriptor-based Random Forest model. We find Automatminer achieves the best performance on 8 of 13 tasks in the benchmark. We also show our test suite is capable of exposing predictive advantages of each algorithm---namely, that crystal graph methods appear to outperform traditional machine learning methods given {\textasciitilde}104 or greater data points. We encourage evaluating materials ML algorithms on the Matbench benchmark and comparing them against the latest version of Automatminer.}, issn={2057-3960}, doi={10.1038/s41524-020-00406-3}, url={https://doi.org/10.1038/s41524-020-00406-3} }

@article{Jain2013, author = {Jain, Anubhav and Ong, Shyue Ping and Hautier, Geoffroy and Chen, Wei and Richards, William Davidson and Dacek, Stephen and Cholia, Shreyas and Gunter, Dan and Skinner, David and Ceder, Gerbrand and Persson, Kristin a.}, doi = {10.1063/1.4812323}, issn = {2166532X}, journal = {APL Materials}, number = {1}, pages = {011002}, title = {{The Materials Project: A materials genome approach to accelerating materials innovation}}, url = {http://link.aip.org/link/AMPADS/v1/i1/p011002/s1\&amp;Agg=doi}, volume = {1}, year = {2013} }

@article{Petousis2017, author={Petousis, Ioannis and Mrdjenovich, David and Ballouz, Eric and Liu, Miao and Winston, Donald and Chen, Wei and Graf, Tanja and Schladt, Thomas D. and Persson, Kristin A. and Prinz, Fritz B.}, title={High-throughput screening of inorganic compounds for the discovery of novel dielectric and optical materials}, journal={Scientific Data}, year={2017}, month={Jan}, day={31}, publisher={The Author(s)}, volume={4}, pages={160134}, note={Data Descriptor}, url={http://dx.doi.org/10.1038/sdata.2016.134} }
</pre></div>
</div>
</section>
<section id="matbench-expt-gap">
<h2>matbench_expt_gap<a class="headerlink" href="#matbench-expt-gap" title="Permalink to this heading">¶</a></h2>
<p>Matbench v0.1 test dataset for predicting experimental band gap from composition alone. Retrieved from Zhuo et al. supplementary information. Deduplicated according to composition, removing compositions with reported band gaps spanning more than a 0.1eV range; remaining compositions were assigned values based on the closest experimental value to the mean experimental value for that composition among all reports. For benchmarking w/ nested cross validation, the order of the dataset must be identical to the retrieved data; refer to the Automatminer/Matbench publication for more details.</p>
<p><strong>Number of entries:</strong> 4604</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">composition</span></code></p></td>
<td><p>Chemical formula.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">gap</span> <span class="pre">expt</span></code></p></td>
<td><p>Target variable. Experimentally measured gap, in eV.</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<ol class="upperalpha simple" start="25">
<li><p>Zhuo, A. Masouri Tehrani, J. Brgoch (2018) Predicting the Band Gaps of Inorganic Solids by Machine Learning J. Phys. Chem. Lett. 2018, 9, 7, 1668-1673 <a class="reference external" href="https:doi.org/10.1021/acs.jpclett.8b00124">https:doi.org/10.1021/acs.jpclett.8b00124</a>.</p></li>
</ol>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@Article{Dunn2020, author={Dunn, Alexander and Wang, Qi and Ganose, Alex and Dopp, Daniel and Jain, Anubhav}, title={Benchmarking materials property prediction methods: the Matbench test set and Automatminer reference algorithm}, journal={npj Computational Materials}, year={2020}, month={Sep}, day={15}, volume={6}, number={1}, pages={138}, abstract={We present a benchmark test suite and an automated machine learning procedure for evaluating supervised machine learning (ML) models for predicting properties of inorganic bulk materials. The test suite, Matbench, is a set of 13{\thinspace}ML tasks that range in size from 312 to 132k samples and contain data from 10 density functional theory-derived and experimental sources. Tasks include predicting optical, thermal, electronic, thermodynamic, tensile, and elastic properties given a material&#39;s composition and/or crystal structure. The reference algorithm, Automatminer, is a highly-extensible, fully automated ML pipeline for predicting materials properties from materials primitives (such as composition and crystal structure) without user intervention or hyperparameter tuning. We test Automatminer on the Matbench test suite and compare its predictive power with state-of-the-art crystal graph neural networks and a traditional descriptor-based Random Forest model. We find Automatminer achieves the best performance on 8 of 13 tasks in the benchmark. We also show our test suite is capable of exposing predictive advantages of each algorithm---namely, that crystal graph methods appear to outperform traditional machine learning methods given {\textasciitilde}104 or greater data points. We encourage evaluating materials ML algorithms on the Matbench benchmark and comparing them against the latest version of Automatminer.}, issn={2057-3960}, doi={10.1038/s41524-020-00406-3}, url={https://doi.org/10.1038/s41524-020-00406-3} }

@article{doi:10.1021/acs.jpclett.8b00124, author = {Zhuo, Ya and Mansouri Tehrani, Aria and Brgoch, Jakoah}, title = {Predicting the Band Gaps of Inorganic Solids by Machine Learning}, journal = {The Journal of Physical Chemistry Letters}, volume = {9}, number = {7}, pages = {1668-1673}, year = {2018}, doi = {10.1021/acs.jpclett.8b00124}, note ={PMID: 29532658}, eprint = { https://doi.org/10.1021/acs.jpclett.8b00124  }}
</pre></div>
</div>
</section>
<section id="matbench-expt-is-metal">
<h2>matbench_expt_is_metal<a class="headerlink" href="#matbench-expt-is-metal" title="Permalink to this heading">¶</a></h2>
<p>Matbench v0.1 test dataset for classifying metallicity from composition alone. Retrieved from Zhuo et al. supplementary information. Deduplicated according to composition, ensuring no conflicting reports were entered for any compositions (i.e., no reported compositions were both metal and nonmetal). For benchmarking w/ nested cross validation, the order of the dataset must be identical to the retrieved data; refer to the Automatminer/Matbench publication for more details.</p>
<p><strong>Number of entries:</strong> 4921</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">composition</span></code></p></td>
<td><p>Chemical formula.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">is_metal</span></code></p></td>
<td><p>Target variable. 1 if is a metal, 0 if nonmetal.</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<ol class="upperalpha simple" start="25">
<li><p>Zhuo, A. Masouri Tehrani, J. Brgoch (2018) Predicting the Band Gaps of Inorganic Solids by Machine Learning J. Phys. Chem. Lett. 2018, 9, 7, 1668-1673</p></li>
</ol>
<blockquote>
<div><p>https//:doi.org/10.1021/acs.jpclett.8b00124.</p>
</div></blockquote>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@Article{Dunn2020, author={Dunn, Alexander and Wang, Qi and Ganose, Alex and Dopp, Daniel and Jain, Anubhav}, title={Benchmarking materials property prediction methods: the Matbench test set and Automatminer reference algorithm}, journal={npj Computational Materials}, year={2020}, month={Sep}, day={15}, volume={6}, number={1}, pages={138}, abstract={We present a benchmark test suite and an automated machine learning procedure for evaluating supervised machine learning (ML) models for predicting properties of inorganic bulk materials. The test suite, Matbench, is a set of 13{\thinspace}ML tasks that range in size from 312 to 132k samples and contain data from 10 density functional theory-derived and experimental sources. Tasks include predicting optical, thermal, electronic, thermodynamic, tensile, and elastic properties given a material&#39;s composition and/or crystal structure. The reference algorithm, Automatminer, is a highly-extensible, fully automated ML pipeline for predicting materials properties from materials primitives (such as composition and crystal structure) without user intervention or hyperparameter tuning. We test Automatminer on the Matbench test suite and compare its predictive power with state-of-the-art crystal graph neural networks and a traditional descriptor-based Random Forest model. We find Automatminer achieves the best performance on 8 of 13 tasks in the benchmark. We also show our test suite is capable of exposing predictive advantages of each algorithm---namely, that crystal graph methods appear to outperform traditional machine learning methods given {\textasciitilde}104 or greater data points. We encourage evaluating materials ML algorithms on the Matbench benchmark and comparing them against the latest version of Automatminer.}, issn={2057-3960}, doi={10.1038/s41524-020-00406-3}, url={https://doi.org/10.1038/s41524-020-00406-3} }

@article{doi:10.1021/acs.jpclett.8b00124, author = {Zhuo, Ya and Mansouri Tehrani, Aria and Brgoch, Jakoah}, title= {Predicting the Band Gaps of Inorganic Solids by Machine Learning}, journal = {The Journal of Physical Chemistry Letters}, volume = {9}, number = {7}, pages = {1668-1673}, year = {2018}, doi = {10.1021/acs.jpclett.8b00124}, note ={PMID: 29532658}, eprint = { https://doi.org/10.1021/acs.jpclett.8b00124  }}
</pre></div>
</div>
</section>
<section id="matbench-glass">
<h2>matbench_glass<a class="headerlink" href="#matbench-glass" title="Permalink to this heading">¶</a></h2>
<p>Matbench v0.1 test dataset for predicting full bulk metallic glass formation ability from chemical formula. Retrieved from “Nonequilibrium Phase Diagrams of Ternary Amorphous Alloys,’ a volume of the Landolt– Börnstein collection. Deduplicated according to composition, ensuring no compositions were reported as both GFA and not GFA (i.e., all reports agreed on the classification designation). For benchmarking w/ nested cross validation, the order of the dataset must be identical to the retrieved data; refer to the Automatminer/Matbench publication for more details.</p>
<p><strong>Number of entries:</strong> 5680</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">composition</span></code></p></td>
<td><p>Chemical formula.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">gfa</span></code></p></td>
<td><p>Target variable. Glass forming ability: 1 means glass forming and corresponds to amorphous, 0 means non full glass forming.</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>Y. Kawazoe, T. Masumoto, A.-P. Tsai, J.-Z. Yu, T. Aihara Jr. (1997) Y. Kawazoe, J.-Z. Yu, A.-P. Tsai, T. Masumoto (ed.) SpringerMaterials
Nonequilibrium Phase Diagrams of Ternary Amorphous Alloys · 1 Introduction Landolt-Börnstein - Group III Condensed Matter 37A (Nonequilibrium Phase Diagrams of Ternary Amorphous Alloys) <a class="reference external" href="https://www.springer.com/gp/book/9783540605072">https://www.springer.com/gp/book/9783540605072</a> (Springer-Verlag Berlin Heidelberg © 1997) Accessed: 03-09-2019</p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@Article{Dunn2020, author={Dunn, Alexander and Wang, Qi and Ganose, Alex and Dopp, Daniel and Jain, Anubhav}, title={Benchmarking materials property prediction methods: the Matbench test set and Automatminer reference algorithm}, journal={npj Computational Materials}, year={2020}, month={Sep}, day={15}, volume={6}, number={1}, pages={138}, abstract={We present a benchmark test suite and an automated machine learning procedure for evaluating supervised machine learning (ML) models for predicting properties of inorganic bulk materials. The test suite, Matbench, is a set of 13{\thinspace}ML tasks that range in size from 312 to 132k samples and contain data from 10 density functional theory-derived and experimental sources. Tasks include predicting optical, thermal, electronic, thermodynamic, tensile, and elastic properties given a material&#39;s composition and/or crystal structure. The reference algorithm, Automatminer, is a highly-extensible, fully automated ML pipeline for predicting materials properties from materials primitives (such as composition and crystal structure) without user intervention or hyperparameter tuning. We test Automatminer on the Matbench test suite and compare its predictive power with state-of-the-art crystal graph neural networks and a traditional descriptor-based Random Forest model. We find Automatminer achieves the best performance on 8 of 13 tasks in the benchmark. We also show our test suite is capable of exposing predictive advantages of each algorithm---namely, that crystal graph methods appear to outperform traditional machine learning methods given {\textasciitilde}104 or greater data points. We encourage evaluating materials ML algorithms on the Matbench benchmark and comparing them against the latest version of Automatminer.}, issn={2057-3960}, doi={10.1038/s41524-020-00406-3}, url={https://doi.org/10.1038/s41524-020-00406-3} }

@Misc{LandoltBornstein1997:sm_lbs_978-3-540-47679-5_2, author=&quot;Kawazoe, Y. and Masumoto, T. and Tsai, A.-P. and Yu, J.-Z. and Aihara Jr., T.&quot;, editor=&quot;Kawazoe, Y. and Yu, J.-Z. and Tsai, A.-P. and Masumoto, T.&quot;, title=&quot;Nonequilibrium Phase Diagrams of Ternary Amorphous Alloys {\textperiodcentered} 1 Introduction: Datasheet from Landolt-B{\&quot;o}rnstein - Group III Condensed Matter {\textperiodcentered} Volume 37A: ``Nonequilibrium Phase Diagrams of Ternary Amorphous Alloys&#39;&#39; in SpringerMaterials (https://dx.doi.org/10.1007/10510374{\_}2)&quot;, publisher=&quot;Springer-Verlag Berlin Heidelberg&quot;, note=&quot;Copyright 1997 Springer-Verlag Berlin Heidelberg&quot;, note=&quot;Part of SpringerMaterials&quot;, note=&quot;accessed 2018-10-23&quot;, doi=&quot;10.1007/10510374_2&quot;, url=&quot;https://materials.springer.com/lb/docs/sm_lbs_978-3-540-47679-5_2&quot; }

@Article{Ward2016, author={Ward, Logan and Agrawal, Ankit and Choudhary, Alok and Wolverton, Christopher}, title={A general-purpose machine learning framework for predicting properties of inorganic materials}, journal={Npj Computational Materials}, year={2016}, month={Aug}, day={26}, publisher={The Author(s)}, volume={2}, pages={16028}, note={Article}, url={http://dx.doi.org/10.1038/npjcompumats.2016.28} }
</pre></div>
</div>
</section>
<section id="matbench-jdft2d">
<h2>matbench_jdft2d<a class="headerlink" href="#matbench-jdft2d" title="Permalink to this heading">¶</a></h2>
<p>Matbench v0.1 test dataset for predicting exfoliation energies from crystal structure (computed with the OptB88vdW and TBmBJ functionals). Adapted from the JARVIS DFT database. For benchmarking w/ nested cross validation, the order of the dataset must be identical to the retrieved data; refer to the Automatminer/Matbench publication for more details.</p>
<p><strong>Number of entries:</strong> 636</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">exfoliation_en</span></code></p></td>
<td><p>Target variable. Exfoliation energy (meV/atom).</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">structure</span></code></p></td>
<td><p>Pymatgen Structure of the material.</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>2D Dataset discussed in:
High-throughput Identification and Characterization of Two dimensional Materials using Density functional theory Kamal Choudhary, Irina Kalish, Ryan Beams &amp; Francesca Tavazza Scientific Reports volume 7, Article number: 5179 (2017)
Original 2D Data file sourced from:
choudhary, kamal; <a class="reference external" href="https://orcid.org/0000-0001-9737-8074">https://orcid.org/0000-0001-9737-8074</a> (2018): jdft_2d-7-7-2018.json. figshare. Dataset.</p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@Article{Dunn2020, author={Dunn, Alexander and Wang, Qi and Ganose, Alex and Dopp, Daniel and Jain, Anubhav}, title={Benchmarking materials property prediction methods: the Matbench test set and Automatminer reference algorithm}, journal={npj Computational Materials}, year={2020}, month={Sep}, day={15}, volume={6}, number={1}, pages={138}, abstract={We present a benchmark test suite and an automated machine learning procedure for evaluating supervised machine learning (ML) models for predicting properties of inorganic bulk materials. The test suite, Matbench, is a set of 13{\thinspace}ML tasks that range in size from 312 to 132k samples and contain data from 10 density functional theory-derived and experimental sources. Tasks include predicting optical, thermal, electronic, thermodynamic, tensile, and elastic properties given a material&#39;s composition and/or crystal structure. The reference algorithm, Automatminer, is a highly-extensible, fully automated ML pipeline for predicting materials properties from materials primitives (such as composition and crystal structure) without user intervention or hyperparameter tuning. We test Automatminer on the Matbench test suite and compare its predictive power with state-of-the-art crystal graph neural networks and a traditional descriptor-based Random Forest model. We find Automatminer achieves the best performance on 8 of 13 tasks in the benchmark. We also show our test suite is capable of exposing predictive advantages of each algorithm---namely, that crystal graph methods appear to outperform traditional machine learning methods given {\textasciitilde}104 or greater data points. We encourage evaluating materials ML algorithms on the Matbench benchmark and comparing them against the latest version of Automatminer.}, issn={2057-3960}, doi={10.1038/s41524-020-00406-3}, url={https://doi.org/10.1038/s41524-020-00406-3} }

@Article{Choudhary2017, author={Choudhary, Kamal and Kalish, Irina and Beams, Ryan and Tavazza, Francesca}, title={High-throughput Identification and Characterization of Two-dimensional Materials using Density functional theory}, journal={Scientific Reports}, year={2017}, volume={7}, number={1}, pages={5179}, abstract={We introduce a simple criterion to identify two-dimensional (2D) materials based on the comparison between experimental lattice constants and lattice constants mainly obtained from Materials-Project (MP) density functional theory (DFT) calculation repository. Specifically, if the relative difference between the two lattice constants for a specific material is greater than or equal to 5%, we predict them to be good candidates for 2D materials. We have predicted at least 1356 such 2D materials. For all the systems satisfying our criterion, we manually create single layer systems and calculate their energetics, structural, electronic, and elastic properties for both the bulk and the single layer cases. Currently the database consists of 1012 bulk and 430 single layer materials, of which 371 systems are common to bulk and single layer. The rest of calculations are underway. To validate our criterion, we calculated the exfoliation energy of the suggested layered materials, and we found that in 88.9% of the cases the currently accepted criterion for exfoliation was satisfied. Also, using molybdenum telluride as a test case, we performed X-ray diffraction and Raman scattering experiments to benchmark our calculations and understand their applicability and limitations. The data is publicly available at the website http://www.ctcms.nist.gov/{    extasciitilde}knc6/JVASP.html.}, issn={2045-2322}, doi={10.1038/s41598-017-05402-0}, url={https://doi.org/10.1038/s41598-017-05402-0} }

@misc{choudhary__2018, title={jdft_2d-7-7-2018.json}, url={https://figshare.com/articles/jdft_2d-7-7-2018_json/6815705/1}, DOI={10.6084/m9.figshare.6815705.v1}, abstractNote={2D materials}, publisher={figshare}, author={choudhary, kamal and https://orcid.org/0000-0001-9737-8074}, year={2018}, month={Jul}}
</pre></div>
</div>
</section>
<section id="matbench-log-gvrh">
<h2>matbench_log_gvrh<a class="headerlink" href="#matbench-log-gvrh" title="Permalink to this heading">¶</a></h2>
<p>Matbench v0.1 test dataset for predicting DFT log10 VRH-average shear modulus from structure. Adapted from Materials Project database. Removed entries having a formation energy (or energy above the convex hull) more than 150meV and those having negative G_Voigt, G_Reuss, G_VRH, K_Voigt, K_Reuss, or K_VRH and those failing G_Reuss &lt;= G_VRH &lt;= G_Voigt or K_Reuss &lt;= K_VRH &lt;= K_Voigt and those containing noble gases. Retrieved April 2, 2019. For benchmarking w/ nested cross validation, the order of the dataset must be identical to the retrieved data; refer to the Automatminer/Matbench publication for more details.</p>
<p><strong>Number of entries:</strong> 10987</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">log10(G_VRH)</span></code></p></td>
<td><p>Target variable. Base 10 logarithm of the DFT Voigt-Reuss-Hill average shear moduli in GPa</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">structure</span></code></p></td>
<td><p>Pymatgen Structure of the material.</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>Jong, M. De, Chen, W., Angsten, T., Jain, A., Notestine, R., Gamst,
A., Sluiter, M., Ande, C. K., Zwaag, S. Van Der, Plata, J. J., Toher,
C., Curtarolo, S., Ceder, G., Persson, K. and Asta, M., “Charting
the complete elastic properties of inorganic crystalline compounds”,
Scientific Data volume 2, Article number: 150009 (2015)</p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@Article{Dunn2020, author={Dunn, Alexander and Wang, Qi and Ganose, Alex and Dopp, Daniel and Jain, Anubhav}, title={Benchmarking materials property prediction methods: the Matbench test set and Automatminer reference algorithm}, journal={npj Computational Materials}, year={2020}, month={Sep}, day={15}, volume={6}, number={1}, pages={138}, abstract={We present a benchmark test suite and an automated machine learning procedure for evaluating supervised machine learning (ML) models for predicting properties of inorganic bulk materials. The test suite, Matbench, is a set of 13{\thinspace}ML tasks that range in size from 312 to 132k samples and contain data from 10 density functional theory-derived and experimental sources. Tasks include predicting optical, thermal, electronic, thermodynamic, tensile, and elastic properties given a material&#39;s composition and/or crystal structure. The reference algorithm, Automatminer, is a highly-extensible, fully automated ML pipeline for predicting materials properties from materials primitives (such as composition and crystal structure) without user intervention or hyperparameter tuning. We test Automatminer on the Matbench test suite and compare its predictive power with state-of-the-art crystal graph neural networks and a traditional descriptor-based Random Forest model. We find Automatminer achieves the best performance on 8 of 13 tasks in the benchmark. We also show our test suite is capable of exposing predictive advantages of each algorithm---namely, that crystal graph methods appear to outperform traditional machine learning methods given {\textasciitilde}104 or greater data points. We encourage evaluating materials ML algorithms on the Matbench benchmark and comparing them against the latest version of Automatminer.}, issn={2057-3960}, doi={10.1038/s41524-020-00406-3}, url={https://doi.org/10.1038/s41524-020-00406-3} }

@Article{deJong2015, author={de Jong, Maarten and Chen, Wei and Angsten, Thomas and Jain, Anubhav and Notestine, Randy and Gamst, Anthony and Sluiter, Marcel and Krishna Ande, Chaitanya and van der Zwaag, Sybrand and Plata, Jose J. and Toher, Cormac and Curtarolo, Stefano and Ceder, Gerbrand and Persson, Kristin A. and Asta, Mark}, title={Charting the complete elastic properties of inorganic crystalline compounds}, journal={Scientific Data}, year={2015}, month={Mar}, day={17}, publisher={The Author(s)}, volume={2}, pages={150009}, note={Data Descriptor}, url={http://dx.doi.org/10.1038/sdata.2015.9} }
</pre></div>
</div>
</section>
<section id="matbench-log-kvrh">
<h2>matbench_log_kvrh<a class="headerlink" href="#matbench-log-kvrh" title="Permalink to this heading">¶</a></h2>
<p>Matbench v0.1 test dataset for predicting DFT log10 VRH-average bulk modulus from structure. Adapted from Materials Project database. Removed entries having a formation energy (or energy above the convex hull) more than 150meV and those having negative G_Voigt, G_Reuss, G_VRH, K_Voigt, K_Reuss, or K_VRH and those failing G_Reuss &lt;= G_VRH &lt;= G_Voigt or K_Reuss &lt;= K_VRH &lt;= K_Voigt and those containing noble gases. Retrieved April 2, 2019. For benchmarking w/ nested cross validation, the order of the dataset must be identical to the retrieved data; refer to the Automatminer/Matbench publication for more details.</p>
<p><strong>Number of entries:</strong> 10987</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">log10(K_VRH)</span></code></p></td>
<td><p>Target variable. Base 10 logarithm of the DFT Voigt-Reuss-Hill average bulk moduli in GPa.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">structure</span></code></p></td>
<td><p>Pymatgen Structure of the material.</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>Jong, M. De, Chen, W., Angsten, T., Jain, A., Notestine, R., Gamst,
A., Sluiter, M., Ande, C. K., Zwaag, S. Van Der, Plata, J. J., Toher,
C., Curtarolo, S., Ceder, G., Persson, K. and Asta, M., “Charting
the complete elastic properties of inorganic crystalline compounds”,
Scientific Data volume 2, Article number: 150009 (2015)</p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@Article{Dunn2020, author={Dunn, Alexander and Wang, Qi and Ganose, Alex and Dopp, Daniel and Jain, Anubhav}, title={Benchmarking materials property prediction methods: the Matbench test set and Automatminer reference algorithm}, journal={npj Computational Materials}, year={2020}, month={Sep}, day={15}, volume={6}, number={1}, pages={138}, abstract={We present a benchmark test suite and an automated machine learning procedure for evaluating supervised machine learning (ML) models for predicting properties of inorganic bulk materials. The test suite, Matbench, is a set of 13{\thinspace}ML tasks that range in size from 312 to 132k samples and contain data from 10 density functional theory-derived and experimental sources. Tasks include predicting optical, thermal, electronic, thermodynamic, tensile, and elastic properties given a material&#39;s composition and/or crystal structure. The reference algorithm, Automatminer, is a highly-extensible, fully automated ML pipeline for predicting materials properties from materials primitives (such as composition and crystal structure) without user intervention or hyperparameter tuning. We test Automatminer on the Matbench test suite and compare its predictive power with state-of-the-art crystal graph neural networks and a traditional descriptor-based Random Forest model. We find Automatminer achieves the best performance on 8 of 13 tasks in the benchmark. We also show our test suite is capable of exposing predictive advantages of each algorithm---namely, that crystal graph methods appear to outperform traditional machine learning methods given {\textasciitilde}104 or greater data points. We encourage evaluating materials ML algorithms on the Matbench benchmark and comparing them against the latest version of Automatminer.}, issn={2057-3960}, doi={10.1038/s41524-020-00406-3}, url={https://doi.org/10.1038/s41524-020-00406-3} }

@Article{deJong2015, author={de Jong, Maarten and Chen, Wei and Angsten, Thomas and Jain, Anubhav and Notestine, Randy and Gamst, Anthony and Sluiter, Marcel and Krishna Ande, Chaitanya and van der Zwaag, Sybrand and Plata, Jose J. and Toher, Cormac and Curtarolo, Stefano and Ceder, Gerbrand and Persson, Kristin A. and Asta, Mark}, title={Charting the complete elastic properties of inorganic crystalline compounds}, journal={Scientific Data}, year={2015}, month={Mar}, day={17}, publisher={The Author(s)}, volume={2}, pages={150009}, note={Data Descriptor}, url={http://dx.doi.org/10.1038/sdata.2015.9} }
</pre></div>
</div>
</section>
<section id="matbench-mp-e-form">
<h2>matbench_mp_e_form<a class="headerlink" href="#matbench-mp-e-form" title="Permalink to this heading">¶</a></h2>
<p>Matbench v0.1 test dataset for predicting DFT formation energy from structure. Adapted from Materials Project database. Removed entries having formation energy more than 2.5eV and those containing noble gases. Retrieved April 2, 2019. For benchmarking w/ nested cross validation, the order of the dataset must be identical to the retrieved data; refer to the Automatminer/Matbench publication for more details.</p>
<p><strong>Number of entries:</strong> 132752</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">e_form</span></code></p></td>
<td><p>Target variable. Formation energy in eV as calculated by the Materials Project.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">structure</span></code></p></td>
<td><p>Pymatgen Structure of the material.</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>A. Jain*, S.P. Ong*, G. Hautier, W. Chen, W.D. Richards, S. Dacek, S. Cholia, D. Gunter, D. Skinner, G. Ceder, K.A. Persson (<a href="#id1"><span class="problematic" id="id2">*</span></a>=equal contributions)
The Materials Project: A materials genome approach to accelerating materials innovation
APL Materials, 2013, 1(1), 011002.
doi:10.1063/1.4812323</p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@Article{Dunn2020, author={Dunn, Alexander and Wang, Qi and Ganose, Alex and Dopp, Daniel and Jain, Anubhav}, title={Benchmarking materials property prediction methods: the Matbench test set and Automatminer reference algorithm}, journal={npj Computational Materials}, year={2020}, month={Sep}, day={15}, volume={6}, number={1}, pages={138}, abstract={We present a benchmark test suite and an automated machine learning procedure for evaluating supervised machine learning (ML) models for predicting properties of inorganic bulk materials. The test suite, Matbench, is a set of 13{\thinspace}ML tasks that range in size from 312 to 132k samples and contain data from 10 density functional theory-derived and experimental sources. Tasks include predicting optical, thermal, electronic, thermodynamic, tensile, and elastic properties given a material&#39;s composition and/or crystal structure. The reference algorithm, Automatminer, is a highly-extensible, fully automated ML pipeline for predicting materials properties from materials primitives (such as composition and crystal structure) without user intervention or hyperparameter tuning. We test Automatminer on the Matbench test suite and compare its predictive power with state-of-the-art crystal graph neural networks and a traditional descriptor-based Random Forest model. We find Automatminer achieves the best performance on 8 of 13 tasks in the benchmark. We also show our test suite is capable of exposing predictive advantages of each algorithm---namely, that crystal graph methods appear to outperform traditional machine learning methods given {\textasciitilde}104 or greater data points. We encourage evaluating materials ML algorithms on the Matbench benchmark and comparing them against the latest version of Automatminer.}, issn={2057-3960}, doi={10.1038/s41524-020-00406-3}, url={https://doi.org/10.1038/s41524-020-00406-3} }

@article{Jain2013, author = {Jain, Anubhav and Ong, Shyue Ping and Hautier, Geoffroy and Chen, Wei and Richards, William Davidson and Dacek, Stephen and Cholia, Shreyas and Gunter, Dan and Skinner, David and Ceder, Gerbrand and Persson, Kristin a.}, doi = {10.1063/1.4812323}, issn = {2166532X}, journal = {APL Materials}, number = {1}, pages = {011002}, title = {{The Materials Project: A materials genome approach to accelerating materials innovation}}, url = {http://link.aip.org/link/AMPADS/v1/i1/p011002/s1\&amp;Agg=doi}, volume = {1}, year = {2013} }
</pre></div>
</div>
</section>
<section id="matbench-mp-gap">
<h2>matbench_mp_gap<a class="headerlink" href="#matbench-mp-gap" title="Permalink to this heading">¶</a></h2>
<p>Matbench v0.1 test dataset for predicting DFT PBE band gap from structure. Adapted from Materials Project database. Removed entries having a formation energy (or energy above the convex hull) more than 150meV and those containing noble gases. Retrieved April 2, 2019. For benchmarking w/ nested cross validation, the order of the dataset must be identical to the retrieved data; refer to the Automatminer/Matbench publication for more details.</p>
<p><strong>Number of entries:</strong> 106113</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">gap</span> <span class="pre">pbe</span></code></p></td>
<td><p>Target variable. The band gap as calculated by PBE DFT from the Materials Project, in eV.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">structure</span></code></p></td>
<td><p>Pymatgen Structure of the material.</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>A. Jain*, S.P. Ong*, G. Hautier, W. Chen, W.D. Richards, S. Dacek, S. Cholia, D. Gunter, D. Skinner, G. Ceder, K.A. Persson (<a href="#id3"><span class="problematic" id="id4">*</span></a>=equal contributions)
The Materials Project: A materials genome approach to accelerating materials innovation
APL Materials, 2013, 1(1), 011002.
doi:10.1063/1.4812323</p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@Article{Dunn2020, author={Dunn, Alexander and Wang, Qi and Ganose, Alex and Dopp, Daniel and Jain, Anubhav}, title={Benchmarking materials property prediction methods: the Matbench test set and Automatminer reference algorithm}, journal={npj Computational Materials}, year={2020}, month={Sep}, day={15}, volume={6}, number={1}, pages={138}, abstract={We present a benchmark test suite and an automated machine learning procedure for evaluating supervised machine learning (ML) models for predicting properties of inorganic bulk materials. The test suite, Matbench, is a set of 13{\thinspace}ML tasks that range in size from 312 to 132k samples and contain data from 10 density functional theory-derived and experimental sources. Tasks include predicting optical, thermal, electronic, thermodynamic, tensile, and elastic properties given a material&#39;s composition and/or crystal structure. The reference algorithm, Automatminer, is a highly-extensible, fully automated ML pipeline for predicting materials properties from materials primitives (such as composition and crystal structure) without user intervention or hyperparameter tuning. We test Automatminer on the Matbench test suite and compare its predictive power with state-of-the-art crystal graph neural networks and a traditional descriptor-based Random Forest model. We find Automatminer achieves the best performance on 8 of 13 tasks in the benchmark. We also show our test suite is capable of exposing predictive advantages of each algorithm---namely, that crystal graph methods appear to outperform traditional machine learning methods given {\textasciitilde}104 or greater data points. We encourage evaluating materials ML algorithms on the Matbench benchmark and comparing them against the latest version of Automatminer.}, issn={2057-3960}, doi={10.1038/s41524-020-00406-3}, url={https://doi.org/10.1038/s41524-020-00406-3} }

@article{Jain2013, author = {Jain, Anubhav and Ong, Shyue Ping and Hautier, Geoffroy and Chen, Wei and Richards, William Davidson and Dacek, Stephen and Cholia, Shreyas and Gunter, Dan and Skinner, David and Ceder, Gerbrand and Persson, Kristin a.}, doi = {10.1063/1.4812323}, issn = {2166532X}, journal = {APL Materials}, number = {1}, pages = {011002}, title = {{The Materials Project: A materials genome approach to accelerating materials innovation}}, url = {http://link.aip.org/link/AMPADS/v1/i1/p011002/s1\&amp;Agg=doi}, volume = {1}, year = {2013} }
</pre></div>
</div>
</section>
<section id="matbench-mp-is-metal">
<h2>matbench_mp_is_metal<a class="headerlink" href="#matbench-mp-is-metal" title="Permalink to this heading">¶</a></h2>
<p>Matbench v0.1 test dataset for predicting DFT metallicity from structure. Adapted from Materials Project database. Removed entries having a formation energy (or energy above the convex hull) more than 150meV and those containing noble gases. Retrieved April 2, 2019. For benchmarking w/ nested cross validation, the order of the dataset must be identical to the retrieved data; refer to the Automatminer/Matbench publication for more details.</p>
<p><strong>Number of entries:</strong> 106113</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">is_metal</span></code></p></td>
<td><p>Target variable. 1 if the compound is a metal, 0 if the compound is not a metal. Metallicity determined with pymatgen</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">structure</span></code></p></td>
<td><p>Pymatgen Structure of the material.</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>A. Jain*, S.P. Ong*, G. Hautier, W. Chen, W.D. Richards, S. Dacek, S. Cholia, D. Gunter, D. Skinner, G. Ceder, K.A. Persson (<a href="#id5"><span class="problematic" id="id6">*</span></a>=equal contributions)
The Materials Project: A materials genome approach to accelerating materials innovation
APL Materials, 2013, 1(1), 011002.
doi:10.1063/1.4812323</p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@Article{Dunn2020, author={Dunn, Alexander and Wang, Qi and Ganose, Alex and Dopp, Daniel and Jain, Anubhav}, title={Benchmarking materials property prediction methods: the Matbench test set and Automatminer reference algorithm}, journal={npj Computational Materials}, year={2020}, month={Sep}, day={15}, volume={6}, number={1}, pages={138}, abstract={We present a benchmark test suite and an automated machine learning procedure for evaluating supervised machine learning (ML) models for predicting properties of inorganic bulk materials. The test suite, Matbench, is a set of 13{\thinspace}ML tasks that range in size from 312 to 132k samples and contain data from 10 density functional theory-derived and experimental sources. Tasks include predicting optical, thermal, electronic, thermodynamic, tensile, and elastic properties given a material&#39;s composition and/or crystal structure. The reference algorithm, Automatminer, is a highly-extensible, fully automated ML pipeline for predicting materials properties from materials primitives (such as composition and crystal structure) without user intervention or hyperparameter tuning. We test Automatminer on the Matbench test suite and compare its predictive power with state-of-the-art crystal graph neural networks and a traditional descriptor-based Random Forest model. We find Automatminer achieves the best performance on 8 of 13 tasks in the benchmark. We also show our test suite is capable of exposing predictive advantages of each algorithm---namely, that crystal graph methods appear to outperform traditional machine learning methods given {\textasciitilde}104 or greater data points. We encourage evaluating materials ML algorithms on the Matbench benchmark and comparing them against the latest version of Automatminer.}, issn={2057-3960}, doi={10.1038/s41524-020-00406-3}, url={https://doi.org/10.1038/s41524-020-00406-3} }

@article{Jain2013, author = {Jain, Anubhav and Ong, Shyue Ping and Hautier, Geoffroy and Chen, Wei and Richards, William Davidson and Dacek, Stephen and Cholia, Shreyas and Gunter, Dan and Skinner, David and Ceder, Gerbrand and Persson, Kristin a.}, doi = {10.1063/1.4812323}, issn = {2166532X}, journal = {APL Materials}, number = {1}, pages = {011002}, title = {{The Materials Project: A materials genome approach to accelerating materials innovation}}, url = {http://link.aip.org/link/AMPADS/v1/i1/p011002/s1\&amp;Agg=doi}, volume = {1}, year = {2013} }
</pre></div>
</div>
</section>
<section id="matbench-perovskites">
<h2>matbench_perovskites<a class="headerlink" href="#matbench-perovskites" title="Permalink to this heading">¶</a></h2>
<p>Matbench v0.1 test dataset for predicting formation energy from crystal structure. Adapted from an original dataset generated by Castelli et al. For benchmarking w/ nested cross validation, the order of the dataset must be identical to the retrieved data; refer to the Automatminer/Matbench publication for more details.</p>
<p><strong>Number of entries:</strong> 18928</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">e_form</span></code></p></td>
<td><p>Target variable. Heat of formation of the entire 5-atom perovskite cell, in eV as calculated by RPBE GGA-DFT. Note the reference state for oxygen was computed from oxygen’s chemical potential in water vapor, not as oxygen molecules, to reflect the application which these perovskites were studied for.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">structure</span></code></p></td>
<td><p>Pymatgen Structure of the material.</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>Ivano E. Castelli, David D. Landis, Kristian S. Thygesen, Søren Dahl, Ib Chorkendorff, Thomas F. Jaramillo and Karsten W. Jacobsen (2012) New cubic perovskites for one- and two-photon water splitting using the computational materials repository. Energy Environ. Sci., 2012,5, 9034-9043 <a class="reference external" href="https://doi.org/10.1039/C2EE22341D">https://doi.org/10.1039/C2EE22341D</a></p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@Article{Dunn2020, author={Dunn, Alexander and Wang, Qi and Ganose, Alex and Dopp, Daniel and Jain, Anubhav}, title={Benchmarking materials property prediction methods: the Matbench test set and Automatminer reference algorithm}, journal={npj Computational Materials}, year={2020}, month={Sep}, day={15}, volume={6}, number={1}, pages={138}, abstract={We present a benchmark test suite and an automated machine learning procedure for evaluating supervised machine learning (ML) models for predicting properties of inorganic bulk materials. The test suite, Matbench, is a set of 13{\thinspace}ML tasks that range in size from 312 to 132k samples and contain data from 10 density functional theory-derived and experimental sources. Tasks include predicting optical, thermal, electronic, thermodynamic, tensile, and elastic properties given a material&#39;s composition and/or crystal structure. The reference algorithm, Automatminer, is a highly-extensible, fully automated ML pipeline for predicting materials properties from materials primitives (such as composition and crystal structure) without user intervention or hyperparameter tuning. We test Automatminer on the Matbench test suite and compare its predictive power with state-of-the-art crystal graph neural networks and a traditional descriptor-based Random Forest model. We find Automatminer achieves the best performance on 8 of 13 tasks in the benchmark. We also show our test suite is capable of exposing predictive advantages of each algorithm---namely, that crystal graph methods appear to outperform traditional machine learning methods given {\textasciitilde}104 or greater data points. We encourage evaluating materials ML algorithms on the Matbench benchmark and comparing them against the latest version of Automatminer.}, issn={2057-3960}, doi={10.1038/s41524-020-00406-3}, url={https://doi.org/10.1038/s41524-020-00406-3} }

@Article{C2EE22341D, author =&quot;Castelli, Ivano E. and Landis, David D. and Thygesen, Kristian S. and Dahl, Søren and Chorkendorff, Ib and Jaramillo, Thomas F. and Jacobsen, Karsten W.&quot;, title  =&quot;New cubic perovskites for one- and two-photon water splitting using the computational materials repository&quot;, journal  =&quot;Energy Environ. Sci.&quot;, year  =&quot;2012&quot;, volume  =&quot;5&quot;, issue  =&quot;10&quot;, pages  =&quot;9034-9043&quot;, publisher  =&quot;The Royal Society of Chemistry&quot;, doi  =&quot;10.1039/C2EE22341D&quot;, url  =&quot;http://dx.doi.org/10.1039/C2EE22341D&quot;, abstract  =&quot;A new efficient photoelectrochemical cell (PEC) is one of the possible solutions to the energy and climate problems of our time. Such a device requires development of new semiconducting materials with tailored properties with respect to stability and light absorption. Here we perform computational screening of around 19 000 oxides{,} oxynitrides{,} oxysulfides{,} oxyfluorides{,} and oxyfluoronitrides in the cubic perovskite structure with PEC applications in mind. We address three main applications: light absorbers for one- and two-photon water splitting and high-stability transparent shields to protect against corrosion. We end up with 20{,} 12{,} and 15 different combinations of oxides{,} oxynitrides and oxyfluorides{,} respectively{,} inviting further experimental investigation.&quot;}
</pre></div>
</div>
</section>
<section id="matbench-phonons">
<h2>matbench_phonons<a class="headerlink" href="#matbench-phonons" title="Permalink to this heading">¶</a></h2>
<p>Matbench v0.1 test dataset for predicting vibration properties from crystal structure. Original data retrieved from Petretto et al. Original calculations done via ABINIT in the harmonic approximation based on density functional perturbation theory. Removed entries having a formation energy (or energy above the convex hull) more than 150meV. For benchmarking w/ nested cross validation, the order of the dataset must be identical to the retrieved data; refer to the Automatminer/Matbench publication for more details.</p>
<p><strong>Number of entries:</strong> 1265</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">last</span> <span class="pre">phdos</span> <span class="pre">peak</span></code></p></td>
<td><p>Target variable. Frequency of the highest frequency optical phonon mode peak, in units of 1/cm; ; may be used as an estimation of dominant longitudinal optical phonon frequency.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">structure</span></code></p></td>
<td><p>Pymatgen Structure of the material.</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>Petretto, G. et al. High-throughput density functional perturbation theory phonons for inorganic materials. Sci. Data 5:180065 doi: 10.1038/sdata.2018.65 (2018).
Petretto, G. et al. High-throughput density functional perturbation theory phonons for inorganic materials. (2018). figshare. Collection.</p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@Article{Dunn2020, author={Dunn, Alexander and Wang, Qi and Ganose, Alex and Dopp, Daniel and Jain, Anubhav}, title={Benchmarking materials property prediction methods: the Matbench test set and Automatminer reference algorithm}, journal={npj Computational Materials}, year={2020}, month={Sep}, day={15}, volume={6}, number={1}, pages={138}, abstract={We present a benchmark test suite and an automated machine learning procedure for evaluating supervised machine learning (ML) models for predicting properties of inorganic bulk materials. The test suite, Matbench, is a set of 13{\thinspace}ML tasks that range in size from 312 to 132k samples and contain data from 10 density functional theory-derived and experimental sources. Tasks include predicting optical, thermal, electronic, thermodynamic, tensile, and elastic properties given a material&#39;s composition and/or crystal structure. The reference algorithm, Automatminer, is a highly-extensible, fully automated ML pipeline for predicting materials properties from materials primitives (such as composition and crystal structure) without user intervention or hyperparameter tuning. We test Automatminer on the Matbench test suite and compare its predictive power with state-of-the-art crystal graph neural networks and a traditional descriptor-based Random Forest model. We find Automatminer achieves the best performance on 8 of 13 tasks in the benchmark. We also show our test suite is capable of exposing predictive advantages of each algorithm---namely, that crystal graph methods appear to outperform traditional machine learning methods given {\textasciitilde}104 or greater data points. We encourage evaluating materials ML algorithms on the Matbench benchmark and comparing them against the latest version of Automatminer.}, issn={2057-3960}, doi={10.1038/s41524-020-00406-3}, url={https://doi.org/10.1038/s41524-020-00406-3} }

@Article{Petretto2018, author={Petretto, Guido and Dwaraknath, Shyam and P.C. Miranda, Henrique and Winston, Donald and Giantomassi, Matteo and van Setten, Michiel J. and Gonze, Xavier and Persson, Kristin A. and Hautier, Geoffroy and Rignanese, Gian-Marco}, title={High-throughput density-functional perturbation theory phonons for inorganic materials}, journal={Scientific Data}, year={2018}, month={May}, day={01}, publisher={The Author(s)}, volume={5}, pages={180065}, note={Data Descriptor}, url={http://dx.doi.org/10.1038/sdata.2018.65} }

@misc{petretto_dwaraknath_miranda_winston_giantomassi_rignanese_van setten_gonze_persson_hautier_2018, title={High-throughput Density-Functional Perturbation Theory phonons for inorganic materials}, url={https://figshare.com/collections/High-throughput_Density-Functional_Perturbation_Theory_phonons_for_inorganic_materials/3938023/1}, DOI={10.6084/m9.figshare.c.3938023.v1}, abstractNote={The knowledge of the vibrational properties of a material is of key importance to understand physical phenomena such as thermal conductivity, superconductivity, and ferroelectricity among others. However, detailed experimental phonon spectra are available only for a limited number of materials which hinders the large-scale analysis of vibrational properties and their derived quantities. In this work, we perform ab initio calculations of the full phonon dispersion and vibrational density of states for 1521 semiconductor compounds in the harmonic approximation based on density functional perturbation theory. The data is collected along with derived dielectric and thermodynamic properties. We present the procedure used to obtain the results, the details of the provided database and a validation based on the comparison with experimental data.}, publisher={figshare}, author={Petretto, Guido and Dwaraknath, Shyam and Miranda, Henrique P. C. and Winston, Donald and Giantomassi, Matteo and Rignanese, Gian-Marco and Van Setten, Michiel J. and Gonze, Xavier and Persson, Kristin A and Hautier, Geoffroy}, year={2018}, month={Apr}}
</pre></div>
</div>
</section>
<section id="matbench-steels">
<h2>matbench_steels<a class="headerlink" href="#matbench-steels" title="Permalink to this heading">¶</a></h2>
<p>Matbench v0.1 test dataset for predicting steel yield strengths from chemical composition alone. Retrieved from Citrine informatics. Deduplicated. For benchmarking w/ nested cross validation, the order of the dataset must be identical to the retrieved data; refer to the Automatminer/Matbench publication for more details.</p>
<p><strong>Number of entries:</strong> 312</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">composition</span></code></p></td>
<td><p>Chemical formula.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">yield</span> <span class="pre">strength</span></code></p></td>
<td><p>Target variable. Experimentally measured steel yield strengths, in MPa.</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p><a class="reference external" href="https://citrination.com/datasets/153092/">https://citrination.com/datasets/153092/</a></p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@Article{Dunn2020, author={Dunn, Alexander and Wang, Qi and Ganose, Alex and Dopp, Daniel and Jain, Anubhav}, title={Benchmarking materials property prediction methods: the Matbench test set and Automatminer reference algorithm}, journal={npj Computational Materials}, year={2020}, month={Sep}, day={15}, volume={6}, number={1}, pages={138}, abstract={We present a benchmark test suite and an automated machine learning procedure for evaluating supervised machine learning (ML) models for predicting properties of inorganic bulk materials. The test suite, Matbench, is a set of 13{\thinspace}ML tasks that range in size from 312 to 132k samples and contain data from 10 density functional theory-derived and experimental sources. Tasks include predicting optical, thermal, electronic, thermodynamic, tensile, and elastic properties given a material&#39;s composition and/or crystal structure. The reference algorithm, Automatminer, is a highly-extensible, fully automated ML pipeline for predicting materials properties from materials primitives (such as composition and crystal structure) without user intervention or hyperparameter tuning. We test Automatminer on the Matbench test suite and compare its predictive power with state-of-the-art crystal graph neural networks and a traditional descriptor-based Random Forest model. We find Automatminer achieves the best performance on 8 of 13 tasks in the benchmark. We also show our test suite is capable of exposing predictive advantages of each algorithm---namely, that crystal graph methods appear to outperform traditional machine learning methods given {\textasciitilde}104 or greater data points. We encourage evaluating materials ML algorithms on the Matbench benchmark and comparing them against the latest version of Automatminer.}, issn={2057-3960}, doi={10.1038/s41524-020-00406-3}, url={https://doi.org/10.1038/s41524-020-00406-3} }

@misc{Citrine Informatics, title = {Mechanical properties of some steels}, howpublished = {\url{https://citrination.com/datasets/153092/}, }
</pre></div>
</div>
</section>
<section id="mp-all-20181018">
<h2>mp_all_20181018<a class="headerlink" href="#mp-all-20181018" title="Permalink to this heading">¶</a></h2>
<p>A complete copy of the Materials Project database as of 10/18/2018. mp_all files contain structure data for each material while mp_nostruct does not.</p>
<p><strong>Number of entries:</strong> 83989</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">bulk</span> <span class="pre">modulus</span></code></p></td>
<td><p>in GPa, average of Voight, Reuss, and Hill</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">e_form</span></code></p></td>
<td><p>Formation energy per atom (eV)</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">e_hull</span></code></p></td>
<td><p>The calculated energy above the convex hull, in eV per atom</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">elastic</span> <span class="pre">anisotropy</span></code></p></td>
<td><p>The ratio of elastic anisotropy.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">formula</span></code></p></td>
<td><p>The chemical formula of the MP entry</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">gap</span> <span class="pre">pbe</span></code></p></td>
<td><p>The band gap in eV calculated with PBE-DFT functional</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">initial</span> <span class="pre">structure</span></code></p></td>
<td><p>A Pymatgen Structure object describing the material crystal structure prior to relaxation</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">mpid</span></code></p></td>
<td><p>(input): The Materials Project mpid, as a string.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">mu_b</span></code></p></td>
<td><p>The total magnetization of the unit cell.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">shear</span> <span class="pre">modulus</span></code></p></td>
<td><p>in GPa, average of Voight, Reuss, and Hill</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">structure</span></code></p></td>
<td><p>A Pymatgen Structure object describing the material crystal structure</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>A. Jain*, S.P. Ong*, G. Hautier, W. Chen, W.D. Richards, S. Dacek, S. Cholia, D. Gunter, D. Skinner, G. Ceder, K.A. Persson (<a href="#id7"><span class="problematic" id="id8">*</span></a>=equal contributions)
The Materials Project: A materials genome approach to accelerating materials innovation
APL Materials, 2013, 1(1), 011002.
doi:10.1063/1.4812323</p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@article{Jain2013, author = {Jain, Anubhav and Ong, Shyue Ping and Hautier, Geoffroy and Chen, Wei and Richards, William Davidson and Dacek, Stephen and Cholia, Shreyas and Gunter, Dan and Skinner, David and Ceder, Gerbrand and Persson, Kristin a.}, doi = {10.1063/1.4812323}, issn = {2166532X}, journal = {APL Materials}, number = {1}, pages = {011002}, title = {{The Materials Project: A materials genome approach to accelerating materials innovation}}, url = {http://link.aip.org/link/AMPADS/v1/i1/p011002/s1\&amp;Agg=doi}, volume = {1}, year = {2013} }
</pre></div>
</div>
</section>
<section id="mp-nostruct-20181018">
<h2>mp_nostruct_20181018<a class="headerlink" href="#mp-nostruct-20181018" title="Permalink to this heading">¶</a></h2>
<p>A complete copy of the Materials Project database as of 10/18/2018. mp_all files contain structure data for each material while mp_nostruct does not.</p>
<p><strong>Number of entries:</strong> 83989</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">bulk</span> <span class="pre">modulus</span></code></p></td>
<td><p>in GPa, average of Voight, Reuss, and Hill</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">e_form</span></code></p></td>
<td><p>Formation energy per atom (eV)</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">e_hull</span></code></p></td>
<td><p>The calculated energy above the convex hull, in eV per atom</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">elastic</span> <span class="pre">anisotropy</span></code></p></td>
<td><p>The ratio of elastic anisotropy.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">formula</span></code></p></td>
<td><p>The chemical formula of the MP entry</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">gap</span> <span class="pre">pbe</span></code></p></td>
<td><p>The band gap in eV calculated with PBE-DFT functional</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">mpid</span></code></p></td>
<td><p>(input): The Materials Project mpid, as a string.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">mu_b</span></code></p></td>
<td><p>The total magnetization of the unit cell.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">shear</span> <span class="pre">modulus</span></code></p></td>
<td><p>in GPa, average of Voight, Reuss, and Hill</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>A. Jain*, S.P. Ong*, G. Hautier, W. Chen, W.D. Richards, S. Dacek, S. Cholia, D. Gunter, D. Skinner, G. Ceder, K.A. Persson (<a href="#id9"><span class="problematic" id="id10">*</span></a>=equal contributions)
The Materials Project: A materials genome approach to accelerating materials innovation
APL Materials, 2013, 1(1), 011002.
doi:10.1063/1.4812323</p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@article{Jain2013, author = {Jain, Anubhav and Ong, Shyue Ping and Hautier, Geoffroy and Chen, Wei and Richards, William Davidson and Dacek, Stephen and Cholia, Shreyas and Gunter, Dan and Skinner, David and Ceder, Gerbrand and Persson, Kristin a.}, doi = {10.1063/1.4812323}, issn = {2166532X}, journal = {APL Materials}, number = {1}, pages = {011002}, title = {{The Materials Project: A materials genome approach to accelerating materials innovation}}, url = {http://link.aip.org/link/AMPADS/v1/i1/p011002/s1\&amp;Agg=doi}, volume = {1}, year = {2013} }
</pre></div>
</div>
</section>
<section id="phonon-dielectric-mp">
<h2>phonon_dielectric_mp<a class="headerlink" href="#phonon-dielectric-mp" title="Permalink to this heading">¶</a></h2>
<p>Phonon (lattice/atoms vibrations) and dielectric properties of 1296 compounds computed via ABINIT software package in the harmonic approximation based on density functional perturbation theory.</p>
<p><strong>Number of entries:</strong> 1296</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">eps_electronic</span></code></p></td>
<td><p>A target variable of the dataset, electronic contribution to the calculated dielectric constant; unitless.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">eps_total</span></code></p></td>
<td><p>A target variable of the dataset, total calculated dielectric constant. Unitless: it is a ratio over the dielectric constant at vacuum.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">formula</span></code></p></td>
<td><p>The chemical formula of the material</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">last</span> <span class="pre">phdos</span> <span class="pre">peak</span></code></p></td>
<td><p>A target variable of the dataset, the frequency of the last calculated phonon density of states in 1/cm; may be used as an estimation of dominant longitudinal optical phonon frequency, a descriptor.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">mpid</span></code></p></td>
<td><p>The Materials Project identifier for the material</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">structure</span></code></p></td>
<td><p>A pymatgen Structure object describing the chemical strucutre of the material</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>Petretto, G. et al. High-throughput density functional perturbation theory phonons for inorganic materials. Sci. Data 5:180065 doi: 10.1038/sdata.2018.65 (2018).
Petretto, G. et al. High-throughput density functional perturbation theory phonons for inorganic materials. (2018). figshare. Collection.</p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@Article{Petretto2018, author={Petretto, Guido and Dwaraknath, Shyam and P.C. Miranda, Henrique and Winston, Donald and Giantomassi, Matteo and van Setten, Michiel J. and Gonze, Xavier and Persson, Kristin A. and Hautier, Geoffroy and Rignanese, Gian-Marco}, title={High-throughput density-functional perturbation theory phonons for inorganic materials}, journal={Scientific Data}, year={2018}, month={May}, day={01}, publisher={The Author(s)}, volume={5}, pages={180065}, note={Data Descriptor}, url={http://dx.doi.org/10.1038/sdata.2018.65} }

@misc{petretto_dwaraknath_miranda_winston_giantomassi_rignanese_van setten_gonze_persson_hautier_2018, title={High-throughput Density-Functional Perturbation Theory phonons for inorganic materials}, url={https://figshare.com/collections/High-throughput_Density-Functional_Perturbation_Theory_phonons_for_inorganic_materials/3938023/1}, DOI={10.6084/m9.figshare.c.3938023.v1}, abstractNote={The knowledge of the vibrational properties of a material is of key importance to understand physical phenomena such as thermal conductivity, superconductivity, and ferroelectricity among others. However, detailed experimental phonon spectra are available only for a limited number of materials which hinders the large-scale analysis of vibrational properties and their derived quantities. In this work, we perform ab initio calculations of the full phonon dispersion and vibrational density of states for 1521 semiconductor compounds in the harmonic approximation based on density functional perturbation theory. The data is collected along with derived dielectric and thermodynamic properties. We present the procedure used to obtain the results, the details of the provided database and a validation based on the comparison with experimental data.}, publisher={figshare}, author={Petretto, Guido and Dwaraknath, Shyam and Miranda, Henrique P. C. and Winston, Donald and Giantomassi, Matteo and Rignanese, Gian-Marco and Van Setten, Michiel J. and Gonze, Xavier and Persson, Kristin A and Hautier, Geoffroy}, year={2018}, month={Apr}}
</pre></div>
</div>
</section>
<section id="piezoelectric-tensor">
<h2>piezoelectric_tensor<a class="headerlink" href="#piezoelectric-tensor" title="Permalink to this heading">¶</a></h2>
<p>941 structures with piezoelectric properties, calculated with DFT-PBE.</p>
<p><strong>Number of entries:</strong> 941</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">cif</span></code></p></td>
<td><p>optional: Description string for structure</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">eij_max</span></code></p></td>
<td><p>Piezoelectric modulus</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">formula</span></code></p></td>
<td><p>Chemical formula of the material</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">material_id</span></code></p></td>
<td><p>Materials Project ID of the material</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">meta</span></code></p></td>
<td><p>optional, metadata descriptor of the datapoint</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">nsites</span></code></p></td>
<td><p>The # of atoms in the unit cell of the calculation.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">piezoelectric_tensor</span></code></p></td>
<td><p>Tensor describing the piezoelectric properties of the material</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">point_group</span></code></p></td>
<td><p>Descriptor of crystallographic structure of the material</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">poscar</span></code></p></td>
<td><p>optional: Poscar metadata</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">space_group</span></code></p></td>
<td><p>Integer specifying the crystallographic structure of the material</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">structure</span></code></p></td>
<td><p>pandas Series defining the structure of the material</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">v_max</span></code></p></td>
<td><p>Crystallographic direction</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">volume</span></code></p></td>
<td><p>Volume of the unit cell in cubic angstroms, For supercell calculations, this quantity refers to the volume of the full supercell.</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>de Jong, M., Chen, W., Geerlings, H., Asta, M. &amp; Persson, K. A.
A database to enable discovery and design of piezoelectric materials.
Sci. Data 2, 150053 (2015)</p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@Article{deJong2015, author={de Jong, Maarten and Chen, Wei and Geerlings, Henry and Asta, Mark and Persson, Kristin Aslaug}, title={A database to enable discovery and design of piezoelectric materials}, journal={Scientific Data}, year={2015}, month={Sep}, day={29}, publisher={The Author(s)}, volume={2}, pages={150053}, note={Data Descriptor}, url={http://dx.doi.org/10.1038/sdata.2015.53} }
</pre></div>
</div>
</section>
<section id="ricci-boltztrap-mp-tabular">
<h2>ricci_boltztrap_mp_tabular<a class="headerlink" href="#ricci-boltztrap-mp-tabular" title="Permalink to this heading">¶</a></h2>
<p>Ab-initio electronic transport database for inorganic materials. Complex multivariable BoltzTraP simulation data is condensed down into tabular form of two main motifs: average eigenvalues at set moderate carrier concentrations and temperatures, and optimal values among all carrier concentrations and temperatures within certain ranges. Here are reported the average of the eigenvalues of conductivity effective mass (mₑᶜᵒⁿᵈ), the Seebeck coefficient (S), the conductivity (σ), the electronic thermal conductivity (κₑ), and the Power Factor (PF) at a doping level of 10¹⁸ cm⁻³ and at a temperature of 300 K for n- and p-type. Also, the maximum values for S, σ, PF, and the minimum value for κₑ chosen among the temperatures [100, 1300] K, the doping levels [10¹⁶, 10²¹] cm⁻³, and doping types are reported. The properties that depend on the relaxation time are reported divided by the constant value 10⁻¹⁴. The average of the eigenvalues for all the properties at all the temperatures, doping levels, and doping types are reported in the tables for each entry. Data is indexed by materials project id (mpid)</p>
<p><strong>Number of entries:</strong> 47737</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">task</span></code></p></td>
<td><p>Materials project task_id.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">functional</span></code></p></td>
<td><p>Type of DFT functional (GGA=generalized gradient approximation, GGA+U=GGA + U approximation)</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">is_metal</span></code></p></td>
<td><p>If True, crystal is a metal.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">ΔE</span> <span class="pre">[eV]</span></code></p></td>
<td><p>Band gap, in eV.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">V</span> <span class="pre">[Å³]</span></code></p></td>
<td><p>Unit cell volume, in cubic angstrom.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">S.p</span> <span class="pre">[µV/K]</span></code></p></td>
<td><p>Average eigenvalue of the Seebeck coefficient with hole concentration of 10^-18 carriers/cm^-3 (p-type) at 300K, in microVolts/Kelvin.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">S.n</span> <span class="pre">[µV/K]</span></code></p></td>
<td><p>Average eigenvalue of the Seebeck coefficient with electron concentration of 10^-18 carriers/cm^-3 (n-type) at 300K, in microVolts/Kelvin.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">Sᵉ.p.v</span> <span class="pre">[µV/K]</span></code></p></td>
<td><p>Value of p-type Seebeck coefficient at maximum average eigenvalue of Seebeck coefficient chosen among temperatures 100-1300K, doping levels 10^16-10^21cm^-3.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">Sᵉ.p.T</span> <span class="pre">[K]</span></code></p></td>
<td><p>Temperature corresponding to Sᵉ.p.v [µV/K] (max p-type Seebeck), in Kelvin.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">Sᵉ.p.c</span> <span class="pre">[cm⁻³]</span></code></p></td>
<td><p>Carrier concentration corresponding to Sᵉ.p.v [µV/K] (max p-type Seebeck), in cm^-3</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">Sᵉ.n.v</span> <span class="pre">[µV/K]</span></code></p></td>
<td><p>Value of n-type Seebeck coefficient at maximum average eigenvalue of Seebeck coefficient chosen among temperatures 100-1300K, doping levels 10^16-10^21cm^-3.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">Sᵉ.n.T</span> <span class="pre">[K]</span></code></p></td>
<td><p>Temperature corresponding to Sᵉ.n.v [µV/K] (max n-type Seebeck), in Kelvin.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">Sᵉ.n.c</span> <span class="pre">[cm⁻³]</span></code></p></td>
<td><p>Carrier concentration corresponding to Sᵉ.n.v [µV/K] (max n-type Seebeck), in cm^-3</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">σ.p</span> <span class="pre">[1/Ω/m/s]</span></code></p></td>
<td><p>Average eigenvalue of the conductivity with hole concentration of 10^-18 carriers/cm^-3 (p-type) at 300K, in 1/Ω/m/s.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">σ.n</span> <span class="pre">[1/Ω/m/s]</span></code></p></td>
<td><p>Average eigenvalue of the conductivity with electron concentration of 10^-18 carriers/cm^-3 (n-type) at 300K, in 1/Ω/m/s.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">PF.p</span> <span class="pre">[µW/cm/K²/s]</span></code></p></td>
<td><p>Average eigenvalue of the power factor with hole concentration of 10^-18 carriers/cm^-3 (p-type) at 300K, in µW/cm/K²/s.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">PF.n</span> <span class="pre">[µW/cm/K²/s]</span></code></p></td>
<td><p>Average eigenvalue of the power factor with electron concentration of 10^-18 carriers/cm^-3 (n-type) at 300K, in µW/cm/K²/s.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">σᵉ.p.v</span> <span class="pre">[1/Ω/m/s]</span></code></p></td>
<td><p>Value of p-type conductivity at maximum average eigenvalue of conductivity chosen among temperatures 100-1300K, doping levels 10^16-10^21cm^-3.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">σᵉ.p.T</span> <span class="pre">[K]</span></code></p></td>
<td><p>Temperature corresponding to σᵉ.p.T [1/Ω/m/s], in Kelvin.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">σᵉ.p.c</span> <span class="pre">[cm⁻³]</span></code></p></td>
<td><p>Carrier concentration corresponding to σᵉ.p.T [1/Ω/m/s], in cm^-3.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">σᵉ.n.v</span> <span class="pre">[1/Ω/m/s]</span></code></p></td>
<td><p>Value of n-type conductivity at maximum average eigenvalue of conductivity chosen among temperatures 100-1300K, doping levels 10^16-10^21cm^-3.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">σᵉ.n.T</span> <span class="pre">[K]</span></code></p></td>
<td><p>Temperature corresponding to σᵉ.n.T [1/Ω/m/s], in Kelvin.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">σᵉ.n.c</span> <span class="pre">[cm⁻³]</span></code></p></td>
<td><p>Carrier concentration corresponding to σᵉ.n.T [1/Ω/m/s], in cm^-3.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">PFᵉ.p.v</span> <span class="pre">[µW/cm/K²/s]</span></code></p></td>
<td><p>Value of p-type power factor at maximum average eigenvalue of power factor chosen among temperatures 100-1300K, doping levels 10^16-10^21cm^-3.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">PFᵉ.p.T</span> <span class="pre">[K]</span></code></p></td>
<td><p>Temperature corresponding to PFᵉ.p.v [µW/cm/K²/s], in Kelvin.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">PFᵉ.p.c</span> <span class="pre">[cm⁻³]</span></code></p></td>
<td><p>Carrier concentration corresponding to PFᵉ.p.v [µW/cm/K²/s], in cm^-3.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">PFᵉ.n.v</span> <span class="pre">[µW/cm/K²/s]</span></code></p></td>
<td><p>Value of n-type power factor at maximum average eigenvalue of power factor chosen among temperatures 100-1300K, doping levels 10^16-10^21cm^-3.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">PFᵉ.n.T</span> <span class="pre">[K]</span></code></p></td>
<td><p>Temperature corresponding to PFᵉ.n.v [µW/cm/K²/s], in Kelvin.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">PFᵉ.n.c</span> <span class="pre">[cm⁻³]</span></code></p></td>
<td><p>Carrier concentration corresponding to PFᵉ.n.v [µW/cm/K²/s], in cm^-3.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">κₑ.p</span> <span class="pre">[W/K/m/s]</span></code></p></td>
<td><p>Average eigenvalue of electrical thermal conductivity with hole concentration of 10^-18 carriers/cm^-3 (p-type) at 300K, in [W/K/m/s].</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">κₑ.n</span> <span class="pre">[W/K/m/s]</span></code></p></td>
<td><p>Average eigenvalue of electrical thermal conductivity with electron concentration of 10^-18 carriers/cm^-3 (n-type) at 300K, in [W/K/m/s].</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">κₑᵉ.p.v</span> <span class="pre">[W/K/m/s]</span></code></p></td>
<td><p>Value of p-type electrical thermal conductivity at maximum average eigenvalue of electrical thermal conductivity chosen among temperatures 100-1300K, doping levels 10^16-10^21cm^-3.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">κₑᵉ.p.T</span> <span class="pre">[K]</span></code></p></td>
<td><p>Temperature corresponding to κₑᵉ.p.v [W/K/m/s], in Kelvin.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">κₑᵉ.p.c</span> <span class="pre">[cm⁻³]</span></code></p></td>
<td><p>Carrier concentration corresponding to κₑᵉ.p.v [W/K/m/s], in cm^-3.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">κₑᵉ.n.v</span> <span class="pre">[W/K/m/s]</span></code></p></td>
<td><p>Value of n-type electrical thermal conductivity at maximum average eigenvalue of electrical thermal conductivity chosen among temperatures 100-1300K, doping levels 10^16-10^21cm^-3.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">κₑᵉ.n.T</span> <span class="pre">[K]</span></code></p></td>
<td><p>Temperature corresponding to κₑᵉ.n.v [W/K/m/s], in Kelvin.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">κₑᵉ.n.c</span> <span class="pre">[cm⁻³]</span></code></p></td>
<td><p>Carrier concentration corresponding to κₑᵉ.n.v [W/K/m/s], in cm^-3.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">mₑᶜ.p.ε̄</span> <span class="pre">[mₑ]</span></code></p></td>
<td><p>Average (ε̄) eigenvalue of conductivity effective mass with hole concentration of 10^-18 carriers/cm^-3 (p-type) at 300K.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">mₑᶜ.p.ε₁</span> <span class="pre">[mₑ]</span></code></p></td>
<td><p>1st eigenvalue of conductivity effective mass with hole concentration of 10^-18 carriers/cm^-3 (p-type) at 300K.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">mₑᶜ.p.ε₂</span> <span class="pre">[mₑ]</span></code></p></td>
<td><p>2nd eigenvalue of conductivity effective mass with hole concentration of 10^-18 carriers/cm^-3 (p-type) at 300K.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">mₑᶜ.p.ε₃</span> <span class="pre">[mₑ]</span></code></p></td>
<td><p>3rd eigenvalue of conductivity effective mass with hole concentration of 10^-18 carriers/cm^-3 (p-type) at 300K.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">mₑᶜ.n.ε̄</span> <span class="pre">[mₑ]</span></code></p></td>
<td><p>Average (ε̄) eigenvalue of conductivity effective mass with electron concentration of 10^-18 carriers/cm^-3 (n-type) at 300K.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">mₑᶜ.n.ε₁</span> <span class="pre">[mₑ]</span></code></p></td>
<td><p>1st eigenvalue of conductivity effective mass with electron concentration of 10^-18 carriers/cm^-3 (n-type) at 300K.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">mₑᶜ.n.ε₂</span> <span class="pre">[mₑ]</span></code></p></td>
<td><p>2nd eigenvalue of conductivity effective mass with electron concentration of 10^-18 carriers/cm^-3 (n-type) at 300K.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">mₑᶜ.n.ε₃</span> <span class="pre">[mₑ]</span></code></p></td>
<td><p>3rd eigenvalue of conductivity effective mass with electron concentration of 10^-18 carriers/cm^-3 (n-type) at 300K.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">structure</span></code></p></td>
<td><p>Pymatgen structure, taken from Materials Project April 2021</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">pretty_formula</span></code></p></td>
<td><p>Formula for composition corresponding to MPID.</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>Ricci, F. et al. An ab initio electronic transport database for inorganic materials. Sci. Data 4:170085 doi: 10.1038/sdata.2017.85 (2017).
Ricci F, Chen W, Aydemir U, Snyder J, Rignanese G, Jain A, Hautier G (2017) Data from: An ab initio electronic transport database for inorganic materials. Dryad Digital Repository. <a class="reference external" href="https://doi.org/10.5061/dryad.gn001">https://doi.org/10.5061/dryad.gn001</a></p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@Article{Ricci2017, author={Ricci, Francesco and Chen, Wei and Aydemir, Umut and Snyder, G. Jeffrey and Rignanese, Gian-Marco and Jain, Anubhav and Hautier, Geoffroy}, title={An ab initio electronic transport database for inorganic materials}, journal={Scientific Data}, year={2017}, month={Jul}, day={04}, publisher={The Author(s)}, volume={4}, pages={170085}, note={Data Descriptor}, url={http://dx.doi.org/10.1038/sdata.2017.85} }

@misc{dryad_gn001, title = {Data from: An ab initio electronic transport database for inorganic materials}, author = {Ricci, F and Chen, W and Aydemir, U and Snyder, J and Rignanese, G and Jain, A and Hautier, G}, year = {2017}, journal = {Scientific Data}, URL = {https://doi.org/10.5061/dryad.gn001}, doi = {doi:10.5061/dryad.gn001}, publisher = {Dryad Digital Repository} }
</pre></div>
</div>
</section>
<section id="steel-strength">
<h2>steel_strength<a class="headerlink" href="#steel-strength" title="Permalink to this heading">¶</a></h2>
<p>312 steels with experimental yield strength and ultimate tensile strength, extracted and cleaned (including de-duplicating) from Citrine.</p>
<p><strong>Number of entries:</strong> 312</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">al</span></code></p></td>
<td><p>weight percent of Al</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">c</span></code></p></td>
<td><p>weight percent of C</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">co</span></code></p></td>
<td><p>weight percent of Co</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">cr</span></code></p></td>
<td><p>weight percent of Cr</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">elongation</span></code></p></td>
<td><p>elongation in %</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">formula</span></code></p></td>
<td><p>Chemical formula of the entry</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">mn</span></code></p></td>
<td><p>weight percent of Mn</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">mo</span></code></p></td>
<td><p>weight percent of Mo</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">n</span></code></p></td>
<td><p>weight percent of N</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">nb</span></code></p></td>
<td><p>weight percent of Nb</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">ni</span></code></p></td>
<td><p>weight percent of Ni</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">si</span></code></p></td>
<td><p>weight percent of Si</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">tensile</span> <span class="pre">strength</span></code></p></td>
<td><p>ultimate tensile strength in MPa</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">ti</span></code></p></td>
<td><p>weight percent of Ti</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">v</span></code></p></td>
<td><p>weight percent of V</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">w</span></code></p></td>
<td><p>weight percent of W</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">yield</span> <span class="pre">strength</span></code></p></td>
<td><p>yield strength in MPa</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p><a class="reference external" href="https://citrination.com/datasets/153092/">https://citrination.com/datasets/153092/</a></p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@misc{Citrine Informatics, title = {Mechanical properties of some steels}, howpublished = {\url{https://citrination.com/datasets/153092/}, }
</pre></div>
</div>
</section>
<section id="superconductivity2018">
<h2>superconductivity2018<a class="headerlink" href="#superconductivity2018" title="Permalink to this heading">¶</a></h2>
<p>Dataset of ~16,000 experimental superconductivity records (critical temperatures) from Stanev et al., originally from the Japanese National Institute for Materials Science. Does not include structural data. Includes ~300 measurements from materials found without superconductivity (Tc=0). No modifications were made to the core dataset, aside from basic file type change to json for (un)packaging with matminer. Reproduced under the Creative Commons 4.0 license, which can be found here: <a class="reference external" href="http://creativecommons.org/licenses/by/4.0/">http://creativecommons.org/licenses/by/4.0/</a>.</p>
<p><strong>Number of entries:</strong> 16414</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">composition</span></code></p></td>
<td><p>Chemical formula.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">Tc</span></code></p></td>
<td><p>Experimental superconducting temperature, in K.</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p><a class="reference external" href="https://doi.org/10.1038/s41524-018-0085-8">https://doi.org/10.1038/s41524-018-0085-8</a></p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@article{Stanev2018,   doi = {10.1038/s41524-018-0085-8},   url = {https://doi.org/10.1038/s41524-018-0085-8},   year = {2018},   month = jun,   publisher = {Springer Science and Business Media {LLC}},   volume = {4},   number = {1},   author = {Valentin Stanev and Corey Oses and A. Gilad Kusne and Efrain Rodriguez and Johnpierre Paglione and Stefano Curtarolo and Ichiro Takeuchi},   title = {Machine learning modeling of superconducting critical temperature},   journal = {npj Computational Materials} }

@misc{NIMSSuperCon, howpublished={http://supercon.nims.go.jp/index_en.html},  title={SuperCon}, author={National Institute of Materials Science, Materials Information Station}}
</pre></div>
</div>
</section>
<section id="tholander-nitrides">
<h2>tholander_nitrides<a class="headerlink" href="#tholander-nitrides" title="Permalink to this heading">¶</a></h2>
<p>A challenging data set for quantum machine learning containing a diverse set of 12.8k polymorphs in the Zn-Ti-N, Zn-Zr-N and Zn-Hf-N chemical systems. The phase diagrams of the Ti-Zn-N, Zr-Zn-N, and Hf-Zn-N systems are determined using large-scale high-throughput density functional calculations (DFT-GGA) (PBE). In total 12,815 relaxed structures are shared alongside their energy calculated using the VASP DFT code. The High-Throughput Toolkit was used to manage the calculations. Data adapted and deduplicated from the original data on Zenodo at <a class="reference external" href="https://zenodo.org/record/5530535#.YjJ3ZhDMJLQ">https://zenodo.org/record/5530535#.YjJ3ZhDMJLQ</a>, published under MIT licence. Collated from separate files of chemical systems and deduplicated according to identical structures matching ht_ids. Prepared in collaboration with Rhys Goodall.</p>
<p><strong>Number of entries:</strong> 12815</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">material_id</span></code></p></td>
<td><p>Human readable identifier for each material.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">ht_id</span></code></p></td>
<td><p>Unique identifier to track the calculation in httk</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">initial_structure</span></code></p></td>
<td><p>A pymatgen structure object representing the structure before relaxation.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">final_structure</span></code></p></td>
<td><p>A pymatgen structure object representing the structure after relaxation.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">E_vasp_per_atom</span></code></p></td>
<td><p>The VASP calculated energy per atom for the final structure, in eV/atom</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">chemical_system</span></code></p></td>
<td><p>The chemical system represented by the atoms actually contained in the structure</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p><a class="reference external" href="https://zenodo.org/record/5530535#.YjJ3ZhDMJLQ">https://zenodo.org/record/5530535#.YjJ3ZhDMJLQ</a></p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@article{tholander2016strong,   title={Strong piezoelectric response in stable TiZnN2, ZrZnN2, and HfZnN2 found by ab initio high-throughput approach},   author={Tholander, Christopher and Andersson, CBA and Armiento, Rickard and Tasnadi, Ferenc and Alling, Bj{\&quot;o}rn},   journal={Journal of Applied Physics},   volume={120},   number={22},   pages={225102},   year={2016},   publisher={AIP Publishing LLC} }
</pre></div>
</div>
</section>
<section id="ucsb-thermoelectrics">
<h2>ucsb_thermoelectrics<a class="headerlink" href="#ucsb-thermoelectrics" title="Permalink to this heading">¶</a></h2>
<p>Database of ~1,100 experimental thermoelectric materials from UCSB aggregated from 108 source publications and personal communications. Downloaded from Citrine. Source UCSB webpage is <a class="reference external" href="http://www.mrl.ucsb.edu:8080/datamine/thermoelectric.jsp">http://www.mrl.ucsb.edu:8080/datamine/thermoelectric.jsp</a>. See reference for more information on original data aggregation. No duplicate entries are present, but each src may result in multiple measurements of the same materials’ properties at different temperatures or conditions.</p>
<p><strong>Number of entries:</strong> 1093</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">composition</span></code></p></td>
<td><p>Chemical formula.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">crystallinity</span></code></p></td>
<td><p>Either single crystal, polycrystalline, or nanoparticles.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">synthesis</span></code></p></td>
<td><p>Brief string describing the synthesis method</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">spacegroup</span></code></p></td>
<td><p>Spacegroup number, if available</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">rho</span> <span class="pre">(ohm.cm)</span></code></p></td>
<td><p>Electrical resistivity, in ohm.cm</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">S</span> <span class="pre">[muV/K]</span></code></p></td>
<td><p>Seebeck coefficient, in microVolts/K, if available</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">PF</span> <span class="pre">[W/mK^2]</span></code></p></td>
<td><p>Thermoelectric power factor, conductivity * Seebeck^2, in [W/mK^2] if available</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">zT</span></code></p></td>
<td><p>Thermoelectric figure of merit, PF * T/K, unitless, if available</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">kappa</span> <span class="pre">[W/mK]</span></code></p></td>
<td><p>Thermal conductivity in Watt/ meter * Kelvin, if available</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">sigma</span> <span class="pre">[S/cm]</span></code></p></td>
<td><p>Electrical conductivity, in Siemens/cm, if available</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">T</span> <span class="pre">[K]</span></code></p></td>
<td><p>Temperature in Kelvin at which these properties were obtained, if available</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">src</span></code></p></td>
<td><p>Original source of the recording. To cite the aggregator of the data, see the bibtext_refs section of this metadata.</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p><a class="reference external" href="https://citrination.com/datasets/150557/">https://citrination.com/datasets/150557/</a></p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@article{Gaultois2013,   doi = {10.1021/cm400893e},   url = {https://doi.org/10.1021/cm400893e},   year = {2013},   month = may,   publisher = {American Chemical Society ({ACS})},   volume = {25},   number = {15},   pages = {2911--2920},   author = {Michael W. Gaultois and Taylor D. Sparks and Christopher K. H. Borg and Ram Seshadri and William D. Bonificio and David R. Clarke},   title = {Data-Driven Review of Thermoelectric Materials: Performance and Resource Considerations},   journal = {Chemistry of Materials} }

@misc{Citrine Informatics, title = {UCSB Thermoelectrics Database}, howpublished = {\url{https://citrination.com/datasets/150557/}, }
</pre></div>
</div>
</section>
<section id="wolverton-oxides">
<h2>wolverton_oxides<a class="headerlink" href="#wolverton-oxides" title="Permalink to this heading">¶</a></h2>
<p>4,914 perovskite oxides containing composition data, lattice constants, and formation + vacancy formation energies. All perovskites are of the form ABO3. Adapted from a dataset presented by Emery and Wolverton.</p>
<p><strong>Number of entries:</strong> 4914</p>
<table class="docutils align-left">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Column</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">a</span></code></p></td>
<td><p>Lattice parameter a, in A (angstrom)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">alpha</span></code></p></td>
<td><p>Lattice angle alpha, in degrees</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">atom</span> <span class="pre">a</span></code></p></td>
<td><p>The atom in the ‘A’ site of the pervoskite.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">atom</span> <span class="pre">b</span></code></p></td>
<td><p>The atom in the ‘B’ site of the perovskite.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">b</span></code></p></td>
<td><p>Lattice parameter b, in A (angstrom)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">beta</span></code></p></td>
<td><p>Lattice angle beta, in degrees</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">c</span></code></p></td>
<td><p>Lattice parameter c, in A (angstrom)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">e_form</span></code></p></td>
<td><p>Formation energy in eV</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">e_form</span> <span class="pre">oxygen</span></code></p></td>
<td><p>Formation energy of oxygen vacancy (eV)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">e_hull</span></code></p></td>
<td><p>Energy above convex hull, wrt. OQMD db (eV)</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">formula</span></code></p></td>
<td><p>Chemical formula of the entry</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">gamma</span></code></p></td>
<td><p>Lattice angle gamma, in degrees</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">gap</span> <span class="pre">pbe</span></code></p></td>
<td><p>Bandgap in eV from PBE calculations</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">lowest</span> <span class="pre">distortion</span></code></p></td>
<td><p>Local distortion crystal structure with lowest energy among all considered distortions.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">mu_b</span></code></p></td>
<td><p>Magnetic moment</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">vpa</span></code></p></td>
<td><p>Volume per atom (A^3/atom)</p></td>
</tr>
</tbody>
</table>
<p><strong>Reference</strong></p>
<p>Emery, A. A. &amp; Wolverton, C. High-throughput DFT calculations of formation energy, stability and oxygen vacancy formation energy of ABO3 perovskites. Sci. Data 4:170153 doi: 10.1038/sdata.2017.153 (2017).
Emery, A. A., &amp; Wolverton, C. Figshare <a class="reference external" href="http://dx.doi.org/10.6084/m9.figshare.5334142">http://dx.doi.org/10.6084/m9.figshare.5334142</a> (2017)</p>
<p><strong>Bibtex Formatted Citations</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>@Article{Emery2017, author={Emery, Antoine A. and Wolverton, Chris}, title={High-throughput DFT calculations of formation energy, stability and oxygen vacancy formation energy of ABO3 perovskites}, journal={Scientific Data}, year={2017}, month={Oct}, day={17}, publisher={The Author(s)}, volume={4}, pages={170153}, note={Data Descriptor}, url={http://dx.doi.org/10.1038/sdata.2017.153} }

@misc{emery_2017, title={High-throughput DFT calculations of formation energy, stability and oxygen vacancy formation energy of ABO3 perovskites}, url={https://figshare.com/articles/High-throughput_DFT_calculations_of_formation_energy_stability_and_oxygen_vacancy_formation_energy_of_ABO3_perovskites/5334142/1}, DOI={10.6084/m9.figshare.5334142.v1}, abstractNote={ABO3 perovskites are oxide materials that are used for a variety of applications such as solid oxide fuel cells, piezo-, ferro-electricity and water splitting. Due to their remarkable stability with respect to cation substitution, new compounds for such applications potentially await discovery. In this work, we present an exhaustive dataset of formation energies of 5,329 cubic and distorted perovskites that were calculated using first-principles density functional theory. In addition to formation energies, several additional properties such as oxidation states, band gap, oxygen vacancy formation energy, and thermodynamic stability with respect to all phases in the Open Quantum Materials Database are also made publicly available. This large dataset for this ubiquitous crystal structure type contains 395 perovskites that are predicted to be thermodynamically stable, of which many have not yet been experimentally reported, and therefore represent theoretical predictions. The dataset thus opens avenues for future use, including materials discovery in many research-active areas.}, publisher={figshare}, author={Emery, Antoine}, year={2017}, month={Aug}}
</pre></div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Table of Datasets</a></li>
<li><a class="reference internal" href="#dataset-info">Dataset info</a><ul>
<li><a class="reference internal" href="#boltztrap-mp">boltztrap_mp</a></li>
<li><a class="reference internal" href="#brgoch-superhard-training">brgoch_superhard_training</a></li>
<li><a class="reference internal" href="#castelli-perovskites">castelli_perovskites</a></li>
<li><a class="reference internal" href="#citrine-thermal-conductivity">citrine_thermal_conductivity</a></li>
<li><a class="reference internal" href="#dielectric-constant">dielectric_constant</a></li>
<li><a class="reference internal" href="#double-perovskites-gap">double_perovskites_gap</a></li>
<li><a class="reference internal" href="#double-perovskites-gap-lumo">double_perovskites_gap_lumo</a></li>
<li><a class="reference internal" href="#elastic-tensor-2015">elastic_tensor_2015</a></li>
<li><a class="reference internal" href="#expt-formation-enthalpy">expt_formation_enthalpy</a></li>
<li><a class="reference internal" href="#expt-formation-enthalpy-kingsbury">expt_formation_enthalpy_kingsbury</a></li>
<li><a class="reference internal" href="#expt-gap">expt_gap</a></li>
<li><a class="reference internal" href="#expt-gap-kingsbury">expt_gap_kingsbury</a></li>
<li><a class="reference internal" href="#flla">flla</a></li>
<li><a class="reference internal" href="#glass-binary">glass_binary</a></li>
<li><a class="reference internal" href="#glass-binary-v2">glass_binary_v2</a></li>
<li><a class="reference internal" href="#glass-ternary-hipt">glass_ternary_hipt</a></li>
<li><a class="reference internal" href="#glass-ternary-landolt">glass_ternary_landolt</a></li>
<li><a class="reference internal" href="#heusler-magnetic">heusler_magnetic</a></li>
<li><a class="reference internal" href="#jarvis-dft-2d">jarvis_dft_2d</a></li>
<li><a class="reference internal" href="#jarvis-dft-3d">jarvis_dft_3d</a></li>
<li><a class="reference internal" href="#jarvis-ml-dft-training">jarvis_ml_dft_training</a></li>
<li><a class="reference internal" href="#m2ax">m2ax</a></li>
<li><a class="reference internal" href="#matbench-dielectric">matbench_dielectric</a></li>
<li><a class="reference internal" href="#matbench-expt-gap">matbench_expt_gap</a></li>
<li><a class="reference internal" href="#matbench-expt-is-metal">matbench_expt_is_metal</a></li>
<li><a class="reference internal" href="#matbench-glass">matbench_glass</a></li>
<li><a class="reference internal" href="#matbench-jdft2d">matbench_jdft2d</a></li>
<li><a class="reference internal" href="#matbench-log-gvrh">matbench_log_gvrh</a></li>
<li><a class="reference internal" href="#matbench-log-kvrh">matbench_log_kvrh</a></li>
<li><a class="reference internal" href="#matbench-mp-e-form">matbench_mp_e_form</a></li>
<li><a class="reference internal" href="#matbench-mp-gap">matbench_mp_gap</a></li>
<li><a class="reference internal" href="#matbench-mp-is-metal">matbench_mp_is_metal</a></li>
<li><a class="reference internal" href="#matbench-perovskites">matbench_perovskites</a></li>
<li><a class="reference internal" href="#matbench-phonons">matbench_phonons</a></li>
<li><a class="reference internal" href="#matbench-steels">matbench_steels</a></li>
<li><a class="reference internal" href="#mp-all-20181018">mp_all_20181018</a></li>
<li><a class="reference internal" href="#mp-nostruct-20181018">mp_nostruct_20181018</a></li>
<li><a class="reference internal" href="#phonon-dielectric-mp">phonon_dielectric_mp</a></li>
<li><a class="reference internal" href="#piezoelectric-tensor">piezoelectric_tensor</a></li>
<li><a class="reference internal" href="#ricci-boltztrap-mp-tabular">ricci_boltztrap_mp_tabular</a></li>
<li><a class="reference internal" href="#steel-strength">steel_strength</a></li>
<li><a class="reference internal" href="#superconductivity2018">superconductivity2018</a></li>
<li><a class="reference internal" href="#tholander-nitrides">tholander_nitrides</a></li>
<li><a class="reference internal" href="#ucsb-thermoelectrics">ucsb_thermoelectrics</a></li>
<li><a class="reference internal" href="#wolverton-oxides">wolverton_oxides</a></li>
</ul>
</li>
</ul>

  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/dataset_summary.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Table of Datasets</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2015, Anubhav Jain.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.3.0.
    </div>

  </body>
</html>