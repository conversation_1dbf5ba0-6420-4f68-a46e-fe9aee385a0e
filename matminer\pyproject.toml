[build-system]
requires = [
     # pin NumPy version used in the build
     "numpy>=1.20.1",
     "setuptools>=43.0.0"
]
build-backend = "setuptools.build_meta"


[tool.black]
line-length = 120
target-version = ['py38']
include = '\.pyi?$'
exclude = '''

(
  /(
      \.eggs         # exclude a few common directories in the
    | \.git          # root of the project
    | \.hg
    | \.mypy_cache
    | \.tox
    | \.venv
    | _build
    | buck-out
    | build
    | dist
  )/
)
'''
