
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

    <title>matminer package &#8212; matminer 0.9.0 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="_static/nature.css" />
    <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="_static/doctools.js"></script>
    <script src="_static/sphinx_highlight.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
 
<link href='https://fonts.googleapis.com/css?family=Lato:400,700' rel='stylesheet' type='text/css'>

  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer package</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="matminer-package">
<h1>matminer package<a class="headerlink" href="#matminer-package" title="Permalink to this heading">¶</a></h1>
<section id="subpackages">
<h2>Subpackages<a class="headerlink" href="#subpackages" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="matminer.data_retrieval.html">matminer.data_retrieval package</a><ul>
<li class="toctree-l2"><a class="reference internal" href="matminer.data_retrieval.html#subpackages">Subpackages</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.data_retrieval.tests.html">matminer.data_retrieval.tests package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.tests.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.tests.html#module-matminer.data_retrieval.tests.base">matminer.data_retrieval.tests.base module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.tests.html#module-matminer.data_retrieval.tests.test_retrieve_AFLOW">matminer.data_retrieval.tests.test_retrieve_AFLOW module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.tests.html#module-matminer.data_retrieval.tests.test_retrieve_Citrine">matminer.data_retrieval.tests.test_retrieve_Citrine module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.tests.html#module-matminer.data_retrieval.tests.test_retrieve_MDF">matminer.data_retrieval.tests.test_retrieve_MDF module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.tests.html#module-matminer.data_retrieval.tests.test_retrieve_MP">matminer.data_retrieval.tests.test_retrieve_MP module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.tests.html#module-matminer.data_retrieval.tests.test_retrieve_MPDS">matminer.data_retrieval.tests.test_retrieve_MPDS module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.tests.html#module-matminer.data_retrieval.tests.test_retrieve_MongoDB">matminer.data_retrieval.tests.test_retrieve_MongoDB module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.tests.html#module-matminer.data_retrieval.tests">Module contents</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.data_retrieval.html#submodules">Submodules</a></li>
<li class="toctree-l2"><a class="reference internal" href="matminer.data_retrieval.html#module-matminer.data_retrieval.retrieve_AFLOW">matminer.data_retrieval.retrieve_AFLOW module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_AFLOW.AFLOWDataRetrieval"><code class="docutils literal notranslate"><span class="pre">AFLOWDataRetrieval</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_AFLOW.AFLOWDataRetrieval.api_link"><code class="docutils literal notranslate"><span class="pre">AFLOWDataRetrieval.api_link()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_AFLOW.AFLOWDataRetrieval.citations"><code class="docutils literal notranslate"><span class="pre">AFLOWDataRetrieval.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_AFLOW.AFLOWDataRetrieval.get_dataframe"><code class="docutils literal notranslate"><span class="pre">AFLOWDataRetrieval.get_dataframe()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_AFLOW.AFLOWDataRetrieval.get_relaxed_structure"><code class="docutils literal notranslate"><span class="pre">AFLOWDataRetrieval.get_relaxed_structure()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_AFLOW.RetrievalQuery"><code class="docutils literal notranslate"><span class="pre">RetrievalQuery</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_AFLOW.RetrievalQuery.from_pymongo"><code class="docutils literal notranslate"><span class="pre">RetrievalQuery.from_pymongo()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.data_retrieval.html#module-matminer.data_retrieval.retrieve_Citrine">matminer.data_retrieval.retrieve_Citrine module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_Citrine.CitrineDataRetrieval"><code class="docutils literal notranslate"><span class="pre">CitrineDataRetrieval</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_Citrine.CitrineDataRetrieval.__init__"><code class="docutils literal notranslate"><span class="pre">CitrineDataRetrieval.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_Citrine.CitrineDataRetrieval.api_link"><code class="docutils literal notranslate"><span class="pre">CitrineDataRetrieval.api_link()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_Citrine.CitrineDataRetrieval.citations"><code class="docutils literal notranslate"><span class="pre">CitrineDataRetrieval.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_Citrine.CitrineDataRetrieval.get_data"><code class="docutils literal notranslate"><span class="pre">CitrineDataRetrieval.get_data()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_Citrine.CitrineDataRetrieval.get_dataframe"><code class="docutils literal notranslate"><span class="pre">CitrineDataRetrieval.get_dataframe()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_Citrine.get_value"><code class="docutils literal notranslate"><span class="pre">get_value()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_Citrine.parse_scalars"><code class="docutils literal notranslate"><span class="pre">parse_scalars()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.data_retrieval.html#module-matminer.data_retrieval.retrieve_MDF">matminer.data_retrieval.retrieve_MDF module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MDF.MDFDataRetrieval"><code class="docutils literal notranslate"><span class="pre">MDFDataRetrieval</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MDF.MDFDataRetrieval.__init__"><code class="docutils literal notranslate"><span class="pre">MDFDataRetrieval.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MDF.MDFDataRetrieval.api_link"><code class="docutils literal notranslate"><span class="pre">MDFDataRetrieval.api_link()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MDF.MDFDataRetrieval.citations"><code class="docutils literal notranslate"><span class="pre">MDFDataRetrieval.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MDF.MDFDataRetrieval.get_data"><code class="docutils literal notranslate"><span class="pre">MDFDataRetrieval.get_data()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MDF.MDFDataRetrieval.get_dataframe"><code class="docutils literal notranslate"><span class="pre">MDFDataRetrieval.get_dataframe()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MDF.make_dataframe"><code class="docutils literal notranslate"><span class="pre">make_dataframe()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.data_retrieval.html#module-matminer.data_retrieval.retrieve_MP">matminer.data_retrieval.retrieve_MP module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MP.MPDataRetrieval"><code class="docutils literal notranslate"><span class="pre">MPDataRetrieval</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MP.MPDataRetrieval.__init__"><code class="docutils literal notranslate"><span class="pre">MPDataRetrieval.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MP.MPDataRetrieval.api_link"><code class="docutils literal notranslate"><span class="pre">MPDataRetrieval.api_link()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MP.MPDataRetrieval.citations"><code class="docutils literal notranslate"><span class="pre">MPDataRetrieval.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MP.MPDataRetrieval.get_data"><code class="docutils literal notranslate"><span class="pre">MPDataRetrieval.get_data()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MP.MPDataRetrieval.get_dataframe"><code class="docutils literal notranslate"><span class="pre">MPDataRetrieval.get_dataframe()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MP.MPDataRetrieval.try_get_prop_by_material_id"><code class="docutils literal notranslate"><span class="pre">MPDataRetrieval.try_get_prop_by_material_id()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.data_retrieval.html#module-matminer.data_retrieval.retrieve_MPDS">matminer.data_retrieval.retrieve_MPDS module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MPDS.APIError"><code class="docutils literal notranslate"><span class="pre">APIError</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MPDS.APIError.__init__"><code class="docutils literal notranslate"><span class="pre">APIError.__init__()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MPDS.MPDSDataRetrieval"><code class="docutils literal notranslate"><span class="pre">MPDSDataRetrieval</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MPDS.MPDSDataRetrieval.__init__"><code class="docutils literal notranslate"><span class="pre">MPDSDataRetrieval.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MPDS.MPDSDataRetrieval.api_link"><code class="docutils literal notranslate"><span class="pre">MPDSDataRetrieval.api_link()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MPDS.MPDSDataRetrieval.chillouttime"><code class="docutils literal notranslate"><span class="pre">MPDSDataRetrieval.chillouttime</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MPDS.MPDSDataRetrieval.citations"><code class="docutils literal notranslate"><span class="pre">MPDSDataRetrieval.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MPDS.MPDSDataRetrieval.compile_crystal"><code class="docutils literal notranslate"><span class="pre">MPDSDataRetrieval.compile_crystal()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MPDS.MPDSDataRetrieval.default_properties"><code class="docutils literal notranslate"><span class="pre">MPDSDataRetrieval.default_properties</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MPDS.MPDSDataRetrieval.endpoint"><code class="docutils literal notranslate"><span class="pre">MPDSDataRetrieval.endpoint</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MPDS.MPDSDataRetrieval.get_data"><code class="docutils literal notranslate"><span class="pre">MPDSDataRetrieval.get_data()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MPDS.MPDSDataRetrieval.get_dataframe"><code class="docutils literal notranslate"><span class="pre">MPDSDataRetrieval.get_dataframe()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MPDS.MPDSDataRetrieval.maxnpages"><code class="docutils literal notranslate"><span class="pre">MPDSDataRetrieval.maxnpages</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MPDS.MPDSDataRetrieval.pagesize"><code class="docutils literal notranslate"><span class="pre">MPDSDataRetrieval.pagesize</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.data_retrieval.html#module-matminer.data_retrieval.retrieve_MongoDB">matminer.data_retrieval.retrieve_MongoDB module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MongoDB.MongoDataRetrieval"><code class="docutils literal notranslate"><span class="pre">MongoDataRetrieval</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MongoDB.MongoDataRetrieval.__init__"><code class="docutils literal notranslate"><span class="pre">MongoDataRetrieval.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MongoDB.MongoDataRetrieval.api_link"><code class="docutils literal notranslate"><span class="pre">MongoDataRetrieval.api_link()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MongoDB.MongoDataRetrieval.get_dataframe"><code class="docutils literal notranslate"><span class="pre">MongoDataRetrieval.get_dataframe()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MongoDB.clean_projection"><code class="docutils literal notranslate"><span class="pre">clean_projection()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MongoDB.is_int"><code class="docutils literal notranslate"><span class="pre">is_int()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_MongoDB.remove_ints"><code class="docutils literal notranslate"><span class="pre">remove_ints()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.data_retrieval.html#module-matminer.data_retrieval.retrieve_base">matminer.data_retrieval.retrieve_base module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_base.BaseDataRetrieval"><code class="docutils literal notranslate"><span class="pre">BaseDataRetrieval</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_base.BaseDataRetrieval.api_link"><code class="docutils literal notranslate"><span class="pre">BaseDataRetrieval.api_link()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_base.BaseDataRetrieval.citations"><code class="docutils literal notranslate"><span class="pre">BaseDataRetrieval.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#matminer.data_retrieval.retrieve_base.BaseDataRetrieval.get_dataframe"><code class="docutils literal notranslate"><span class="pre">BaseDataRetrieval.get_dataframe()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.data_retrieval.html#module-matminer.data_retrieval">Module contents</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="matminer.datasets.html">matminer.datasets package</a><ul>
<li class="toctree-l2"><a class="reference internal" href="matminer.datasets.html#subpackages">Subpackages</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.tests.html">matminer.datasets.tests package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#module-matminer.datasets.tests.base">matminer.datasets.tests.base module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#module-matminer.datasets.tests.test_convenience_loaders">matminer.datasets.tests.test_convenience_loaders module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#module-matminer.datasets.tests.test_dataset_retrieval">matminer.datasets.tests.test_dataset_retrieval module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#module-matminer.datasets.tests.test_datasets">matminer.datasets.tests.test_datasets module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#module-matminer.datasets.tests.test_utils">matminer.datasets.tests.test_utils module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#module-matminer.datasets.tests">Module contents</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.datasets.html#submodules">Submodules</a></li>
<li class="toctree-l2"><a class="reference internal" href="matminer.datasets.html#module-matminer.datasets.convenience_loaders">matminer.datasets.convenience_loaders module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.convenience_loaders.load_boltztrap_mp"><code class="docutils literal notranslate"><span class="pre">load_boltztrap_mp()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.convenience_loaders.load_brgoch_superhard_training"><code class="docutils literal notranslate"><span class="pre">load_brgoch_superhard_training()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.convenience_loaders.load_castelli_perovskites"><code class="docutils literal notranslate"><span class="pre">load_castelli_perovskites()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.convenience_loaders.load_citrine_thermal_conductivity"><code class="docutils literal notranslate"><span class="pre">load_citrine_thermal_conductivity()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.convenience_loaders.load_dielectric_constant"><code class="docutils literal notranslate"><span class="pre">load_dielectric_constant()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.convenience_loaders.load_double_perovskites_gap"><code class="docutils literal notranslate"><span class="pre">load_double_perovskites_gap()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.convenience_loaders.load_double_perovskites_gap_lumo"><code class="docutils literal notranslate"><span class="pre">load_double_perovskites_gap_lumo()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.convenience_loaders.load_elastic_tensor"><code class="docutils literal notranslate"><span class="pre">load_elastic_tensor()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.convenience_loaders.load_expt_formation_enthalpy"><code class="docutils literal notranslate"><span class="pre">load_expt_formation_enthalpy()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.convenience_loaders.load_expt_gap"><code class="docutils literal notranslate"><span class="pre">load_expt_gap()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.convenience_loaders.load_flla"><code class="docutils literal notranslate"><span class="pre">load_flla()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.convenience_loaders.load_glass_binary"><code class="docutils literal notranslate"><span class="pre">load_glass_binary()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.convenience_loaders.load_glass_ternary_hipt"><code class="docutils literal notranslate"><span class="pre">load_glass_ternary_hipt()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.convenience_loaders.load_glass_ternary_landolt"><code class="docutils literal notranslate"><span class="pre">load_glass_ternary_landolt()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.convenience_loaders.load_heusler_magnetic"><code class="docutils literal notranslate"><span class="pre">load_heusler_magnetic()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.convenience_loaders.load_jarvis_dft_2d"><code class="docutils literal notranslate"><span class="pre">load_jarvis_dft_2d()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.convenience_loaders.load_jarvis_dft_3d"><code class="docutils literal notranslate"><span class="pre">load_jarvis_dft_3d()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.convenience_loaders.load_jarvis_ml_dft_training"><code class="docutils literal notranslate"><span class="pre">load_jarvis_ml_dft_training()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.convenience_loaders.load_m2ax"><code class="docutils literal notranslate"><span class="pre">load_m2ax()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.convenience_loaders.load_mp"><code class="docutils literal notranslate"><span class="pre">load_mp()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.convenience_loaders.load_phonon_dielectric_mp"><code class="docutils literal notranslate"><span class="pre">load_phonon_dielectric_mp()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.convenience_loaders.load_piezoelectric_tensor"><code class="docutils literal notranslate"><span class="pre">load_piezoelectric_tensor()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.convenience_loaders.load_steel_strength"><code class="docutils literal notranslate"><span class="pre">load_steel_strength()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.convenience_loaders.load_wolverton_oxides"><code class="docutils literal notranslate"><span class="pre">load_wolverton_oxides()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.datasets.html#module-matminer.datasets.dataset_retrieval">matminer.datasets.dataset_retrieval module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.dataset_retrieval.get_all_dataset_info"><code class="docutils literal notranslate"><span class="pre">get_all_dataset_info()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.dataset_retrieval.get_available_datasets"><code class="docutils literal notranslate"><span class="pre">get_available_datasets()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.dataset_retrieval.get_dataset_attribute"><code class="docutils literal notranslate"><span class="pre">get_dataset_attribute()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.dataset_retrieval.get_dataset_citations"><code class="docutils literal notranslate"><span class="pre">get_dataset_citations()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.dataset_retrieval.get_dataset_column_description"><code class="docutils literal notranslate"><span class="pre">get_dataset_column_description()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.dataset_retrieval.get_dataset_columns"><code class="docutils literal notranslate"><span class="pre">get_dataset_columns()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.dataset_retrieval.get_dataset_description"><code class="docutils literal notranslate"><span class="pre">get_dataset_description()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.dataset_retrieval.get_dataset_num_entries"><code class="docutils literal notranslate"><span class="pre">get_dataset_num_entries()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.dataset_retrieval.get_dataset_reference"><code class="docutils literal notranslate"><span class="pre">get_dataset_reference()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html#matminer.datasets.dataset_retrieval.load_dataset"><code class="docutils literal notranslate"><span class="pre">load_dataset()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.datasets.html#module-matminer.datasets.utils">matminer.datasets.utils module</a></li>
<li class="toctree-l2"><a class="reference internal" href="matminer.datasets.html#module-matminer.datasets">Module contents</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="matminer.featurizers.html">matminer.featurizers package</a><ul>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.html#subpackages">Subpackages</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.composition.html">matminer.featurizers.composition package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#subpackages">Subpackages</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#module-matminer.featurizers.composition.alloy">matminer.featurizers.composition.alloy module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#module-matminer.featurizers.composition.composite">matminer.featurizers.composition.composite module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#module-matminer.featurizers.composition.element">matminer.featurizers.composition.element module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#module-matminer.featurizers.composition.ion">matminer.featurizers.composition.ion module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#module-matminer.featurizers.composition.orbital">matminer.featurizers.composition.orbital module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#module-matminer.featurizers.composition.packing">matminer.featurizers.composition.packing module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#module-matminer.featurizers.composition.thermo">matminer.featurizers.composition.thermo module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#module-matminer.featurizers.composition">Module contents</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.site.html">matminer.featurizers.site package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#subpackages">Subpackages</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#module-matminer.featurizers.site.bonding">matminer.featurizers.site.bonding module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#module-matminer.featurizers.site.chemical">matminer.featurizers.site.chemical module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#module-matminer.featurizers.site.external">matminer.featurizers.site.external module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#module-matminer.featurizers.site.fingerprint">matminer.featurizers.site.fingerprint module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#module-matminer.featurizers.site.misc">matminer.featurizers.site.misc module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#module-matminer.featurizers.site.rdf">matminer.featurizers.site.rdf module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#module-matminer.featurizers.site">Module contents</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.html">matminer.featurizers.structure package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#subpackages">Subpackages</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#module-matminer.featurizers.structure.bonding">matminer.featurizers.structure.bonding module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#module-matminer.featurizers.structure.composite">matminer.featurizers.structure.composite module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#module-matminer.featurizers.structure.matrix">matminer.featurizers.structure.matrix module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#module-matminer.featurizers.structure.misc">matminer.featurizers.structure.misc module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#module-matminer.featurizers.structure.order">matminer.featurizers.structure.order module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#module-matminer.featurizers.structure.rdf">matminer.featurizers.structure.rdf module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#module-matminer.featurizers.structure.sites">matminer.featurizers.structure.sites module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#module-matminer.featurizers.structure.symmetry">matminer.featurizers.structure.symmetry module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#module-matminer.featurizers.structure">Module contents</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.tests.html">matminer.featurizers.tests package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#module-matminer.featurizers.tests.test_bandstructure">matminer.featurizers.tests.test_bandstructure module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#module-matminer.featurizers.tests.test_base">matminer.featurizers.tests.test_base module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#module-matminer.featurizers.tests.test_conversions">matminer.featurizers.tests.test_conversions module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#module-matminer.featurizers.tests.test_dos">matminer.featurizers.tests.test_dos module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#module-matminer.featurizers.tests.test_function">matminer.featurizers.tests.test_function module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#module-matminer.featurizers.tests">Module contents</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.utils.html">matminer.featurizers.utils package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#subpackages">Subpackages</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#module-matminer.featurizers.utils.grdf">matminer.featurizers.utils.grdf module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#module-matminer.featurizers.utils.oxidation">matminer.featurizers.utils.oxidation module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#module-matminer.featurizers.utils.stats">matminer.featurizers.utils.stats module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#module-matminer.featurizers.utils">Module contents</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.html#submodules">Submodules</a></li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.html#module-matminer.featurizers.bandstructure">matminer.featurizers.bandstructure module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.bandstructure.BandFeaturizer"><code class="docutils literal notranslate"><span class="pre">BandFeaturizer</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.bandstructure.BandFeaturizer.__init__"><code class="docutils literal notranslate"><span class="pre">BandFeaturizer.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.bandstructure.BandFeaturizer.citations"><code class="docutils literal notranslate"><span class="pre">BandFeaturizer.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.bandstructure.BandFeaturizer.feature_labels"><code class="docutils literal notranslate"><span class="pre">BandFeaturizer.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.bandstructure.BandFeaturizer.featurize"><code class="docutils literal notranslate"><span class="pre">BandFeaturizer.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.bandstructure.BandFeaturizer.get_bindex_bspin"><code class="docutils literal notranslate"><span class="pre">BandFeaturizer.get_bindex_bspin()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.bandstructure.BandFeaturizer.implementors"><code class="docutils literal notranslate"><span class="pre">BandFeaturizer.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.bandstructure.BranchPointEnergy"><code class="docutils literal notranslate"><span class="pre">BranchPointEnergy</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.bandstructure.BranchPointEnergy.__init__"><code class="docutils literal notranslate"><span class="pre">BranchPointEnergy.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.bandstructure.BranchPointEnergy.citations"><code class="docutils literal notranslate"><span class="pre">BranchPointEnergy.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.bandstructure.BranchPointEnergy.feature_labels"><code class="docutils literal notranslate"><span class="pre">BranchPointEnergy.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.bandstructure.BranchPointEnergy.featurize"><code class="docutils literal notranslate"><span class="pre">BranchPointEnergy.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.bandstructure.BranchPointEnergy.implementors"><code class="docutils literal notranslate"><span class="pre">BranchPointEnergy.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.html#module-matminer.featurizers.base">matminer.featurizers.base module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer.chunksize"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.chunksize</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer.citations"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer.feature_labels"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer.featurize"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer.featurize_dataframe"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.featurize_dataframe()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer.featurize_many"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.featurize_many()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer.featurize_wrapper"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.featurize_wrapper()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer.fit"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.fit()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer.fit_featurize_dataframe"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.fit_featurize_dataframe()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer.implementors"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.implementors()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer.n_jobs"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.n_jobs</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer.precheck"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.precheck()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer.precheck_dataframe"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.precheck_dataframe()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer.set_chunksize"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.set_chunksize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer.set_n_jobs"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.set_n_jobs()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer.transform"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.transform()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.MultipleFeaturizer"><code class="docutils literal notranslate"><span class="pre">MultipleFeaturizer</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.MultipleFeaturizer.__init__"><code class="docutils literal notranslate"><span class="pre">MultipleFeaturizer.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.MultipleFeaturizer.citations"><code class="docutils literal notranslate"><span class="pre">MultipleFeaturizer.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.MultipleFeaturizer.feature_labels"><code class="docutils literal notranslate"><span class="pre">MultipleFeaturizer.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.MultipleFeaturizer.featurize"><code class="docutils literal notranslate"><span class="pre">MultipleFeaturizer.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.MultipleFeaturizer.featurize_many"><code class="docutils literal notranslate"><span class="pre">MultipleFeaturizer.featurize_many()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.MultipleFeaturizer.featurize_wrapper"><code class="docutils literal notranslate"><span class="pre">MultipleFeaturizer.featurize_wrapper()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.MultipleFeaturizer.fit"><code class="docutils literal notranslate"><span class="pre">MultipleFeaturizer.fit()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.MultipleFeaturizer.implementors"><code class="docutils literal notranslate"><span class="pre">MultipleFeaturizer.implementors()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.MultipleFeaturizer.set_n_jobs"><code class="docutils literal notranslate"><span class="pre">MultipleFeaturizer.set_n_jobs()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.StackedFeaturizer"><code class="docutils literal notranslate"><span class="pre">StackedFeaturizer</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.StackedFeaturizer.__init__"><code class="docutils literal notranslate"><span class="pre">StackedFeaturizer.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.StackedFeaturizer.citations"><code class="docutils literal notranslate"><span class="pre">StackedFeaturizer.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.StackedFeaturizer.feature_labels"><code class="docutils literal notranslate"><span class="pre">StackedFeaturizer.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.StackedFeaturizer.featurize"><code class="docutils literal notranslate"><span class="pre">StackedFeaturizer.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.StackedFeaturizer.implementors"><code class="docutils literal notranslate"><span class="pre">StackedFeaturizer.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.html#module-matminer.featurizers.conversions">matminer.featurizers.conversions module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.ASEAtomstoStructure"><code class="docutils literal notranslate"><span class="pre">ASEAtomstoStructure</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.ASEAtomstoStructure.__init__"><code class="docutils literal notranslate"><span class="pre">ASEAtomstoStructure.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.ASEAtomstoStructure.featurize"><code class="docutils literal notranslate"><span class="pre">ASEAtomstoStructure.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.ASEAtomstoStructure.implementors"><code class="docutils literal notranslate"><span class="pre">ASEAtomstoStructure.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.CompositionToOxidComposition"><code class="docutils literal notranslate"><span class="pre">CompositionToOxidComposition</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.CompositionToOxidComposition.__init__"><code class="docutils literal notranslate"><span class="pre">CompositionToOxidComposition.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.CompositionToOxidComposition.citations"><code class="docutils literal notranslate"><span class="pre">CompositionToOxidComposition.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.CompositionToOxidComposition.featurize"><code class="docutils literal notranslate"><span class="pre">CompositionToOxidComposition.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.CompositionToOxidComposition.implementors"><code class="docutils literal notranslate"><span class="pre">CompositionToOxidComposition.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.CompositionToStructureFromMP"><code class="docutils literal notranslate"><span class="pre">CompositionToStructureFromMP</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.CompositionToStructureFromMP.__init__"><code class="docutils literal notranslate"><span class="pre">CompositionToStructureFromMP.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.CompositionToStructureFromMP.citations"><code class="docutils literal notranslate"><span class="pre">CompositionToStructureFromMP.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.CompositionToStructureFromMP.featurize"><code class="docutils literal notranslate"><span class="pre">CompositionToStructureFromMP.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.CompositionToStructureFromMP.implementors"><code class="docutils literal notranslate"><span class="pre">CompositionToStructureFromMP.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.ConversionFeaturizer"><code class="docutils literal notranslate"><span class="pre">ConversionFeaturizer</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.ConversionFeaturizer.__init__"><code class="docutils literal notranslate"><span class="pre">ConversionFeaturizer.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.ConversionFeaturizer.citations"><code class="docutils literal notranslate"><span class="pre">ConversionFeaturizer.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.ConversionFeaturizer.feature_labels"><code class="docutils literal notranslate"><span class="pre">ConversionFeaturizer.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.ConversionFeaturizer.featurize"><code class="docutils literal notranslate"><span class="pre">ConversionFeaturizer.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.ConversionFeaturizer.featurize_dataframe"><code class="docutils literal notranslate"><span class="pre">ConversionFeaturizer.featurize_dataframe()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.ConversionFeaturizer.implementors"><code class="docutils literal notranslate"><span class="pre">ConversionFeaturizer.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.DictToObject"><code class="docutils literal notranslate"><span class="pre">DictToObject</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.DictToObject.__init__"><code class="docutils literal notranslate"><span class="pre">DictToObject.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.DictToObject.citations"><code class="docutils literal notranslate"><span class="pre">DictToObject.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.DictToObject.featurize"><code class="docutils literal notranslate"><span class="pre">DictToObject.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.DictToObject.implementors"><code class="docutils literal notranslate"><span class="pre">DictToObject.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.JsonToObject"><code class="docutils literal notranslate"><span class="pre">JsonToObject</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.JsonToObject.__init__"><code class="docutils literal notranslate"><span class="pre">JsonToObject.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.JsonToObject.citations"><code class="docutils literal notranslate"><span class="pre">JsonToObject.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.JsonToObject.featurize"><code class="docutils literal notranslate"><span class="pre">JsonToObject.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.JsonToObject.implementors"><code class="docutils literal notranslate"><span class="pre">JsonToObject.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.PymatgenFunctionApplicator"><code class="docutils literal notranslate"><span class="pre">PymatgenFunctionApplicator</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.PymatgenFunctionApplicator.__init__"><code class="docutils literal notranslate"><span class="pre">PymatgenFunctionApplicator.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.PymatgenFunctionApplicator.featurize"><code class="docutils literal notranslate"><span class="pre">PymatgenFunctionApplicator.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.PymatgenFunctionApplicator.implementors"><code class="docutils literal notranslate"><span class="pre">PymatgenFunctionApplicator.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.StrToComposition"><code class="docutils literal notranslate"><span class="pre">StrToComposition</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.StrToComposition.__init__"><code class="docutils literal notranslate"><span class="pre">StrToComposition.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.StrToComposition.citations"><code class="docutils literal notranslate"><span class="pre">StrToComposition.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.StrToComposition.featurize"><code class="docutils literal notranslate"><span class="pre">StrToComposition.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.StrToComposition.implementors"><code class="docutils literal notranslate"><span class="pre">StrToComposition.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.StructureToComposition"><code class="docutils literal notranslate"><span class="pre">StructureToComposition</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.StructureToComposition.__init__"><code class="docutils literal notranslate"><span class="pre">StructureToComposition.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.StructureToComposition.citations"><code class="docutils literal notranslate"><span class="pre">StructureToComposition.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.StructureToComposition.featurize"><code class="docutils literal notranslate"><span class="pre">StructureToComposition.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.StructureToComposition.implementors"><code class="docutils literal notranslate"><span class="pre">StructureToComposition.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.StructureToIStructure"><code class="docutils literal notranslate"><span class="pre">StructureToIStructure</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.StructureToIStructure.__init__"><code class="docutils literal notranslate"><span class="pre">StructureToIStructure.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.StructureToIStructure.citations"><code class="docutils literal notranslate"><span class="pre">StructureToIStructure.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.StructureToIStructure.featurize"><code class="docutils literal notranslate"><span class="pre">StructureToIStructure.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.StructureToIStructure.implementors"><code class="docutils literal notranslate"><span class="pre">StructureToIStructure.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.StructureToOxidStructure"><code class="docutils literal notranslate"><span class="pre">StructureToOxidStructure</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.StructureToOxidStructure.__init__"><code class="docutils literal notranslate"><span class="pre">StructureToOxidStructure.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.StructureToOxidStructure.citations"><code class="docutils literal notranslate"><span class="pre">StructureToOxidStructure.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.StructureToOxidStructure.featurize"><code class="docutils literal notranslate"><span class="pre">StructureToOxidStructure.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.conversions.StructureToOxidStructure.implementors"><code class="docutils literal notranslate"><span class="pre">StructureToOxidStructure.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.html#module-matminer.featurizers.dos">matminer.featurizers.dos module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.DOSFeaturizer"><code class="docutils literal notranslate"><span class="pre">DOSFeaturizer</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.DOSFeaturizer.__init__"><code class="docutils literal notranslate"><span class="pre">DOSFeaturizer.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.DOSFeaturizer.citations"><code class="docutils literal notranslate"><span class="pre">DOSFeaturizer.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.DOSFeaturizer.feature_labels"><code class="docutils literal notranslate"><span class="pre">DOSFeaturizer.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.DOSFeaturizer.featurize"><code class="docutils literal notranslate"><span class="pre">DOSFeaturizer.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.DOSFeaturizer.implementors"><code class="docutils literal notranslate"><span class="pre">DOSFeaturizer.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.DopingFermi"><code class="docutils literal notranslate"><span class="pre">DopingFermi</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.DopingFermi.__init__"><code class="docutils literal notranslate"><span class="pre">DopingFermi.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.DopingFermi.citations"><code class="docutils literal notranslate"><span class="pre">DopingFermi.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.DopingFermi.feature_labels"><code class="docutils literal notranslate"><span class="pre">DopingFermi.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.DopingFermi.featurize"><code class="docutils literal notranslate"><span class="pre">DopingFermi.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.DopingFermi.implementors"><code class="docutils literal notranslate"><span class="pre">DopingFermi.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.DosAsymmetry"><code class="docutils literal notranslate"><span class="pre">DosAsymmetry</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.DosAsymmetry.__init__"><code class="docutils literal notranslate"><span class="pre">DosAsymmetry.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.DosAsymmetry.citations"><code class="docutils literal notranslate"><span class="pre">DosAsymmetry.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.DosAsymmetry.feature_labels"><code class="docutils literal notranslate"><span class="pre">DosAsymmetry.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.DosAsymmetry.featurize"><code class="docutils literal notranslate"><span class="pre">DosAsymmetry.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.DosAsymmetry.implementors"><code class="docutils literal notranslate"><span class="pre">DosAsymmetry.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.Hybridization"><code class="docutils literal notranslate"><span class="pre">Hybridization</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.Hybridization.__init__"><code class="docutils literal notranslate"><span class="pre">Hybridization.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.Hybridization.citations"><code class="docutils literal notranslate"><span class="pre">Hybridization.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.Hybridization.feature_labels"><code class="docutils literal notranslate"><span class="pre">Hybridization.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.Hybridization.featurize"><code class="docutils literal notranslate"><span class="pre">Hybridization.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.Hybridization.implementors"><code class="docutils literal notranslate"><span class="pre">Hybridization.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.SiteDOS"><code class="docutils literal notranslate"><span class="pre">SiteDOS</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.SiteDOS.__init__"><code class="docutils literal notranslate"><span class="pre">SiteDOS.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.SiteDOS.citations"><code class="docutils literal notranslate"><span class="pre">SiteDOS.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.SiteDOS.feature_labels"><code class="docutils literal notranslate"><span class="pre">SiteDOS.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.SiteDOS.featurize"><code class="docutils literal notranslate"><span class="pre">SiteDOS.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.SiteDOS.implementors"><code class="docutils literal notranslate"><span class="pre">SiteDOS.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.get_cbm_vbm_scores"><code class="docutils literal notranslate"><span class="pre">get_cbm_vbm_scores()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.dos.get_site_dos_scores"><code class="docutils literal notranslate"><span class="pre">get_site_dos_scores()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.html#module-matminer.featurizers.function">matminer.featurizers.function module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.function.FunctionFeaturizer"><code class="docutils literal notranslate"><span class="pre">FunctionFeaturizer</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.function.FunctionFeaturizer.ILLEGAL_CHARACTERS"><code class="docutils literal notranslate"><span class="pre">FunctionFeaturizer.ILLEGAL_CHARACTERS</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.function.FunctionFeaturizer.__init__"><code class="docutils literal notranslate"><span class="pre">FunctionFeaturizer.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.function.FunctionFeaturizer.citations"><code class="docutils literal notranslate"><span class="pre">FunctionFeaturizer.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.function.FunctionFeaturizer.exp_dict"><code class="docutils literal notranslate"><span class="pre">FunctionFeaturizer.exp_dict</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.function.FunctionFeaturizer.feature_labels"><code class="docutils literal notranslate"><span class="pre">FunctionFeaturizer.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.function.FunctionFeaturizer.featurize"><code class="docutils literal notranslate"><span class="pre">FunctionFeaturizer.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.function.FunctionFeaturizer.fit"><code class="docutils literal notranslate"><span class="pre">FunctionFeaturizer.fit()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.function.FunctionFeaturizer.generate_string_expressions"><code class="docutils literal notranslate"><span class="pre">FunctionFeaturizer.generate_string_expressions()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.function.FunctionFeaturizer.implementors"><code class="docutils literal notranslate"><span class="pre">FunctionFeaturizer.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.function.generate_expressions_combinations"><code class="docutils literal notranslate"><span class="pre">generate_expressions_combinations()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.html#module-matminer.featurizers">Module contents</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="matminer.utils.html">matminer.utils package</a><ul>
<li class="toctree-l2"><a class="reference internal" href="matminer.utils.html#subpackages">Subpackages</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.data_files.html">matminer.utils.data_files package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.data_files.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.data_files.html#module-matminer.utils.data_files.deml_elementdata">matminer.utils.data_files.deml_elementdata module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.data_files.html#module-matminer.utils.data_files">Module contents</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.tests.html">matminer.utils.tests package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#module-matminer.utils.tests.test_caching">matminer.utils.tests.test_caching module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#module-matminer.utils.tests.test_data">matminer.utils.tests.test_data module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#module-matminer.utils.tests.test_flatten_dict">matminer.utils.tests.test_flatten_dict module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#module-matminer.utils.tests.test_io">matminer.utils.tests.test_io module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.tests.html#module-matminer.utils.tests">Module contents</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.utils.html#submodules">Submodules</a></li>
<li class="toctree-l2"><a class="reference internal" href="matminer.utils.html#module-matminer.utils.caching">matminer.utils.caching module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.html#matminer.utils.caching.get_all_nearest_neighbors"><code class="docutils literal notranslate"><span class="pre">get_all_nearest_neighbors()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.html#matminer.utils.caching.get_nearest_neighbors"><code class="docutils literal notranslate"><span class="pre">get_nearest_neighbors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.utils.html#module-matminer.utils.data">matminer.utils.data module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.AbstractData"><code class="docutils literal notranslate"><span class="pre">AbstractData</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.AbstractData.get_elemental_properties"><code class="docutils literal notranslate"><span class="pre">AbstractData.get_elemental_properties()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.AbstractData.get_elemental_property"><code class="docutils literal notranslate"><span class="pre">AbstractData.get_elemental_property()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.CohesiveEnergyData"><code class="docutils literal notranslate"><span class="pre">CohesiveEnergyData</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.CohesiveEnergyData.__init__"><code class="docutils literal notranslate"><span class="pre">CohesiveEnergyData.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.CohesiveEnergyData.get_elemental_property"><code class="docutils literal notranslate"><span class="pre">CohesiveEnergyData.get_elemental_property()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.DemlData"><code class="docutils literal notranslate"><span class="pre">DemlData</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.DemlData.__init__"><code class="docutils literal notranslate"><span class="pre">DemlData.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.DemlData.get_charge_dependent_property"><code class="docutils literal notranslate"><span class="pre">DemlData.get_charge_dependent_property()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.DemlData.get_elemental_property"><code class="docutils literal notranslate"><span class="pre">DemlData.get_elemental_property()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.DemlData.get_oxidation_states"><code class="docutils literal notranslate"><span class="pre">DemlData.get_oxidation_states()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.IUCrBondValenceData"><code class="docutils literal notranslate"><span class="pre">IUCrBondValenceData</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.IUCrBondValenceData.__init__"><code class="docutils literal notranslate"><span class="pre">IUCrBondValenceData.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.IUCrBondValenceData.get_bv_params"><code class="docutils literal notranslate"><span class="pre">IUCrBondValenceData.get_bv_params()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.IUCrBondValenceData.interpolate_soft_anions"><code class="docutils literal notranslate"><span class="pre">IUCrBondValenceData.interpolate_soft_anions()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.MEGNetElementData"><code class="docutils literal notranslate"><span class="pre">MEGNetElementData</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.MEGNetElementData.__init__"><code class="docutils literal notranslate"><span class="pre">MEGNetElementData.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.MEGNetElementData.get_elemental_property"><code class="docutils literal notranslate"><span class="pre">MEGNetElementData.get_elemental_property()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.MagpieData"><code class="docutils literal notranslate"><span class="pre">MagpieData</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.MagpieData.__init__"><code class="docutils literal notranslate"><span class="pre">MagpieData.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.MagpieData.get_elemental_property"><code class="docutils literal notranslate"><span class="pre">MagpieData.get_elemental_property()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.MagpieData.get_oxidation_states"><code class="docutils literal notranslate"><span class="pre">MagpieData.get_oxidation_states()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.MatscholarElementData"><code class="docutils literal notranslate"><span class="pre">MatscholarElementData</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.MatscholarElementData.__init__"><code class="docutils literal notranslate"><span class="pre">MatscholarElementData.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.MatscholarElementData.get_elemental_property"><code class="docutils literal notranslate"><span class="pre">MatscholarElementData.get_elemental_property()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.MixingEnthalpy"><code class="docutils literal notranslate"><span class="pre">MixingEnthalpy</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.MixingEnthalpy.__init__"><code class="docutils literal notranslate"><span class="pre">MixingEnthalpy.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.MixingEnthalpy.get_mixing_enthalpy"><code class="docutils literal notranslate"><span class="pre">MixingEnthalpy.get_mixing_enthalpy()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.OxidationStateDependentData"><code class="docutils literal notranslate"><span class="pre">OxidationStateDependentData</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.OxidationStateDependentData.get_charge_dependent_property"><code class="docutils literal notranslate"><span class="pre">OxidationStateDependentData.get_charge_dependent_property()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.OxidationStateDependentData.get_charge_dependent_property_from_specie"><code class="docutils literal notranslate"><span class="pre">OxidationStateDependentData.get_charge_dependent_property_from_specie()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.OxidationStatesMixin"><code class="docutils literal notranslate"><span class="pre">OxidationStatesMixin</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.OxidationStatesMixin.get_oxidation_states"><code class="docutils literal notranslate"><span class="pre">OxidationStatesMixin.get_oxidation_states()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.PymatgenData"><code class="docutils literal notranslate"><span class="pre">PymatgenData</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.PymatgenData.__init__"><code class="docutils literal notranslate"><span class="pre">PymatgenData.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.PymatgenData.get_charge_dependent_property"><code class="docutils literal notranslate"><span class="pre">PymatgenData.get_charge_dependent_property()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.PymatgenData.get_elemental_property"><code class="docutils literal notranslate"><span class="pre">PymatgenData.get_elemental_property()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.data.PymatgenData.get_oxidation_states"><code class="docutils literal notranslate"><span class="pre">PymatgenData.get_oxidation_states()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.utils.html#module-matminer.utils.flatten_dict">matminer.utils.flatten_dict module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.html#matminer.utils.flatten_dict.flatten_dict"><code class="docutils literal notranslate"><span class="pre">flatten_dict()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.utils.html#module-matminer.utils.io">matminer.utils.io module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.html#matminer.utils.io.load_dataframe_from_json"><code class="docutils literal notranslate"><span class="pre">load_dataframe_from_json()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.html#matminer.utils.io.store_dataframe_as_json"><code class="docutils literal notranslate"><span class="pre">store_dataframe_as_json()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.utils.html#module-matminer.utils.kernels">matminer.utils.kernels module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.html#matminer.utils.kernels.gaussian_kernel"><code class="docutils literal notranslate"><span class="pre">gaussian_kernel()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.html#matminer.utils.kernels.laplacian_kernel"><code class="docutils literal notranslate"><span class="pre">laplacian_kernel()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.utils.html#module-matminer.utils.pipeline">matminer.utils.pipeline module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.html#matminer.utils.pipeline.DropExcluded"><code class="docutils literal notranslate"><span class="pre">DropExcluded</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.pipeline.DropExcluded.__init__"><code class="docutils literal notranslate"><span class="pre">DropExcluded.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.pipeline.DropExcluded.fit"><code class="docutils literal notranslate"><span class="pre">DropExcluded.fit()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.pipeline.DropExcluded.transform"><code class="docutils literal notranslate"><span class="pre">DropExcluded.transform()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.html#matminer.utils.pipeline.ItemSelector"><code class="docutils literal notranslate"><span class="pre">ItemSelector</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.pipeline.ItemSelector.__init__"><code class="docutils literal notranslate"><span class="pre">ItemSelector.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.pipeline.ItemSelector.fit"><code class="docutils literal notranslate"><span class="pre">ItemSelector.fit()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#matminer.utils.pipeline.ItemSelector.transform"><code class="docutils literal notranslate"><span class="pre">ItemSelector.transform()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.utils.html#module-matminer.utils.utils">matminer.utils.utils module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.html#matminer.utils.utils.homogenize_multiindex"><code class="docutils literal notranslate"><span class="pre">homogenize_multiindex()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.utils.html#module-matminer.utils">Module contents</a></li>
</ul>
</li>
</ul>
</div>
</section>
<section id="module-matminer">
<span id="module-contents"></span><h2>Module contents<a class="headerlink" href="#module-matminer" title="Permalink to this heading">¶</a></h2>
<p>data mining materials properties</p>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">matminer package</a><ul>
<li><a class="reference internal" href="#subpackages">Subpackages</a></li>
<li><a class="reference internal" href="#module-matminer">Module contents</a></li>
</ul>
</li>
</ul>

  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/matminer.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer package</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2015, Anubhav Jain.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.3.0.
    </div>

  </body>
</html>