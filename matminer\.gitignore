*.py[cod]

# doc builds
docs/_build/*
docs/_build/*/*
docs/_build/*/*/*

# C extensions
*.so

# Packages
*.egg
*.egg-info
dist
build
eggs
.eggs/*
parts
bin
var
sdist
develop-eggs
.installed.cfg
lib
lib64

# Installer logs
pip-log.txt

# Unit test / coverage reports
.coverage
.tox
nosetests.xml

# Translations
*.mo

# Mr Developer
.mr.developer.cfg
.project
.pydevproject

# IPython checkpoints
*-checkpoint.ipynb

# Pycharm
.idea/*

# Dataset generation logs
dataset_prep.log

# Datasets
matminer/datasets/*.json.gz
