
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

    <title>matminer.featurizers.structure.tests package &#8212; matminer 0.9.0 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="_static/nature.css" />
    <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="_static/doctools.js"></script>
    <script src="_static/sphinx_highlight.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
 
<link href='https://fonts.googleapis.com/css?family=Lato:400,700' rel='stylesheet' type='text/css'>

  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer.featurizers.structure.tests package</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="matminer-featurizers-structure-tests-package">
<h1>matminer.featurizers.structure.tests package<a class="headerlink" href="#matminer-featurizers-structure-tests-package" title="Permalink to this heading">¶</a></h1>
<section id="submodules">
<h2>Submodules<a class="headerlink" href="#submodules" title="Permalink to this heading">¶</a></h2>
</section>
<section id="module-matminer.featurizers.structure.tests.base">
<span id="matminer-featurizers-structure-tests-base-module"></span><h2>matminer.featurizers.structure.tests.base module<a class="headerlink" href="#module-matminer.featurizers.structure.tests.base" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.base.StructureFeaturesTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.tests.base.</span></span><span class="sig-name descname"><span class="pre">StructureFeaturesTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.base.StructureFeaturesTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">PymatgenTest</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.base.StructureFeaturesTest.setUp">
<span class="sig-name descname"><span class="pre">setUp</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.base.StructureFeaturesTest.setUp" title="Permalink to this definition">¶</a></dt>
<dd><p>Hook method for setting up the test fixture before exercising it.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.structure.tests.test_bonding">
<span id="matminer-featurizers-structure-tests-test-bonding-module"></span><h2>matminer.featurizers.structure.tests.test_bonding module<a class="headerlink" href="#module-matminer.featurizers.structure.tests.test_bonding" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_bonding.BondingStructureTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.tests.test_bonding.</span></span><span class="sig-name descname"><span class="pre">BondingStructureTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_bonding.BondingStructureTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.structure.tests.base.StructureFeaturesTest" title="matminer.featurizers.structure.tests.base.StructureFeaturesTest"><code class="xref py py-class docutils literal notranslate"><span class="pre">StructureFeaturesTest</span></code></a></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_bonding.BondingStructureTest.test_GlobalInstabilityIndex">
<span class="sig-name descname"><span class="pre">test_GlobalInstabilityIndex</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_bonding.BondingStructureTest.test_GlobalInstabilityIndex" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_bonding.BondingStructureTest.test_bob">
<span class="sig-name descname"><span class="pre">test_bob</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_bonding.BondingStructureTest.test_bob" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_bonding.BondingStructureTest.test_bondfractions">
<span class="sig-name descname"><span class="pre">test_bondfractions</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_bonding.BondingStructureTest.test_bondfractions" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_bonding.BondingStructureTest.test_min_relative_distances">
<span class="sig-name descname"><span class="pre">test_min_relative_distances</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_bonding.BondingStructureTest.test_min_relative_distances" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_bonding.BondingStructureTest.test_ward_prb_2017_strhet">
<span class="sig-name descname"><span class="pre">test_ward_prb_2017_strhet</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_bonding.BondingStructureTest.test_ward_prb_2017_strhet" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.structure.tests.test_composite">
<span id="matminer-featurizers-structure-tests-test-composite-module"></span><h2>matminer.featurizers.structure.tests.test_composite module<a class="headerlink" href="#module-matminer.featurizers.structure.tests.test_composite" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_composite.CompositeStructureFeaturesTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.tests.test_composite.</span></span><span class="sig-name descname"><span class="pre">CompositeStructureFeaturesTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_composite.CompositeStructureFeaturesTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.structure.tests.base.StructureFeaturesTest" title="matminer.featurizers.structure.tests.base.StructureFeaturesTest"><code class="xref py py-class docutils literal notranslate"><span class="pre">StructureFeaturesTest</span></code></a></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_composite.CompositeStructureFeaturesTest.test_jarvisCFID">
<span class="sig-name descname"><span class="pre">test_jarvisCFID</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_composite.CompositeStructureFeaturesTest.test_jarvisCFID" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.structure.tests.test_matrix">
<span id="matminer-featurizers-structure-tests-test-matrix-module"></span><h2>matminer.featurizers.structure.tests.test_matrix module<a class="headerlink" href="#module-matminer.featurizers.structure.tests.test_matrix" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_matrix.MatrixStructureFeaturesTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.tests.test_matrix.</span></span><span class="sig-name descname"><span class="pre">MatrixStructureFeaturesTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_matrix.MatrixStructureFeaturesTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.structure.tests.base.StructureFeaturesTest" title="matminer.featurizers.structure.tests.base.StructureFeaturesTest"><code class="xref py py-class docutils literal notranslate"><span class="pre">StructureFeaturesTest</span></code></a></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_matrix.MatrixStructureFeaturesTest.test_coulomb_matrix">
<span class="sig-name descname"><span class="pre">test_coulomb_matrix</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_matrix.MatrixStructureFeaturesTest.test_coulomb_matrix" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_matrix.MatrixStructureFeaturesTest.test_orbital_field_matrix">
<span class="sig-name descname"><span class="pre">test_orbital_field_matrix</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_matrix.MatrixStructureFeaturesTest.test_orbital_field_matrix" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_matrix.MatrixStructureFeaturesTest.test_sine_coulomb_matrix">
<span class="sig-name descname"><span class="pre">test_sine_coulomb_matrix</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_matrix.MatrixStructureFeaturesTest.test_sine_coulomb_matrix" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.structure.tests.test_misc">
<span id="matminer-featurizers-structure-tests-test-misc-module"></span><h2>matminer.featurizers.structure.tests.test_misc module<a class="headerlink" href="#module-matminer.featurizers.structure.tests.test_misc" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_misc.MiscStructureFeaturesTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.tests.test_misc.</span></span><span class="sig-name descname"><span class="pre">MiscStructureFeaturesTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_misc.MiscStructureFeaturesTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.structure.tests.base.StructureFeaturesTest" title="matminer.featurizers.structure.tests.base.StructureFeaturesTest"><code class="xref py py-class docutils literal notranslate"><span class="pre">StructureFeaturesTest</span></code></a></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_misc.MiscStructureFeaturesTest.test_composition_features">
<span class="sig-name descname"><span class="pre">test_composition_features</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_misc.MiscStructureFeaturesTest.test_composition_features" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_misc.MiscStructureFeaturesTest.test_ewald">
<span class="sig-name descname"><span class="pre">test_ewald</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_misc.MiscStructureFeaturesTest.test_ewald" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_misc.MiscStructureFeaturesTest.test_xrd_powderPattern">
<span class="sig-name descname"><span class="pre">test_xrd_powderPattern</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_misc.MiscStructureFeaturesTest.test_xrd_powderPattern" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.structure.tests.test_order">
<span id="matminer-featurizers-structure-tests-test-order-module"></span><h2>matminer.featurizers.structure.tests.test_order module<a class="headerlink" href="#module-matminer.featurizers.structure.tests.test_order" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_order.OrderStructureFeaturesTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.tests.test_order.</span></span><span class="sig-name descname"><span class="pre">OrderStructureFeaturesTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_order.OrderStructureFeaturesTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.structure.tests.base.StructureFeaturesTest" title="matminer.featurizers.structure.tests.base.StructureFeaturesTest"><code class="xref py py-class docutils literal notranslate"><span class="pre">StructureFeaturesTest</span></code></a></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_order.OrderStructureFeaturesTest.test_density_features">
<span class="sig-name descname"><span class="pre">test_density_features</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_order.OrderStructureFeaturesTest.test_density_features" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_order.OrderStructureFeaturesTest.test_ordering_param">
<span class="sig-name descname"><span class="pre">test_ordering_param</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_order.OrderStructureFeaturesTest.test_ordering_param" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_order.OrderStructureFeaturesTest.test_packing_efficiency">
<span class="sig-name descname"><span class="pre">test_packing_efficiency</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_order.OrderStructureFeaturesTest.test_packing_efficiency" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_order.OrderStructureFeaturesTest.test_structural_complexity">
<span class="sig-name descname"><span class="pre">test_structural_complexity</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_order.OrderStructureFeaturesTest.test_structural_complexity" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.structure.tests.test_rdf">
<span id="matminer-featurizers-structure-tests-test-rdf-module"></span><h2>matminer.featurizers.structure.tests.test_rdf module<a class="headerlink" href="#module-matminer.featurizers.structure.tests.test_rdf" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_rdf.StructureRDFTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.tests.test_rdf.</span></span><span class="sig-name descname"><span class="pre">StructureRDFTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_rdf.StructureRDFTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.structure.tests.base.StructureFeaturesTest" title="matminer.featurizers.structure.tests.base.StructureFeaturesTest"><code class="xref py py-class docutils literal notranslate"><span class="pre">StructureFeaturesTest</span></code></a></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_rdf.StructureRDFTest.test_get_rdf_bin_labels">
<span class="sig-name descname"><span class="pre">test_get_rdf_bin_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_rdf.StructureRDFTest.test_get_rdf_bin_labels" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_rdf.StructureRDFTest.test_prdf">
<span class="sig-name descname"><span class="pre">test_prdf</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_rdf.StructureRDFTest.test_prdf" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_rdf.StructureRDFTest.test_rdf_and_peaks">
<span class="sig-name descname"><span class="pre">test_rdf_and_peaks</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_rdf.StructureRDFTest.test_rdf_and_peaks" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_rdf.StructureRDFTest.test_redf">
<span class="sig-name descname"><span class="pre">test_redf</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_rdf.StructureRDFTest.test_redf" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.structure.tests.test_sites">
<span id="matminer-featurizers-structure-tests-test-sites-module"></span><h2>matminer.featurizers.structure.tests.test_sites module<a class="headerlink" href="#module-matminer.featurizers.structure.tests.test_sites" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_sites.PartialStructureSitesFeaturesTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.tests.test_sites.</span></span><span class="sig-name descname"><span class="pre">PartialStructureSitesFeaturesTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_sites.PartialStructureSitesFeaturesTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.structure.tests.base.StructureFeaturesTest" title="matminer.featurizers.structure.tests.base.StructureFeaturesTest"><code class="xref py py-class docutils literal notranslate"><span class="pre">StructureFeaturesTest</span></code></a></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_sites.PartialStructureSitesFeaturesTest.test_partialsitestatsfingerprint">
<span class="sig-name descname"><span class="pre">test_partialsitestatsfingerprint</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_sites.PartialStructureSitesFeaturesTest.test_partialsitestatsfingerprint" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_sites.PartialStructureSitesFeaturesTest.test_ward_prb_2017_efftcn">
<span class="sig-name descname"><span class="pre">test_ward_prb_2017_efftcn</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_sites.PartialStructureSitesFeaturesTest.test_ward_prb_2017_efftcn" title="Permalink to this definition">¶</a></dt>
<dd><p>Test the effective coordination number attributes of Ward 2017</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_sites.PartialStructureSitesFeaturesTest.test_ward_prb_2017_lpd">
<span class="sig-name descname"><span class="pre">test_ward_prb_2017_lpd</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_sites.PartialStructureSitesFeaturesTest.test_ward_prb_2017_lpd" title="Permalink to this definition">¶</a></dt>
<dd><p>Test the local property difference attributes from Ward 2017</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_sites.StructureSitesFeaturesTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.tests.test_sites.</span></span><span class="sig-name descname"><span class="pre">StructureSitesFeaturesTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_sites.StructureSitesFeaturesTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.structure.tests.base.StructureFeaturesTest" title="matminer.featurizers.structure.tests.base.StructureFeaturesTest"><code class="xref py py-class docutils literal notranslate"><span class="pre">StructureFeaturesTest</span></code></a></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_sites.StructureSitesFeaturesTest.test_sitestatsfingerprint">
<span class="sig-name descname"><span class="pre">test_sitestatsfingerprint</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_sites.StructureSitesFeaturesTest.test_sitestatsfingerprint" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_sites.StructureSitesFeaturesTest.test_ward_prb_2017_efftcn">
<span class="sig-name descname"><span class="pre">test_ward_prb_2017_efftcn</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_sites.StructureSitesFeaturesTest.test_ward_prb_2017_efftcn" title="Permalink to this definition">¶</a></dt>
<dd><p>Test the effective coordination number attributes of Ward 2017</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_sites.StructureSitesFeaturesTest.test_ward_prb_2017_lpd">
<span class="sig-name descname"><span class="pre">test_ward_prb_2017_lpd</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_sites.StructureSitesFeaturesTest.test_ward_prb_2017_lpd" title="Permalink to this definition">¶</a></dt>
<dd><p>Test the local property difference attributes from Ward 2017</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.structure.tests.test_symmetry">
<span id="matminer-featurizers-structure-tests-test-symmetry-module"></span><h2>matminer.featurizers.structure.tests.test_symmetry module<a class="headerlink" href="#module-matminer.featurizers.structure.tests.test_symmetry" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_symmetry.StructureSymmetryFeaturesTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.tests.test_symmetry.</span></span><span class="sig-name descname"><span class="pre">StructureSymmetryFeaturesTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_symmetry.StructureSymmetryFeaturesTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.structure.tests.base.StructureFeaturesTest" title="matminer.featurizers.structure.tests.base.StructureFeaturesTest"><code class="xref py py-class docutils literal notranslate"><span class="pre">StructureFeaturesTest</span></code></a></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_symmetry.StructureSymmetryFeaturesTest.test_dimensionality">
<span class="sig-name descname"><span class="pre">test_dimensionality</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_symmetry.StructureSymmetryFeaturesTest.test_dimensionality" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.tests.test_symmetry.StructureSymmetryFeaturesTest.test_global_symmetry">
<span class="sig-name descname"><span class="pre">test_global_symmetry</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.tests.test_symmetry.StructureSymmetryFeaturesTest.test_global_symmetry" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.structure.tests">
<span id="module-contents"></span><h2>Module contents<a class="headerlink" href="#module-matminer.featurizers.structure.tests" title="Permalink to this heading">¶</a></h2>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">matminer.featurizers.structure.tests package</a><ul>
<li><a class="reference internal" href="#submodules">Submodules</a></li>
<li><a class="reference internal" href="#module-matminer.featurizers.structure.tests.base">matminer.featurizers.structure.tests.base module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.base.StructureFeaturesTest"><code class="docutils literal notranslate"><span class="pre">StructureFeaturesTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.base.StructureFeaturesTest.setUp"><code class="docutils literal notranslate"><span class="pre">StructureFeaturesTest.setUp()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.structure.tests.test_bonding">matminer.featurizers.structure.tests.test_bonding module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_bonding.BondingStructureTest"><code class="docutils literal notranslate"><span class="pre">BondingStructureTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_bonding.BondingStructureTest.test_GlobalInstabilityIndex"><code class="docutils literal notranslate"><span class="pre">BondingStructureTest.test_GlobalInstabilityIndex()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_bonding.BondingStructureTest.test_bob"><code class="docutils literal notranslate"><span class="pre">BondingStructureTest.test_bob()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_bonding.BondingStructureTest.test_bondfractions"><code class="docutils literal notranslate"><span class="pre">BondingStructureTest.test_bondfractions()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_bonding.BondingStructureTest.test_min_relative_distances"><code class="docutils literal notranslate"><span class="pre">BondingStructureTest.test_min_relative_distances()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_bonding.BondingStructureTest.test_ward_prb_2017_strhet"><code class="docutils literal notranslate"><span class="pre">BondingStructureTest.test_ward_prb_2017_strhet()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.structure.tests.test_composite">matminer.featurizers.structure.tests.test_composite module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_composite.CompositeStructureFeaturesTest"><code class="docutils literal notranslate"><span class="pre">CompositeStructureFeaturesTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_composite.CompositeStructureFeaturesTest.test_jarvisCFID"><code class="docutils literal notranslate"><span class="pre">CompositeStructureFeaturesTest.test_jarvisCFID()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.structure.tests.test_matrix">matminer.featurizers.structure.tests.test_matrix module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_matrix.MatrixStructureFeaturesTest"><code class="docutils literal notranslate"><span class="pre">MatrixStructureFeaturesTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_matrix.MatrixStructureFeaturesTest.test_coulomb_matrix"><code class="docutils literal notranslate"><span class="pre">MatrixStructureFeaturesTest.test_coulomb_matrix()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_matrix.MatrixStructureFeaturesTest.test_orbital_field_matrix"><code class="docutils literal notranslate"><span class="pre">MatrixStructureFeaturesTest.test_orbital_field_matrix()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_matrix.MatrixStructureFeaturesTest.test_sine_coulomb_matrix"><code class="docutils literal notranslate"><span class="pre">MatrixStructureFeaturesTest.test_sine_coulomb_matrix()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.structure.tests.test_misc">matminer.featurizers.structure.tests.test_misc module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_misc.MiscStructureFeaturesTest"><code class="docutils literal notranslate"><span class="pre">MiscStructureFeaturesTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_misc.MiscStructureFeaturesTest.test_composition_features"><code class="docutils literal notranslate"><span class="pre">MiscStructureFeaturesTest.test_composition_features()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_misc.MiscStructureFeaturesTest.test_ewald"><code class="docutils literal notranslate"><span class="pre">MiscStructureFeaturesTest.test_ewald()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_misc.MiscStructureFeaturesTest.test_xrd_powderPattern"><code class="docutils literal notranslate"><span class="pre">MiscStructureFeaturesTest.test_xrd_powderPattern()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.structure.tests.test_order">matminer.featurizers.structure.tests.test_order module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_order.OrderStructureFeaturesTest"><code class="docutils literal notranslate"><span class="pre">OrderStructureFeaturesTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_order.OrderStructureFeaturesTest.test_density_features"><code class="docutils literal notranslate"><span class="pre">OrderStructureFeaturesTest.test_density_features()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_order.OrderStructureFeaturesTest.test_ordering_param"><code class="docutils literal notranslate"><span class="pre">OrderStructureFeaturesTest.test_ordering_param()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_order.OrderStructureFeaturesTest.test_packing_efficiency"><code class="docutils literal notranslate"><span class="pre">OrderStructureFeaturesTest.test_packing_efficiency()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_order.OrderStructureFeaturesTest.test_structural_complexity"><code class="docutils literal notranslate"><span class="pre">OrderStructureFeaturesTest.test_structural_complexity()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.structure.tests.test_rdf">matminer.featurizers.structure.tests.test_rdf module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_rdf.StructureRDFTest"><code class="docutils literal notranslate"><span class="pre">StructureRDFTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_rdf.StructureRDFTest.test_get_rdf_bin_labels"><code class="docutils literal notranslate"><span class="pre">StructureRDFTest.test_get_rdf_bin_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_rdf.StructureRDFTest.test_prdf"><code class="docutils literal notranslate"><span class="pre">StructureRDFTest.test_prdf()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_rdf.StructureRDFTest.test_rdf_and_peaks"><code class="docutils literal notranslate"><span class="pre">StructureRDFTest.test_rdf_and_peaks()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_rdf.StructureRDFTest.test_redf"><code class="docutils literal notranslate"><span class="pre">StructureRDFTest.test_redf()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.structure.tests.test_sites">matminer.featurizers.structure.tests.test_sites module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_sites.PartialStructureSitesFeaturesTest"><code class="docutils literal notranslate"><span class="pre">PartialStructureSitesFeaturesTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_sites.PartialStructureSitesFeaturesTest.test_partialsitestatsfingerprint"><code class="docutils literal notranslate"><span class="pre">PartialStructureSitesFeaturesTest.test_partialsitestatsfingerprint()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_sites.PartialStructureSitesFeaturesTest.test_ward_prb_2017_efftcn"><code class="docutils literal notranslate"><span class="pre">PartialStructureSitesFeaturesTest.test_ward_prb_2017_efftcn()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_sites.PartialStructureSitesFeaturesTest.test_ward_prb_2017_lpd"><code class="docutils literal notranslate"><span class="pre">PartialStructureSitesFeaturesTest.test_ward_prb_2017_lpd()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_sites.StructureSitesFeaturesTest"><code class="docutils literal notranslate"><span class="pre">StructureSitesFeaturesTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_sites.StructureSitesFeaturesTest.test_sitestatsfingerprint"><code class="docutils literal notranslate"><span class="pre">StructureSitesFeaturesTest.test_sitestatsfingerprint()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_sites.StructureSitesFeaturesTest.test_ward_prb_2017_efftcn"><code class="docutils literal notranslate"><span class="pre">StructureSitesFeaturesTest.test_ward_prb_2017_efftcn()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_sites.StructureSitesFeaturesTest.test_ward_prb_2017_lpd"><code class="docutils literal notranslate"><span class="pre">StructureSitesFeaturesTest.test_ward_prb_2017_lpd()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.structure.tests.test_symmetry">matminer.featurizers.structure.tests.test_symmetry module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_symmetry.StructureSymmetryFeaturesTest"><code class="docutils literal notranslate"><span class="pre">StructureSymmetryFeaturesTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_symmetry.StructureSymmetryFeaturesTest.test_dimensionality"><code class="docutils literal notranslate"><span class="pre">StructureSymmetryFeaturesTest.test_dimensionality()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.tests.test_symmetry.StructureSymmetryFeaturesTest.test_global_symmetry"><code class="docutils literal notranslate"><span class="pre">StructureSymmetryFeaturesTest.test_global_symmetry()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.structure.tests">Module contents</a></li>
</ul>
</li>
</ul>

  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/matminer.featurizers.structure.tests.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer.featurizers.structure.tests package</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2015, Anubhav Jain.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.3.0.
    </div>

  </body>
</html>