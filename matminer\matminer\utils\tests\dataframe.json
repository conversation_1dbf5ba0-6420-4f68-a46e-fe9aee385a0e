{"index": [0], "columns": ["structure"], "data": [[{"@module": "pymatgen.core.structure", "@class": "Structure", "charge": null, "lattice": {"matrix": [[2.189, 0.0, 1.264], [0.73, 2.064, 1.264], [0.0, 0.0, 2.528]], "a": 2.527729613704757, "b": 2.527981803731981, "c": 2.528, "alpha": 59.999761893688444, "beta": 59.996461462463536, "gamma": 59.99338631300925, "volume": 11.421746688}, "sites": [{"species": [{"element": "C", "oxidation_state": 0.0, "occu": 1}], "abc": [0.8749428962996801, 0.875, 0.8746329822299068], "xyz": [2.554, 1.806, 4.423], "label": "C0+", "properties": {}}, {"species": [{"element": "C", "oxidation_state": 0.0, "occu": 1}], "abc": [0.12505710370031978, 0.125, 0.12497144814984013], "xyz": [0.365, 0.258, 0.632], "label": "C0+", "properties": {}}], "@version": "2019.10.2"}]]}