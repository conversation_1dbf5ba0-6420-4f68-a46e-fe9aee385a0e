
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

    <title>matminer.featurizers.utils package &#8212; matminer 0.9.0 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="_static/nature.css" />
    <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="_static/doctools.js"></script>
    <script src="_static/sphinx_highlight.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
 
<link href='https://fonts.googleapis.com/css?family=Lato:400,700' rel='stylesheet' type='text/css'>

  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer.featurizers.utils package</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="matminer-featurizers-utils-package">
<h1>matminer.featurizers.utils package<a class="headerlink" href="#matminer-featurizers-utils-package" title="Permalink to this heading">¶</a></h1>
<section id="subpackages">
<h2>Subpackages<a class="headerlink" href="#subpackages" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="matminer.featurizers.utils.tests.html">matminer.featurizers.utils.tests package</a><ul>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.utils.tests.html#submodules">Submodules</a></li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.utils.tests.html#module-matminer.featurizers.utils.tests.test_grdf">matminer.featurizers.utils.tests.test_grdf module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.utils.tests.html#matminer.featurizers.utils.tests.test_grdf.GRDFTests"><code class="docutils literal notranslate"><span class="pre">GRDFTests</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.tests.html#matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_bessel"><code class="docutils literal notranslate"><span class="pre">GRDFTests.test_bessel()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.tests.html#matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_cosine"><code class="docutils literal notranslate"><span class="pre">GRDFTests.test_cosine()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.tests.html#matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_gaussian"><code class="docutils literal notranslate"><span class="pre">GRDFTests.test_gaussian()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.tests.html#matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_histogram"><code class="docutils literal notranslate"><span class="pre">GRDFTests.test_histogram()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.tests.html#matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_load_class"><code class="docutils literal notranslate"><span class="pre">GRDFTests.test_load_class()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.tests.html#matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_sin"><code class="docutils literal notranslate"><span class="pre">GRDFTests.test_sin()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.utils.tests.html#module-matminer.featurizers.utils.tests.test_oxidation">matminer.featurizers.utils.tests.test_oxidation module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.utils.tests.html#matminer.featurizers.utils.tests.test_oxidation.OxidationTest"><code class="docutils literal notranslate"><span class="pre">OxidationTest</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.tests.html#matminer.featurizers.utils.tests.test_oxidation.OxidationTest.test_has_oxidation_states"><code class="docutils literal notranslate"><span class="pre">OxidationTest.test_has_oxidation_states()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.utils.tests.html#module-matminer.featurizers.utils.tests.test_stats">matminer.featurizers.utils.tests.test_stats module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.utils.tests.html#matminer.featurizers.utils.tests.test_stats.TestPropertyStats"><code class="docutils literal notranslate"><span class="pre">TestPropertyStats</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.tests.html#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.setUp"><code class="docutils literal notranslate"><span class="pre">TestPropertyStats.setUp()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.tests.html#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_avg_dev"><code class="docutils literal notranslate"><span class="pre">TestPropertyStats.test_avg_dev()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.tests.html#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_geom_std_dev"><code class="docutils literal notranslate"><span class="pre">TestPropertyStats.test_geom_std_dev()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.tests.html#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_holder_mean"><code class="docutils literal notranslate"><span class="pre">TestPropertyStats.test_holder_mean()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.tests.html#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_kurtosis"><code class="docutils literal notranslate"><span class="pre">TestPropertyStats.test_kurtosis()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.tests.html#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_maximum"><code class="docutils literal notranslate"><span class="pre">TestPropertyStats.test_maximum()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.tests.html#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_mean"><code class="docutils literal notranslate"><span class="pre">TestPropertyStats.test_mean()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.tests.html#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_minimum"><code class="docutils literal notranslate"><span class="pre">TestPropertyStats.test_minimum()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.tests.html#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_mode"><code class="docutils literal notranslate"><span class="pre">TestPropertyStats.test_mode()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.tests.html#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_quantile"><code class="docutils literal notranslate"><span class="pre">TestPropertyStats.test_quantile()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.tests.html#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_range"><code class="docutils literal notranslate"><span class="pre">TestPropertyStats.test_range()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.tests.html#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_skewness"><code class="docutils literal notranslate"><span class="pre">TestPropertyStats.test_skewness()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.tests.html#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_std_dev"><code class="docutils literal notranslate"><span class="pre">TestPropertyStats.test_std_dev()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.utils.tests.html#module-matminer.featurizers.utils.tests">Module contents</a></li>
</ul>
</li>
</ul>
</div>
</section>
<section id="submodules">
<h2>Submodules<a class="headerlink" href="#submodules" title="Permalink to this heading">¶</a></h2>
</section>
<section id="module-matminer.featurizers.utils.grdf">
<span id="matminer-featurizers-utils-grdf-module"></span><h2>matminer.featurizers.utils.grdf module<a class="headerlink" href="#module-matminer.featurizers.utils.grdf" title="Permalink to this heading">¶</a></h2>
<p>Functions designed to work with General Radial Distribution Function</p>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.utils.grdf.AbstractPairwise">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.utils.grdf.</span></span><span class="sig-name descname"><span class="pre">AbstractPairwise</span></span><a class="headerlink" href="#matminer.featurizers.utils.grdf.AbstractPairwise" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference external" href="https://docs.python.org/3/library/functions.html#object" title="(in Python v3.11)"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></p>
<p>Abstract class for pairwise functions used in Generalized Radial Distribution Function</p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.grdf.AbstractPairwise.name">
<span class="sig-name descname"><span class="pre">name</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.grdf.AbstractPairwise.name" title="Permalink to this definition">¶</a></dt>
<dd><p>Make a label for this pairwise function</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>(string) Label for the function</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.grdf.AbstractPairwise.volume">
<span class="sig-name descname"><span class="pre">volume</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cutoff</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.grdf.AbstractPairwise.volume" title="Permalink to this definition">¶</a></dt>
<dd><p>Compute the volume of this pairwise function</p>
<dl class="simple">
<dt>Args:</dt><dd><p>cutoff (float): Cutoff distance for radial distribution function</p>
</dd>
<dt>Returns:</dt><dd><p>(float): Volume of bin</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.utils.grdf.Bessel">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.utils.grdf.</span></span><span class="sig-name descname"><span class="pre">Bessel</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.grdf.Bessel" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.utils.grdf.AbstractPairwise" title="matminer.featurizers.utils.grdf.AbstractPairwise"><code class="xref py py-class docutils literal notranslate"><span class="pre">AbstractPairwise</span></code></a></p>
<p>Bessel pairwise function</p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.grdf.Bessel.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.grdf.Bessel.__init__" title="Permalink to this definition">¶</a></dt>
<dd><p>Initialize the function</p>
<dl class="simple">
<dt>Args:</dt><dd><p>n (int): Degree of Bessel function</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.utils.grdf.Cosine">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.utils.grdf.</span></span><span class="sig-name descname"><span class="pre">Cosine</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">a</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.grdf.Cosine" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.utils.grdf.AbstractPairwise" title="matminer.featurizers.utils.grdf.AbstractPairwise"><code class="xref py py-class docutils literal notranslate"><span class="pre">AbstractPairwise</span></code></a></p>
<p>Cosine pairwise function: <span class="math">cos(ar)</span></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.grdf.Cosine.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">a</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.grdf.Cosine.__init__" title="Permalink to this definition">¶</a></dt>
<dd><p>Initialize the function</p>
<dl class="simple">
<dt>Args:</dt><dd><p>a (float): Frequency factor for cosine function</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.grdf.Cosine.volume">
<span class="sig-name descname"><span class="pre">volume</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cutoff</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.grdf.Cosine.volume" title="Permalink to this definition">¶</a></dt>
<dd><p>Compute the volume of this pairwise function</p>
<dl class="simple">
<dt>Args:</dt><dd><p>cutoff (float): Cutoff distance for radial distribution function</p>
</dd>
<dt>Returns:</dt><dd><p>(float): Volume of bin</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.utils.grdf.Gaussian">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.utils.grdf.</span></span><span class="sig-name descname"><span class="pre">Gaussian</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">width</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">center</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.grdf.Gaussian" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.utils.grdf.AbstractPairwise" title="matminer.featurizers.utils.grdf.AbstractPairwise"><code class="xref py py-class docutils literal notranslate"><span class="pre">AbstractPairwise</span></code></a></p>
<p>Gaussian function, with specified width and center</p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.grdf.Gaussian.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">width</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">center</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.grdf.Gaussian.__init__" title="Permalink to this definition">¶</a></dt>
<dd><p>Initialize the gaussian function</p>
<dl class="simple">
<dt>Args:</dt><dd><p>width (float): Width of the gaussian
center (float): Center of the gaussian</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.grdf.Gaussian.volume">
<span class="sig-name descname"><span class="pre">volume</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cutoff</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.grdf.Gaussian.volume" title="Permalink to this definition">¶</a></dt>
<dd><p>Compute the volume of this pairwise function</p>
<dl class="simple">
<dt>Args:</dt><dd><p>cutoff (float): Cutoff distance for radial distribution function</p>
</dd>
<dt>Returns:</dt><dd><p>(float): Volume of bin</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.utils.grdf.Histogram">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.utils.grdf.</span></span><span class="sig-name descname"><span class="pre">Histogram</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">start</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">width</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.grdf.Histogram" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.utils.grdf.AbstractPairwise" title="matminer.featurizers.utils.grdf.AbstractPairwise"><code class="xref py py-class docutils literal notranslate"><span class="pre">AbstractPairwise</span></code></a></p>
<p>Rectangular window function, used in conventional Radial Distribution Functions</p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.grdf.Histogram.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">start</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">width</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.grdf.Histogram.__init__" title="Permalink to this definition">¶</a></dt>
<dd><p>Initialize the window function</p>
<dl class="simple">
<dt>Args:</dt><dd><p>start (float): Beginning of window
width (float): Size of window</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.grdf.Histogram.volume">
<span class="sig-name descname"><span class="pre">volume</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cutoff</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.grdf.Histogram.volume" title="Permalink to this definition">¶</a></dt>
<dd><p>Compute the volume of this pairwise function</p>
<dl class="simple">
<dt>Args:</dt><dd><p>cutoff (float): Cutoff distance for radial distribution function</p>
</dd>
<dt>Returns:</dt><dd><p>(float): Volume of bin</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.utils.grdf.Sine">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.utils.grdf.</span></span><span class="sig-name descname"><span class="pre">Sine</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">a</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.grdf.Sine" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.utils.grdf.AbstractPairwise" title="matminer.featurizers.utils.grdf.AbstractPairwise"><code class="xref py py-class docutils literal notranslate"><span class="pre">AbstractPairwise</span></code></a></p>
<p>Sine pairwise function: <span class="math">sin(ar)</span></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.grdf.Sine.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">a</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.grdf.Sine.__init__" title="Permalink to this definition">¶</a></dt>
<dd><p>Initialize the function</p>
<dl class="simple">
<dt>Args:</dt><dd><p>a (float): Frequency factor for sine function</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.grdf.Sine.volume">
<span class="sig-name descname"><span class="pre">volume</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cutoff</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.grdf.Sine.volume" title="Permalink to this definition">¶</a></dt>
<dd><p>Compute the volume of this pairwise function</p>
<dl class="simple">
<dt>Args:</dt><dd><p>cutoff (float): Cutoff distance for radial distribution function</p>
</dd>
<dt>Returns:</dt><dd><p>(float): Volume of bin</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.featurizers.utils.grdf.initialize_pairwise_function">
<span class="sig-prename descclassname"><span class="pre">matminer.featurizers.utils.grdf.</span></span><span class="sig-name descname"><span class="pre">initialize_pairwise_function</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">options</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.grdf.initialize_pairwise_function" title="Permalink to this definition">¶</a></dt>
<dd><p>Create a new pairwise function object</p>
<dl class="simple">
<dt>Args:</dt><dd><p>name (string): Name of class to instantiate</p>
</dd>
<dt>Keyword Arguments:</dt><dd><p>Any options for the pairwise class (see each pairwise function for details)</p>
</dd>
</dl>
</dd></dl>

</section>
<section id="module-matminer.featurizers.utils.oxidation">
<span id="matminer-featurizers-utils-oxidation-module"></span><h2>matminer.featurizers.utils.oxidation module<a class="headerlink" href="#module-matminer.featurizers.utils.oxidation" title="Permalink to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="matminer.featurizers.utils.oxidation.has_oxidation_states">
<span class="sig-prename descclassname"><span class="pre">matminer.featurizers.utils.oxidation.</span></span><span class="sig-name descname"><span class="pre">has_oxidation_states</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">comp</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.oxidation.has_oxidation_states" title="Permalink to this definition">¶</a></dt>
<dd><p>Check if a composition object has oxidation states for each element</p>
<dl class="simple">
<dt>Args:</dt><dd><p>comp (Composition): Composition to check</p>
</dd>
<dt>Returns:</dt><dd><p>(boolean) Whether this composition object contains oxidation states</p>
</dd>
</dl>
</dd></dl>

</section>
<section id="module-matminer.featurizers.utils.stats">
<span id="matminer-featurizers-utils-stats-module"></span><h2>matminer.featurizers.utils.stats module<a class="headerlink" href="#module-matminer.featurizers.utils.stats" title="Permalink to this heading">¶</a></h2>
<p>General methods for computing property statistics from a list of values</p>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.utils.stats.PropertyStats">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.utils.stats.</span></span><span class="sig-name descname"><span class="pre">PropertyStats</span></span><a class="headerlink" href="#matminer.featurizers.utils.stats.PropertyStats" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference external" href="https://docs.python.org/3/library/functions.html#object" title="(in Python v3.11)"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></p>
<p>This class contains statistical operations that are commonly employed
when computing features.</p>
<p>The primary way for interacting with this class is to call the
<code class="docutils literal notranslate"><span class="pre">calc_stat</span></code> function, which takes the name of the statistic you would
like to compute and the weights/values of data to be assessed. For example,
computing the mean of a list looks like:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">x</span> <span class="o">=</span> <span class="p">[</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">]</span>
<span class="n">PropertyStats</span><span class="o">.</span><span class="n">calc_stat</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="s1">&#39;mean&#39;</span><span class="p">)</span> <span class="c1"># Result is 2</span>
<span class="n">PropertyStats</span><span class="o">.</span><span class="n">calc_stat</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="s1">&#39;mean&#39;</span><span class="p">,</span> <span class="n">weights</span><span class="o">=</span><span class="p">[</span><span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">])</span> <span class="c1"># Result is 3</span>
</pre></div>
</div>
<p>Some of the statistics functions take options (e.g., Holder means). You can
pass them to the statistics functions by adding them after the name and
two colons. For example, the 0th Holder mean would be:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">PropertyStats</span><span class="o">.</span><span class="n">calc_stat</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="s1">&#39;holder_mean::0&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p>You can, of course, call the statistical functions directly. All take at
least two arguments.  The first is the data being assessed and the second,
optional, argument is the weights.</p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.stats.PropertyStats.avg_dev">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">avg_dev</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data_lst</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">weights</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.stats.PropertyStats.avg_dev" title="Permalink to this definition">¶</a></dt>
<dd><p>Mean absolute deviation of list of element data.</p>
<p>This is computed by first calculating the mean of the list,
and then computing the average absolute difference between each value
and the mean.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>data_lst (list of floats): List of values to be assessed
weights (list of floats): Weights for each value</p>
</dd>
<dt>Returns:</dt><dd><p>mean absolute deviation</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.stats.PropertyStats.calc_stat">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">calc_stat</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data_lst</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stat</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">weights</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.stats.PropertyStats.calc_stat" title="Permalink to this definition">¶</a></dt>
<dd><p>Compute a property statistic</p>
<dl>
<dt>Args:</dt><dd><p>data_lst (list of floats): list of values
stat (str) - Name of property to be compute. If there are arguments to the statistics function, these</p>
<blockquote>
<div><p>should be added after the name and separated by two colons. For example, the 2nd Holder mean would
be “holder_mean::2”</p>
</div></blockquote>
<p>weights (list of floats): (Optional) weights for each element in data_lst</p>
</dd>
<dt>Returns:</dt><dd><p>float - Desired statistic</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.stats.PropertyStats.eigenvalues">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">eigenvalues</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data_lst</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">symm</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sort</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.stats.PropertyStats.eigenvalues" title="Permalink to this definition">¶</a></dt>
<dd><p>Return the eigenvalues of a matrix as a numpy array
Args:</p>
<blockquote>
<div><p>data_lst: (matrix-like) of values
symm: whether to assume the matrix is symmetric
sort: whether to sort the eigenvalues</p>
</div></blockquote>
<p>Returns: eigenvalues</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.stats.PropertyStats.flatten">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">flatten</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data_lst</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">weights</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.stats.PropertyStats.flatten" title="Permalink to this definition">¶</a></dt>
<dd><p>Returns a flattened copy of data_lst-as a numpy array</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.stats.PropertyStats.geom_std_dev">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">geom_std_dev</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data_lst</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">weights</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.stats.PropertyStats.geom_std_dev" title="Permalink to this definition">¶</a></dt>
<dd><p>Geometric standard deviation</p>
<dl class="simple">
<dt>Args:</dt><dd><p>data_lst (list of floats): List of values to be assessed
weights (list of floats): Weights for each value</p>
</dd>
<dt>Returns:</dt><dd><p>geometric standard deviation</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.stats.PropertyStats.holder_mean">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">holder_mean</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data_lst</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">weights</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">power</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.stats.PropertyStats.holder_mean" title="Permalink to this definition">¶</a></dt>
<dd><p>Get Holder mean
Args:</p>
<blockquote>
<div><p>data_lst: (list/array) of values
weights: (list/array) of weights
power: (int/float/str) which holder mean to compute</p>
</div></blockquote>
<p>Returns: Holder mean</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.stats.PropertyStats.inverse_mean">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">inverse_mean</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data_lst</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">weights</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.stats.PropertyStats.inverse_mean" title="Permalink to this definition">¶</a></dt>
<dd><p>Mean of the inverse of each entry</p>
<dl class="simple">
<dt>Args:</dt><dd><p>data_lst (list of floats): List of values to be assessed
weights (list of floats): Weights for each value</p>
</dd>
<dt>Returns:</dt><dd><p>inverse mean</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.stats.PropertyStats.kurtosis">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">kurtosis</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data_lst</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">weights</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.stats.PropertyStats.kurtosis" title="Permalink to this definition">¶</a></dt>
<dd><p>Kurtosis of a list of data</p>
<dl class="simple">
<dt>Args:</dt><dd><p>data_lst (list of floats): List of values to be assessed
weights (list of floats): Weights for each value</p>
</dd>
<dt>Returns:</dt><dd><p>kurtosis</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.stats.PropertyStats.maximum">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">maximum</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data_lst</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">weights</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.stats.PropertyStats.maximum" title="Permalink to this definition">¶</a></dt>
<dd><p>Maximum value in a list</p>
<dl class="simple">
<dt>Args:</dt><dd><p>data_lst (list of floats): List of values to be assessed
weights: (ignored)</p>
</dd>
<dt>Returns:</dt><dd><p>maximum value</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.stats.PropertyStats.mean">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">mean</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data_lst</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">weights</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.stats.PropertyStats.mean" title="Permalink to this definition">¶</a></dt>
<dd><p>Arithmetic mean of list</p>
<dl class="simple">
<dt>Args:</dt><dd><p>data_lst (list of floats): List of values to be assessed
weights (list of floats): Weights for each value</p>
</dd>
<dt>Returns:</dt><dd><p>mean value</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.stats.PropertyStats.minimum">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">minimum</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data_lst</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">weights</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.stats.PropertyStats.minimum" title="Permalink to this definition">¶</a></dt>
<dd><p>Minimum value in a list</p>
<dl class="simple">
<dt>Args:</dt><dd><p>data_lst (list of floats): List of values to be assessed
weights: (ignored)</p>
</dd>
<dt>Returns:</dt><dd><p>minimum value</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.stats.PropertyStats.mode">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">mode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data_lst</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">weights</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.stats.PropertyStats.mode" title="Permalink to this definition">¶</a></dt>
<dd><p>Mode of a list of data.</p>
<p>If multiple elements occur equally-frequently (or same weight, if
weights are provided), this function will return the minimum of those
values.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>data_lst (list of floats): List of values to be assessed
weights (list of floats): Weights for each value</p>
</dd>
<dt>Returns:</dt><dd><p>mode</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.stats.PropertyStats.quantile">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">quantile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data_lst</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">weights</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">q</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.5</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.stats.PropertyStats.quantile" title="Permalink to this definition">¶</a></dt>
<dd><p>Return a specific quantile.
Args:</p>
<blockquote>
<div><dl class="simple">
<dt>data_lst (list or np.ndarray): 1D data list to be used for computing</dt><dd><p>quantiles</p>
</dd>
</dl>
<p>q (float): The quantile, as a fraction between 0 and 1.</p>
</div></blockquote>
<dl class="simple">
<dt>Returns:</dt><dd><p>(float) The computed quantile of the data_lst.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.stats.PropertyStats.range">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">range</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data_lst</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">weights</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.stats.PropertyStats.range" title="Permalink to this definition">¶</a></dt>
<dd><p>Range of a list</p>
<dl class="simple">
<dt>Args:</dt><dd><p>data_lst (list of floats): List of values to be assessed
weights: (ignored)</p>
</dd>
<dt>Returns:</dt><dd><p>range</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.stats.PropertyStats.skewness">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">skewness</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data_lst</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">weights</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.stats.PropertyStats.skewness" title="Permalink to this definition">¶</a></dt>
<dd><p>Skewness of a list of data</p>
<dl class="simple">
<dt>Args:</dt><dd><p>data_lst (list of floats): List of values to be assessed
weights (list of floats): Weights for each value</p>
</dd>
<dt>Returns:</dt><dd><p>shewness</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.stats.PropertyStats.sorted">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">sorted</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data_lst</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">weights</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.stats.PropertyStats.sorted" title="Permalink to this definition">¶</a></dt>
<dd><p>Returns the sorted data_lst</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.stats.PropertyStats.std_dev">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">std_dev</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data_lst</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">weights</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.stats.PropertyStats.std_dev" title="Permalink to this definition">¶</a></dt>
<dd><p>Standard deviation of a list of element data</p>
<dl class="simple">
<dt>Args:</dt><dd><p>data_lst (list of floats): List of values to be assessed
weights (list of floats): Weights for each value</p>
</dd>
<dt>Returns:</dt><dd><p>standard deviation</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.utils">
<span id="module-contents"></span><h2>Module contents<a class="headerlink" href="#module-matminer.featurizers.utils" title="Permalink to this heading">¶</a></h2>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">matminer.featurizers.utils package</a><ul>
<li><a class="reference internal" href="#subpackages">Subpackages</a></li>
<li><a class="reference internal" href="#submodules">Submodules</a></li>
<li><a class="reference internal" href="#module-matminer.featurizers.utils.grdf">matminer.featurizers.utils.grdf module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.utils.grdf.AbstractPairwise"><code class="docutils literal notranslate"><span class="pre">AbstractPairwise</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.utils.grdf.AbstractPairwise.name"><code class="docutils literal notranslate"><span class="pre">AbstractPairwise.name()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.grdf.AbstractPairwise.volume"><code class="docutils literal notranslate"><span class="pre">AbstractPairwise.volume()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.utils.grdf.Bessel"><code class="docutils literal notranslate"><span class="pre">Bessel</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.utils.grdf.Bessel.__init__"><code class="docutils literal notranslate"><span class="pre">Bessel.__init__()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.utils.grdf.Cosine"><code class="docutils literal notranslate"><span class="pre">Cosine</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.utils.grdf.Cosine.__init__"><code class="docutils literal notranslate"><span class="pre">Cosine.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.grdf.Cosine.volume"><code class="docutils literal notranslate"><span class="pre">Cosine.volume()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.utils.grdf.Gaussian"><code class="docutils literal notranslate"><span class="pre">Gaussian</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.utils.grdf.Gaussian.__init__"><code class="docutils literal notranslate"><span class="pre">Gaussian.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.grdf.Gaussian.volume"><code class="docutils literal notranslate"><span class="pre">Gaussian.volume()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.utils.grdf.Histogram"><code class="docutils literal notranslate"><span class="pre">Histogram</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.utils.grdf.Histogram.__init__"><code class="docutils literal notranslate"><span class="pre">Histogram.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.grdf.Histogram.volume"><code class="docutils literal notranslate"><span class="pre">Histogram.volume()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.utils.grdf.Sine"><code class="docutils literal notranslate"><span class="pre">Sine</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.utils.grdf.Sine.__init__"><code class="docutils literal notranslate"><span class="pre">Sine.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.grdf.Sine.volume"><code class="docutils literal notranslate"><span class="pre">Sine.volume()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.utils.grdf.initialize_pairwise_function"><code class="docutils literal notranslate"><span class="pre">initialize_pairwise_function()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.utils.oxidation">matminer.featurizers.utils.oxidation module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.utils.oxidation.has_oxidation_states"><code class="docutils literal notranslate"><span class="pre">has_oxidation_states()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.utils.stats">matminer.featurizers.utils.stats module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.utils.stats.PropertyStats"><code class="docutils literal notranslate"><span class="pre">PropertyStats</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.utils.stats.PropertyStats.avg_dev"><code class="docutils literal notranslate"><span class="pre">PropertyStats.avg_dev()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.stats.PropertyStats.calc_stat"><code class="docutils literal notranslate"><span class="pre">PropertyStats.calc_stat()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.stats.PropertyStats.eigenvalues"><code class="docutils literal notranslate"><span class="pre">PropertyStats.eigenvalues()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.stats.PropertyStats.flatten"><code class="docutils literal notranslate"><span class="pre">PropertyStats.flatten()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.stats.PropertyStats.geom_std_dev"><code class="docutils literal notranslate"><span class="pre">PropertyStats.geom_std_dev()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.stats.PropertyStats.holder_mean"><code class="docutils literal notranslate"><span class="pre">PropertyStats.holder_mean()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.stats.PropertyStats.inverse_mean"><code class="docutils literal notranslate"><span class="pre">PropertyStats.inverse_mean()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.stats.PropertyStats.kurtosis"><code class="docutils literal notranslate"><span class="pre">PropertyStats.kurtosis()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.stats.PropertyStats.maximum"><code class="docutils literal notranslate"><span class="pre">PropertyStats.maximum()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.stats.PropertyStats.mean"><code class="docutils literal notranslate"><span class="pre">PropertyStats.mean()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.stats.PropertyStats.minimum"><code class="docutils literal notranslate"><span class="pre">PropertyStats.minimum()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.stats.PropertyStats.mode"><code class="docutils literal notranslate"><span class="pre">PropertyStats.mode()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.stats.PropertyStats.quantile"><code class="docutils literal notranslate"><span class="pre">PropertyStats.quantile()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.stats.PropertyStats.range"><code class="docutils literal notranslate"><span class="pre">PropertyStats.range()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.stats.PropertyStats.skewness"><code class="docutils literal notranslate"><span class="pre">PropertyStats.skewness()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.stats.PropertyStats.sorted"><code class="docutils literal notranslate"><span class="pre">PropertyStats.sorted()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.stats.PropertyStats.std_dev"><code class="docutils literal notranslate"><span class="pre">PropertyStats.std_dev()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.utils">Module contents</a></li>
</ul>
</li>
</ul>

  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/matminer.featurizers.utils.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer.featurizers.utils package</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2015, Anubhav Jain.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.3.0.
    </div>

  </body>
</html>