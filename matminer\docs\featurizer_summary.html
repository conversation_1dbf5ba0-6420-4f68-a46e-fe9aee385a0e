
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

    <title>bandstructure &#8212; matminer 0.9.0 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="_static/nature.css" />
    <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="_static/doctools.js"></script>
    <script src="_static/sphinx_highlight.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
 
<link href='https://fonts.googleapis.com/css?family=Lato:400,700' rel='stylesheet' type='text/css'>

  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">bandstructure</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <p>Below, you will find a description of each featurizer, listed in tables grouped by module.</p>
<section id="bandstructure">
<h1>bandstructure<a class="headerlink" href="#bandstructure" title="Permalink to this heading">¶</a></h1>
<section id="features-derived-from-a-material-s-electronic-bandstructure">
<h2>Features derived from a material’s electronic bandstructure.<a class="headerlink" href="#features-derived-from-a-material-s-electronic-bandstructure" title="Permalink to this heading">¶</a></h2>
<p><code class="code docutils literal notranslate"><span class="pre">matminer.featurizers.bandstructure</span></code></p>
<table class="docutils align-left">
<colgroup>
<col style="width: 30.0%" />
<col style="width: 70.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">BranchPointEnergy</span></code></p></td>
<td><p>Branch point energy and absolute band edge position.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">BandFeaturizer</span></code></p></td>
<td><p>Featurizes a pymatgen band structure object.</p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="base">
<h1>base<a class="headerlink" href="#base" title="Permalink to this heading">¶</a></h1>
<section id="parent-classes-and-meta-featurizers">
<h2>Parent classes and meta-featurizers.<a class="headerlink" href="#parent-classes-and-meta-featurizers" title="Permalink to this heading">¶</a></h2>
<p><code class="code docutils literal notranslate"><span class="pre">matminer.featurizers.base</span></code></p>
<table class="docutils align-left">
<colgroup>
<col style="width: 30.0%" />
<col style="width: 70.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">MultipleFeaturizer</span></code></p></td>
<td><p>Class to run multiple featurizers on the same input data.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">StackedFeaturizer</span></code></p></td>
<td><p>Use the output of a machine learning model as features</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></p></td>
<td><p>Abstract class to calculate features from raw materials input data</p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="composition">
<h1>composition<a class="headerlink" href="#composition" title="Permalink to this heading">¶</a></h1>
<section id="features-based-on-a-material-s-composition">
<h2>Features based on a material’s composition.<a class="headerlink" href="#features-based-on-a-material-s-composition" title="Permalink to this heading">¶</a></h2>
<section id="alloy">
<h3>alloy<a class="headerlink" href="#alloy" title="Permalink to this heading">¶</a></h3>
<p><code class="code docutils literal notranslate"><span class="pre">matminer.featurizers.composition.alloy</span></code></p>
<blockquote>
<div><p>Composition featurizers specialized for use with alloys.</p>
</div></blockquote>
<table class="docutils align-left">
<colgroup>
<col style="width: 30.0%" />
<col style="width: 70.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">Miedema</span></code></p></td>
<td><p>Formation enthalpies of intermetallic compounds, from Miedema et al.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">YangSolidSolution</span></code></p></td>
<td><p>Mixing thermochemistry and size mismatch terms of Yang and Zhang (2012)</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">WenAlloys</span></code></p></td>
<td><p>Calculate features for alloy properties.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="composite">
<h3>composite<a class="headerlink" href="#composite" title="Permalink to this heading">¶</a></h3>
<p><code class="code docutils literal notranslate"><span class="pre">matminer.featurizers.composition.composite</span></code></p>
<blockquote>
<div><p>Composition featurizers for composite features containing more than 1 category of general-purpose data.</p>
</div></blockquote>
<table class="docutils align-left">
<colgroup>
<col style="width: 30.0%" />
<col style="width: 70.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">ElementProperty</span></code></p></td>
<td><p>Class to calculate elemental property attributes.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">Meredig</span></code></p></td>
<td><p>Class to calculate features as defined in Meredig et. al.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="element">
<h3>element<a class="headerlink" href="#element" title="Permalink to this heading">¶</a></h3>
<p><code class="code docutils literal notranslate"><span class="pre">matminer.featurizers.composition.element</span></code></p>
<blockquote>
<div><p>Composition featurizers for elemental data and stoichiometry.</p>
</div></blockquote>
<table class="docutils align-left">
<colgroup>
<col style="width: 30.0%" />
<col style="width: 70.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">ElementFraction</span></code></p></td>
<td><p>Class to calculate the atomic fraction of each element in a composition.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">TMetalFraction</span></code></p></td>
<td><p>Class to calculate fraction of magnetic transition metals in a composition.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">Stoichiometry</span></code></p></td>
<td><p>Calculate norms of stoichiometric attributes.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">BandCenter</span></code></p></td>
<td><p>Estimation of absolute position of band center using electronegativity.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="ion">
<h3>ion<a class="headerlink" href="#ion" title="Permalink to this heading">¶</a></h3>
<p><code class="code docutils literal notranslate"><span class="pre">matminer.featurizers.composition.ion</span></code></p>
<blockquote>
<div><p>Composition featurizers for compositions with ionic data.</p>
</div></blockquote>
<table class="docutils align-left">
<colgroup>
<col style="width: 30.0%" />
<col style="width: 70.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">OxidationStates</span></code></p></td>
<td><p>Statistics about the oxidation states for each specie.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">IonProperty</span></code></p></td>
<td><p>Ionic property attributes. Similar to ElementProperty.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">ElectronAffinity</span></code></p></td>
<td><p>Calculate average electron affinity times formal charge of anion elements.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">ElectronegativityDiff</span></code></p></td>
<td><p>Features from electronegativity differences between anions and cations.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="orbital">
<h3>orbital<a class="headerlink" href="#orbital" title="Permalink to this heading">¶</a></h3>
<p><code class="code docutils literal notranslate"><span class="pre">matminer.featurizers.composition.orbital</span></code></p>
<blockquote>
<div><p>Composition featurizers for orbital data.</p>
</div></blockquote>
<table class="docutils align-left">
<colgroup>
<col style="width: 30.0%" />
<col style="width: 70.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">AtomicOrbitals</span></code></p></td>
<td><p>Determine HOMO/LUMO features based on a composition.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">ValenceOrbital</span></code></p></td>
<td><p>Attributes of valence orbital shells</p></td>
</tr>
</tbody>
</table>
</section>
<section id="packing">
<h3>packing<a class="headerlink" href="#packing" title="Permalink to this heading">¶</a></h3>
<p><code class="code docutils literal notranslate"><span class="pre">matminer.featurizers.composition.packing</span></code></p>
<blockquote>
<div><p>Composition featurizers for determining packing characteristics.</p>
</div></blockquote>
<table class="docutils align-left">
<colgroup>
<col style="width: 30.0%" />
<col style="width: 70.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">AtomicPackingEfficiency</span></code></p></td>
<td><p>Packing efficiency based on a geometric theory of the amorphous packing</p></td>
</tr>
</tbody>
</table>
</section>
<section id="thermo">
<h3>thermo<a class="headerlink" href="#thermo" title="Permalink to this heading">¶</a></h3>
<p><code class="code docutils literal notranslate"><span class="pre">matminer.featurizers.composition.thermo</span></code></p>
<blockquote>
<div><p>Composition featurizers for thermodynamic properties.</p>
</div></blockquote>
<table class="docutils align-left">
<colgroup>
<col style="width: 30.0%" />
<col style="width: 70.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">CohesiveEnergy</span></code></p></td>
<td><p>Cohesive energy per atom using elemental cohesive energies and</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">CohesiveEnergyMP</span></code></p></td>
<td><p>Cohesive energy per atom lookup using Materials Project</p></td>
</tr>
</tbody>
</table>
</section>
</section>
</section>
<section id="conversions">
<h1>conversions<a class="headerlink" href="#conversions" title="Permalink to this heading">¶</a></h1>
<section id="conversion-utilities">
<h2>Conversion utilities.<a class="headerlink" href="#conversion-utilities" title="Permalink to this heading">¶</a></h2>
<p><code class="code docutils literal notranslate"><span class="pre">matminer.featurizers.conversions</span></code></p>
<table class="docutils align-left">
<colgroup>
<col style="width: 30.0%" />
<col style="width: 70.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">ConversionFeaturizer</span></code></p></td>
<td><p>Abstract class to perform data conversions.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">StrToComposition</span></code></p></td>
<td><p>Utility featurizer to convert a string to a Composition</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">StructureToComposition</span></code></p></td>
<td><p>Utility featurizer to convert a Structure to a Composition.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">StructureToIStructure</span></code></p></td>
<td><p>Utility featurizer to convert a Structure to an immutable IStructure.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">DictToObject</span></code></p></td>
<td><p>Utility featurizer to decode a dict to Python object via MSON.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">JsonToObject</span></code></p></td>
<td><p>Utility featurizer to decode json data to a Python object via MSON.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">StructureToOxidStructure</span></code></p></td>
<td><p>Utility featurizer to add oxidation states to a pymatgen Structure.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">CompositionToOxidComposition</span></code></p></td>
<td><p>Utility featurizer to add oxidation states to a pymatgen Composition.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">CompositionToStructureFromMP</span></code></p></td>
<td><p>Featurizer to get a Structure object from Materials Project using the</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">PymatgenFunctionApplicator</span></code></p></td>
<td><p>Featurizer to run any function using on/from pymatgen primitives.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">ASEAtomstoStructure</span></code></p></td>
<td><p>Convert dataframes of ase structures to pymatgen structures for further use with</p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="dos">
<h1>dos<a class="headerlink" href="#dos" title="Permalink to this heading">¶</a></h1>
<section id="features-based-on-a-material-s-electronic-density-of-states">
<h2>Features based on a material’s electronic density of states.<a class="headerlink" href="#features-based-on-a-material-s-electronic-density-of-states" title="Permalink to this heading">¶</a></h2>
<p><code class="code docutils literal notranslate"><span class="pre">matminer.featurizers.dos</span></code></p>
<table class="docutils align-left">
<colgroup>
<col style="width: 30.0%" />
<col style="width: 70.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">SiteDOS</span></code></p></td>
<td><p>report the fractional s/p/d/f dos for a particular site. a CompleteDos</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">DOSFeaturizer</span></code></p></td>
<td><p>Significant character and contribution of the density of state from a</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">DopingFermi</span></code></p></td>
<td><p>The fermi level (w.r.t. selected reference energy) associated with a</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">Hybridization</span></code></p></td>
<td><p>quantify s/p/d/f orbital character and their hybridizations at band edges</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">DosAsymmetry</span></code></p></td>
<td><p>Quantifies the asymmetry of the DOS near the Fermi level.</p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="function">
<h1>function<a class="headerlink" href="#function" title="Permalink to this heading">¶</a></h1>
<section id="classes-for-expanding-sets-of-features-calculated-with-other-featurizers">
<h2>Classes for expanding sets of features calculated with other featurizers.<a class="headerlink" href="#classes-for-expanding-sets-of-features-calculated-with-other-featurizers" title="Permalink to this heading">¶</a></h2>
<p><code class="code docutils literal notranslate"><span class="pre">matminer.featurizers.function</span></code></p>
<table class="docutils align-left">
<colgroup>
<col style="width: 30.0%" />
<col style="width: 70.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">FunctionFeaturizer</span></code></p></td>
<td><p>Features from functions applied to existing features, e.g. “1/x”</p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="site">
<h1>site<a class="headerlink" href="#site" title="Permalink to this heading">¶</a></h1>
<section id="features-from-individual-sites-in-a-material-s-crystal-structure">
<h2>Features from individual sites in a material’s crystal structure.<a class="headerlink" href="#features-from-individual-sites-in-a-material-s-crystal-structure" title="Permalink to this heading">¶</a></h2>
<section id="bonding">
<h3>bonding<a class="headerlink" href="#bonding" title="Permalink to this heading">¶</a></h3>
<p><code class="code docutils literal notranslate"><span class="pre">matminer.featurizers.site.bonding</span></code></p>
<blockquote>
<div><p>Site featurizers based on bonding.</p>
</div></blockquote>
<table class="docutils align-left">
<colgroup>
<col style="width: 30.0%" />
<col style="width: 70.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">BondOrientationalParameter</span></code></p></td>
<td><p>Averages of spherical harmonics of local neighbors</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">AverageBondLength</span></code></p></td>
<td><p>Determines the average bond length between one specific site</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">AverageBondAngle</span></code></p></td>
<td><p>Determines the average bond angles of a specific site with</p></td>
</tr>
</tbody>
</table>
</section>
<section id="chemical">
<h3>chemical<a class="headerlink" href="#chemical" title="Permalink to this heading">¶</a></h3>
<p><code class="code docutils literal notranslate"><span class="pre">matminer.featurizers.site.chemical</span></code></p>
<blockquote>
<div><p>Site featurizers based on local chemical information, rather than geometry alone.</p>
</div></blockquote>
<table class="docutils align-left">
<colgroup>
<col style="width: 30.0%" />
<col style="width: 70.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">ChemicalSRO</span></code></p></td>
<td><p>Chemical short range ordering, deviation of local site and nominal structure compositions</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">EwaldSiteEnergy</span></code></p></td>
<td><p>Compute site energy from Coulombic interactions</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">LocalPropertyDifference</span></code></p></td>
<td><p>Differences in elemental properties between site and its neighboring sites.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">SiteElementalProperty</span></code></p></td>
<td><p>Elemental properties of atom on a certain site</p></td>
</tr>
</tbody>
</table>
</section>
<section id="external">
<h3>external<a class="headerlink" href="#external" title="Permalink to this heading">¶</a></h3>
<p><code class="code docutils literal notranslate"><span class="pre">matminer.featurizers.site.external</span></code></p>
<blockquote>
<div><p>Site featurizers requiring external libraries for core functionality.</p>
</div></blockquote>
<table class="docutils align-left">
<colgroup>
<col style="width: 30.0%" />
<col style="width: 70.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">SOAP</span></code></p></td>
<td><p>Smooth overlap of atomic positions (interface via DScribe).</p></td>
</tr>
</tbody>
</table>
</section>
<section id="fingerprint">
<h3>fingerprint<a class="headerlink" href="#fingerprint" title="Permalink to this heading">¶</a></h3>
<p><code class="code docutils literal notranslate"><span class="pre">matminer.featurizers.site.fingerprint</span></code></p>
<blockquote>
<div><p>Site featurizers that fingerprint a site using local geometry.</p>
</div></blockquote>
<table class="docutils align-left">
<colgroup>
<col style="width: 30.0%" />
<col style="width: 70.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">AGNIFingerprints</span></code></p></td>
<td><p>Product integral of RDF and Gaussian window function, from <a class="reference external" href="http://pubs.acs.org/doi/abs/10.1021/acs.jpcc.6b10908">Botu et al</a>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">OPSiteFingerprint</span></code></p></td>
<td><p>Local structure order parameters computed from a site’s neighbor env.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">CrystalNNFingerprint</span></code></p></td>
<td><p>A local order parameter fingerprint for periodic crystals.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">VoronoiFingerprint</span></code></p></td>
<td><p>Voronoi tessellation-based features around target site.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">ChemEnvSiteFingerprint</span></code></p></td>
<td><p>Resemblance of given sites to ideal environments</p></td>
</tr>
</tbody>
</table>
</section>
<section id="misc">
<h3>misc<a class="headerlink" href="#misc" title="Permalink to this heading">¶</a></h3>
<p><code class="code docutils literal notranslate"><span class="pre">matminer.featurizers.site.misc</span></code></p>
<blockquote>
<div><p>Miscellaneous site featurizers.</p>
</div></blockquote>
<table class="docutils align-left">
<colgroup>
<col style="width: 30.0%" />
<col style="width: 70.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">IntersticeDistribution</span></code></p></td>
<td><p>Interstice distribution in the neighboring cluster around an atom site.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">CoordinationNumber</span></code></p></td>
<td><p>Number of first nearest neighbors of a site.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="rdf">
<h3>rdf<a class="headerlink" href="#rdf" title="Permalink to this heading">¶</a></h3>
<p><code class="code docutils literal notranslate"><span class="pre">matminer.featurizers.site.rdf</span></code></p>
<blockquote>
<div><p>Site featurizers based on distribution functions.</p>
</div></blockquote>
<table class="docutils align-left">
<colgroup>
<col style="width: 30.0%" />
<col style="width: 70.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">GaussianSymmFunc</span></code></p></td>
<td><p>Gaussian symmetry function features suggested by Behler et al.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">GeneralizedRadialDistributionFunction</span></code></p></td>
<td><p>Compute the general radial distribution function (GRDF) for a site.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">AngularFourierSeries</span></code></p></td>
<td><p>Compute the angular Fourier series (AFS), including both angular and radial info</p></td>
</tr>
</tbody>
</table>
</section>
</section>
</section>
<section id="structure">
<h1>structure<a class="headerlink" href="#structure" title="Permalink to this heading">¶</a></h1>
<section id="generating-features-based-on-a-material-s-crystal-structure">
<h2>Generating features based on a material’s crystal structure.<a class="headerlink" href="#generating-features-based-on-a-material-s-crystal-structure" title="Permalink to this heading">¶</a></h2>
<section id="id1">
<h3>bonding<a class="headerlink" href="#id1" title="Permalink to this heading">¶</a></h3>
<p><code class="code docutils literal notranslate"><span class="pre">matminer.featurizers.structure.bonding</span></code></p>
<blockquote>
<div><p>Structure featurizers based on bonding.</p>
</div></blockquote>
<table class="docutils align-left">
<colgroup>
<col style="width: 30.0%" />
<col style="width: 70.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">BondFractions</span></code></p></td>
<td><p>Compute the fraction of each bond in a structure, based on NearestNeighbors.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">BagofBonds</span></code></p></td>
<td><p>Compute a Bag of Bonds vector, as first described by Hansen et al. (2015).</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">GlobalInstabilityIndex</span></code></p></td>
<td><p>The global instability index of a structure.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">StructuralHeterogeneity</span></code></p></td>
<td><p>Variance in the bond lengths and atomic volumes in a structure</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">MinimumRelativeDistances</span></code></p></td>
<td><p>Determines the relative distance of each site to its closest neighbor.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="id2">
<h3>composite<a class="headerlink" href="#id2" title="Permalink to this heading">¶</a></h3>
<p><code class="code docutils literal notranslate"><span class="pre">matminer.featurizers.structure.composite</span></code></p>
<blockquote>
<div><p>Structure featurizers producing more than one kind of structure feature data.</p>
</div></blockquote>
<table class="docutils align-left">
<colgroup>
<col style="width: 30.0%" />
<col style="width: 70.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">JarvisCFID</span></code></p></td>
<td><p>Classical Force-Field Inspired Descriptors (CFID) from Jarvis-ML.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="matrix">
<h3>matrix<a class="headerlink" href="#matrix" title="Permalink to this heading">¶</a></h3>
<p><code class="code docutils literal notranslate"><span class="pre">matminer.featurizers.structure.matrix</span></code></p>
<blockquote>
<div><p>Structure featurizers generating a matrix for each structure.  Most matrix structure featurizers contain the ability to flatten matrices to be dataframe-friendly.</p>
</div></blockquote>
<table class="docutils align-left">
<colgroup>
<col style="width: 30.0%" />
<col style="width: 70.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">CoulombMatrix</span></code></p></td>
<td><p>The Coulomb matrix, a representation of nuclear coulombic interaction.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">SineCoulombMatrix</span></code></p></td>
<td><p>A variant of the Coulomb matrix developed for periodic crystals.</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">OrbitalFieldMatrix</span></code></p></td>
<td><p>Representation based on the valence shell electrons of neighboring atoms.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="id3">
<h3>misc<a class="headerlink" href="#id3" title="Permalink to this heading">¶</a></h3>
<p><code class="code docutils literal notranslate"><span class="pre">matminer.featurizers.structure.misc</span></code></p>
<blockquote>
<div><p>Miscellaneous structure featurizers.</p>
</div></blockquote>
<table class="docutils align-left">
<colgroup>
<col style="width: 30.0%" />
<col style="width: 70.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">EwaldEnergy</span></code></p></td>
<td><p>Compute the energy from Coulombic interactions.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">StructureComposition</span></code></p></td>
<td><p>Features related to the composition of a structure</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">XRDPowderPattern</span></code></p></td>
<td><p>1D array representing powder diffraction of a structure as calculated by</p></td>
</tr>
</tbody>
</table>
</section>
<section id="order">
<h3>order<a class="headerlink" href="#order" title="Permalink to this heading">¶</a></h3>
<p><code class="code docutils literal notranslate"><span class="pre">matminer.featurizers.structure.order</span></code></p>
<blockquote>
<div><p>Structure featurizers based on packing or ordering.</p>
</div></blockquote>
<table class="docutils align-left">
<colgroup>
<col style="width: 30.0%" />
<col style="width: 70.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">DensityFeatures</span></code></p></td>
<td><p>Calculates density and density-like features</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">ChemicalOrdering</span></code></p></td>
<td><p>How much the ordering of species in the structure differs from random</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">MaximumPackingEfficiency</span></code></p></td>
<td><p>Maximum possible packing efficiency of this structure</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">StructuralComplexity</span></code></p></td>
<td><p>Shannon information entropy of a structure.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="id4">
<h3>rdf<a class="headerlink" href="#id4" title="Permalink to this heading">¶</a></h3>
<p><code class="code docutils literal notranslate"><span class="pre">matminer.featurizers.structure.rdf</span></code></p>
<blockquote>
<div><p>Structure featurizers implementing radial distribution functions.</p>
</div></blockquote>
<table class="docutils align-left">
<colgroup>
<col style="width: 30.0%" />
<col style="width: 70.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">RadialDistributionFunction</span></code></p></td>
<td><p>Calculate the radial distribution function (RDF) of a crystal structure.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">PartialRadialDistributionFunction</span></code></p></td>
<td><p>Compute the partial radial distribution function (PRDF) of an xtal structure</p></td>
</tr>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">ElectronicRadialDistributionFunction</span></code></p></td>
<td><p>Calculate the inherent electronic radial distribution function (ReDF)</p></td>
</tr>
</tbody>
</table>
</section>
<section id="sites">
<h3>sites<a class="headerlink" href="#sites" title="Permalink to this heading">¶</a></h3>
<p><code class="code docutils literal notranslate"><span class="pre">matminer.featurizers.structure.sites</span></code></p>
<blockquote>
<div><p>Structure featurizers based on aggregating site features.</p>
</div></blockquote>
<table class="docutils align-left">
<colgroup>
<col style="width: 30.0%" />
<col style="width: 70.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">SiteStatsFingerprint</span></code></p></td>
<td><p>Computes statistics of properties across all sites in a structure.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="symmetry">
<h3>symmetry<a class="headerlink" href="#symmetry" title="Permalink to this heading">¶</a></h3>
<p><code class="code docutils literal notranslate"><span class="pre">matminer.featurizers.structure.symmetry</span></code></p>
<blockquote>
<div><p>Structure featurizers based on symmetry.</p>
</div></blockquote>
<table class="docutils align-left">
<colgroup>
<col style="width: 30.0%" />
<col style="width: 70.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="code docutils literal notranslate"><span class="pre">GlobalSymmetryFeatures</span></code></p></td>
<td><p>Determines symmetry features, e.g. spacegroup number and  crystal system</p></td>
</tr>
<tr class="row-odd"><td><p><code class="code docutils literal notranslate"><span class="pre">Dimensionality</span></code></p></td>
<td><p>Returns dimensionality of structure: 1 means linear chains of atoms OR</p></td>
</tr>
</tbody>
</table>
</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">bandstructure</a><ul>
<li><a class="reference internal" href="#features-derived-from-a-material-s-electronic-bandstructure">Features derived from a material’s electronic bandstructure.</a></li>
</ul>
</li>
<li><a class="reference internal" href="#base">base</a><ul>
<li><a class="reference internal" href="#parent-classes-and-meta-featurizers">Parent classes and meta-featurizers.</a></li>
</ul>
</li>
<li><a class="reference internal" href="#composition">composition</a><ul>
<li><a class="reference internal" href="#features-based-on-a-material-s-composition">Features based on a material’s composition.</a><ul>
<li><a class="reference internal" href="#alloy">alloy</a></li>
<li><a class="reference internal" href="#composite">composite</a></li>
<li><a class="reference internal" href="#element">element</a></li>
<li><a class="reference internal" href="#ion">ion</a></li>
<li><a class="reference internal" href="#orbital">orbital</a></li>
<li><a class="reference internal" href="#packing">packing</a></li>
<li><a class="reference internal" href="#thermo">thermo</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#conversions">conversions</a><ul>
<li><a class="reference internal" href="#conversion-utilities">Conversion utilities.</a></li>
</ul>
</li>
<li><a class="reference internal" href="#dos">dos</a><ul>
<li><a class="reference internal" href="#features-based-on-a-material-s-electronic-density-of-states">Features based on a material’s electronic density of states.</a></li>
</ul>
</li>
<li><a class="reference internal" href="#function">function</a><ul>
<li><a class="reference internal" href="#classes-for-expanding-sets-of-features-calculated-with-other-featurizers">Classes for expanding sets of features calculated with other featurizers.</a></li>
</ul>
</li>
<li><a class="reference internal" href="#site">site</a><ul>
<li><a class="reference internal" href="#features-from-individual-sites-in-a-material-s-crystal-structure">Features from individual sites in a material’s crystal structure.</a><ul>
<li><a class="reference internal" href="#bonding">bonding</a></li>
<li><a class="reference internal" href="#chemical">chemical</a></li>
<li><a class="reference internal" href="#external">external</a></li>
<li><a class="reference internal" href="#fingerprint">fingerprint</a></li>
<li><a class="reference internal" href="#misc">misc</a></li>
<li><a class="reference internal" href="#rdf">rdf</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#structure">structure</a><ul>
<li><a class="reference internal" href="#generating-features-based-on-a-material-s-crystal-structure">Generating features based on a material’s crystal structure.</a><ul>
<li><a class="reference internal" href="#id1">bonding</a></li>
<li><a class="reference internal" href="#id2">composite</a></li>
<li><a class="reference internal" href="#matrix">matrix</a></li>
<li><a class="reference internal" href="#id3">misc</a></li>
<li><a class="reference internal" href="#order">order</a></li>
<li><a class="reference internal" href="#id4">rdf</a></li>
<li><a class="reference internal" href="#sites">sites</a></li>
<li><a class="reference internal" href="#symmetry">symmetry</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/featurizer_summary.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">bandstructure</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2015, Anubhav Jain.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.3.0.
    </div>

  </body>
</html>