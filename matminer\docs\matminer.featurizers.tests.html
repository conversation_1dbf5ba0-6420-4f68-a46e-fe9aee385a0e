
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

    <title>matminer.featurizers.tests package &#8212; matminer 0.9.0 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="_static/nature.css" />
    <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="_static/doctools.js"></script>
    <script src="_static/sphinx_highlight.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
 
<link href='https://fonts.googleapis.com/css?family=Lato:400,700' rel='stylesheet' type='text/css'>

  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer.featurizers.tests package</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="matminer-featurizers-tests-package">
<h1>matminer.featurizers.tests package<a class="headerlink" href="#matminer-featurizers-tests-package" title="Permalink to this heading">¶</a></h1>
<section id="submodules">
<h2>Submodules<a class="headerlink" href="#submodules" title="Permalink to this heading">¶</a></h2>
</section>
<section id="module-matminer.featurizers.tests.test_bandstructure">
<span id="matminer-featurizers-tests-test-bandstructure-module"></span><h2>matminer.featurizers.tests.test_bandstructure module<a class="headerlink" href="#module-matminer.featurizers.tests.test_bandstructure" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_bandstructure.BandstructureFeaturesTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.tests.test_bandstructure.</span></span><span class="sig-name descname"><span class="pre">BandstructureFeaturesTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_bandstructure.BandstructureFeaturesTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">PymatgenTest</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_bandstructure.BandstructureFeaturesTest.setUp">
<span class="sig-name descname"><span class="pre">setUp</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_bandstructure.BandstructureFeaturesTest.setUp" title="Permalink to this definition">¶</a></dt>
<dd><p>Hook method for setting up the test fixture before exercising it.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_bandstructure.BandstructureFeaturesTest.test_BandFeaturizer">
<span class="sig-name descname"><span class="pre">test_BandFeaturizer</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_bandstructure.BandstructureFeaturesTest.test_BandFeaturizer" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_bandstructure.BandstructureFeaturesTest.test_BranchPointEnergy">
<span class="sig-name descname"><span class="pre">test_BranchPointEnergy</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_bandstructure.BandstructureFeaturesTest.test_BranchPointEnergy" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.tests.test_base">
<span id="matminer-featurizers-tests-test-base-module"></span><h2>matminer.featurizers.tests.test_base module<a class="headerlink" href="#module-matminer.featurizers.tests.test_base" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.FittableFeaturizer">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.tests.test_base.</span></span><span class="sig-name descname"><span class="pre">FittableFeaturizer</span></span><a class="headerlink" href="#matminer.featurizers.tests.test_base.FittableFeaturizer" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>This test featurizer tests fitting qualities of BaseFeaturizer, including
refittability and different results based on different fits.</p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.FittableFeaturizer.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.FittableFeaturizer.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.FittableFeaturizer.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.FittableFeaturizer.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.FittableFeaturizer.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.FittableFeaturizer.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Main featurizer function, which has to be implemented
in any derived featurizer subclass.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>x: input data to featurize (type depends on featurizer).</p>
</dd>
<dt>Returns:</dt><dd><p>(list) one or more features.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.FittableFeaturizer.fit">
<span class="sig-name descname"><span class="pre">fit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">X</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">fit_kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.FittableFeaturizer.fit" title="Permalink to this definition">¶</a></dt>
<dd><p>Update the parameters of this featurizer based on available data</p>
<dl class="simple">
<dt>Args:</dt><dd><p>X - [list of tuples], training data</p>
</dd>
<dt>Returns:</dt><dd><p>self</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.FittableFeaturizer.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.FittableFeaturizer.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.MatrixFeaturizer">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.tests.test_base.</span></span><span class="sig-name descname"><span class="pre">MatrixFeaturizer</span></span><a class="headerlink" href="#matminer.featurizers.tests.test_base.MatrixFeaturizer" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.MatrixFeaturizer.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.MatrixFeaturizer.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.MatrixFeaturizer.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.MatrixFeaturizer.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.MatrixFeaturizer.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.MatrixFeaturizer.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Main featurizer function, which has to be implemented
in any derived featurizer subclass.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>x: input data to featurize (type depends on featurizer).</p>
</dd>
<dt>Returns:</dt><dd><p>(list) one or more features.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.MatrixFeaturizer.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.MatrixFeaturizer.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.MultiArgs2">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.tests.test_base.</span></span><span class="sig-name descname"><span class="pre">MultiArgs2</span></span><a class="headerlink" href="#matminer.featurizers.tests.test_base.MultiArgs2" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.MultiArgs2.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.MultiArgs2.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.MultiArgs2.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.MultiArgs2.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.MultiArgs2.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.MultiArgs2.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.MultiArgs2.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.MultiArgs2.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Main featurizer function, which has to be implemented
in any derived featurizer subclass.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>x: input data to featurize (type depends on featurizer).</p>
</dd>
<dt>Returns:</dt><dd><p>(list) one or more features.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.MultiArgs2.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.MultiArgs2.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.MultiTypeFeaturizer">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.tests.test_base.</span></span><span class="sig-name descname"><span class="pre">MultiTypeFeaturizer</span></span><a class="headerlink" href="#matminer.featurizers.tests.test_base.MultiTypeFeaturizer" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>A featurizer that returns multiple dtypes</p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.MultiTypeFeaturizer.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.MultiTypeFeaturizer.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.MultiTypeFeaturizer.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.MultiTypeFeaturizer.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.MultiTypeFeaturizer.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.MultiTypeFeaturizer.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Main featurizer function, which has to be implemented
in any derived featurizer subclass.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>x: input data to featurize (type depends on featurizer).</p>
</dd>
<dt>Returns:</dt><dd><p>(list) one or more features.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.MultiTypeFeaturizer.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.MultiTypeFeaturizer.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.MultipleFeatureFeaturizer">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.tests.test_base.</span></span><span class="sig-name descname"><span class="pre">MultipleFeatureFeaturizer</span></span><a class="headerlink" href="#matminer.featurizers.tests.test_base.MultipleFeatureFeaturizer" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.MultipleFeatureFeaturizer.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.MultipleFeatureFeaturizer.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.MultipleFeatureFeaturizer.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.MultipleFeatureFeaturizer.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.MultipleFeatureFeaturizer.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.MultipleFeatureFeaturizer.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Main featurizer function, which has to be implemented
in any derived featurizer subclass.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>x: input data to featurize (type depends on featurizer).</p>
</dd>
<dt>Returns:</dt><dd><p>(list) one or more features.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.MultipleFeatureFeaturizer.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.MultipleFeatureFeaturizer.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.SingleFeaturizer">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.tests.test_base.</span></span><span class="sig-name descname"><span class="pre">SingleFeaturizer</span></span><a class="headerlink" href="#matminer.featurizers.tests.test_base.SingleFeaturizer" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.SingleFeaturizer.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.SingleFeaturizer.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.SingleFeaturizer.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.SingleFeaturizer.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.SingleFeaturizer.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.SingleFeaturizer.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Main featurizer function, which has to be implemented
in any derived featurizer subclass.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>x: input data to featurize (type depends on featurizer).</p>
</dd>
<dt>Returns:</dt><dd><p>(list) one or more features.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.SingleFeaturizer.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.SingleFeaturizer.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.SingleFeaturizerMultiArgs">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.tests.test_base.</span></span><span class="sig-name descname"><span class="pre">SingleFeaturizerMultiArgs</span></span><a class="headerlink" href="#matminer.featurizers.tests.test_base.SingleFeaturizerMultiArgs" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.tests.test_base.SingleFeaturizer" title="matminer.featurizers.tests.test_base.SingleFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">SingleFeaturizer</span></code></a></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.SingleFeaturizerMultiArgs.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.SingleFeaturizerMultiArgs.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Main featurizer function, which has to be implemented
in any derived featurizer subclass.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>x: input data to featurize (type depends on featurizer).</p>
</dd>
<dt>Returns:</dt><dd><p>(list) one or more features.</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.SingleFeaturizerMultiArgsWithPrecheck">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.tests.test_base.</span></span><span class="sig-name descname"><span class="pre">SingleFeaturizerMultiArgsWithPrecheck</span></span><a class="headerlink" href="#matminer.featurizers.tests.test_base.SingleFeaturizerMultiArgsWithPrecheck" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.tests.test_base.SingleFeaturizerMultiArgs" title="matminer.featurizers.tests.test_base.SingleFeaturizerMultiArgs"><code class="xref py py-class docutils literal notranslate"><span class="pre">SingleFeaturizerMultiArgs</span></code></a></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.SingleFeaturizerMultiArgsWithPrecheck.precheck">
<span class="sig-name descname"><span class="pre">precheck</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.SingleFeaturizerMultiArgsWithPrecheck.precheck" title="Permalink to this definition">¶</a></dt>
<dd><p>Precheck (provide an estimate of whether a featurizer will work or not)
for a single entry (e.g., a single composition). If the entry fails the
precheck, it will most likely fail featurization; if it passes, it is
likely (but not guaranteed) to featurize correctly.</p>
<dl class="simple">
<dt>Prechecks should be:</dt><dd><ul class="simple">
<li><p>accurate (but can be good estimates rather than ground truth)</p></li>
<li><p>fast to evaluate</p></li>
<li><dl class="simple">
<dt>unlikely to be obsolete via changes in the featurizer in the near</dt><dd><p>future</p>
</dd>
</dl>
</li>
</ul>
</dd>
</dl>
<p>This method should be overridden by any featurizer requiring its
use, as by default all entries will pass prechecking. Also, precheck
is a good opportunity to throw warnings about long runtimes (e.g., doing
nearest neighbors computations on a structure with many thousand sites).</p>
<p>See the documentation for precheck_dataframe for more information.</p>
<dl class="simple">
<dt>Args:</dt><dd><dl class="simple">
<dt><a href="#id1"><span class="problematic" id="id2">*</span></a>x (Composition, Structure, etc.): Input to-be-featurized. Can be</dt><dd><p>a single input or multiple inputs.</p>
</dd>
</dl>
</dd>
<dt>Returns:</dt><dd><p>(bool): True, if passes the precheck. False, if fails.</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.SingleFeaturizerWithPrecheck">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.tests.test_base.</span></span><span class="sig-name descname"><span class="pre">SingleFeaturizerWithPrecheck</span></span><a class="headerlink" href="#matminer.featurizers.tests.test_base.SingleFeaturizerWithPrecheck" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.tests.test_base.SingleFeaturizer" title="matminer.featurizers.tests.test_base.SingleFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">SingleFeaturizer</span></code></a></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.SingleFeaturizerWithPrecheck.precheck">
<span class="sig-name descname"><span class="pre">precheck</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.SingleFeaturizerWithPrecheck.precheck" title="Permalink to this definition">¶</a></dt>
<dd><p>Precheck (provide an estimate of whether a featurizer will work or not)
for a single entry (e.g., a single composition). If the entry fails the
precheck, it will most likely fail featurization; if it passes, it is
likely (but not guaranteed) to featurize correctly.</p>
<dl class="simple">
<dt>Prechecks should be:</dt><dd><ul class="simple">
<li><p>accurate (but can be good estimates rather than ground truth)</p></li>
<li><p>fast to evaluate</p></li>
<li><dl class="simple">
<dt>unlikely to be obsolete via changes in the featurizer in the near</dt><dd><p>future</p>
</dd>
</dl>
</li>
</ul>
</dd>
</dl>
<p>This method should be overridden by any featurizer requiring its
use, as by default all entries will pass prechecking. Also, precheck
is a good opportunity to throw warnings about long runtimes (e.g., doing
nearest neighbors computations on a structure with many thousand sites).</p>
<p>See the documentation for precheck_dataframe for more information.</p>
<dl class="simple">
<dt>Args:</dt><dd><dl class="simple">
<dt><a href="#id3"><span class="problematic" id="id4">*</span></a>x (Composition, Structure, etc.): Input to-be-featurized. Can be</dt><dd><p>a single input or multiple inputs.</p>
</dd>
</dl>
</dd>
<dt>Returns:</dt><dd><p>(bool): True, if passes the precheck. False, if fails.</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.TestBaseClass">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.tests.test_base.</span></span><span class="sig-name descname"><span class="pre">TestBaseClass</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.TestBaseClass" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">PymatgenTest</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.TestBaseClass.make_test_data">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">make_test_data</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.TestBaseClass.make_test_data" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.TestBaseClass.setUp">
<span class="sig-name descname"><span class="pre">setUp</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.TestBaseClass.setUp" title="Permalink to this definition">¶</a></dt>
<dd><p>Hook method for setting up the test fixture before exercising it.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.TestBaseClass.test_caching">
<span class="sig-name descname"><span class="pre">test_caching</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_caching" title="Permalink to this definition">¶</a></dt>
<dd><p>Test whether MultiFeaturizer properly caches</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.TestBaseClass.test_dataframe">
<span class="sig-name descname"><span class="pre">test_dataframe</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_dataframe" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.TestBaseClass.test_featurize_many">
<span class="sig-name descname"><span class="pre">test_featurize_many</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_featurize_many" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.TestBaseClass.test_fittable">
<span class="sig-name descname"><span class="pre">test_fittable</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_fittable" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.TestBaseClass.test_ignore_errors">
<span class="sig-name descname"><span class="pre">test_ignore_errors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_ignore_errors" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.TestBaseClass.test_indices">
<span class="sig-name descname"><span class="pre">test_indices</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_indices" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.TestBaseClass.test_inplace">
<span class="sig-name descname"><span class="pre">test_inplace</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_inplace" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.TestBaseClass.test_matrix">
<span class="sig-name descname"><span class="pre">test_matrix</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_matrix" title="Permalink to this definition">¶</a></dt>
<dd><p>Test the ability to add features that are matrices to a dataframe</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.TestBaseClass.test_multifeature_no_zero_index">
<span class="sig-name descname"><span class="pre">test_multifeature_no_zero_index</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_multifeature_no_zero_index" title="Permalink to this definition">¶</a></dt>
<dd><p>Test whether multifeaturizer can handle series that lack a entry
with index==0</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.TestBaseClass.test_multifeatures_multiargs">
<span class="sig-name descname"><span class="pre">test_multifeatures_multiargs</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_multifeatures_multiargs" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.TestBaseClass.test_multiindex_in_multifeaturizer">
<span class="sig-name descname"><span class="pre">test_multiindex_in_multifeaturizer</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_multiindex_in_multifeaturizer" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.TestBaseClass.test_multiindex_inplace">
<span class="sig-name descname"><span class="pre">test_multiindex_inplace</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_multiindex_inplace" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.TestBaseClass.test_multiindex_return">
<span class="sig-name descname"><span class="pre">test_multiindex_return</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_multiindex_return" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.TestBaseClass.test_multiple">
<span class="sig-name descname"><span class="pre">test_multiple</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_multiple" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.TestBaseClass.test_multiprocessing_df">
<span class="sig-name descname"><span class="pre">test_multiprocessing_df</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_multiprocessing_df" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.TestBaseClass.test_multitype_multifeat">
<span class="sig-name descname"><span class="pre">test_multitype_multifeat</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_multitype_multifeat" title="Permalink to this definition">¶</a></dt>
<dd><p>Test Multifeaturizer when a featurizer returns a non-numeric type</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.TestBaseClass.test_precheck">
<span class="sig-name descname"><span class="pre">test_precheck</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_precheck" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_base.TestBaseClass.test_stacked_featurizer">
<span class="sig-name descname"><span class="pre">test_stacked_featurizer</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_stacked_featurizer" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.tests.test_conversions">
<span id="matminer-featurizers-tests-test-conversions-module"></span><h2>matminer.featurizers.tests.test_conversions module<a class="headerlink" href="#module-matminer.featurizers.tests.test_conversions" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_conversions.TestConversions">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.tests.test_conversions.</span></span><span class="sig-name descname"><span class="pre">TestConversions</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_conversions.TestConversions" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">PymatgenTest</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_conversions.TestConversions.test_ase_conversion">
<span class="sig-name descname"><span class="pre">test_ase_conversion</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_conversions.TestConversions.test_ase_conversion" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_conversions.TestConversions.test_composition_to_oxidcomposition">
<span class="sig-name descname"><span class="pre">test_composition_to_oxidcomposition</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_conversions.TestConversions.test_composition_to_oxidcomposition" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_conversions.TestConversions.test_composition_to_structurefromMP">
<span class="sig-name descname"><span class="pre">test_composition_to_structurefromMP</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_conversions.TestConversions.test_composition_to_structurefromMP" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_conversions.TestConversions.test_conversion_multiindex">
<span class="sig-name descname"><span class="pre">test_conversion_multiindex</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_conversions.TestConversions.test_conversion_multiindex" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_conversions.TestConversions.test_conversion_multiindex_dynamic">
<span class="sig-name descname"><span class="pre">test_conversion_multiindex_dynamic</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_conversions.TestConversions.test_conversion_multiindex_dynamic" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_conversions.TestConversions.test_conversion_overwrite">
<span class="sig-name descname"><span class="pre">test_conversion_overwrite</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_conversions.TestConversions.test_conversion_overwrite" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_conversions.TestConversions.test_dict_to_object">
<span class="sig-name descname"><span class="pre">test_dict_to_object</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_conversions.TestConversions.test_dict_to_object" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_conversions.TestConversions.test_json_to_object">
<span class="sig-name descname"><span class="pre">test_json_to_object</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_conversions.TestConversions.test_json_to_object" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_conversions.TestConversions.test_pymatgen_general_converter">
<span class="sig-name descname"><span class="pre">test_pymatgen_general_converter</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_conversions.TestConversions.test_pymatgen_general_converter" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_conversions.TestConversions.test_str_to_composition">
<span class="sig-name descname"><span class="pre">test_str_to_composition</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_conversions.TestConversions.test_str_to_composition" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_conversions.TestConversions.test_structure_to_composition">
<span class="sig-name descname"><span class="pre">test_structure_to_composition</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_conversions.TestConversions.test_structure_to_composition" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_conversions.TestConversions.test_structure_to_oxidstructure">
<span class="sig-name descname"><span class="pre">test_structure_to_oxidstructure</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_conversions.TestConversions.test_structure_to_oxidstructure" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_conversions.TestConversions.test_to_istructure">
<span class="sig-name descname"><span class="pre">test_to_istructure</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_conversions.TestConversions.test_to_istructure" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.tests.test_dos">
<span id="matminer-featurizers-tests-test-dos-module"></span><h2>matminer.featurizers.tests.test_dos module<a class="headerlink" href="#module-matminer.featurizers.tests.test_dos" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_dos.DOSFeaturesTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.tests.test_dos.</span></span><span class="sig-name descname"><span class="pre">DOSFeaturesTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_dos.DOSFeaturesTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">PymatgenTest</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_dos.DOSFeaturesTest.setUp">
<span class="sig-name descname"><span class="pre">setUp</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_dos.DOSFeaturesTest.setUp" title="Permalink to this definition">¶</a></dt>
<dd><p>Hook method for setting up the test fixture before exercising it.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_dos.DOSFeaturesTest.test_DOSFeaturizer">
<span class="sig-name descname"><span class="pre">test_DOSFeaturizer</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_dos.DOSFeaturesTest.test_DOSFeaturizer" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_dos.DOSFeaturesTest.test_DopingFermi">
<span class="sig-name descname"><span class="pre">test_DopingFermi</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_dos.DOSFeaturesTest.test_DopingFermi" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_dos.DOSFeaturesTest.test_DosAsymmetry">
<span class="sig-name descname"><span class="pre">test_DosAsymmetry</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_dos.DOSFeaturesTest.test_DosAsymmetry" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_dos.DOSFeaturesTest.test_Hybridization">
<span class="sig-name descname"><span class="pre">test_Hybridization</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_dos.DOSFeaturesTest.test_Hybridization" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_dos.DOSFeaturesTest.test_SiteDOS">
<span class="sig-name descname"><span class="pre">test_SiteDOS</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_dos.DOSFeaturesTest.test_SiteDOS" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.tests.test_function">
<span id="matminer-featurizers-tests-test-function-module"></span><h2>matminer.featurizers.tests.test_function module<a class="headerlink" href="#module-matminer.featurizers.tests.test_function" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_function.TestFunctionFeaturizer">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.tests.test_function.</span></span><span class="sig-name descname"><span class="pre">TestFunctionFeaturizer</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_function.TestFunctionFeaturizer" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_function.TestFunctionFeaturizer.setUp">
<span class="sig-name descname"><span class="pre">setUp</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_function.TestFunctionFeaturizer.setUp" title="Permalink to this definition">¶</a></dt>
<dd><p>Hook method for setting up the test fixture before exercising it.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_function.TestFunctionFeaturizer.test_featurize">
<span class="sig-name descname"><span class="pre">test_featurize</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_function.TestFunctionFeaturizer.test_featurize" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_function.TestFunctionFeaturizer.test_featurize_labels">
<span class="sig-name descname"><span class="pre">test_featurize_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_function.TestFunctionFeaturizer.test_featurize_labels" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_function.TestFunctionFeaturizer.test_helper_functions">
<span class="sig-name descname"><span class="pre">test_helper_functions</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_function.TestFunctionFeaturizer.test_helper_functions" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.tests.test_function.TestFunctionFeaturizer.test_multi_featurizer">
<span class="sig-name descname"><span class="pre">test_multi_featurizer</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.tests.test_function.TestFunctionFeaturizer.test_multi_featurizer" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.tests">
<span id="module-contents"></span><h2>Module contents<a class="headerlink" href="#module-matminer.featurizers.tests" title="Permalink to this heading">¶</a></h2>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">matminer.featurizers.tests package</a><ul>
<li><a class="reference internal" href="#submodules">Submodules</a></li>
<li><a class="reference internal" href="#module-matminer.featurizers.tests.test_bandstructure">matminer.featurizers.tests.test_bandstructure module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_bandstructure.BandstructureFeaturesTest"><code class="docutils literal notranslate"><span class="pre">BandstructureFeaturesTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_bandstructure.BandstructureFeaturesTest.setUp"><code class="docutils literal notranslate"><span class="pre">BandstructureFeaturesTest.setUp()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_bandstructure.BandstructureFeaturesTest.test_BandFeaturizer"><code class="docutils literal notranslate"><span class="pre">BandstructureFeaturesTest.test_BandFeaturizer()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_bandstructure.BandstructureFeaturesTest.test_BranchPointEnergy"><code class="docutils literal notranslate"><span class="pre">BandstructureFeaturesTest.test_BranchPointEnergy()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.tests.test_base">matminer.featurizers.tests.test_base module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.FittableFeaturizer"><code class="docutils literal notranslate"><span class="pre">FittableFeaturizer</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.FittableFeaturizer.citations"><code class="docutils literal notranslate"><span class="pre">FittableFeaturizer.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.FittableFeaturizer.feature_labels"><code class="docutils literal notranslate"><span class="pre">FittableFeaturizer.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.FittableFeaturizer.featurize"><code class="docutils literal notranslate"><span class="pre">FittableFeaturizer.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.FittableFeaturizer.fit"><code class="docutils literal notranslate"><span class="pre">FittableFeaturizer.fit()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.FittableFeaturizer.implementors"><code class="docutils literal notranslate"><span class="pre">FittableFeaturizer.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.MatrixFeaturizer"><code class="docutils literal notranslate"><span class="pre">MatrixFeaturizer</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.MatrixFeaturizer.citations"><code class="docutils literal notranslate"><span class="pre">MatrixFeaturizer.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.MatrixFeaturizer.feature_labels"><code class="docutils literal notranslate"><span class="pre">MatrixFeaturizer.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.MatrixFeaturizer.featurize"><code class="docutils literal notranslate"><span class="pre">MatrixFeaturizer.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.MatrixFeaturizer.implementors"><code class="docutils literal notranslate"><span class="pre">MatrixFeaturizer.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.MultiArgs2"><code class="docutils literal notranslate"><span class="pre">MultiArgs2</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.MultiArgs2.__init__"><code class="docutils literal notranslate"><span class="pre">MultiArgs2.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.MultiArgs2.citations"><code class="docutils literal notranslate"><span class="pre">MultiArgs2.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.MultiArgs2.feature_labels"><code class="docutils literal notranslate"><span class="pre">MultiArgs2.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.MultiArgs2.featurize"><code class="docutils literal notranslate"><span class="pre">MultiArgs2.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.MultiArgs2.implementors"><code class="docutils literal notranslate"><span class="pre">MultiArgs2.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.MultiTypeFeaturizer"><code class="docutils literal notranslate"><span class="pre">MultiTypeFeaturizer</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.MultiTypeFeaturizer.citations"><code class="docutils literal notranslate"><span class="pre">MultiTypeFeaturizer.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.MultiTypeFeaturizer.feature_labels"><code class="docutils literal notranslate"><span class="pre">MultiTypeFeaturizer.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.MultiTypeFeaturizer.featurize"><code class="docutils literal notranslate"><span class="pre">MultiTypeFeaturizer.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.MultiTypeFeaturizer.implementors"><code class="docutils literal notranslate"><span class="pre">MultiTypeFeaturizer.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.MultipleFeatureFeaturizer"><code class="docutils literal notranslate"><span class="pre">MultipleFeatureFeaturizer</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.MultipleFeatureFeaturizer.citations"><code class="docutils literal notranslate"><span class="pre">MultipleFeatureFeaturizer.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.MultipleFeatureFeaturizer.feature_labels"><code class="docutils literal notranslate"><span class="pre">MultipleFeatureFeaturizer.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.MultipleFeatureFeaturizer.featurize"><code class="docutils literal notranslate"><span class="pre">MultipleFeatureFeaturizer.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.MultipleFeatureFeaturizer.implementors"><code class="docutils literal notranslate"><span class="pre">MultipleFeatureFeaturizer.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.SingleFeaturizer"><code class="docutils literal notranslate"><span class="pre">SingleFeaturizer</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.SingleFeaturizer.citations"><code class="docutils literal notranslate"><span class="pre">SingleFeaturizer.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.SingleFeaturizer.feature_labels"><code class="docutils literal notranslate"><span class="pre">SingleFeaturizer.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.SingleFeaturizer.featurize"><code class="docutils literal notranslate"><span class="pre">SingleFeaturizer.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.SingleFeaturizer.implementors"><code class="docutils literal notranslate"><span class="pre">SingleFeaturizer.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.SingleFeaturizerMultiArgs"><code class="docutils literal notranslate"><span class="pre">SingleFeaturizerMultiArgs</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.SingleFeaturizerMultiArgs.featurize"><code class="docutils literal notranslate"><span class="pre">SingleFeaturizerMultiArgs.featurize()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.SingleFeaturizerMultiArgsWithPrecheck"><code class="docutils literal notranslate"><span class="pre">SingleFeaturizerMultiArgsWithPrecheck</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.SingleFeaturizerMultiArgsWithPrecheck.precheck"><code class="docutils literal notranslate"><span class="pre">SingleFeaturizerMultiArgsWithPrecheck.precheck()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.SingleFeaturizerWithPrecheck"><code class="docutils literal notranslate"><span class="pre">SingleFeaturizerWithPrecheck</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.SingleFeaturizerWithPrecheck.precheck"><code class="docutils literal notranslate"><span class="pre">SingleFeaturizerWithPrecheck.precheck()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.TestBaseClass"><code class="docutils literal notranslate"><span class="pre">TestBaseClass</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.TestBaseClass.make_test_data"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.make_test_data()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.TestBaseClass.setUp"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.setUp()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_caching"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_caching()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_dataframe"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_dataframe()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_featurize_many"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_featurize_many()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_fittable"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_fittable()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_ignore_errors"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_ignore_errors()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_indices"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_indices()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_inplace"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_inplace()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_matrix"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_matrix()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_multifeature_no_zero_index"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_multifeature_no_zero_index()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_multifeatures_multiargs"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_multifeatures_multiargs()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_multiindex_in_multifeaturizer"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_multiindex_in_multifeaturizer()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_multiindex_inplace"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_multiindex_inplace()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_multiindex_return"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_multiindex_return()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_multiple"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_multiple()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_multiprocessing_df"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_multiprocessing_df()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_multitype_multifeat"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_multitype_multifeat()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_precheck"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_precheck()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_base.TestBaseClass.test_stacked_featurizer"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_stacked_featurizer()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.tests.test_conversions">matminer.featurizers.tests.test_conversions module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_conversions.TestConversions"><code class="docutils literal notranslate"><span class="pre">TestConversions</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_conversions.TestConversions.test_ase_conversion"><code class="docutils literal notranslate"><span class="pre">TestConversions.test_ase_conversion()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_conversions.TestConversions.test_composition_to_oxidcomposition"><code class="docutils literal notranslate"><span class="pre">TestConversions.test_composition_to_oxidcomposition()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_conversions.TestConversions.test_composition_to_structurefromMP"><code class="docutils literal notranslate"><span class="pre">TestConversions.test_composition_to_structurefromMP()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_conversions.TestConversions.test_conversion_multiindex"><code class="docutils literal notranslate"><span class="pre">TestConversions.test_conversion_multiindex()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_conversions.TestConversions.test_conversion_multiindex_dynamic"><code class="docutils literal notranslate"><span class="pre">TestConversions.test_conversion_multiindex_dynamic()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_conversions.TestConversions.test_conversion_overwrite"><code class="docutils literal notranslate"><span class="pre">TestConversions.test_conversion_overwrite()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_conversions.TestConversions.test_dict_to_object"><code class="docutils literal notranslate"><span class="pre">TestConversions.test_dict_to_object()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_conversions.TestConversions.test_json_to_object"><code class="docutils literal notranslate"><span class="pre">TestConversions.test_json_to_object()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_conversions.TestConversions.test_pymatgen_general_converter"><code class="docutils literal notranslate"><span class="pre">TestConversions.test_pymatgen_general_converter()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_conversions.TestConversions.test_str_to_composition"><code class="docutils literal notranslate"><span class="pre">TestConversions.test_str_to_composition()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_conversions.TestConversions.test_structure_to_composition"><code class="docutils literal notranslate"><span class="pre">TestConversions.test_structure_to_composition()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_conversions.TestConversions.test_structure_to_oxidstructure"><code class="docutils literal notranslate"><span class="pre">TestConversions.test_structure_to_oxidstructure()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_conversions.TestConversions.test_to_istructure"><code class="docutils literal notranslate"><span class="pre">TestConversions.test_to_istructure()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.tests.test_dos">matminer.featurizers.tests.test_dos module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_dos.DOSFeaturesTest"><code class="docutils literal notranslate"><span class="pre">DOSFeaturesTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_dos.DOSFeaturesTest.setUp"><code class="docutils literal notranslate"><span class="pre">DOSFeaturesTest.setUp()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_dos.DOSFeaturesTest.test_DOSFeaturizer"><code class="docutils literal notranslate"><span class="pre">DOSFeaturesTest.test_DOSFeaturizer()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_dos.DOSFeaturesTest.test_DopingFermi"><code class="docutils literal notranslate"><span class="pre">DOSFeaturesTest.test_DopingFermi()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_dos.DOSFeaturesTest.test_DosAsymmetry"><code class="docutils literal notranslate"><span class="pre">DOSFeaturesTest.test_DosAsymmetry()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_dos.DOSFeaturesTest.test_Hybridization"><code class="docutils literal notranslate"><span class="pre">DOSFeaturesTest.test_Hybridization()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_dos.DOSFeaturesTest.test_SiteDOS"><code class="docutils literal notranslate"><span class="pre">DOSFeaturesTest.test_SiteDOS()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.tests.test_function">matminer.featurizers.tests.test_function module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_function.TestFunctionFeaturizer"><code class="docutils literal notranslate"><span class="pre">TestFunctionFeaturizer</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_function.TestFunctionFeaturizer.setUp"><code class="docutils literal notranslate"><span class="pre">TestFunctionFeaturizer.setUp()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_function.TestFunctionFeaturizer.test_featurize"><code class="docutils literal notranslate"><span class="pre">TestFunctionFeaturizer.test_featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_function.TestFunctionFeaturizer.test_featurize_labels"><code class="docutils literal notranslate"><span class="pre">TestFunctionFeaturizer.test_featurize_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_function.TestFunctionFeaturizer.test_helper_functions"><code class="docutils literal notranslate"><span class="pre">TestFunctionFeaturizer.test_helper_functions()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.tests.test_function.TestFunctionFeaturizer.test_multi_featurizer"><code class="docutils literal notranslate"><span class="pre">TestFunctionFeaturizer.test_multi_featurizer()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.tests">Module contents</a></li>
</ul>
</li>
</ul>

  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/matminer.featurizers.tests.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer.featurizers.tests package</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2015, Anubhav Jain.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.3.0.
    </div>

  </body>
</html>