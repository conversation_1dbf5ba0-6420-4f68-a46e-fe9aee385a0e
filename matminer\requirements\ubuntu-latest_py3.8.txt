#
# This file is autogenerated by pip-compile with Python 3.8
# by the following command:
#
#    pip-compile --output-file=requirements/ubuntu-latest_py3.8.txt
#
certifi==2023.7.22
    # via requests
charset-normalizer==3.2.0
    # via requests
contourpy==1.1.0
    # via matplotlib
cycler==0.11.0
    # via matplotlib
dnspython==2.4.2
    # via pymongo
emmet-core==0.67.5
    # via mp-api
fonttools==4.42.1
    # via matplotlib
future==0.18.3
    # via
    #   matminer (setup.py)
    #   uncertainties
idna==3.4
    # via requests
importlib-resources==6.0.1
    # via matplotlib
joblib==1.3.2
    # via
    #   pymatgen
    #   scikit-learn
kiwisolver==1.4.5
    # via matplotlib
latexcodec==2.0.1
    # via pybtex
matplotlib==3.7.2
    # via pymatgen
monty==2023.9.5
    # via
    #   emmet-core
    #   matminer (setup.py)
    #   mp-api
    #   pymatgen
mp-api==0.35.1
    # via pymatgen
mpmath==1.3.0
    # via sympy
msgpack==1.0.5
    # via mp-api
networkx==3.1
    # via pymatgen
numpy==1.24.4
    # via
    #   contourpy
    #   matminer (setup.py)
    #   matplotlib
    #   pandas
    #   pymatgen
    #   scikit-learn
    #   scipy
    #   spglib
packaging==23.1
    # via
    #   matplotlib
    #   plotly
palettable==3.3.3
    # via pymatgen
pandas==2.0.3
    # via
    #   matminer (setup.py)
    #   pymatgen
pillow==10.0.0
    # via matplotlib
plotly==5.16.1
    # via pymatgen
pybtex==0.24.0
    # via
    #   emmet-core
    #   pymatgen
pydantic==1.10.12
    # via
    #   emmet-core
    #   pymatgen
pymatgen==2023.8.10
    # via
    #   emmet-core
    #   matminer (setup.py)
    #   mp-api
pymongo==4.5.0
    # via matminer (setup.py)
pyparsing==3.0.9
    # via matplotlib
python-dateutil==2.8.2
    # via
    #   matplotlib
    #   pandas
pytz==2023.3.post1
    # via pandas
pyyaml==6.0.1
    # via pybtex
requests==2.31.0
    # via
    #   matminer (setup.py)
    #   mp-api
    #   pymatgen
ruamel-yaml==0.17.32
    # via pymatgen
ruamel-yaml-clib==0.2.7
    # via ruamel-yaml
scikit-learn==1.3.0
    # via matminer (setup.py)
scipy==1.10.1
    # via
    #   pymatgen
    #   scikit-learn
six==1.16.0
    # via
    #   latexcodec
    #   pybtex
    #   python-dateutil
spglib==2.0.2
    # via
    #   emmet-core
    #   pymatgen
sympy==1.12
    # via
    #   matminer (setup.py)
    #   pymatgen
tabulate==0.9.0
    # via pymatgen
tenacity==8.2.3
    # via plotly
threadpoolctl==3.2.0
    # via scikit-learn
tqdm==4.66.1
    # via
    #   matminer (setup.py)
    #   pymatgen
typing-extensions==4.7.1
    # via
    #   emmet-core
    #   mp-api
    #   pydantic
tzdata==2023.3
    # via pandas
uncertainties==3.1.7
    # via pymatgen
urllib3==2.0.4
    # via requests
zipp==3.16.2
    # via importlib-resources

# The following packages are considered to be unsafe in a requirements file:
# setuptools
