# reproducibility
deterministic: True # 启用确定性训练模式，保证相同配置下的训练结果完全一致
random_seed: 42 # 设置随机种子，确保实验可复现

# training

pl_trainer:
  fast_dev_run: False # Enable this for debug purposes
  # devices: [5] # 使用GPU设备5进行训练
  accelerator: 'gpu'
  # accelerator: ddp
  precision: 32
  # max_steps: 10000
  max_epochs: 2000 # ${optim.train_max_epochs}
  accumulate_grad_batches: 1  # 梯度累积，设置为1表示每个batch更新一次梯度
  num_sanity_val_steps: 2 # 在训练前进行验证的步数，通常用于检查数据加载和模型是否正常
  gradient_clip_val: 0.5 # 梯度裁剪的阈值，防止梯度爆炸
  gradient_clip_algorithm: value # 梯度裁剪算法，'value'表示按值裁剪
  profiler: simple # 使用简单的性能分析器

monitor_metric: 'val_loss' # 监控的指标，用于早停和模型检查点
monitor_metric_mode: 'min' # 监控指标的模式，'min'表示寻找最小值

early_stopping:
  patience: ${data.early_stopping_patience} # 60
  verbose: False # 是否打印早停信息

full_finetuning: True

model_checkpoints:
  save_top_k: 4 # 保存最好的4个模型
  verbose: False # 是否打印保存信息
  every_n_epochs: 5 # 每隔5个epoch保存一次模型
  save_last: False # 是否保存最后一个模型