# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.

import json
from pathlib import Path
from typing import Literal
import sys
sys.path.append('.')
# print(sys.path)
import fire
import numpy as np
import tqdm
from symmcd.common.utils import load_structures
from symmcd.common.utils import get_device
from scripts.evaluation.evaluate import evaluate
from scripts.evaluation.reference.reference_dataset_serializer import LMDBGZSerializer
from scripts.evaluation.utils.structure_matcher import (
    DefaultDisorderedStructureMatcher,
    DefaultOrderedStructureMatcher,
)
import time

def main(
    structures_path: str,
    relax: bool = True,
    energies_path: str | None = None,
    structure_matcher: Literal["ordered", "disordered"] = "disordered",
    save_as: str | None = None,
    potential_load_path: (
        Literal["MatterSim-v1.0.0-1M.pth", "MatterSim-v1.0.0-5M.pth"] | None
    ) = None,
    reference_dataset_path: str | None = None,
    device: str = str(get_device()),
    structures_output_path: str | None = None,
):
    structures = load_structures(Path(structures_path))
    print('The quantity of crystal structures:', len(structures))

    energies = np.load(energies_path) if energies_path else None
    structure_matcher = (
        DefaultDisorderedStructureMatcher()
        if structure_matcher == "disordered"
        else DefaultOrderedStructureMatcher()
    )
    reference = None
    start_time = time.time()
    if reference_dataset_path:
        reference = LMDBGZSerializer().deserialize(reference_dataset_path)
    print('Reference dataset loading time:', time.time() - start_time)

    metrics = evaluate(
        structures=structures,
        relax=relax,
        energies=energies,
        structure_matcher=structure_matcher,
        save_as=save_as,
        potential_load_path=potential_load_path,
        reference=reference,
        device=device,
        structures_output_path=structures_output_path,
    )
    print(json.dumps(metrics, indent=2))
    print('Total time:', time.time() - start_time)

def _main():
    fire.Fire(main)


if __name__ == "__main__":
    _main()
