# Originally from http://github.com/ademl/predict_Etot_dHf/blob/master/elemental_properties.py

# data tables of elemental properties used for Etot/dHf model
# values omitted elements without FERE values (e.g. noble gases, Tc, Ru,...)
# 2015-10-05 A. <PERSON>

# See also: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Predicting
# density functional theory total energies and enthalpies of formation of
# metal-nonmetal compounds by linear regression, Phys. Rev. B - Condens.
# Matter Mater. Phys. 93 (2016).

###########################
# TMs considered as magnetic
magn_elem = [
    "Ti",
    "V",
    "Cr",
    "Mn",
    "Fe",
    "Co",
    "Ni",
    "Cu",
    "Nb",
    "Mo",
    "Tc",
    "Ru",
    "Rh",
    "Pd",
    "Ag",
    "Ta",
    "W",
    "<PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "Pt",
]

###########################
# atomic number
atom_num = {
    "H": 1,
    "<PERSON>": 2,
    "<PERSON>": 3,
    "<PERSON>": 4,
    "B": 5,
    "<PERSON>": 6,
    "N": 7,
    "O": 8,
    "F": 9,
    "Ne": 10,
    "Na": 11,
    "Mg": 12,
    "Al": 13,
    "Si": 14,
    "P": 15,
    "S": 16,
    "<PERSON>l": 17,
    "Ar": 18,
    "K": 19,
    "<PERSON>a": 20,
    "Sc": 21,
    "Ti": 22,
    "V": 23,
    "Cr": 24,
    "Mn": 25,
    "Fe": 26,
    "Co": 27,
    "Ni": 28,
    "Cu": 29,
    "Zn": 30,
    "Ga": 31,
    "Ge": 32,
    "As": 33,
    "Se": 34,
    "Br": 35,
    "Kr": 36,
    "Rb": 37,
    "Sr": 38,
    "Y": 39,
    "Zr": 40,
    "Nb": 41,
    "Mo": 42,
    "Tc": 43,
    "Ru": 44,
    "Rh": 45,
    "Pd": 46,
    "Ag": 47,
    "Cd": 48,
    "In": 49,
    "Sn": 50,
    "Sb": 51,
    "Te": 52,
    "I": 53,
    "Xe": 54,
    "Cs": 55,
    "Ba": 56,
    "La": 57,
    "Ce": 58,
    "Pr": 59,
    "Nd": 60,
    "Pm": 61,
    "Sm": 62,
    "Eu": 63,
    "Gd": 64,
    "Tb": 65,
    "Dy": 66,
    "Ho": 67,
    "Er": 68,
    "Tm": 69,
    "Yb": 70,
    "Lu": 71,
    "Hf": 72,
    "Ta": 73,
    "W": 74,
    "Re": 75,
    "Os": 76,
    "Ir": 77,
    "Pt": 78,
    "Au": 79,
    "Hg": 80,
    "Tl": 81,
    "Pb": 82,
    "Bi": 83,
    "Po": 84,
    "At": 85,
    "Rn": 86,
    "Fr": 87,
    "Ra": 88,
    "Ac": 89,
    "Th": 90,
    "Pa": 91,
    "U": 92,
    "Np": 93,
    "Pu": 94,
    "Am": 95,
    "Cm": 96,
    "Bk": 97,
    "Cf": 98,
    "Es": 99,
    "Fm": 100,
    "Md": 101,
    "No": 102,
    "Lr": 103,
    "Rf": 104,
    "Db": 105,
    "Sg": 106,
    "Bh": 107,
    "Hs": 108,
    "Mt": 109,
}

# atomic mass
atom_mass = {
    "H": 1.00794,
    "He": 4.002602,
    "Li": 6.941,
    "Be": 9.012182,
    "B": 10.811,
    "C": 12.0107,
    "N": 14.0067,
    "O": 15.9994,
    "F": 18.9984032,
    "Ne": 20.1797,
    "Na": 22.98976928,
    "Mg": 24.305,
    "Al": 26.9815386,
    "Si": 28.0855,
    "P": 30.973762,
    "S": 32.065,
    "Cl": 35.453,
    "Ar": 39.948,
    "K": 39.0983,
    "Ca": 40.078,
    "Sc": 44.955912,
    "Ti": 47.867,
    "V": 50.9415,
    "Cr": 51.9961,
    "Mn": 54.938045,
    "Fe": 55.845,
    "Co": 58.933195,
    "Ni": 58.6934,
    "Cu": 63.546,
    "Zn": 65.38,
    "Ga": 69.723,
    "Ge": 72.64,
    "As": 74.9216,
    "Se": 78.96,
    "Br": 79.904,
    "Kr": 83.798,
    "Rb": 85.4678,
    "Sr": 87.62,
    "Y": 88.90585,
    "Zr": 91.224,
    "Nb": 92.90638,
    "Mo": 95.96,
    "Tc": 98,
    "Ru": 101.07,
    "Rh": 102.9055,
    "Pd": 106.42,
    "Ag": 107.8682,
    "Cd": 112.411,
    "In": 114.818,
    "Sn": 118.71,
    "Sb": 121.76,
    "Te": 127.6,
    "I": 126.90447,
    "Xe": 131.293,
    "Cs": 132.9054519,
    "Ba": 137.327,
    "La": 138.90547,
    "Ce": 140.116,
    "Pr": 140.90765,
    "Nd": 144.242,
    "Pm": 145,
    "Sm": 150.36,
    "Eu": 151.964,
    "Gd": 157.25,
    "Tb": 158.92535,
    "Dy": 162.5,
    "Ho": 164.93032,
    "Er": 167.259,
    "Tm": 168.93421,
    "Yb": 173.054,
    "Lu": 174.9668,
    "Hf": 178.49,
    "Ta": 180.94788,
    "W": 183.84,
    "Re": 186.207,
    "Os": 190.23,
    "Ir": 192.217,
    "Pt": 195.084,
    "Au": 196.966569,
    "Hg": 200.59,
    "Tl": 204.3833,
    "Pb": 207.2,
    "Bi": 208.9804,
    "Po": 209,
    "At": 210,
    "Rn": 222,
    "Fr": 223,
    "Ra": 226,
    "Ac": 227,
    "Th": 232.03806,
    "Pa": 231.03588,
    "U": 238.02891,
    "Np": 237,
    "Pu": 244,
    "Am": 243,
    "Cm": 247,
    "Bk": 247,
    "Cf": 251,
    "Es": 252,
    "Fm": 257,
    "Md": 258,
    "No": 259,
    "Lr": 262,
    "Rf": 267,
    "Db": 268,
    "Sg": 271,
    "Bh": 272,
    "Hs": 270,
    "Mt": 276,
}

# row number
row_num = {
    "H": 1,
    "He": 1,
    "Li": 2,
    "Be": 2,
    "B": 2,
    "C": 2,
    "N": 2,
    "O": 2,
    "F": 2,
    "Ne": 2,
    "Na": 3,
    "Mg": 3,
    "Al": 3,
    "Si": 3,
    "P": 3,
    "S": 3,
    "Cl": 3,
    "Ar": 3,
    "K": 4,
    "Ca": 4,
    "Sc": 4,
    "Ti": 4,
    "V": 4,
    "Cr": 4,
    "Mn": 4,
    "Fe": 4,
    "Co": 4,
    "Ni": 4,
    "Cu": 4,
    "Zn": 4,
    "Ga": 4,
    "Ge": 4,
    "As": 4,
    "Se": 4,
    "Br": 4,
    "Kr": 4,
    "Rb": 5,
    "Sr": 5,
    "Y": 5,
    "Zr": 5,
    "Nb": 5,
    "Mo": 5,
    "Tc": 5,
    "Ru": 5,
    "Rh": 5,
    "Pd": 5,
    "Ag": 5,
    "Cd": 5,
    "In": 5,
    "Sn": 5,
    "Sb": 5,
    "Te": 5,
    "I": 5,
    "Xe": 5,
    "Cs": 6,
    "Ba": 6,
    "La": 6,
    "Ce": 6,
    "Pr": 6,
    "Nd": 6,
    "Pm": 6,
    "Sm": 6,
    "Eu": 6,
    "Gd": 6,
    "Tb": 6,
    "Dy": 6,
    "Ho": 6,
    "Er": 6,
    "Tm": 6,
    "Yb": 6,
    "Lu": 6,
    "Hf": 6,
    "Ta": 6,
    "W": 6,
    "Re": 6,
    "Os": 6,
    "Ir": 6,
    "Pt": 6,
    "Au": 6,
    "Hg": 6,
    "Tl": 6,
    "Pb": 6,
    "Bi": 6,
    "Po": 6,
    "At": 6,
    "Rn": 6,
    "Fr": 7,
    "Ra": 7,
    "Ac": 7,
    "Th": 7,
    "Pa": 7,
    "U": 7,
    "Np": 7,
    "Pu": 7,
    "Am": 7,
    "Cm": 7,
    "Bk": 7,
    "Cf": 7,
    "Es": 7,
    "Fm": 7,
    "Md": 7,
    "No": 7,
    "Lr": 7,
    "Rf": 7,
    "Db": 7,
    "Sg": 7,
    "Bh": 7,
    "Hs": 7,
    "Mt": 7,
}

# column number
col_num = {
    "H": 1,
    "He": 2,
    "Li": 1,
    "Be": 2,
    "B": 13,
    "C": 14,
    "N": 15,
    "O": 16,
    "F": 17,
    "Ne": 18,
    "Na": 1,
    "Mg": 2,
    "Al": 13,
    "Si": 14,
    "P": 15,
    "S": 16,
    "Cl": 17,
    "Ar": 18,
    "K": 1,
    "Ca": 2,
    "Sc": 3,
    "Ti": 4,
    "V": 5,
    "Cr": 6,
    "Mn": 7,
    "Fe": 8,
    "Co": 9,
    "Ni": 10,
    "Cu": 11,
    "Zn": 12,
    "Ga": 13,
    "Ge": 14,
    "As": 15,
    "Se": 16,
    "Br": 17,
    "Kr": 18,
    "Rb": 1,
    "Sr": 2,
    "Y": 3,
    "Zr": 4,
    "Nb": 5,
    "Mo": 6,
    "Tc": 7,
    "Ru": 8,
    "Rh": 9,
    "Pd": 10,
    "Ag": 11,
    "Cd": 12,
    "In": 13,
    "Sn": 14,
    "Sb": 15,
    "Te": 16,
    "I": 17,
    "Xe": 18,
    "Cs": 1,
    "Ba": 2,
    "La": 3,
    "Ce": float("NaN"),
    "Pr": float("NaN"),
    "Nd": float("NaN"),
    "Pm": float("NaN"),
    "Sm": float("NaN"),
    "Eu": float("NaN"),
    "Gd": float("NaN"),
    "Tb": float("NaN"),
    "Dy": float("NaN"),
    "Ho": float("NaN"),
    "Er": float("NaN"),
    "Tm": float("NaN"),
    "Yb": float("NaN"),
    "Lu": float("NaN"),
    "Hf": 4,
    "Ta": 5,
    "W": 6,
    "Re": 7,
    "Os": 8,
    "Ir": 9,
    "Pt": 10,
    "Au": 11,
    "Hg": 12,
    "Tl": 13,
    "Pb": 14,
    "Bi": 15,
    "Po": 16,
    "At": 17,
    "Rn": 18,
    "Fr": 1,
    "Ra": 2,
    "Ac": 3,
    "Th": float("NaN"),
    "Pa": float("NaN"),
    "U": float("NaN"),
    "Np": float("NaN"),
    "Pu": float("NaN"),
    "Am": float("NaN"),
    "Cm": float("NaN"),
    "Bk": float("NaN"),
    "Cf": float("NaN"),
    "Es": float("NaN"),
    "Fm": float("NaN"),
    "Md": float("NaN"),
    "No": float("NaN"),
    "Lr": float("NaN"),
    "Rf": 4,
    "Db": 5,
    "Sg": 6,
    "Bh": 7,
    "Hs": 8,
    "Mt": 9,
}

# valence electrons by column
valence_e = {
    1: {"s": 1, "p": 0, "d": 0},
    2: {"s": 2, "p": 0, "d": 0},
    3: {"s": 2, "p": 0, "d": 1},
    4: {"s": 2, "p": 0, "d": 2},
    5: {"s": 2, "p": 0, "d": 3},
    6: {"s": 2, "p": 0, "d": 4},
    7: {"s": 2, "p": 0, "d": 5},
    8: {"s": 2, "p": 0, "d": 6},
    9: {"s": 2, "p": 0, "d": 7},
    10: {"s": 2, "p": 0, "d": 8},
    11: {"s": 2, "p": 0, "d": 9},
    12: {"s": 2, "p": 0, "d": 10},
    13: {"s": 2, "p": 1, "d": 0},
    14: {"s": 2, "p": 2, "d": 0},
    15: {"s": 2, "p": 3, "d": 0},
    16: {"s": 2, "p": 4, "d": 0},
    17: {"s": 2, "p": 5, "d": 0},
}

# atomic radius (pm)
atom_radius = {
    "H": 25,
    "He": float("NaN"),
    "Li": 145,
    "Be": 105,
    "B": 85,
    "C": 70,
    "N": 65,
    "O": 60,
    "F": 50,
    "Ne": float("NaN"),
    "Na": 180,
    "Mg": 150,
    "Al": 125,
    "Si": 110,
    "P": 100,
    "S": 100,
    "Cl": 100,
    "Ar": float("NaN"),
    "K": 220,
    "Ca": 180,
    "Sc": 160,
    "Ti": 140,
    "V": 135,
    "Cr": 140,
    "Mn": 140,
    "Fe": 140,
    "Co": 135,
    "Ni": 135,
    "Cu": 135,
    "Zn": 135,
    "Ga": 130,
    "Ge": 125,
    "As": 115,
    "Se": 115,
    "Br": 115,
    "Kr": float("NaN"),
    "Rb": 235,
    "Sr": 200,
    "Y": 180,
    "Zr": 155,
    "Nb": 145,
    "Mo": 145,
    "Tc": 135,
    "Ru": 130,
    "Rh": 135,
    "Pd": 140,
    "Ag": 160,
    "Cd": 155,
    "In": 155,
    "Sn": 145,
    "Sb": 145,
    "Te": 140,
    "I": 140,
    "Xe": float("NaN"),
    "Cs": 260,
    "Ba": 215,
    "La": 195,
    "Ce": 185,
    "Pr": 185,
    "Nd": 185,
    "Pm": 185,
    "Sm": 185,
    "Eu": 185,
    "Gd": 180,
    "Tb": 175,
    "Dy": 175,
    "Ho": 175,
    "Er": 175,
    "Tm": 175,
    "Yb": 175,
    "Lu": 175,
    "Hf": 155,
    "Ta": 145,
    "W": 135,
    "Re": 135,
    "Os": 130,
    "Ir": 135,
    "Pt": 135,
    "Au": 135,
    "Hg": 150,
    "Tl": 190,
    "Pb": 180,
    "Bi": 160,
    "Po": 190,
    "At": float("NaN"),
    "Rn": float("NaN"),
    "Fr": float("NaN"),
    "Ra": 215,
    "Ac": 195,
    "Th": 180,
    "Pa": 180,
    "U": 175,
    "Np": 175,
    "Pu": 175,
    "Am": 175,
    "Cm": float("NaN"),
    "Bk": float("NaN"),
    "Cf": float("NaN"),
    "Es": float("NaN"),
    "Fm": float("NaN"),
    "Md": float("NaN"),
    "No": float("NaN"),
    "Lr": float("NaN"),
    "Rf": float("NaN"),
    "Db": float("NaN"),
    "Sg": float("NaN"),
    "Bh": float("NaN"),
    "Hs": float("NaN"),
    "Mt": float("NaN"),
}

# molar volume (cm^3/mol)
molar_vol = {
    "H": 11.42,
    "He": 21,
    "Li": 13.02,
    "Be": 4.85,
    "B": 4.39,
    "C": 5.29,
    "N": 13.54,
    "O": 17.36,
    "F": 11.2,
    "Ne": 13.23,
    "Na": 23.78,
    "Mg": 14,
    "Al": 10,
    "Si": 12.06,
    "P": 17.02,
    "S": 15.53,
    "Cl": 17.39,
    "Ar": 22.56,
    "K": 45.94,
    "Ca": 26.2,
    "Sc": 15,
    "Ti": 10.64,
    "V": 8.32,
    "Cr": 7.23,
    "Mn": 7.35,
    "Fe": 7.09,
    "Co": 6.67,
    "Ni": 6.59,
    "Cu": 7.11,
    "Zn": 9.16,
    "Ga": 11.8,
    "Ge": 13.63,
    "As": 12.95,
    "Se": 16.42,
    "Br": 19.78,
    "Kr": 27.99,
    "Rb": 55.76,
    "Sr": 33.94,
    "Y": 19.88,
    "Zr": 14.02,
    "Nb": 10.83,
    "Mo": 9.38,
    "Tc": 8.63,
    "Ru": 8.17,
    "Rh": 8.28,
    "Pd": 8.56,
    "Ag": 10.27,
    "Cd": 13,
    "In": 15.76,
    "Sn": 16.29,
    "Sb": 18.19,
    "Te": 20.46,
    "I": 25.72,
    "Xe": 35.92,
    "Cs": 70.94,
    "Ba": 38.16,
    "La": 22.39,
    "Ce": 20.69,
    "Pr": 20.8,
    "Nd": 20.59,
    "Pm": 20.23,
    "Sm": 19.98,
    "Eu": 28.97,
    "Gd": 19.9,
    "Tb": 19.3,
    "Dy": 19.01,
    "Ho": 18.74,
    "Er": 18.46,
    "Tm": 19.1,
    "Yb": 24.84,
    "Lu": 17.78,
    "Hf": 13.44,
    "Ta": 10.85,
    "W": 9.47,
    "Re": 8.86,
    "Os": 8.42,
    "Ir": 8.52,
    "Pt": 9.09,
    "Au": 10.21,
    "Hg": 14.09,
    "Tl": 17.22,
    "Pb": 18.26,
    "Bi": 21.31,
    "Po": 22.97,
    "At": float("NaN"),
    "Rn": 50.5,
    "Fr": float("NaN"),
    "Ra": 41.09,
    "Ac": 22.55,
    "Th": 19.8,
    "Pa": 15.18,
    "U": 12.49,
    "Np": 11.59,
    "Pu": 12.29,
    "Am": 17.63,
    "Cm": 18.05,
    "Bk": 16.84,
    "Cf": 16.5,
    "Es": 28.52,
    "Fm": float("NaN"),
    "Md": float("NaN"),
    "No": float("NaN"),
    "Lr": float("NaN"),
    "Rf": float("NaN"),
    "Db": float("NaN"),
    "Sg": float("NaN"),
    "Bh": float("NaN"),
    "Hs": float("NaN"),
    "Mt": float("NaN"),
}

# latent heat of fusion (J/mol)
heat_fusion = {
    "H": 558,
    "He": 20,
    "Li": 3000,
    "Be": 7950,
    "B": 50000,
    "C": float("NaN"),
    "N": 360,
    "O": 222,
    "F": 260,
    "Ne": 340,
    "Na": 2600,
    "Mg": 8700,
    "Al": 10700,
    "Si": 50200,
    "P": 640,
    "S": 1730,
    "Cl": 3200,
    "Ar": 1180,
    "K": 2330,
    "Ca": 8540,
    "Sc": 16000,
    "Ti": 18700,
    "V": 22800,
    "Cr": 20500,
    "Mn": 13200,
    "Fe": 13800,
    "Co": 16200,
    "Ni": 17200,
    "Cu": 13100,
    "Zn": 7350,
    "Ga": 5590,
    "Ge": 31800,
    "As": 27700,
    "Se": 5400,
    "Br": 5800,
    "Kr": 1640,
    "Rb": 2190,
    "Sr": 8000,
    "Y": 11400,
    "Zr": 21000,
    "Nb": 26800,
    "Mo": 36000,
    "Tc": 23000,
    "Ru": 25700,
    "Rh": 21700,
    "Pd": 16700,
    "Ag": 11300,
    "Cd": 6300,
    "In": 3260,
    "Sn": 7000,
    "Sb": 19700,
    "Te": 17500,
    "I": 7760,
    "Xe": 2300,
    "Cs": 2090,
    "Ba": 8000,
    "La": 6200,
    "Ce": 5500,
    "Pr": 6900,
    "Nd": 7100,
    "Pm": 7700,
    "Sm": 8600,
    "Eu": 9200,
    "Gd": 10000,
    "Tb": 10800,
    "Dy": 11100,
    "Ho": 17000,
    "Er": 19900,
    "Tm": 16800,
    "Yb": 7700,
    "Lu": 22000,
    "Hf": 25500,
    "Ta": 36000,
    "W": 35000,
    "Re": 33000,
    "Os": 31000,
    "Ir": 26000,
    "Pt": 20000,
    "Au": 12500,
    "Hg": 2290,
    "Tl": 4200,
    "Pb": 4770,
    "Bi": 10900,
    "Po": 13000,
    "At": 6000,
    "Rn": 3000,
    "Fr": 2000,
    "Ra": 8000,
    "Ac": 14000,
    "Th": 16000,
    "Pa": 15000,
    "U": 14000,
    "Np": 10000,
    "Pu": float("NaN"),
    "Am": float("NaN"),
    "Cm": float("NaN"),
    "Bk": float("NaN"),
    "Cf": float("NaN"),
    "Es": float("NaN"),
    "Fm": float("NaN"),
    "Md": float("NaN"),
    "No": float("NaN"),
    "Lr": float("NaN"),
    "Rf": float("NaN"),
    "Db": float("NaN"),
    "Sg": float("NaN"),
    "Bh": float("NaN"),
    "Hs": float("NaN"),
    "Mt": float("NaN"),
}

# melting point (K)
mp = {
    "H": 14.01,
    "He": 0.95,
    "Li": 453.69,
    "Be": 1560,
    "B": 2349,
    "C": 3800,
    "N": 63.05,
    "O": 54.8,
    "F": 53.53,
    "Ne": 24.56,
    "Na": 370.87,
    "Mg": 923,
    "Al": 933.47,
    "Si": 1687,
    "P": 317.3,
    "S": 388.36,
    "Cl": 171.6,
    "Ar": 83.8,
    "K": 336.53,
    "Ca": 1115,
    "Sc": 1814,
    "Ti": 1941,
    "V": 2183,
    "Cr": 2180,
    "Mn": 1519,
    "Fe": 1811,
    "Co": 1768,
    "Ni": 1728,
    "Cu": 1357.77,
    "Zn": 692.68,
    "Ga": 302.91,
    "Ge": 1211.4,
    "As": 1090,
    "Se": 494,
    "Br": 265.8,
    "Kr": 115.79,
    "Rb": 312.46,
    "Sr": 1050,
    "Y": 1799,
    "Zr": 2128,
    "Nb": 2750,
    "Mo": 2896,
    "Tc": 2430,
    "Ru": 2607,
    "Rh": 2237,
    "Pd": 1828.05,
    "Ag": 1234.93,
    "Cd": 594.22,
    "In": 429.75,
    "Sn": 505.08,
    "Sb": 903.78,
    "Te": 722.66,
    "I": 386.85,
    "Xe": 161.4,
    "Cs": 301.59,
    "Ba": 1000,
    "La": 1193,
    "Ce": 1068,
    "Pr": 1208,
    "Nd": 1297,
    "Pm": 1373,
    "Sm": 1345,
    "Eu": 1099,
    "Gd": 1585,
    "Tb": 1629,
    "Dy": 1680,
    "Ho": 1734,
    "Er": 1802,
    "Tm": 1818,
    "Yb": 1097,
    "Lu": 1925,
    "Hf": 2506,
    "Ta": 3290,
    "W": 3695,
    "Re": 3459,
    "Os": 3306,
    "Ir": 2739,
    "Pt": 2041.4,
    "Au": 1337.33,
    "Hg": 234.32,
    "Tl": 577,
    "Pb": 600.61,
    "Bi": 544.4,
    "Po": 527,
    "At": 575,
    "Rn": 202,
    "Fr": 300,
    "Ra": 973,
}

# boiling point T (K)
bp = {
    "H": 20.28,
    "He": 4.22,
    "Li": 1615,
    "Be": 2742,
    "B": 4200,
    "C": 4300,
    "N": 77.36,
    "O": 90.2,
    "F": 85.03,
    "Ne": 27.07,
    "Na": 1156,
    "Mg": 1363,
    "Al": 2792,
    "Si": 3173,
    "P": 550,
    "S": 717.87,
    "Cl": 239.11,
    "Ar": 87.3,
    "K": 1032,
    "Ca": 1757,
    "Sc": 3103,
    "Ti": 3560,
    "V": 3680,
    "Cr": 2944,
    "Mn": 2334,
    "Fe": 3134,
    "Co": 3200,
    "Ni": 3186,
    "Cu": 3200,
    "Zn": 1180,
    "Ga": 2477,
    "Ge": 3093,
    "As": 887,
    "Se": 958,
    "Br": 332,
    "Kr": 119.93,
    "Rb": 961,
    "Sr": 1655,
    "Y": 3609,
    "Zr": 4682,
    "Nb": 5017,
    "Mo": 4912,
    "Tc": 4538,
    "Ru": 4423,
    "Rh": 3968,
    "Pd": 3236,
    "Ag": 2435,
    "Cd": 1040,
    "In": 2345,
    "Sn": 2875,
    "Sb": 1860,
    "Te": 1261,
    "I": 457.4,
    "Xe": 165.1,
    "Cs": 944,
    "Ba": 2143,
    "La": 3743,
    "Ce": 3633,
    "Pr": 3563,
    "Nd": 3373,
    "Pm": 3273,
    "Sm": 2076,
    "Eu": 1800,
    "Gd": 3523,
    "Tb": 3503,
    "Dy": 2840,
    "Ho": 2993,
    "Er": 3141,
    "Tm": 2223,
    "Yb": 1469,
    "Lu": 3675,
    "Hf": 4876,
    "Ta": 5731,
    "W": 5828,
    "Re": 5869,
    "Os": 5285,
    "Ir": 4701,
    "Pt": 4098,
    "Au": 3129,
    "Hg": 629.88,
    "Tl": 1746,
    "Pb": 2022,
    "Bi": 1837,
    "Po": 1235,
    "At": float("NaN"),
    "Rn": 211.3,
    "Fr": float("NaN"),
    "Ra": 2010,
}

# heat capacity of the elements
# from CRC handbook: 25C and thermo stable standard state, 1 bar
# units: J/mol.K
heat_cap = {
    "Li": 24.86,
    "Be": 16.443,
    "B": 11.087,
    "C": 6.115,
    "N": 29.124,
    "O": 29.378,
    "F": 31.304,
    "Na": 28.23,
    "Mg": 24.869,
    "Al": 24.2,
    "Si": 19.789,
    "P": 23.824,
    "S": 22.75,
    "Cl": 33.949,
    "K": 29.60,
    "Ca": 25.929,
    "Sc": 25.52,
    "Ti": 25.060,
    "V": 24.89,
    "Cr": 23.35,
    "Mn": 26.32,
    "Fe": 25.1,
    "Co": 24.81,
    "Ni": 26.07,
    "Cu": 24.440,
    "Zn": 25.39,
    "Ga": 25.86,
    "Ge": 23.22,
    "As": 24.64,
    "Se": 25.363,
    "Br": 75.69,
    "Rb": 31.060,
    "Sr": 26.4,
    "Y": 26.53,
    "Zr": 25.36,
    "Nb": 24.6,
    "Mo": 24.06,
    "Rh": 24.06,
    "Pd": 25.98,
    "Ag": 25.35,
    "Cd": 26.02,
    "In": 26.74,
    "Sn": 27.112,
    "Sb": 25.23,
    "Te": 25.73,
    "I": 54.44,
    "Cs": 32.21,
    "Ba": 28.07,
    "Hf": 25.73,
    "Ta": 25.63,
    "W": 24.27,
    "Ir": 25.10,
    "Pt": 25.86,
    "Au": 25.418,
    "Hg": 27.983,
    "Pb": 26.65,
    "Bi": 25.52,
    "La": 27.11,
}

# ionization energies (J/mol)
ionization_en = {
    "H": [1312000],
    "He": [2372300, 5250500],
    "Li": [520200, 7298100, 11815000],
    "Be": [899500, 1757100, 14848700, 21006600],
    "B": [800600, 2427100, 3659700, 25025800, 32826700],
    "C": [1086500, 2352600, 4620500, 6222700, 37831000, 47277000],
    "N": [1402300, 2856000, 4578100, 7475000, 9444900, 53266600, 64360000],
    "O": [1313900, 3388300, 5300500, 7469200, 10989500, 13326500, 71330000, 84078000],
    "F": [
        1681000,
        3374200,
        6050400,
        8407700,
        11022700,
        15164100,
        17868000,
        92038100,
        106434300,
    ],
    "Ne": [
        2080700,
        3952300,
        6122000,
        9371000,
        12177000,
        15238000,
        19999000,
        23069500,
        115379500,
        131432000,
    ],
    "Na": [
        495800,
        4562000,
        6910300,
        9543000,
        13354000,
        16613000,
        20117000,
        25496000,
        28932000,
        141362000,
        159075000,
    ],
    "Mg": [
        737700,
        1450700,
        7732700,
        10542500,
        13630000,
        18020000,
        21711000,
        25661000,
        31653000,
        35458000,
        169988000,
        189367700,
    ],
    "Al": [
        577500,
        1816700,
        2744800,
        11577000,
        14842000,
        18379000,
        23326000,
        27465000,
        31853000,
        38473000,
        42646000,
        201266000,
        222315000,
    ],
    "Si": [
        786500,
        1577100,
        3231600,
        4355500,
        16091000,
        19805000,
        23780000,
        29287000,
        33878000,
        38726000,
        45962000,
        50502000,
        235195000,
        257922000,
    ],
    "P": [
        1011800,
        1907000,
        2914100,
        4963600,
        6273900,
        21267000,
        25431000,
        29872000,
        35905000,
        40950000,
        46261000,
        54110000,
        59024000,
        271790000,
        296194000,
    ],
    "S": [
        999600,
        2252000,
        3357000,
        4556000,
        7004300,
        8495800,
        27107000,
        31719000,
        36621000,
        43177000,
        48710000,
        54460000,
        62930000,
        68216000,
        311046000,
        337137000,
    ],
    "Cl": [
        1251200,
        2298000,
        3822000,
        5158600,
        6542000,
        9362000,
        11018000,
        33604000,
        38600000,
        43961000,
        51068000,
        57118000,
        63363000,
        72341000,
        78095000,
        352992000,
        380758000,
    ],
    "Ar": [
        1520600,
        2665800,
        3931000,
        5771000,
        7238000,
        8781000,
        11995000,
        13842000,
        40760000,
        46186000,
        52002000,
        59653000,
        66198000,
        72918000,
        82472000,
        88576000,
        397604000,
        427065000,
    ],
    "K": [
        418800,
        3052000,
        4420000,
        5877000,
        7975000,
        9590000,
        11343000,
        14944000,
        16963700,
        48610000,
        54490000,
        60730000,
        68950000,
        75900000,
        83080000,
        93400000,
        99710000,
        444870000,
        476061000,
    ],
    "Ca": [
        589800,
        1145400,
        4912400,
        6491000,
        8153000,
        10496000,
        12270000,
        14206000,
        18191000,
        20385000,
        57110000,
        63410000,
        70110000,
        78890000,
        86310000,
        94000000,
        104900000,
        111710000,
        494850000,
        527760000,
    ],
    "Sc": [
        633100,
        1235000,
        2388600,
        7090600,
        8843000,
        10679000,
        13310000,
        15250000,
        17370000,
        21726000,
        24102000,
        66320000,
        73010000,
        80160000,
        89490000,
        97400000,
        105600000,
        117000000,
        124270000,
        547530000,
        582163000,
    ],
    "Ti": [
        658800,
        1309800,
        2652500,
        4174600,
        9581000,
        11533000,
        13590000,
        16440000,
        18530000,
        20833000,
        25575000,
        28125000,
        76015000,
        83280000,
        90880000,
        100700000,
        109100000,
        117800000,
        129900000,
        137530000,
        602930000,
    ],
    "V": [
        650900,
        1414000,
        2830000,
        4507000,
        6298700,
        12363000,
        14530000,
        16730000,
        19860000,
        22240000,
        24670000,
        29730000,
        32446000,
        86450000,
        94170000,
        102300000,
        112700000,
        121600000,
        130700000,
        143400000,
        151440000,
    ],
    "Cr": [
        652900,
        1590600,
        2987000,
        4743000,
        6702000,
        8744900,
        15455000,
        17820000,
        20190000,
        23580000,
        26130000,
        28750000,
        34230000,
        37066000,
        97510000,
        105800000,
        114300000,
        125300000,
        134700000,
        144300000,
        157700000,
    ],
    "Mn": [
        717300,
        1509000,
        3248000,
        4940000,
        6990000,
        9220000,
        11500000,
        18770000,
        21400000,
        23960000,
        27590000,
        30330000,
        33150000,
        38880000,
        41987000,
        109480000,
        118100000,
        127100000,
        138600000,
        148500000,
        158600000,
    ],
    "Fe": [
        762500,
        1561900,
        2957000,
        5290000,
        7240000,
        9560000,
        12060000,
        14580000,
        22540000,
        25290000,
        28000000,
        31920000,
        34830000,
        37840000,
        44100000,
        47206000,
        122200000,
        131000000,
        140500000,
        152600000,
        163000000,
    ],
    "Co": [
        760400,
        1648000,
        3232000,
        4950000,
        7670000,
        9840000,
        12440000,
        15230000,
        17959000,
        26570000,
        29400000,
        32400000,
        36600000,
        39700000,
        42800000,
        49396000,
        52737000,
        134810000,
        145170000,
        154700000,
        167400000,
    ],
    "Ni": [
        737100,
        1753000,
        3395000,
        5300000,
        7339000,
        10400000,
        12800000,
        15600000,
        18600000,
        21670000,
        30970000,
        34000000,
        37100000,
        41500000,
        44800000,
        48100000,
        55101000,
        58570000,
        148700000,
        159000000,
        169400000,
    ],
    "Cu": [
        745500,
        1957900,
        3555000,
        5536000,
        7700000,
        9900000,
        13400000,
        16000000,
        19200000,
        22400000,
        25600000,
        35600000,
        38700000,
        42000000,
        46700000,
        50200000,
        53700000,
        61100000,
        64702000,
        163700000,
        174100000,
    ],
    "Zn": [
        906400,
        1733300,
        3833000,
        5731000,
        7970000,
        10400000,
        12900000,
        16800000,
        19600000,
        23000000,
        26400000,
        29990000,
        40490000,
        43800000,
        47300000,
        52300000,
        55900000,
        59700000,
        67300000,
        71200000,
        179100000,
    ],
    "Ga": [578800, 1979300, 2963000, 6180000],
    "Ge": [762000, 1537500, 3302100, 4411000, 9020000],
    "As": [947000, 1798000, 2735000, 4837000, 6043000, 12310000],
    "Se": [941000, 2045000, 2973700, 4144000, 6590000, 7880000, 14990000],
    "Br": [1139900, 2103000, 3470000, 4560000, 5760000, 8550000, 9940000, 18600000],
    "Kr": [
        1350800,
        2350400,
        3565000,
        5070000,
        6240000,
        7570000,
        10710000,
        12138000,
        22274000,
        25880000,
        29700000,
        33800000,
        37700000,
        43100000,
        47500000,
        52200000,
        57100000,
        61800000,
        75800000,
        80400000,
        85300000,
    ],
    "Rb": [
        403000,
        2633000,
        3860000,
        5080000,
        6850000,
        8140000,
        9570000,
        13120000,
        14500000,
        26740000,
    ],
    "Sr": [
        549500,
        1064200,
        4138000,
        5500000,
        6910000,
        8760000,
        10230000,
        11800000,
        15600000,
        17100000,
        31270000,
    ],
    "Y": [
        600000,
        1180000,
        1980000,
        5847000,
        7430000,
        8970000,
        11190000,
        12450000,
        14110000,
        18400000,
        19900000,
        36090000,
    ],
    "Zr": [640100, 1270000, 2218000, 3313000, 7752000, 9500000],
    "Nb": [652100, 1380000, 2416000, 3700000, 4877000, 9847000, 12100000],
    "Mo": [
        684300,
        1560000,
        2618000,
        4480000,
        5257000,
        6640800,
        12125000,
        13860000,
        15835000,
        17980000,
        20190000,
        22219000,
        26930000,
        29196000,
        52490000,
        55000000,
        61400000,
        67700000,
        74000000,
        80400000,
        87000000,
    ],
    "Tc": [702000, 1470000, 2850000],
    "Ru": [710200, 1620000, 2747000],
    "Rh": [719700, 1740000, 2997000],
    "Pd": [804400, 1870000, 3177000],
    "Ag": [731000, 2070000, 3361000],
    "Cd": [867800, 1631400, 3616000],
    "In": [558300, 1820700, 2704000, 5210000],
    "Sn": [708600, 1411800, 2943000, 3930300, 7456000],
    "Sb": [834000, 1594900, 2440000, 4260000, 5400000, 10400000],
    "Te": [869300, 1790000, 2698000, 3610000, 5668000, 6820000, 13200000],
    "I": [1008400, 1845900, 3180000],
    "Xe": [1170400, 2046400, 3099400],
    "Cs": [375700, 2234300, 3400000],
    "Ba": [502900, 965200, 3600000],
    "La": [538100, 1067000, 1850300, 4819000, 5940000],
    "Ce": [534400, 1050000, 1949000, 3547000, 6325000, 7490000],
    "Pr": [527000, 1020000, 2086000, 3761000, 5551000],
    "Nd": [533100, 1040000, 2130000, 3900000],
    "Pm": [540000, 1050000, 2150000, 3970000],
    "Sm": [544500, 1070000, 2260000, 3990000],
    "Eu": [547100, 1085000, 2404000, 4120000],
    "Gd": [593400, 1170000, 1990000, 4250000],
    "Tb": [565800, 1110000, 2114000, 3839000],
    "Dy": [573000, 1130000, 2200000, 3990000],
    "Ho": [581000, 1140000, 2204000, 4100000],
    "Er": [589300, 1150000, 2194000, 4120000],
    "Tm": [596700, 1160000, 2285000, 4120000],
    "Yb": [603400, 1174800, 2417000, 4203000],
    "Lu": [523500, 1340000, 2022300, 4370000, 6445000],
    "Hf": [658500, 1440000, 2250000, 3216000],
    "Ta": [761000, 1500000],
    "W": [770000, 1700000],
    "Re": [760000, 1260000, 2510000, 3640000],
    "Os": [840000, 1600000],
    "Ir": [880000, 1600000],
    "Pt": [870000, 1791000],
    "Au": [890100, 1980000],
    "Hg": [1007100, 1810000, 3300000],
    "Tl": [589400, 1971000, 2878000],
    "Pb": [715600, 1450500, 3081500, 4083000, 6640000],
    "Bi": [703000, 1610000, 2466000, 4370000, 5400000, 8520000],
    "Po": [812100],
    "At": [920000],
    "Rn": [1037000],
    "Fr": [380000],
    "Ra": [509300, 979000],
}

# electron affinity (J/mol)
electron_affin = {
    "H": 72800,
    "He": 0,
    "Li": 59600,
    "Be": 0,
    "B": 26700,
    "C": 153900,
    "N": 7000,
    "O": 141000,
    "F": 328000,
    "Ne": 0,
    "Na": 52800,
    "Mg": 0,
    "Al": 42500,
    "Si": 133600,
    "P": 72000,
    "S": 200000,
    "Cl": 349000,
    "Ar": 0,
    "K": 48400,
    "Ca": 2370,
    "Sc": 18100,
    "Ti": 7600,
    "V": 50600,
    "Cr": 64300,
    "Mn": 0,
    "Fe": 15700,
    "Co": 63700,
    "Ni": 112000,
    "Cu": 118400,
    "Zn": 0,
    "Ga": 28900,
    "Ge": 119000,
    "As": 78000,
    "Se": 195000,
    "Br": 324600,
    "Kr": 0,
    "Rb": 46900,
    "Sr": 5030,
    "Y": 29600,
    "Zr": 41100,
    "Nb": 86100,
    "Mo": 71900,
    "Tc": 53000,
    "Ru": 101300,
    "Rh": 109700,
    "Pd": 53700,
    "Ag": 125600,
    "Cd": 0,
    "In": 28900,
    "Sn": 107300,
    "Sb": 103200,
    "Te": 190200,
    "I": 295200,
    "Xe": 0,
    "Cs": 45500,
    "Ba": 13950,
    "La": 48000,
    "Ce": 50000,
    "Pr": 50000,
    "Nd": 50000,
    "Pm": 50000,
    "Sm": 50000,
    "Eu": 50000,
    "Gd": 50000,
    "Tb": 50000,
    "Dy": 50000,
    "Ho": 50000,
    "Er": 50000,
    "Tm": 50000,
    "Yb": 50000,
    "Lu": 33000,
    "Hf": 0,
    "Ta": 31000,
    "W": 78600,
    "Re": 14500,
    "Os": 106100,
    "Ir": 151000,
    "Pt": 205300,
    "Au": 222800,
    "Hg": 0,
    "Tl": 19200,
    "Pb": 35100,
    "Bi": 91200,
    "Po": 183300,
    "At": 270100,
    "Rn": float("NaN"),
}

# Pauling electronegativity
pauling = {
    "H": 2.2,
    "He": float("NaN"),
    "Li": 0.98,
    "Be": 1.57,
    "B": 2.04,
    "C": 2.55,
    "N": 3.04,
    "O": 3.44,
    "F": 3.98,
    "Ne": float("NaN"),
    "Na": 0.93,
    "Mg": 1.31,
    "Al": 1.61,
    "Si": 1.9,
    "P": 2.19,
    "S": 2.58,
    "Cl": 3.16,
    "Ar": float("NaN"),
    "K": 0.82,
    "Ca": 1,
    "Sc": 1.36,
    "Ti": 1.54,
    "V": 1.63,
    "Cr": 1.66,
    "Mn": 1.55,
    "Fe": 1.83,
    "Co": 1.88,
    "Ni": 1.91,
    "Cu": 1.9,
    "Zn": 1.65,
    "Ga": 1.81,
    "Ge": 2.01,
    "As": 2.18,
    "Se": 2.55,
    "Br": 2.96,
    "Kr": 3,
    "Rb": 0.82,
    "Sr": 0.95,
    "Y": 1.22,
    "Zr": 1.33,
    "Nb": 1.6,
    "Mo": 2.16,
    "Tc": 1.9,
    "Ru": 2.2,
    "Rh": 2.28,
    "Pd": 2.2,
    "Ag": 1.93,
    "Cd": 1.69,
    "In": 1.78,
    "Sn": 1.96,
    "Sb": 2.05,
    "Te": 2.1,
    "I": 2.66,
    "Xe": 2.6,
    "Cs": 0.79,
    "Ba": 0.89,
    "La": 1.1,
    "Ce": 1.12,
    "Pr": 1.13,
    "Nd": 1.14,
    "Pm": float("NaN"),
    "Sm": 1.17,
    "Eu": float("NaN"),
    "Gd": 1.2,
    "Tb": float("NaN"),
    "Dy": 1.22,
    "Ho": 1.23,
    "Er": 1.24,
    "Tm": 1.25,
    "Yb": float("NaN"),
    "Lu": 1.27,
    "Hf": 1.3,
    "Ta": 1.5,
    "W": 2.36,
    "Re": 1.9,
    "Os": 2.2,
    "Ir": 2.2,
    "Pt": 2.28,
    "Au": 2.54,
    "Hg": 2,
    "Tl": 1.62,
    "Pb": 2.33,
    "Bi": 2.02,
    "Po": 2,
    "At": 2.2,
    "Rn": float("NaN"),
    "Fr": 0.7,
    "Ra": 0.9,
}

# crystal field splitting energy per electron, 10Dq=f*g
# From: Inorganic Chemistry, 2nd ed, William Porterfield, 1993, Table 11.8
# provide only g values (assume constant ligand with const f)
# assign only non-zero and NaN values
#   enter NaN for elements that are expected to have splittings but no data avail
# all other values assumed to be zero
#   includes cations with zero d or s valence e- and cations with full d valence
xtal_field_split = {
    "V": {2: 12.3, 3: 18.6, 4: float("NaN"), 5: 0},
    "Cr": {2: 14.1, 3: 17, 4: float("NaN"), 5: float("NaN"), 6: 0},
    "Mn": {2: 8.5, 3: 21, 4: 23, 5: float("NaN"), 6: float("NaN"), 7: 0},
    "Fe": {2: 10, 3: 14, 4: float("NaN"), 5: float("NaN"), 6: float("NaN")},
    "Co": {2: 9.3, 3: 19, 4: float("NaN"), 5: float("NaN"), 6: float("NaN")},
    "Ni": {2: 8.9, 3: float("NaN"), 4: float("NaN"), 5: float("NaN"), 6: float("NaN")},
    "Nb": {2: float("NaN"), 3: float("NaN"), 4: float("NaN"), 5: 0},
    "Mo": {2: float("NaN"), 3: 24, 4: float("NaN"), 5: float("NaN"), 6: 0},
    "Rh": {2: float("NaN"), 3: 27, 4: float("NaN"), 5: float("NaN"), 6: float("NaN")},
    "Pd": {
        2: float("NaN"),
        3: float("NaN"),
        4: float("NaN"),
        5: float("NaN"),
        6: float("NaN"),
    },
    "Ta": {2: float("NaN"), 3: float("NaN"), 4: float("NaN"), 5: 0},
    "W": {2: float("NaN"), 3: float("NaN"), 4: float("NaN"), 5: float("NaN"), 6: 0},
    "Ir": {2: float("NaN"), 3: 32, 4: float("NaN"), 5: float("NaN"), 6: float("NaN")},
    "Pt": {2: float("NaN"), 3: float("NaN"), 4: 36, 5: float("NaN"), 6: float("NaN")},
}

# magnetic moments for high spin octahedral complexes
# From: Inorganic Chemistry, 2nd ed, William Porterfield, 1993, Table 11.10
# Again, only assign non-zero values and NaN. All else assumed to be zero.
magn_moment = {
    "V": {2: 3},
    "Cr": {2: 4.47, 3: 3},
    "Mn": {2: 5.2, 3: 4.47, 4: 3},
    "Fe": {2: 5.48, 3: 5.2, 4: 4.47, 5: 3},
    "Co": {2: 5.92, 3: 5.48, 4: 5.2, 5: 4.47, 6: 3},
    "Ni": {2: 5.48, 3: 5.92, 4: 5.48, 5: 5.2, 6: 4.47},
    "Nb": {2: 3},
    "Mo": {2: 4.47, 3: 3},
    "Rh": {2: 5.92, 3: 5.48, 4: 5.2, 5: 4.47, 6: 3},
    "Pd": {2: 5.48, 3: 5.92, 4: 5.48, 5: 5.2, 6: 4.47},
    "Ta": {2: 3},
    "W": {2: 4.47, 3: 3},
    "Ir": {2: 5.92, 3: 5.48, 4: 5.2, 5: 4.47, 6: 3},
    "Pt": {2: 5.48, 3: 5.92, 4: 5.48, 5: 5.2, 6: 4.47},
}

# spin orbit coupling constants for single electrons in TMs
# From: Inorganic Chemistry, 2nd ed, William Porterfield, 1993, Table 11.11
# assign only nonzero and NaN values
#   assign equal to zero if no (d+s) valence e and for full d valence
so_coupling = {
    "V": {2: 170, 3: 210, 4: 250, 5: 0},
    "Cr": {2: 230, 3: 275, 4: 355, 5: float("NaN"), 6: 0},
    "Mn": {2: 300, 3: 355, 4: 415, 5: float("NaN"), 6: float("NaN")},
    "Fe": {2: 400, 3: 460, 4: 520, 5: float("NaN")},
    "Co": {2: 515, 3: 580, 4: 650, 5: float("NaN")},
    "Ni": {2: 630, 3: 705, 4: 790, 5: float("NaN")},
    "Nb": {2: 610, 3: 800, 4: float("NaN"), 5: 0},
    "Mo": {2: 670, 3: 800, 4: 850, 5: 900, 6: 0},
    "Rh": {2: float("NaN"), 3: float("NaN"), 4: 1700, 5: 1850, 6: float("NaN")},
    "Pd": {
        1: 1300,
        2: 1600,
        3: float("NaN"),
        4: float("NaN"),
        5: float("NaN"),
        6: float("NaN"),
    },
    "Ta": {2: float("NaN"), 3: 1400, 4: float("NaN"), 5: 0},
    "Ir": {3: float("NaN"), 4: 5000, 5: 5500, 6: float("NaN")},
    "Pt": {
        1: 3400,
        2: float("NaN"),
        3: float("NaN"),
        4: 5000,
        5: 5500,
        6: float("NaN"),
    },
}

# maximum total electron spin
# assign only nonzero values for magnetic elements with d electrons
sat_magn = {
    "V": {2: 1},
    "Cr": {2: 2, 3: 1},
    "Mn": {2: 3, 3: 2, 4: 1},
    "Fe": {2: 4, 3: 3, 4: 2, 5: 1},
    "Co": {2: 5, 3: 4, 4: 3, 5: 2, 6: 1},
    "Ni": {2: 4, 3: 5, 4: 4, 5: 3, 6: 2},
    "Nb": {2: 1},
    "Mo": {2: 2, 3: 1},
    "Rh": {2: 5, 3: 4, 4: 3, 5: 2, 6: 1},
    "Pd": {2: 4, 3: 5, 4: 4, 5: 3, 6: 2},
    "Ta": {2: 1},
    "W": {2: 2, 3: 1},
    "Ir": {2: 5, 3: 4, 4: 3, 5: 2, 6: 1},
    "Pt": {2: 4, 3: 5, 4: 4, 5: 3, 6: 2},
}

# electric dipole polarizabilities
# from CRC handbook, 95th Ed and an older copy
# units: 10^-24 cm^3
electric_pol = {
    "Li": 24.33,
    "Be": 5.6,
    "N": 1.10,
    "O": 0.802,
    "F": 0.557,
    "Na": 24.11,
    "Mg": 10.6,
    "Al": 6.8,
    "Si": 5.53,
    "P": 3.63,
    "S": 2.90,
    "Cl": 2.18,
    "K": 43.06,
    "Ca": 22.8,
    "Sc": 17.8,
    "Ti": 14.6,
    "V": 12.4,
    "Cr": 11.6,
    "Mn": 9.4,
    "Fe": 8.4,
    "Co": 7.5,
    "Ni": 6.8,
    "Cu": 6.2,
    "Zn": 5.75,
    "Ga": 8.12,
    "Ge": 5.84,
    "As": 4.31,
    "Se": 3.77,
    "Br": 3.05,
    "Rb": 47.24,
    "Sr": 27.6,
    "Y": 22.7,
    "Zr": 17.9,
    "Nb": 15.7,
    "Mo": 12.8,
    "Rh": 8.6,
    "Pd": 4.8,
    "Ag": 7.2,
    "Cd": 7.36,
    "In": 10.2,
    "Sn": 7.7,
    "Sb": 6.6,
    "Te": 5.5,
    "I": 5.35,
    "Cs": 59.6,
    "Ba": 39.7,
    "Hf": 16.2,
    "Ta": 13.1,
    "W": 11.1,
    "Ir": 7.6,
    "Pt": 6.5,
    "Au": 5.8,
    "Hg": 5.02,
    "Pb": 6.98,
    "Bi": 7.4,
    "La": 31.1,
}

# GGA+U total energies of the elements in their reference phases (eV/atom)
GGAU_Etot = {
    "Li": -1.86,
    "Be": -3.75,
    "N": -8.31,
    "O": -4.99,
    "F": -1.86,
    "Na": -1.23,
    "Mg": -1.54,
    "Al": -3.74,
    "Si": -5.42,
    "P": -4.96,
    "S": -4.06,
    "Cl": -1.79,
    "K": -1.08,
    "Ca": -1.93,
    "Sc": -5.12,
    "Ti": -5.57,
    "V": -5.97,
    "Cr": -7.29,
    "Mn": -6.97,
    "Fe": -6,
    "Co": -4.65,
    "Ni": -3.65,
    "Cu": -2.03,
    "Zn": -1.27,
    "Ga": -3.03,
    "Ge": -4.29,
    "As": -4.65,
    "Se": -3.48,
    "Rb": -0.96,
    "Sr": -1.68,
    "Y": -5.48,
    "Zr": -6.6,
    "Nb": -7.04,
    "Mo": -7.378,
    "Rh": -4.23,
    "Pd": -2.84,
    "Ag": -0.71,
    "Cd": -0.91,
    "In": -2.72,
    "Sn": -3.97,
    "Sb": -4.12,
    "Te": -3.14,
    "Ba": -1.93,
    "La": -3.86,
    "Hf": -8.12,
    "Ta": -9.22,
    "W": -9.7359,
    "Ir": -5.77,
    "Pt": -3.52,
    "Au": -0.97,
    "Hg": -0.29,
    "Pb": -3.717901,
    "Bi": -4.06,
}

# FERE chemical potentials, eV/atom
mus_fere = {
    "Li": -1.6529591953887741,
    "Be": -3.3972092621754264,
    "N": -8.51,
    "O": -4.76,
    "F": -1.7037867766570287,
    "Na": -1.0640326227725869,
    "Mg": -0.99,
    "Al": -3.02,
    "Si": -4.9927748122726356,
    "P": -5.64,
    "S": -4.00,
    "Cl": -1.6262437135301639,
    "K": -0.80499202755075006,
    "Ca": -1.64,
    "Sc": -4.6302422200922519,
    "Ti": -5.5167842601434147,
    "V": -6.4219725884764864,
    "Cr": -7.2224146752384204,
    "Mn": -6.9965778258511993,
    "Fe": -6.1521343161090325,
    "Co": -4.7543486260270402,
    "Ni": -3.5687859474688026,
    "Cu": -1.9725806522979044,
    "Zn": -0.84,
    "Ga": -2.37,
    "Ge": -4.137439286830797,
    "As": -5.06,
    "Se": -3.55,
    "Rb": -0.6750560483522855,
    "Sr": -1.1674559193419329,
    "Y": -4.812621315561298,
    "Zr": -5.8747056261113126,
    "Nb": -6.6867516375690608,
    "Mo": -7.6126,
    "Rh": -4.7622899695820369,
    "Pd": -3.1174044624888873,
    "Ag": -0.82700958541595615,
    "Cd": -0.56,
    "In": -2.31,
    "Sn": -3.7894939351245469,
    "Sb": -4.2862260747305099,
    "Te": -3.2503408197224912,
    "Ba": -1.3944992462870172,
    "La": -3.6642174822805287,
    "Hf": -7.397695761161847,
    "Ta": -8.8184831379805324,
    "W": -10.02531,
    "Ir": -5.964577394407752,
    "Pt": -3.9527597082085424,
    "Au": -2.2303410086960551,
    "Hg": -0.12361566177444684,
    "Pb": -3.83942,
    "Bi": -4.3853003286558812,
}

charge_states = {
    "Li": [1],
    "Be": [2],
    "N": [-3],
    "O": [-2],
    "F": [-1],
    "Na": [1],
    "Mg": [2],
    "Al": [3],
    "Si": [-4, 2, 4],
    "P": [-3],
    "S": [-2],
    "Cl": [-1],
    "K": [1],
    "Ca": [2],
    "Sc": [3],
    "Ti": [2, 3, 4],
    "V": [2, 3, 4, 5],
    "Cr": [2, 3, 4, 5, 6],
    "Mn": [2, 3, 4, 5, 6, 7],
    "Fe": [2, 3, 4],
    "Co": [2, 3, 4],
    "Ni": [2, 3, 4],
    "Cu": [1, 2],
    "Zn": [2],
    "Ga": [3],
    "Ge": [-4, 2, 4],
    "As": [-3],
    "Se": [-2],
    "Br": [-1],
    "Rb": [1],
    "Sr": [2],
    "Y": [3],
    "Zr": [4],
    "Nb": [2, 3, 4, 5],
    "Mo": [2, 3, 4, 5, 6],
    "Rh": [2, 3, 4],
    "Pd": [2, 3, 4],
    "Ag": [1],
    "Cd": [2],
    "In": [3],
    "Sn": [-4, 2, 4],
    "Sb": [-3, 3, 5],
    "Bi": [-3, 3, 5],
    "Te": [-2],
    "I": [-1],
    "Cs": [1],
    "Ba": [2],
    "Hf": [4],
    "Ta": [2, 3, 4, 5],
    "W": [2, 3, 4, 5, 6],
}

# Dictionary of properties
properties = {
    "magn_elem": magn_elem,
    "atom_num": atom_num,
    "atom_mass": atom_mass,
    "row_num": row_num,
    "col_num": col_num,
    "valence_e": valence_e,
    "atom_radius": atom_radius,
    "molar_vol": molar_vol,
    "heat_fusion": heat_fusion,
    "melting_point": mp,
    "boiling_point": bp,
    "heat_cap": heat_cap,
    "ionization_en": ionization_en,
    "electron_affin": electron_affin,
    "electronegativity": pauling,
    "xtal_field_split": xtal_field_split,
    "magn_moment": magn_moment,
    "so_coupling": so_coupling,
    "sat_magn": sat_magn,
    "electric_pol": electric_pol,
    "GGAU_Etot": GGAU_Etot,
    "mus_fere": mus_fere,
    "charge_states": charge_states,
}
