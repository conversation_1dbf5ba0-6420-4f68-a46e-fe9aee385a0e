
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

    <title>matminer.utils.tests package &#8212; matminer 0.9.0 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="_static/nature.css" />
    <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="_static/doctools.js"></script>
    <script src="_static/sphinx_highlight.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
 
<link href='https://fonts.googleapis.com/css?family=Lato:400,700' rel='stylesheet' type='text/css'>

  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer.utils.tests package</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="matminer-utils-tests-package">
<h1>matminer.utils.tests package<a class="headerlink" href="#matminer-utils-tests-package" title="Permalink to this heading">¶</a></h1>
<section id="submodules">
<h2>Submodules<a class="headerlink" href="#submodules" title="Permalink to this heading">¶</a></h2>
</section>
<section id="module-matminer.utils.tests.test_caching">
<span id="matminer-utils-tests-test-caching-module"></span><h2>matminer.utils.tests.test_caching module<a class="headerlink" href="#module-matminer.utils.tests.test_caching" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.utils.tests.test_caching.TestCaching">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.utils.tests.test_caching.</span></span><span class="sig-name descname"><span class="pre">TestCaching</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_caching.TestCaching" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">PymatgenTest</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.tests.test_caching.TestCaching.test_cache">
<span class="sig-name descname"><span class="pre">test_cache</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_caching.TestCaching.test_cache" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.utils.tests.test_data">
<span id="matminer-utils-tests-test-data-module"></span><h2>matminer.utils.tests.test_data module<a class="headerlink" href="#module-matminer.utils.tests.test_data" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.utils.tests.test_data.TestDemlData">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.utils.tests.test_data.</span></span><span class="sig-name descname"><span class="pre">TestDemlData</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_data.TestDemlData" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></p>
<p>Tests for the DemlData Class</p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.tests.test_data.TestDemlData.setUp">
<span class="sig-name descname"><span class="pre">setUp</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_data.TestDemlData.setUp" title="Permalink to this definition">¶</a></dt>
<dd><p>Hook method for setting up the test fixture before exercising it.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.tests.test_data.TestDemlData.test_get_oxidation">
<span class="sig-name descname"><span class="pre">test_get_oxidation</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_data.TestDemlData.test_get_oxidation" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.tests.test_data.TestDemlData.test_get_property">
<span class="sig-name descname"><span class="pre">test_get_property</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_data.TestDemlData.test_get_property" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.utils.tests.test_data.TestIUCrBondValenceData">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.utils.tests.test_data.</span></span><span class="sig-name descname"><span class="pre">TestIUCrBondValenceData</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_data.TestIUCrBondValenceData" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.tests.test_data.TestIUCrBondValenceData.setUp">
<span class="sig-name descname"><span class="pre">setUp</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_data.TestIUCrBondValenceData.setUp" title="Permalink to this definition">¶</a></dt>
<dd><p>Hook method for setting up the test fixture before exercising it.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.tests.test_data.TestIUCrBondValenceData.test_get_data">
<span class="sig-name descname"><span class="pre">test_get_data</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_data.TestIUCrBondValenceData.test_get_data" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.utils.tests.test_data.TestMEGNetData">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.utils.tests.test_data.</span></span><span class="sig-name descname"><span class="pre">TestMEGNetData</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_data.TestMEGNetData" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.tests.test_data.TestMEGNetData.setUp">
<span class="sig-name descname"><span class="pre">setUp</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_data.TestMEGNetData.setUp" title="Permalink to this definition">¶</a></dt>
<dd><p>Hook method for setting up the test fixture before exercising it.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.tests.test_data.TestMEGNetData.test_get_property">
<span class="sig-name descname"><span class="pre">test_get_property</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_data.TestMEGNetData.test_get_property" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.utils.tests.test_data.TestMagpieData">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.utils.tests.test_data.</span></span><span class="sig-name descname"><span class="pre">TestMagpieData</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_data.TestMagpieData" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.tests.test_data.TestMagpieData.setUp">
<span class="sig-name descname"><span class="pre">setUp</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_data.TestMagpieData.setUp" title="Permalink to this definition">¶</a></dt>
<dd><p>Hook method for setting up the test fixture before exercising it.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.tests.test_data.TestMagpieData.test_get_oxidation">
<span class="sig-name descname"><span class="pre">test_get_oxidation</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_data.TestMagpieData.test_get_oxidation" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.tests.test_data.TestMagpieData.test_get_property">
<span class="sig-name descname"><span class="pre">test_get_property</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_data.TestMagpieData.test_get_property" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.utils.tests.test_data.TestMatScholarData">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.utils.tests.test_data.</span></span><span class="sig-name descname"><span class="pre">TestMatScholarData</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_data.TestMatScholarData" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.tests.test_data.TestMatScholarData.setUp">
<span class="sig-name descname"><span class="pre">setUp</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_data.TestMatScholarData.setUp" title="Permalink to this definition">¶</a></dt>
<dd><p>Hook method for setting up the test fixture before exercising it.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.tests.test_data.TestMatScholarData.test_get_property">
<span class="sig-name descname"><span class="pre">test_get_property</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_data.TestMatScholarData.test_get_property" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.utils.tests.test_data.TestMixingEnthalpy">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.utils.tests.test_data.</span></span><span class="sig-name descname"><span class="pre">TestMixingEnthalpy</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_data.TestMixingEnthalpy" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.tests.test_data.TestMixingEnthalpy.setUp">
<span class="sig-name descname"><span class="pre">setUp</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_data.TestMixingEnthalpy.setUp" title="Permalink to this definition">¶</a></dt>
<dd><p>Hook method for setting up the test fixture before exercising it.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.tests.test_data.TestMixingEnthalpy.test_get_data">
<span class="sig-name descname"><span class="pre">test_get_data</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_data.TestMixingEnthalpy.test_get_data" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.utils.tests.test_data.TestPymatgenData">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.utils.tests.test_data.</span></span><span class="sig-name descname"><span class="pre">TestPymatgenData</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_data.TestPymatgenData" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.tests.test_data.TestPymatgenData.setUp">
<span class="sig-name descname"><span class="pre">setUp</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_data.TestPymatgenData.setUp" title="Permalink to this definition">¶</a></dt>
<dd><p>Hook method for setting up the test fixture before exercising it.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.tests.test_data.TestPymatgenData.test_get_oxidation">
<span class="sig-name descname"><span class="pre">test_get_oxidation</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_data.TestPymatgenData.test_get_oxidation" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.tests.test_data.TestPymatgenData.test_get_property">
<span class="sig-name descname"><span class="pre">test_get_property</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_data.TestPymatgenData.test_get_property" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.utils.tests.test_flatten_dict">
<span id="matminer-utils-tests-test-flatten-dict-module"></span><h2>matminer.utils.tests.test_flatten_dict module<a class="headerlink" href="#module-matminer.utils.tests.test_flatten_dict" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.utils.tests.test_flatten_dict.FlattenDictTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.utils.tests.test_flatten_dict.</span></span><span class="sig-name descname"><span class="pre">FlattenDictTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_flatten_dict.FlattenDictTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.tests.test_flatten_dict.FlattenDictTest.test_flatten_nested_dict">
<span class="sig-name descname"><span class="pre">test_flatten_nested_dict</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_flatten_dict.FlattenDictTest.test_flatten_nested_dict" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.utils.tests.test_io">
<span id="matminer-utils-tests-test-io-module"></span><h2>matminer.utils.tests.test_io module<a class="headerlink" href="#module-matminer.utils.tests.test_io" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.utils.tests.test_io.IOTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.utils.tests.test_io.</span></span><span class="sig-name descname"><span class="pre">IOTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_io.IOTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">PymatgenTest</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.tests.test_io.IOTest.setUp">
<span class="sig-name descname"><span class="pre">setUp</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_io.IOTest.setUp" title="Permalink to this definition">¶</a></dt>
<dd><p>Hook method for setting up the test fixture before exercising it.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.tests.test_io.IOTest.tearDown">
<span class="sig-name descname"><span class="pre">tearDown</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_io.IOTest.tearDown" title="Permalink to this definition">¶</a></dt>
<dd><p>Hook method for deconstructing the test fixture after testing it.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.tests.test_io.IOTest.test_load_dataframe_from_json">
<span class="sig-name descname"><span class="pre">test_load_dataframe_from_json</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_io.IOTest.test_load_dataframe_from_json" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.utils.tests.test_io.IOTest.test_store_dataframe_as_json">
<span class="sig-name descname"><span class="pre">test_store_dataframe_as_json</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_io.IOTest.test_store_dataframe_as_json" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.utils.tests.test_io.generate_json_files">
<span class="sig-prename descclassname"><span class="pre">matminer.utils.tests.test_io.</span></span><span class="sig-name descname"><span class="pre">generate_json_files</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.utils.tests.test_io.generate_json_files" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</section>
<section id="module-matminer.utils.tests">
<span id="module-contents"></span><h2>Module contents<a class="headerlink" href="#module-matminer.utils.tests" title="Permalink to this heading">¶</a></h2>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">matminer.utils.tests package</a><ul>
<li><a class="reference internal" href="#submodules">Submodules</a></li>
<li><a class="reference internal" href="#module-matminer.utils.tests.test_caching">matminer.utils.tests.test_caching module</a><ul>
<li><a class="reference internal" href="#matminer.utils.tests.test_caching.TestCaching"><code class="docutils literal notranslate"><span class="pre">TestCaching</span></code></a><ul>
<li><a class="reference internal" href="#matminer.utils.tests.test_caching.TestCaching.test_cache"><code class="docutils literal notranslate"><span class="pre">TestCaching.test_cache()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.utils.tests.test_data">matminer.utils.tests.test_data module</a><ul>
<li><a class="reference internal" href="#matminer.utils.tests.test_data.TestDemlData"><code class="docutils literal notranslate"><span class="pre">TestDemlData</span></code></a><ul>
<li><a class="reference internal" href="#matminer.utils.tests.test_data.TestDemlData.setUp"><code class="docutils literal notranslate"><span class="pre">TestDemlData.setUp()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.tests.test_data.TestDemlData.test_get_oxidation"><code class="docutils literal notranslate"><span class="pre">TestDemlData.test_get_oxidation()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.tests.test_data.TestDemlData.test_get_property"><code class="docutils literal notranslate"><span class="pre">TestDemlData.test_get_property()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.utils.tests.test_data.TestIUCrBondValenceData"><code class="docutils literal notranslate"><span class="pre">TestIUCrBondValenceData</span></code></a><ul>
<li><a class="reference internal" href="#matminer.utils.tests.test_data.TestIUCrBondValenceData.setUp"><code class="docutils literal notranslate"><span class="pre">TestIUCrBondValenceData.setUp()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.tests.test_data.TestIUCrBondValenceData.test_get_data"><code class="docutils literal notranslate"><span class="pre">TestIUCrBondValenceData.test_get_data()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.utils.tests.test_data.TestMEGNetData"><code class="docutils literal notranslate"><span class="pre">TestMEGNetData</span></code></a><ul>
<li><a class="reference internal" href="#matminer.utils.tests.test_data.TestMEGNetData.setUp"><code class="docutils literal notranslate"><span class="pre">TestMEGNetData.setUp()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.tests.test_data.TestMEGNetData.test_get_property"><code class="docutils literal notranslate"><span class="pre">TestMEGNetData.test_get_property()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.utils.tests.test_data.TestMagpieData"><code class="docutils literal notranslate"><span class="pre">TestMagpieData</span></code></a><ul>
<li><a class="reference internal" href="#matminer.utils.tests.test_data.TestMagpieData.setUp"><code class="docutils literal notranslate"><span class="pre">TestMagpieData.setUp()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.tests.test_data.TestMagpieData.test_get_oxidation"><code class="docutils literal notranslate"><span class="pre">TestMagpieData.test_get_oxidation()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.tests.test_data.TestMagpieData.test_get_property"><code class="docutils literal notranslate"><span class="pre">TestMagpieData.test_get_property()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.utils.tests.test_data.TestMatScholarData"><code class="docutils literal notranslate"><span class="pre">TestMatScholarData</span></code></a><ul>
<li><a class="reference internal" href="#matminer.utils.tests.test_data.TestMatScholarData.setUp"><code class="docutils literal notranslate"><span class="pre">TestMatScholarData.setUp()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.tests.test_data.TestMatScholarData.test_get_property"><code class="docutils literal notranslate"><span class="pre">TestMatScholarData.test_get_property()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.utils.tests.test_data.TestMixingEnthalpy"><code class="docutils literal notranslate"><span class="pre">TestMixingEnthalpy</span></code></a><ul>
<li><a class="reference internal" href="#matminer.utils.tests.test_data.TestMixingEnthalpy.setUp"><code class="docutils literal notranslate"><span class="pre">TestMixingEnthalpy.setUp()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.tests.test_data.TestMixingEnthalpy.test_get_data"><code class="docutils literal notranslate"><span class="pre">TestMixingEnthalpy.test_get_data()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.utils.tests.test_data.TestPymatgenData"><code class="docutils literal notranslate"><span class="pre">TestPymatgenData</span></code></a><ul>
<li><a class="reference internal" href="#matminer.utils.tests.test_data.TestPymatgenData.setUp"><code class="docutils literal notranslate"><span class="pre">TestPymatgenData.setUp()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.tests.test_data.TestPymatgenData.test_get_oxidation"><code class="docutils literal notranslate"><span class="pre">TestPymatgenData.test_get_oxidation()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.tests.test_data.TestPymatgenData.test_get_property"><code class="docutils literal notranslate"><span class="pre">TestPymatgenData.test_get_property()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.utils.tests.test_flatten_dict">matminer.utils.tests.test_flatten_dict module</a><ul>
<li><a class="reference internal" href="#matminer.utils.tests.test_flatten_dict.FlattenDictTest"><code class="docutils literal notranslate"><span class="pre">FlattenDictTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.utils.tests.test_flatten_dict.FlattenDictTest.test_flatten_nested_dict"><code class="docutils literal notranslate"><span class="pre">FlattenDictTest.test_flatten_nested_dict()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.utils.tests.test_io">matminer.utils.tests.test_io module</a><ul>
<li><a class="reference internal" href="#matminer.utils.tests.test_io.IOTest"><code class="docutils literal notranslate"><span class="pre">IOTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.utils.tests.test_io.IOTest.setUp"><code class="docutils literal notranslate"><span class="pre">IOTest.setUp()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.tests.test_io.IOTest.tearDown"><code class="docutils literal notranslate"><span class="pre">IOTest.tearDown()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.tests.test_io.IOTest.test_load_dataframe_from_json"><code class="docutils literal notranslate"><span class="pre">IOTest.test_load_dataframe_from_json()</span></code></a></li>
<li><a class="reference internal" href="#matminer.utils.tests.test_io.IOTest.test_store_dataframe_as_json"><code class="docutils literal notranslate"><span class="pre">IOTest.test_store_dataframe_as_json()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.utils.tests.test_io.generate_json_files"><code class="docutils literal notranslate"><span class="pre">generate_json_files()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.utils.tests">Module contents</a></li>
</ul>
</li>
</ul>

  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/matminer.utils.tests.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer.utils.tests package</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2015, Anubhav Jain.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.3.0.
    </div>

  </body>
</html>