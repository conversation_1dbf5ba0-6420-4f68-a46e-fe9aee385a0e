
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

    <title>matminer.featurizers package &#8212; matminer 0.9.0 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="_static/nature.css" />
    <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="_static/doctools.js"></script>
    <script src="_static/sphinx_highlight.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
 
<link href='https://fonts.googleapis.com/css?family=Lato:400,700' rel='stylesheet' type='text/css'>

  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer.featurizers package</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="matminer-featurizers-package">
<h1>matminer.featurizers package<a class="headerlink" href="#matminer-featurizers-package" title="Permalink to this heading">¶</a></h1>
<section id="subpackages">
<h2>Subpackages<a class="headerlink" href="#subpackages" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="matminer.featurizers.composition.html">matminer.featurizers.composition package</a><ul>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.composition.html#subpackages">Subpackages</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.composition.tests.html">matminer.featurizers.composition.tests package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.tests.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.tests.html#module-matminer.featurizers.composition.tests.base">matminer.featurizers.composition.tests.base module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.tests.html#module-matminer.featurizers.composition.tests.test_alloy">matminer.featurizers.composition.tests.test_alloy module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.tests.html#module-matminer.featurizers.composition.tests.test_composite">matminer.featurizers.composition.tests.test_composite module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.tests.html#module-matminer.featurizers.composition.tests.test_element">matminer.featurizers.composition.tests.test_element module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.tests.html#module-matminer.featurizers.composition.tests.test_ion">matminer.featurizers.composition.tests.test_ion module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.tests.html#module-matminer.featurizers.composition.tests.test_orbital">matminer.featurizers.composition.tests.test_orbital module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.tests.html#module-matminer.featurizers.composition.tests.test_packing">matminer.featurizers.composition.tests.test_packing module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.tests.html#module-matminer.featurizers.composition.tests.test_thermo">matminer.featurizers.composition.tests.test_thermo module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.tests.html#module-matminer.featurizers.composition.tests">Module contents</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.composition.html#submodules">Submodules</a></li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.composition.html#module-matminer.featurizers.composition.alloy">matminer.featurizers.composition.alloy module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.Miedema"><code class="docutils literal notranslate"><span class="pre">Miedema</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.Miedema.__init__"><code class="docutils literal notranslate"><span class="pre">Miedema.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.Miedema.citations"><code class="docutils literal notranslate"><span class="pre">Miedema.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.Miedema.deltaH_chem"><code class="docutils literal notranslate"><span class="pre">Miedema.deltaH_chem()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.Miedema.deltaH_elast"><code class="docutils literal notranslate"><span class="pre">Miedema.deltaH_elast()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.Miedema.deltaH_struct"><code class="docutils literal notranslate"><span class="pre">Miedema.deltaH_struct()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.Miedema.deltaH_topo"><code class="docutils literal notranslate"><span class="pre">Miedema.deltaH_topo()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.Miedema.feature_labels"><code class="docutils literal notranslate"><span class="pre">Miedema.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.Miedema.featurize"><code class="docutils literal notranslate"><span class="pre">Miedema.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.Miedema.implementors"><code class="docutils literal notranslate"><span class="pre">Miedema.implementors()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.Miedema.precheck"><code class="docutils literal notranslate"><span class="pre">Miedema.precheck()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.WenAlloys"><code class="docutils literal notranslate"><span class="pre">WenAlloys</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.WenAlloys.__init__"><code class="docutils literal notranslate"><span class="pre">WenAlloys.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.WenAlloys.citations"><code class="docutils literal notranslate"><span class="pre">WenAlloys.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.WenAlloys.compute_atomic_fraction"><code class="docutils literal notranslate"><span class="pre">WenAlloys.compute_atomic_fraction()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.WenAlloys.compute_configuration_entropy"><code class="docutils literal notranslate"><span class="pre">WenAlloys.compute_configuration_entropy()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.WenAlloys.compute_delta"><code class="docutils literal notranslate"><span class="pre">WenAlloys.compute_delta()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.WenAlloys.compute_enthalpy"><code class="docutils literal notranslate"><span class="pre">WenAlloys.compute_enthalpy()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.WenAlloys.compute_gamma_radii"><code class="docutils literal notranslate"><span class="pre">WenAlloys.compute_gamma_radii()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.WenAlloys.compute_lambda"><code class="docutils literal notranslate"><span class="pre">WenAlloys.compute_lambda()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.WenAlloys.compute_local_mismatch"><code class="docutils literal notranslate"><span class="pre">WenAlloys.compute_local_mismatch()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.WenAlloys.compute_magpie_summary"><code class="docutils literal notranslate"><span class="pre">WenAlloys.compute_magpie_summary()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.WenAlloys.compute_strength_local_mismatch_shear"><code class="docutils literal notranslate"><span class="pre">WenAlloys.compute_strength_local_mismatch_shear()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.WenAlloys.compute_weight_fraction"><code class="docutils literal notranslate"><span class="pre">WenAlloys.compute_weight_fraction()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.WenAlloys.feature_labels"><code class="docutils literal notranslate"><span class="pre">WenAlloys.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.WenAlloys.featurize"><code class="docutils literal notranslate"><span class="pre">WenAlloys.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.WenAlloys.implementors"><code class="docutils literal notranslate"><span class="pre">WenAlloys.implementors()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.WenAlloys.precheck"><code class="docutils literal notranslate"><span class="pre">WenAlloys.precheck()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.YangSolidSolution"><code class="docutils literal notranslate"><span class="pre">YangSolidSolution</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.YangSolidSolution.__init__"><code class="docutils literal notranslate"><span class="pre">YangSolidSolution.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.YangSolidSolution.citations"><code class="docutils literal notranslate"><span class="pre">YangSolidSolution.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.YangSolidSolution.compute_delta"><code class="docutils literal notranslate"><span class="pre">YangSolidSolution.compute_delta()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.YangSolidSolution.compute_omega"><code class="docutils literal notranslate"><span class="pre">YangSolidSolution.compute_omega()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.YangSolidSolution.feature_labels"><code class="docutils literal notranslate"><span class="pre">YangSolidSolution.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.YangSolidSolution.featurize"><code class="docutils literal notranslate"><span class="pre">YangSolidSolution.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.YangSolidSolution.implementors"><code class="docutils literal notranslate"><span class="pre">YangSolidSolution.implementors()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.alloy.YangSolidSolution.precheck"><code class="docutils literal notranslate"><span class="pre">YangSolidSolution.precheck()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.composition.html#module-matminer.featurizers.composition.composite">matminer.featurizers.composition.composite module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.composite.ElementProperty"><code class="docutils literal notranslate"><span class="pre">ElementProperty</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.composite.ElementProperty.__init__"><code class="docutils literal notranslate"><span class="pre">ElementProperty.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.composite.ElementProperty.citations"><code class="docutils literal notranslate"><span class="pre">ElementProperty.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.composite.ElementProperty.feature_labels"><code class="docutils literal notranslate"><span class="pre">ElementProperty.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.composite.ElementProperty.featurize"><code class="docutils literal notranslate"><span class="pre">ElementProperty.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.composite.ElementProperty.from_preset"><code class="docutils literal notranslate"><span class="pre">ElementProperty.from_preset()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.composite.ElementProperty.implementors"><code class="docutils literal notranslate"><span class="pre">ElementProperty.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.composite.Meredig"><code class="docutils literal notranslate"><span class="pre">Meredig</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.composite.Meredig.__init__"><code class="docutils literal notranslate"><span class="pre">Meredig.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.composite.Meredig.citations"><code class="docutils literal notranslate"><span class="pre">Meredig.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.composite.Meredig.feature_labels"><code class="docutils literal notranslate"><span class="pre">Meredig.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.composite.Meredig.featurize"><code class="docutils literal notranslate"><span class="pre">Meredig.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.composite.Meredig.implementors"><code class="docutils literal notranslate"><span class="pre">Meredig.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.composition.html#module-matminer.featurizers.composition.element">matminer.featurizers.composition.element module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.element.BandCenter"><code class="docutils literal notranslate"><span class="pre">BandCenter</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.element.BandCenter.citations"><code class="docutils literal notranslate"><span class="pre">BandCenter.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.element.BandCenter.deml_data"><code class="docutils literal notranslate"><span class="pre">BandCenter.deml_data</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.element.BandCenter.feature_labels"><code class="docutils literal notranslate"><span class="pre">BandCenter.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.element.BandCenter.featurize"><code class="docutils literal notranslate"><span class="pre">BandCenter.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.element.BandCenter.implementors"><code class="docutils literal notranslate"><span class="pre">BandCenter.implementors()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.element.BandCenter.magpie_data"><code class="docutils literal notranslate"><span class="pre">BandCenter.magpie_data</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.element.ElementFraction"><code class="docutils literal notranslate"><span class="pre">ElementFraction</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.element.ElementFraction.__init__"><code class="docutils literal notranslate"><span class="pre">ElementFraction.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.element.ElementFraction.citations"><code class="docutils literal notranslate"><span class="pre">ElementFraction.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.element.ElementFraction.feature_labels"><code class="docutils literal notranslate"><span class="pre">ElementFraction.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.element.ElementFraction.featurize"><code class="docutils literal notranslate"><span class="pre">ElementFraction.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.element.ElementFraction.implementors"><code class="docutils literal notranslate"><span class="pre">ElementFraction.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.element.Stoichiometry"><code class="docutils literal notranslate"><span class="pre">Stoichiometry</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.element.Stoichiometry.__init__"><code class="docutils literal notranslate"><span class="pre">Stoichiometry.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.element.Stoichiometry.citations"><code class="docutils literal notranslate"><span class="pre">Stoichiometry.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.element.Stoichiometry.feature_labels"><code class="docutils literal notranslate"><span class="pre">Stoichiometry.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.element.Stoichiometry.featurize"><code class="docutils literal notranslate"><span class="pre">Stoichiometry.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.element.Stoichiometry.implementors"><code class="docutils literal notranslate"><span class="pre">Stoichiometry.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.element.TMetalFraction"><code class="docutils literal notranslate"><span class="pre">TMetalFraction</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.element.TMetalFraction.__init__"><code class="docutils literal notranslate"><span class="pre">TMetalFraction.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.element.TMetalFraction.citations"><code class="docutils literal notranslate"><span class="pre">TMetalFraction.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.element.TMetalFraction.feature_labels"><code class="docutils literal notranslate"><span class="pre">TMetalFraction.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.element.TMetalFraction.featurize"><code class="docutils literal notranslate"><span class="pre">TMetalFraction.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.element.TMetalFraction.implementors"><code class="docutils literal notranslate"><span class="pre">TMetalFraction.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.composition.html#module-matminer.featurizers.composition.ion">matminer.featurizers.composition.ion module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.CationProperty"><code class="docutils literal notranslate"><span class="pre">CationProperty</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.CationProperty.citations"><code class="docutils literal notranslate"><span class="pre">CationProperty.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.CationProperty.feature_labels"><code class="docutils literal notranslate"><span class="pre">CationProperty.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.CationProperty.featurize"><code class="docutils literal notranslate"><span class="pre">CationProperty.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.CationProperty.from_preset"><code class="docutils literal notranslate"><span class="pre">CationProperty.from_preset()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.ElectronAffinity"><code class="docutils literal notranslate"><span class="pre">ElectronAffinity</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.ElectronAffinity.__init__"><code class="docutils literal notranslate"><span class="pre">ElectronAffinity.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.ElectronAffinity.citations"><code class="docutils literal notranslate"><span class="pre">ElectronAffinity.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.ElectronAffinity.feature_labels"><code class="docutils literal notranslate"><span class="pre">ElectronAffinity.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.ElectronAffinity.featurize"><code class="docutils literal notranslate"><span class="pre">ElectronAffinity.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.ElectronAffinity.implementors"><code class="docutils literal notranslate"><span class="pre">ElectronAffinity.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.ElectronegativityDiff"><code class="docutils literal notranslate"><span class="pre">ElectronegativityDiff</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.ElectronegativityDiff.__init__"><code class="docutils literal notranslate"><span class="pre">ElectronegativityDiff.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.ElectronegativityDiff.citations"><code class="docutils literal notranslate"><span class="pre">ElectronegativityDiff.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.ElectronegativityDiff.feature_labels"><code class="docutils literal notranslate"><span class="pre">ElectronegativityDiff.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.ElectronegativityDiff.featurize"><code class="docutils literal notranslate"><span class="pre">ElectronegativityDiff.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.ElectronegativityDiff.implementors"><code class="docutils literal notranslate"><span class="pre">ElectronegativityDiff.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.IonProperty"><code class="docutils literal notranslate"><span class="pre">IonProperty</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.IonProperty.__init__"><code class="docutils literal notranslate"><span class="pre">IonProperty.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.IonProperty.citations"><code class="docutils literal notranslate"><span class="pre">IonProperty.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.IonProperty.feature_labels"><code class="docutils literal notranslate"><span class="pre">IonProperty.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.IonProperty.featurize"><code class="docutils literal notranslate"><span class="pre">IonProperty.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.IonProperty.implementors"><code class="docutils literal notranslate"><span class="pre">IonProperty.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.OxidationStates"><code class="docutils literal notranslate"><span class="pre">OxidationStates</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.OxidationStates.__init__"><code class="docutils literal notranslate"><span class="pre">OxidationStates.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.OxidationStates.citations"><code class="docutils literal notranslate"><span class="pre">OxidationStates.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.OxidationStates.feature_labels"><code class="docutils literal notranslate"><span class="pre">OxidationStates.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.OxidationStates.featurize"><code class="docutils literal notranslate"><span class="pre">OxidationStates.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.OxidationStates.from_preset"><code class="docutils literal notranslate"><span class="pre">OxidationStates.from_preset()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.OxidationStates.implementors"><code class="docutils literal notranslate"><span class="pre">OxidationStates.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.ion.is_ionic"><code class="docutils literal notranslate"><span class="pre">is_ionic()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.composition.html#module-matminer.featurizers.composition.orbital">matminer.featurizers.composition.orbital module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.orbital.AtomicOrbitals"><code class="docutils literal notranslate"><span class="pre">AtomicOrbitals</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.orbital.AtomicOrbitals.citations"><code class="docutils literal notranslate"><span class="pre">AtomicOrbitals.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.orbital.AtomicOrbitals.feature_labels"><code class="docutils literal notranslate"><span class="pre">AtomicOrbitals.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.orbital.AtomicOrbitals.featurize"><code class="docutils literal notranslate"><span class="pre">AtomicOrbitals.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.orbital.AtomicOrbitals.implementors"><code class="docutils literal notranslate"><span class="pre">AtomicOrbitals.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.orbital.ValenceOrbital"><code class="docutils literal notranslate"><span class="pre">ValenceOrbital</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.orbital.ValenceOrbital.__init__"><code class="docutils literal notranslate"><span class="pre">ValenceOrbital.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.orbital.ValenceOrbital.citations"><code class="docutils literal notranslate"><span class="pre">ValenceOrbital.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.orbital.ValenceOrbital.feature_labels"><code class="docutils literal notranslate"><span class="pre">ValenceOrbital.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.orbital.ValenceOrbital.featurize"><code class="docutils literal notranslate"><span class="pre">ValenceOrbital.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.orbital.ValenceOrbital.implementors"><code class="docutils literal notranslate"><span class="pre">ValenceOrbital.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.composition.html#module-matminer.featurizers.composition.packing">matminer.featurizers.composition.packing module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.packing.AtomicPackingEfficiency"><code class="docutils literal notranslate"><span class="pre">AtomicPackingEfficiency</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.packing.AtomicPackingEfficiency.__init__"><code class="docutils literal notranslate"><span class="pre">AtomicPackingEfficiency.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.packing.AtomicPackingEfficiency.citations"><code class="docutils literal notranslate"><span class="pre">AtomicPackingEfficiency.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.packing.AtomicPackingEfficiency.compute_nearest_cluster_distance"><code class="docutils literal notranslate"><span class="pre">AtomicPackingEfficiency.compute_nearest_cluster_distance()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.packing.AtomicPackingEfficiency.compute_simultaneous_packing_efficiency"><code class="docutils literal notranslate"><span class="pre">AtomicPackingEfficiency.compute_simultaneous_packing_efficiency()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.packing.AtomicPackingEfficiency.create_cluster_lookup_tool"><code class="docutils literal notranslate"><span class="pre">AtomicPackingEfficiency.create_cluster_lookup_tool()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.packing.AtomicPackingEfficiency.feature_labels"><code class="docutils literal notranslate"><span class="pre">AtomicPackingEfficiency.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.packing.AtomicPackingEfficiency.featurize"><code class="docutils literal notranslate"><span class="pre">AtomicPackingEfficiency.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.packing.AtomicPackingEfficiency.find_ideal_cluster_size"><code class="docutils literal notranslate"><span class="pre">AtomicPackingEfficiency.find_ideal_cluster_size()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.packing.AtomicPackingEfficiency.get_ideal_radius_ratio"><code class="docutils literal notranslate"><span class="pre">AtomicPackingEfficiency.get_ideal_radius_ratio()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.packing.AtomicPackingEfficiency.implementors"><code class="docutils literal notranslate"><span class="pre">AtomicPackingEfficiency.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.composition.html#module-matminer.featurizers.composition.thermo">matminer.featurizers.composition.thermo module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.thermo.CohesiveEnergy"><code class="docutils literal notranslate"><span class="pre">CohesiveEnergy</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.thermo.CohesiveEnergy.__init__"><code class="docutils literal notranslate"><span class="pre">CohesiveEnergy.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.thermo.CohesiveEnergy.citations"><code class="docutils literal notranslate"><span class="pre">CohesiveEnergy.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.thermo.CohesiveEnergy.feature_labels"><code class="docutils literal notranslate"><span class="pre">CohesiveEnergy.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.thermo.CohesiveEnergy.featurize"><code class="docutils literal notranslate"><span class="pre">CohesiveEnergy.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.thermo.CohesiveEnergy.implementors"><code class="docutils literal notranslate"><span class="pre">CohesiveEnergy.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.thermo.CohesiveEnergyMP"><code class="docutils literal notranslate"><span class="pre">CohesiveEnergyMP</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.thermo.CohesiveEnergyMP.__init__"><code class="docutils literal notranslate"><span class="pre">CohesiveEnergyMP.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.thermo.CohesiveEnergyMP.citations"><code class="docutils literal notranslate"><span class="pre">CohesiveEnergyMP.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.thermo.CohesiveEnergyMP.feature_labels"><code class="docutils literal notranslate"><span class="pre">CohesiveEnergyMP.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.thermo.CohesiveEnergyMP.featurize"><code class="docutils literal notranslate"><span class="pre">CohesiveEnergyMP.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.composition.html#matminer.featurizers.composition.thermo.CohesiveEnergyMP.implementors"><code class="docutils literal notranslate"><span class="pre">CohesiveEnergyMP.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.composition.html#module-matminer.featurizers.composition">Module contents</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="matminer.featurizers.site.html">matminer.featurizers.site package</a><ul>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.site.html#subpackages">Subpackages</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.site.tests.html">matminer.featurizers.site.tests package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.tests.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.tests.html#module-matminer.featurizers.site.tests.base">matminer.featurizers.site.tests.base module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.tests.html#module-matminer.featurizers.site.tests.test_bonding">matminer.featurizers.site.tests.test_bonding module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.tests.html#module-matminer.featurizers.site.tests.test_chemical">matminer.featurizers.site.tests.test_chemical module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.tests.html#module-matminer.featurizers.site.tests.test_external">matminer.featurizers.site.tests.test_external module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.tests.html#module-matminer.featurizers.site.tests.test_fingerprint">matminer.featurizers.site.tests.test_fingerprint module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.tests.html#module-matminer.featurizers.site.tests.test_misc">matminer.featurizers.site.tests.test_misc module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.tests.html#module-matminer.featurizers.site.tests.test_rdf">matminer.featurizers.site.tests.test_rdf module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.tests.html#module-matminer.featurizers.site.tests">Module contents</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.site.html#submodules">Submodules</a></li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.site.html#module-matminer.featurizers.site.bonding">matminer.featurizers.site.bonding module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.bonding.AverageBondAngle"><code class="docutils literal notranslate"><span class="pre">AverageBondAngle</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.bonding.AverageBondAngle.__init__"><code class="docutils literal notranslate"><span class="pre">AverageBondAngle.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.bonding.AverageBondAngle.citations"><code class="docutils literal notranslate"><span class="pre">AverageBondAngle.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.bonding.AverageBondAngle.feature_labels"><code class="docutils literal notranslate"><span class="pre">AverageBondAngle.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.bonding.AverageBondAngle.featurize"><code class="docutils literal notranslate"><span class="pre">AverageBondAngle.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.bonding.AverageBondAngle.implementors"><code class="docutils literal notranslate"><span class="pre">AverageBondAngle.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.bonding.AverageBondLength"><code class="docutils literal notranslate"><span class="pre">AverageBondLength</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.bonding.AverageBondLength.__init__"><code class="docutils literal notranslate"><span class="pre">AverageBondLength.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.bonding.AverageBondLength.citations"><code class="docutils literal notranslate"><span class="pre">AverageBondLength.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.bonding.AverageBondLength.feature_labels"><code class="docutils literal notranslate"><span class="pre">AverageBondLength.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.bonding.AverageBondLength.featurize"><code class="docutils literal notranslate"><span class="pre">AverageBondLength.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.bonding.AverageBondLength.implementors"><code class="docutils literal notranslate"><span class="pre">AverageBondLength.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.bonding.BondOrientationalParameter"><code class="docutils literal notranslate"><span class="pre">BondOrientationalParameter</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.bonding.BondOrientationalParameter.__init__"><code class="docutils literal notranslate"><span class="pre">BondOrientationalParameter.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.bonding.BondOrientationalParameter.citations"><code class="docutils literal notranslate"><span class="pre">BondOrientationalParameter.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.bonding.BondOrientationalParameter.feature_labels"><code class="docutils literal notranslate"><span class="pre">BondOrientationalParameter.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.bonding.BondOrientationalParameter.featurize"><code class="docutils literal notranslate"><span class="pre">BondOrientationalParameter.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.bonding.BondOrientationalParameter.implementors"><code class="docutils literal notranslate"><span class="pre">BondOrientationalParameter.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.bonding.get_wigner_coeffs"><code class="docutils literal notranslate"><span class="pre">get_wigner_coeffs()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.site.html#module-matminer.featurizers.site.chemical">matminer.featurizers.site.chemical module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.chemical.ChemicalSRO"><code class="docutils literal notranslate"><span class="pre">ChemicalSRO</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.chemical.ChemicalSRO.__init__"><code class="docutils literal notranslate"><span class="pre">ChemicalSRO.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.chemical.ChemicalSRO.citations"><code class="docutils literal notranslate"><span class="pre">ChemicalSRO.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.chemical.ChemicalSRO.feature_labels"><code class="docutils literal notranslate"><span class="pre">ChemicalSRO.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.chemical.ChemicalSRO.featurize"><code class="docutils literal notranslate"><span class="pre">ChemicalSRO.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.chemical.ChemicalSRO.fit"><code class="docutils literal notranslate"><span class="pre">ChemicalSRO.fit()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.chemical.ChemicalSRO.from_preset"><code class="docutils literal notranslate"><span class="pre">ChemicalSRO.from_preset()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.chemical.ChemicalSRO.implementors"><code class="docutils literal notranslate"><span class="pre">ChemicalSRO.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.chemical.EwaldSiteEnergy"><code class="docutils literal notranslate"><span class="pre">EwaldSiteEnergy</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.chemical.EwaldSiteEnergy.__init__"><code class="docutils literal notranslate"><span class="pre">EwaldSiteEnergy.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.chemical.EwaldSiteEnergy.citations"><code class="docutils literal notranslate"><span class="pre">EwaldSiteEnergy.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.chemical.EwaldSiteEnergy.feature_labels"><code class="docutils literal notranslate"><span class="pre">EwaldSiteEnergy.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.chemical.EwaldSiteEnergy.featurize"><code class="docutils literal notranslate"><span class="pre">EwaldSiteEnergy.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.chemical.EwaldSiteEnergy.implementors"><code class="docutils literal notranslate"><span class="pre">EwaldSiteEnergy.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.chemical.LocalPropertyDifference"><code class="docutils literal notranslate"><span class="pre">LocalPropertyDifference</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.chemical.LocalPropertyDifference.__init__"><code class="docutils literal notranslate"><span class="pre">LocalPropertyDifference.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.chemical.LocalPropertyDifference.citations"><code class="docutils literal notranslate"><span class="pre">LocalPropertyDifference.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.chemical.LocalPropertyDifference.feature_labels"><code class="docutils literal notranslate"><span class="pre">LocalPropertyDifference.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.chemical.LocalPropertyDifference.featurize"><code class="docutils literal notranslate"><span class="pre">LocalPropertyDifference.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.chemical.LocalPropertyDifference.from_preset"><code class="docutils literal notranslate"><span class="pre">LocalPropertyDifference.from_preset()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.chemical.LocalPropertyDifference.implementors"><code class="docutils literal notranslate"><span class="pre">LocalPropertyDifference.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.chemical.SiteElementalProperty"><code class="docutils literal notranslate"><span class="pre">SiteElementalProperty</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.chemical.SiteElementalProperty.__init__"><code class="docutils literal notranslate"><span class="pre">SiteElementalProperty.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.chemical.SiteElementalProperty.citations"><code class="docutils literal notranslate"><span class="pre">SiteElementalProperty.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.chemical.SiteElementalProperty.feature_labels"><code class="docutils literal notranslate"><span class="pre">SiteElementalProperty.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.chemical.SiteElementalProperty.featurize"><code class="docutils literal notranslate"><span class="pre">SiteElementalProperty.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.chemical.SiteElementalProperty.from_preset"><code class="docutils literal notranslate"><span class="pre">SiteElementalProperty.from_preset()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.chemical.SiteElementalProperty.implementors"><code class="docutils literal notranslate"><span class="pre">SiteElementalProperty.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.site.html#module-matminer.featurizers.site.external">matminer.featurizers.site.external module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.external.SOAP"><code class="docutils literal notranslate"><span class="pre">SOAP</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.external.SOAP.__init__"><code class="docutils literal notranslate"><span class="pre">SOAP.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.external.SOAP.citations"><code class="docutils literal notranslate"><span class="pre">SOAP.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.external.SOAP.feature_labels"><code class="docutils literal notranslate"><span class="pre">SOAP.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.external.SOAP.featurize"><code class="docutils literal notranslate"><span class="pre">SOAP.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.external.SOAP.fit"><code class="docutils literal notranslate"><span class="pre">SOAP.fit()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.external.SOAP.from_preset"><code class="docutils literal notranslate"><span class="pre">SOAP.from_preset()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.external.SOAP.implementors"><code class="docutils literal notranslate"><span class="pre">SOAP.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.site.html#module-matminer.featurizers.site.fingerprint">matminer.featurizers.site.fingerprint module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.AGNIFingerprints"><code class="docutils literal notranslate"><span class="pre">AGNIFingerprints</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.AGNIFingerprints.__init__"><code class="docutils literal notranslate"><span class="pre">AGNIFingerprints.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.AGNIFingerprints.citations"><code class="docutils literal notranslate"><span class="pre">AGNIFingerprints.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.AGNIFingerprints.feature_labels"><code class="docutils literal notranslate"><span class="pre">AGNIFingerprints.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.AGNIFingerprints.featurize"><code class="docutils literal notranslate"><span class="pre">AGNIFingerprints.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.AGNIFingerprints.implementors"><code class="docutils literal notranslate"><span class="pre">AGNIFingerprints.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.ChemEnvSiteFingerprint"><code class="docutils literal notranslate"><span class="pre">ChemEnvSiteFingerprint</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.ChemEnvSiteFingerprint.__init__"><code class="docutils literal notranslate"><span class="pre">ChemEnvSiteFingerprint.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.ChemEnvSiteFingerprint.citations"><code class="docutils literal notranslate"><span class="pre">ChemEnvSiteFingerprint.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.ChemEnvSiteFingerprint.feature_labels"><code class="docutils literal notranslate"><span class="pre">ChemEnvSiteFingerprint.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.ChemEnvSiteFingerprint.featurize"><code class="docutils literal notranslate"><span class="pre">ChemEnvSiteFingerprint.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.ChemEnvSiteFingerprint.from_preset"><code class="docutils literal notranslate"><span class="pre">ChemEnvSiteFingerprint.from_preset()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.ChemEnvSiteFingerprint.implementors"><code class="docutils literal notranslate"><span class="pre">ChemEnvSiteFingerprint.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.CrystalNNFingerprint"><code class="docutils literal notranslate"><span class="pre">CrystalNNFingerprint</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.CrystalNNFingerprint.__init__"><code class="docutils literal notranslate"><span class="pre">CrystalNNFingerprint.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.CrystalNNFingerprint.citations"><code class="docutils literal notranslate"><span class="pre">CrystalNNFingerprint.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.CrystalNNFingerprint.feature_labels"><code class="docutils literal notranslate"><span class="pre">CrystalNNFingerprint.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.CrystalNNFingerprint.featurize"><code class="docutils literal notranslate"><span class="pre">CrystalNNFingerprint.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.CrystalNNFingerprint.from_preset"><code class="docutils literal notranslate"><span class="pre">CrystalNNFingerprint.from_preset()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.CrystalNNFingerprint.implementors"><code class="docutils literal notranslate"><span class="pre">CrystalNNFingerprint.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.OPSiteFingerprint"><code class="docutils literal notranslate"><span class="pre">OPSiteFingerprint</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.OPSiteFingerprint.__init__"><code class="docutils literal notranslate"><span class="pre">OPSiteFingerprint.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.OPSiteFingerprint.citations"><code class="docutils literal notranslate"><span class="pre">OPSiteFingerprint.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.OPSiteFingerprint.feature_labels"><code class="docutils literal notranslate"><span class="pre">OPSiteFingerprint.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.OPSiteFingerprint.featurize"><code class="docutils literal notranslate"><span class="pre">OPSiteFingerprint.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.OPSiteFingerprint.implementors"><code class="docutils literal notranslate"><span class="pre">OPSiteFingerprint.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.VoronoiFingerprint"><code class="docutils literal notranslate"><span class="pre">VoronoiFingerprint</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.VoronoiFingerprint.__init__"><code class="docutils literal notranslate"><span class="pre">VoronoiFingerprint.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.VoronoiFingerprint.citations"><code class="docutils literal notranslate"><span class="pre">VoronoiFingerprint.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.VoronoiFingerprint.feature_labels"><code class="docutils literal notranslate"><span class="pre">VoronoiFingerprint.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.VoronoiFingerprint.featurize"><code class="docutils literal notranslate"><span class="pre">VoronoiFingerprint.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.VoronoiFingerprint.implementors"><code class="docutils literal notranslate"><span class="pre">VoronoiFingerprint.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.load_cn_motif_op_params"><code class="docutils literal notranslate"><span class="pre">load_cn_motif_op_params()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.fingerprint.load_cn_target_motif_op"><code class="docutils literal notranslate"><span class="pre">load_cn_target_motif_op()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.site.html#module-matminer.featurizers.site.misc">matminer.featurizers.site.misc module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.misc.CoordinationNumber"><code class="docutils literal notranslate"><span class="pre">CoordinationNumber</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.misc.CoordinationNumber.__init__"><code class="docutils literal notranslate"><span class="pre">CoordinationNumber.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.misc.CoordinationNumber.citations"><code class="docutils literal notranslate"><span class="pre">CoordinationNumber.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.misc.CoordinationNumber.feature_labels"><code class="docutils literal notranslate"><span class="pre">CoordinationNumber.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.misc.CoordinationNumber.featurize"><code class="docutils literal notranslate"><span class="pre">CoordinationNumber.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.misc.CoordinationNumber.from_preset"><code class="docutils literal notranslate"><span class="pre">CoordinationNumber.from_preset()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.misc.CoordinationNumber.implementors"><code class="docutils literal notranslate"><span class="pre">CoordinationNumber.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.misc.IntersticeDistribution"><code class="docutils literal notranslate"><span class="pre">IntersticeDistribution</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.misc.IntersticeDistribution.__init__"><code class="docutils literal notranslate"><span class="pre">IntersticeDistribution.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.misc.IntersticeDistribution.analyze_area_interstice"><code class="docutils literal notranslate"><span class="pre">IntersticeDistribution.analyze_area_interstice()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.misc.IntersticeDistribution.analyze_dist_interstices"><code class="docutils literal notranslate"><span class="pre">IntersticeDistribution.analyze_dist_interstices()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.misc.IntersticeDistribution.analyze_vol_interstice"><code class="docutils literal notranslate"><span class="pre">IntersticeDistribution.analyze_vol_interstice()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.misc.IntersticeDistribution.citations"><code class="docutils literal notranslate"><span class="pre">IntersticeDistribution.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.misc.IntersticeDistribution.feature_labels"><code class="docutils literal notranslate"><span class="pre">IntersticeDistribution.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.misc.IntersticeDistribution.featurize"><code class="docutils literal notranslate"><span class="pre">IntersticeDistribution.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.misc.IntersticeDistribution.implementors"><code class="docutils literal notranslate"><span class="pre">IntersticeDistribution.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.site.html#module-matminer.featurizers.site.rdf">matminer.featurizers.site.rdf module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.rdf.AngularFourierSeries"><code class="docutils literal notranslate"><span class="pre">AngularFourierSeries</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.rdf.AngularFourierSeries.__init__"><code class="docutils literal notranslate"><span class="pre">AngularFourierSeries.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.rdf.AngularFourierSeries.citations"><code class="docutils literal notranslate"><span class="pre">AngularFourierSeries.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.rdf.AngularFourierSeries.feature_labels"><code class="docutils literal notranslate"><span class="pre">AngularFourierSeries.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.rdf.AngularFourierSeries.featurize"><code class="docutils literal notranslate"><span class="pre">AngularFourierSeries.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.rdf.AngularFourierSeries.from_preset"><code class="docutils literal notranslate"><span class="pre">AngularFourierSeries.from_preset()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.rdf.AngularFourierSeries.implementors"><code class="docutils literal notranslate"><span class="pre">AngularFourierSeries.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.rdf.GaussianSymmFunc"><code class="docutils literal notranslate"><span class="pre">GaussianSymmFunc</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.rdf.GaussianSymmFunc.__init__"><code class="docutils literal notranslate"><span class="pre">GaussianSymmFunc.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.rdf.GaussianSymmFunc.citations"><code class="docutils literal notranslate"><span class="pre">GaussianSymmFunc.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.rdf.GaussianSymmFunc.cosine_cutoff"><code class="docutils literal notranslate"><span class="pre">GaussianSymmFunc.cosine_cutoff()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.rdf.GaussianSymmFunc.feature_labels"><code class="docutils literal notranslate"><span class="pre">GaussianSymmFunc.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.rdf.GaussianSymmFunc.featurize"><code class="docutils literal notranslate"><span class="pre">GaussianSymmFunc.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.rdf.GaussianSymmFunc.g2"><code class="docutils literal notranslate"><span class="pre">GaussianSymmFunc.g2()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.rdf.GaussianSymmFunc.g4"><code class="docutils literal notranslate"><span class="pre">GaussianSymmFunc.g4()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.rdf.GaussianSymmFunc.implementors"><code class="docutils literal notranslate"><span class="pre">GaussianSymmFunc.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.rdf.GeneralizedRadialDistributionFunction"><code class="docutils literal notranslate"><span class="pre">GeneralizedRadialDistributionFunction</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.rdf.GeneralizedRadialDistributionFunction.__init__"><code class="docutils literal notranslate"><span class="pre">GeneralizedRadialDistributionFunction.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.rdf.GeneralizedRadialDistributionFunction.citations"><code class="docutils literal notranslate"><span class="pre">GeneralizedRadialDistributionFunction.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.rdf.GeneralizedRadialDistributionFunction.feature_labels"><code class="docutils literal notranslate"><span class="pre">GeneralizedRadialDistributionFunction.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.rdf.GeneralizedRadialDistributionFunction.featurize"><code class="docutils literal notranslate"><span class="pre">GeneralizedRadialDistributionFunction.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.rdf.GeneralizedRadialDistributionFunction.fit"><code class="docutils literal notranslate"><span class="pre">GeneralizedRadialDistributionFunction.fit()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.rdf.GeneralizedRadialDistributionFunction.from_preset"><code class="docutils literal notranslate"><span class="pre">GeneralizedRadialDistributionFunction.from_preset()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.site.html#matminer.featurizers.site.rdf.GeneralizedRadialDistributionFunction.implementors"><code class="docutils literal notranslate"><span class="pre">GeneralizedRadialDistributionFunction.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.site.html#module-matminer.featurizers.site">Module contents</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="matminer.featurizers.structure.html">matminer.featurizers.structure package</a><ul>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.structure.html#subpackages">Subpackages</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.tests.html">matminer.featurizers.structure.tests package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#module-matminer.featurizers.structure.tests.base">matminer.featurizers.structure.tests.base module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#module-matminer.featurizers.structure.tests.test_bonding">matminer.featurizers.structure.tests.test_bonding module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#module-matminer.featurizers.structure.tests.test_composite">matminer.featurizers.structure.tests.test_composite module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#module-matminer.featurizers.structure.tests.test_matrix">matminer.featurizers.structure.tests.test_matrix module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#module-matminer.featurizers.structure.tests.test_misc">matminer.featurizers.structure.tests.test_misc module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#module-matminer.featurizers.structure.tests.test_order">matminer.featurizers.structure.tests.test_order module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#module-matminer.featurizers.structure.tests.test_rdf">matminer.featurizers.structure.tests.test_rdf module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#module-matminer.featurizers.structure.tests.test_sites">matminer.featurizers.structure.tests.test_sites module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#module-matminer.featurizers.structure.tests.test_symmetry">matminer.featurizers.structure.tests.test_symmetry module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#module-matminer.featurizers.structure.tests">Module contents</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.structure.html#submodules">Submodules</a></li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.structure.html#module-matminer.featurizers.structure.bonding">matminer.featurizers.structure.bonding module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.BagofBonds"><code class="docutils literal notranslate"><span class="pre">BagofBonds</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.BagofBonds.__init__"><code class="docutils literal notranslate"><span class="pre">BagofBonds.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.BagofBonds.bag"><code class="docutils literal notranslate"><span class="pre">BagofBonds.bag()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.BagofBonds.citations"><code class="docutils literal notranslate"><span class="pre">BagofBonds.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.BagofBonds.feature_labels"><code class="docutils literal notranslate"><span class="pre">BagofBonds.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.BagofBonds.featurize"><code class="docutils literal notranslate"><span class="pre">BagofBonds.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.BagofBonds.fit"><code class="docutils literal notranslate"><span class="pre">BagofBonds.fit()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.BagofBonds.implementors"><code class="docutils literal notranslate"><span class="pre">BagofBonds.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.BondFractions"><code class="docutils literal notranslate"><span class="pre">BondFractions</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.BondFractions.__init__"><code class="docutils literal notranslate"><span class="pre">BondFractions.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.BondFractions.citations"><code class="docutils literal notranslate"><span class="pre">BondFractions.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.BondFractions.enumerate_all_bonds"><code class="docutils literal notranslate"><span class="pre">BondFractions.enumerate_all_bonds()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.BondFractions.enumerate_bonds"><code class="docutils literal notranslate"><span class="pre">BondFractions.enumerate_bonds()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.BondFractions.feature_labels"><code class="docutils literal notranslate"><span class="pre">BondFractions.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.BondFractions.featurize"><code class="docutils literal notranslate"><span class="pre">BondFractions.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.BondFractions.fit"><code class="docutils literal notranslate"><span class="pre">BondFractions.fit()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.BondFractions.from_preset"><code class="docutils literal notranslate"><span class="pre">BondFractions.from_preset()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.BondFractions.implementors"><code class="docutils literal notranslate"><span class="pre">BondFractions.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.GlobalInstabilityIndex"><code class="docutils literal notranslate"><span class="pre">GlobalInstabilityIndex</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.__init__"><code class="docutils literal notranslate"><span class="pre">GlobalInstabilityIndex.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.calc_bv_sum"><code class="docutils literal notranslate"><span class="pre">GlobalInstabilityIndex.calc_bv_sum()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.calc_gii_iucr"><code class="docutils literal notranslate"><span class="pre">GlobalInstabilityIndex.calc_gii_iucr()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.calc_gii_pymatgen"><code class="docutils literal notranslate"><span class="pre">GlobalInstabilityIndex.calc_gii_pymatgen()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.citations"><code class="docutils literal notranslate"><span class="pre">GlobalInstabilityIndex.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.compute_bv"><code class="docutils literal notranslate"><span class="pre">GlobalInstabilityIndex.compute_bv()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.feature_labels"><code class="docutils literal notranslate"><span class="pre">GlobalInstabilityIndex.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.featurize"><code class="docutils literal notranslate"><span class="pre">GlobalInstabilityIndex.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.get_bv_params"><code class="docutils literal notranslate"><span class="pre">GlobalInstabilityIndex.get_bv_params()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.get_equiv_sites"><code class="docutils literal notranslate"><span class="pre">GlobalInstabilityIndex.get_equiv_sites()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.implementors"><code class="docutils literal notranslate"><span class="pre">GlobalInstabilityIndex.implementors()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.precheck"><code class="docutils literal notranslate"><span class="pre">GlobalInstabilityIndex.precheck()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.MinimumRelativeDistances"><code class="docutils literal notranslate"><span class="pre">MinimumRelativeDistances</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.MinimumRelativeDistances.__init__"><code class="docutils literal notranslate"><span class="pre">MinimumRelativeDistances.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.MinimumRelativeDistances.citations"><code class="docutils literal notranslate"><span class="pre">MinimumRelativeDistances.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.MinimumRelativeDistances.feature_labels"><code class="docutils literal notranslate"><span class="pre">MinimumRelativeDistances.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.MinimumRelativeDistances.featurize"><code class="docutils literal notranslate"><span class="pre">MinimumRelativeDistances.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.MinimumRelativeDistances.fit"><code class="docutils literal notranslate"><span class="pre">MinimumRelativeDistances.fit()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.MinimumRelativeDistances.implementors"><code class="docutils literal notranslate"><span class="pre">MinimumRelativeDistances.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.StructuralHeterogeneity"><code class="docutils literal notranslate"><span class="pre">StructuralHeterogeneity</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.StructuralHeterogeneity.__init__"><code class="docutils literal notranslate"><span class="pre">StructuralHeterogeneity.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.StructuralHeterogeneity.citations"><code class="docutils literal notranslate"><span class="pre">StructuralHeterogeneity.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.StructuralHeterogeneity.feature_labels"><code class="docutils literal notranslate"><span class="pre">StructuralHeterogeneity.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.StructuralHeterogeneity.featurize"><code class="docutils literal notranslate"><span class="pre">StructuralHeterogeneity.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.bonding.StructuralHeterogeneity.implementors"><code class="docutils literal notranslate"><span class="pre">StructuralHeterogeneity.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.structure.html#module-matminer.featurizers.structure.composite">matminer.featurizers.structure.composite module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.composite.JarvisCFID"><code class="docutils literal notranslate"><span class="pre">JarvisCFID</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.composite.JarvisCFID.__init__"><code class="docutils literal notranslate"><span class="pre">JarvisCFID.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.composite.JarvisCFID.citations"><code class="docutils literal notranslate"><span class="pre">JarvisCFID.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.composite.JarvisCFID.feature_labels"><code class="docutils literal notranslate"><span class="pre">JarvisCFID.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.composite.JarvisCFID.featurize"><code class="docutils literal notranslate"><span class="pre">JarvisCFID.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.composite.JarvisCFID.get_chem"><code class="docutils literal notranslate"><span class="pre">JarvisCFID.get_chem()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.composite.JarvisCFID.get_chg"><code class="docutils literal notranslate"><span class="pre">JarvisCFID.get_chg()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.composite.JarvisCFID.get_distributions"><code class="docutils literal notranslate"><span class="pre">JarvisCFID.get_distributions()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.composite.JarvisCFID.implementors"><code class="docutils literal notranslate"><span class="pre">JarvisCFID.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.structure.html#module-matminer.featurizers.structure.matrix">matminer.featurizers.structure.matrix module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.matrix.CoulombMatrix"><code class="docutils literal notranslate"><span class="pre">CoulombMatrix</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.matrix.CoulombMatrix.__init__"><code class="docutils literal notranslate"><span class="pre">CoulombMatrix.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.matrix.CoulombMatrix.citations"><code class="docutils literal notranslate"><span class="pre">CoulombMatrix.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.matrix.CoulombMatrix.feature_labels"><code class="docutils literal notranslate"><span class="pre">CoulombMatrix.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.matrix.CoulombMatrix.featurize"><code class="docutils literal notranslate"><span class="pre">CoulombMatrix.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.matrix.CoulombMatrix.fit"><code class="docutils literal notranslate"><span class="pre">CoulombMatrix.fit()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.matrix.CoulombMatrix.implementors"><code class="docutils literal notranslate"><span class="pre">CoulombMatrix.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.matrix.OrbitalFieldMatrix"><code class="docutils literal notranslate"><span class="pre">OrbitalFieldMatrix</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.__init__"><code class="docutils literal notranslate"><span class="pre">OrbitalFieldMatrix.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.citations"><code class="docutils literal notranslate"><span class="pre">OrbitalFieldMatrix.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.feature_labels"><code class="docutils literal notranslate"><span class="pre">OrbitalFieldMatrix.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.featurize"><code class="docutils literal notranslate"><span class="pre">OrbitalFieldMatrix.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.get_atom_ofms"><code class="docutils literal notranslate"><span class="pre">OrbitalFieldMatrix.get_atom_ofms()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.get_mean_ofm"><code class="docutils literal notranslate"><span class="pre">OrbitalFieldMatrix.get_mean_ofm()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.get_ohv"><code class="docutils literal notranslate"><span class="pre">OrbitalFieldMatrix.get_ohv()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.get_single_ofm"><code class="docutils literal notranslate"><span class="pre">OrbitalFieldMatrix.get_single_ofm()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.get_structure_ofm"><code class="docutils literal notranslate"><span class="pre">OrbitalFieldMatrix.get_structure_ofm()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.implementors"><code class="docutils literal notranslate"><span class="pre">OrbitalFieldMatrix.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.matrix.SineCoulombMatrix"><code class="docutils literal notranslate"><span class="pre">SineCoulombMatrix</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.matrix.SineCoulombMatrix.__init__"><code class="docutils literal notranslate"><span class="pre">SineCoulombMatrix.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.matrix.SineCoulombMatrix.citations"><code class="docutils literal notranslate"><span class="pre">SineCoulombMatrix.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.matrix.SineCoulombMatrix.feature_labels"><code class="docutils literal notranslate"><span class="pre">SineCoulombMatrix.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.matrix.SineCoulombMatrix.featurize"><code class="docutils literal notranslate"><span class="pre">SineCoulombMatrix.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.matrix.SineCoulombMatrix.fit"><code class="docutils literal notranslate"><span class="pre">SineCoulombMatrix.fit()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.matrix.SineCoulombMatrix.implementors"><code class="docutils literal notranslate"><span class="pre">SineCoulombMatrix.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.structure.html#module-matminer.featurizers.structure.misc">matminer.featurizers.structure.misc module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.misc.EwaldEnergy"><code class="docutils literal notranslate"><span class="pre">EwaldEnergy</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.misc.EwaldEnergy.__init__"><code class="docutils literal notranslate"><span class="pre">EwaldEnergy.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.misc.EwaldEnergy.citations"><code class="docutils literal notranslate"><span class="pre">EwaldEnergy.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.misc.EwaldEnergy.feature_labels"><code class="docutils literal notranslate"><span class="pre">EwaldEnergy.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.misc.EwaldEnergy.featurize"><code class="docutils literal notranslate"><span class="pre">EwaldEnergy.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.misc.EwaldEnergy.implementors"><code class="docutils literal notranslate"><span class="pre">EwaldEnergy.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.misc.StructureComposition"><code class="docutils literal notranslate"><span class="pre">StructureComposition</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.misc.StructureComposition.__init__"><code class="docutils literal notranslate"><span class="pre">StructureComposition.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.misc.StructureComposition.citations"><code class="docutils literal notranslate"><span class="pre">StructureComposition.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.misc.StructureComposition.feature_labels"><code class="docutils literal notranslate"><span class="pre">StructureComposition.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.misc.StructureComposition.featurize"><code class="docutils literal notranslate"><span class="pre">StructureComposition.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.misc.StructureComposition.fit"><code class="docutils literal notranslate"><span class="pre">StructureComposition.fit()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.misc.StructureComposition.implementors"><code class="docutils literal notranslate"><span class="pre">StructureComposition.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.misc.XRDPowderPattern"><code class="docutils literal notranslate"><span class="pre">XRDPowderPattern</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.misc.XRDPowderPattern.__init__"><code class="docutils literal notranslate"><span class="pre">XRDPowderPattern.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.misc.XRDPowderPattern.citations"><code class="docutils literal notranslate"><span class="pre">XRDPowderPattern.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.misc.XRDPowderPattern.feature_labels"><code class="docutils literal notranslate"><span class="pre">XRDPowderPattern.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.misc.XRDPowderPattern.featurize"><code class="docutils literal notranslate"><span class="pre">XRDPowderPattern.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.misc.XRDPowderPattern.implementors"><code class="docutils literal notranslate"><span class="pre">XRDPowderPattern.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.structure.html#module-matminer.featurizers.structure.order">matminer.featurizers.structure.order module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.order.ChemicalOrdering"><code class="docutils literal notranslate"><span class="pre">ChemicalOrdering</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.order.ChemicalOrdering.__init__"><code class="docutils literal notranslate"><span class="pre">ChemicalOrdering.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.order.ChemicalOrdering.citations"><code class="docutils literal notranslate"><span class="pre">ChemicalOrdering.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.order.ChemicalOrdering.feature_labels"><code class="docutils literal notranslate"><span class="pre">ChemicalOrdering.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.order.ChemicalOrdering.featurize"><code class="docutils literal notranslate"><span class="pre">ChemicalOrdering.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.order.ChemicalOrdering.implementors"><code class="docutils literal notranslate"><span class="pre">ChemicalOrdering.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.order.DensityFeatures"><code class="docutils literal notranslate"><span class="pre">DensityFeatures</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.order.DensityFeatures.__init__"><code class="docutils literal notranslate"><span class="pre">DensityFeatures.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.order.DensityFeatures.citations"><code class="docutils literal notranslate"><span class="pre">DensityFeatures.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.order.DensityFeatures.feature_labels"><code class="docutils literal notranslate"><span class="pre">DensityFeatures.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.order.DensityFeatures.featurize"><code class="docutils literal notranslate"><span class="pre">DensityFeatures.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.order.DensityFeatures.implementors"><code class="docutils literal notranslate"><span class="pre">DensityFeatures.implementors()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.order.DensityFeatures.precheck"><code class="docutils literal notranslate"><span class="pre">DensityFeatures.precheck()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.order.MaximumPackingEfficiency"><code class="docutils literal notranslate"><span class="pre">MaximumPackingEfficiency</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.order.MaximumPackingEfficiency.citations"><code class="docutils literal notranslate"><span class="pre">MaximumPackingEfficiency.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.order.MaximumPackingEfficiency.feature_labels"><code class="docutils literal notranslate"><span class="pre">MaximumPackingEfficiency.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.order.MaximumPackingEfficiency.featurize"><code class="docutils literal notranslate"><span class="pre">MaximumPackingEfficiency.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.order.MaximumPackingEfficiency.implementors"><code class="docutils literal notranslate"><span class="pre">MaximumPackingEfficiency.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.order.StructuralComplexity"><code class="docutils literal notranslate"><span class="pre">StructuralComplexity</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.order.StructuralComplexity.__init__"><code class="docutils literal notranslate"><span class="pre">StructuralComplexity.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.order.StructuralComplexity.citations"><code class="docutils literal notranslate"><span class="pre">StructuralComplexity.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.order.StructuralComplexity.feature_labels"><code class="docutils literal notranslate"><span class="pre">StructuralComplexity.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.order.StructuralComplexity.featurize"><code class="docutils literal notranslate"><span class="pre">StructuralComplexity.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.order.StructuralComplexity.implementors"><code class="docutils literal notranslate"><span class="pre">StructuralComplexity.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.structure.html#module-matminer.featurizers.structure.rdf">matminer.featurizers.structure.rdf module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction"><code class="docutils literal notranslate"><span class="pre">ElectronicRadialDistributionFunction</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.__init__"><code class="docutils literal notranslate"><span class="pre">ElectronicRadialDistributionFunction.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.citations"><code class="docutils literal notranslate"><span class="pre">ElectronicRadialDistributionFunction.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.feature_labels"><code class="docutils literal notranslate"><span class="pre">ElectronicRadialDistributionFunction.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.featurize"><code class="docutils literal notranslate"><span class="pre">ElectronicRadialDistributionFunction.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.implementors"><code class="docutils literal notranslate"><span class="pre">ElectronicRadialDistributionFunction.implementors()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.precheck"><code class="docutils literal notranslate"><span class="pre">ElectronicRadialDistributionFunction.precheck()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.rdf.PartialRadialDistributionFunction"><code class="docutils literal notranslate"><span class="pre">PartialRadialDistributionFunction</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.__init__"><code class="docutils literal notranslate"><span class="pre">PartialRadialDistributionFunction.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.citations"><code class="docutils literal notranslate"><span class="pre">PartialRadialDistributionFunction.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.compute_prdf"><code class="docutils literal notranslate"><span class="pre">PartialRadialDistributionFunction.compute_prdf()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.feature_labels"><code class="docutils literal notranslate"><span class="pre">PartialRadialDistributionFunction.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.featurize"><code class="docutils literal notranslate"><span class="pre">PartialRadialDistributionFunction.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.fit"><code class="docutils literal notranslate"><span class="pre">PartialRadialDistributionFunction.fit()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.implementors"><code class="docutils literal notranslate"><span class="pre">PartialRadialDistributionFunction.implementors()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.precheck"><code class="docutils literal notranslate"><span class="pre">PartialRadialDistributionFunction.precheck()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.rdf.RadialDistributionFunction"><code class="docutils literal notranslate"><span class="pre">RadialDistributionFunction</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.rdf.RadialDistributionFunction.__init__"><code class="docutils literal notranslate"><span class="pre">RadialDistributionFunction.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.rdf.RadialDistributionFunction.citations"><code class="docutils literal notranslate"><span class="pre">RadialDistributionFunction.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.rdf.RadialDistributionFunction.feature_labels"><code class="docutils literal notranslate"><span class="pre">RadialDistributionFunction.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.rdf.RadialDistributionFunction.featurize"><code class="docutils literal notranslate"><span class="pre">RadialDistributionFunction.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.rdf.RadialDistributionFunction.implementors"><code class="docutils literal notranslate"><span class="pre">RadialDistributionFunction.implementors()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.rdf.RadialDistributionFunction.precheck"><code class="docutils literal notranslate"><span class="pre">RadialDistributionFunction.precheck()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.rdf.get_rdf_bin_labels"><code class="docutils literal notranslate"><span class="pre">get_rdf_bin_labels()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.structure.html#module-matminer.featurizers.structure.sites">matminer.featurizers.structure.sites module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint"><code class="docutils literal notranslate"><span class="pre">PartialsSiteStatsFingerprint</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.__init__"><code class="docutils literal notranslate"><span class="pre">PartialsSiteStatsFingerprint.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.compute_pssf"><code class="docutils literal notranslate"><span class="pre">PartialsSiteStatsFingerprint.compute_pssf()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.feature_labels"><code class="docutils literal notranslate"><span class="pre">PartialsSiteStatsFingerprint.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.featurize"><code class="docutils literal notranslate"><span class="pre">PartialsSiteStatsFingerprint.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.fit"><code class="docutils literal notranslate"><span class="pre">PartialsSiteStatsFingerprint.fit()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.implementors"><code class="docutils literal notranslate"><span class="pre">PartialsSiteStatsFingerprint.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.sites.SiteStatsFingerprint"><code class="docutils literal notranslate"><span class="pre">SiteStatsFingerprint</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.sites.SiteStatsFingerprint.__init__"><code class="docutils literal notranslate"><span class="pre">SiteStatsFingerprint.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.sites.SiteStatsFingerprint.citations"><code class="docutils literal notranslate"><span class="pre">SiteStatsFingerprint.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.sites.SiteStatsFingerprint.feature_labels"><code class="docutils literal notranslate"><span class="pre">SiteStatsFingerprint.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.sites.SiteStatsFingerprint.featurize"><code class="docutils literal notranslate"><span class="pre">SiteStatsFingerprint.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.sites.SiteStatsFingerprint.fit"><code class="docutils literal notranslate"><span class="pre">SiteStatsFingerprint.fit()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.sites.SiteStatsFingerprint.from_preset"><code class="docutils literal notranslate"><span class="pre">SiteStatsFingerprint.from_preset()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.sites.SiteStatsFingerprint.implementors"><code class="docutils literal notranslate"><span class="pre">SiteStatsFingerprint.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.structure.html#module-matminer.featurizers.structure.symmetry">matminer.featurizers.structure.symmetry module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.symmetry.Dimensionality"><code class="docutils literal notranslate"><span class="pre">Dimensionality</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.symmetry.Dimensionality.__init__"><code class="docutils literal notranslate"><span class="pre">Dimensionality.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.symmetry.Dimensionality.citations"><code class="docutils literal notranslate"><span class="pre">Dimensionality.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.symmetry.Dimensionality.feature_labels"><code class="docutils literal notranslate"><span class="pre">Dimensionality.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.symmetry.Dimensionality.featurize"><code class="docutils literal notranslate"><span class="pre">Dimensionality.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.symmetry.Dimensionality.implementors"><code class="docutils literal notranslate"><span class="pre">Dimensionality.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures"><code class="docutils literal notranslate"><span class="pre">GlobalSymmetryFeatures</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.__init__"><code class="docutils literal notranslate"><span class="pre">GlobalSymmetryFeatures.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.all_features"><code class="docutils literal notranslate"><span class="pre">GlobalSymmetryFeatures.all_features</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.citations"><code class="docutils literal notranslate"><span class="pre">GlobalSymmetryFeatures.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.crystal_idx"><code class="docutils literal notranslate"><span class="pre">GlobalSymmetryFeatures.crystal_idx</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.feature_labels"><code class="docutils literal notranslate"><span class="pre">GlobalSymmetryFeatures.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.featurize"><code class="docutils literal notranslate"><span class="pre">GlobalSymmetryFeatures.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.html#matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.implementors"><code class="docutils literal notranslate"><span class="pre">GlobalSymmetryFeatures.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.structure.html#module-matminer.featurizers.structure">Module contents</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="matminer.featurizers.tests.html">matminer.featurizers.tests package</a><ul>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.tests.html#submodules">Submodules</a></li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.tests.html#module-matminer.featurizers.tests.test_bandstructure">matminer.featurizers.tests.test_bandstructure module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_bandstructure.BandstructureFeaturesTest"><code class="docutils literal notranslate"><span class="pre">BandstructureFeaturesTest</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_bandstructure.BandstructureFeaturesTest.setUp"><code class="docutils literal notranslate"><span class="pre">BandstructureFeaturesTest.setUp()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_bandstructure.BandstructureFeaturesTest.test_BandFeaturizer"><code class="docutils literal notranslate"><span class="pre">BandstructureFeaturesTest.test_BandFeaturizer()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_bandstructure.BandstructureFeaturesTest.test_BranchPointEnergy"><code class="docutils literal notranslate"><span class="pre">BandstructureFeaturesTest.test_BranchPointEnergy()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.tests.html#module-matminer.featurizers.tests.test_base">matminer.featurizers.tests.test_base module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.FittableFeaturizer"><code class="docutils literal notranslate"><span class="pre">FittableFeaturizer</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.FittableFeaturizer.citations"><code class="docutils literal notranslate"><span class="pre">FittableFeaturizer.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.FittableFeaturizer.feature_labels"><code class="docutils literal notranslate"><span class="pre">FittableFeaturizer.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.FittableFeaturizer.featurize"><code class="docutils literal notranslate"><span class="pre">FittableFeaturizer.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.FittableFeaturizer.fit"><code class="docutils literal notranslate"><span class="pre">FittableFeaturizer.fit()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.FittableFeaturizer.implementors"><code class="docutils literal notranslate"><span class="pre">FittableFeaturizer.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.MatrixFeaturizer"><code class="docutils literal notranslate"><span class="pre">MatrixFeaturizer</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.MatrixFeaturizer.citations"><code class="docutils literal notranslate"><span class="pre">MatrixFeaturizer.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.MatrixFeaturizer.feature_labels"><code class="docutils literal notranslate"><span class="pre">MatrixFeaturizer.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.MatrixFeaturizer.featurize"><code class="docutils literal notranslate"><span class="pre">MatrixFeaturizer.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.MatrixFeaturizer.implementors"><code class="docutils literal notranslate"><span class="pre">MatrixFeaturizer.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.MultiArgs2"><code class="docutils literal notranslate"><span class="pre">MultiArgs2</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.MultiArgs2.__init__"><code class="docutils literal notranslate"><span class="pre">MultiArgs2.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.MultiArgs2.citations"><code class="docutils literal notranslate"><span class="pre">MultiArgs2.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.MultiArgs2.feature_labels"><code class="docutils literal notranslate"><span class="pre">MultiArgs2.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.MultiArgs2.featurize"><code class="docutils literal notranslate"><span class="pre">MultiArgs2.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.MultiArgs2.implementors"><code class="docutils literal notranslate"><span class="pre">MultiArgs2.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.MultiTypeFeaturizer"><code class="docutils literal notranslate"><span class="pre">MultiTypeFeaturizer</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.MultiTypeFeaturizer.citations"><code class="docutils literal notranslate"><span class="pre">MultiTypeFeaturizer.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.MultiTypeFeaturizer.feature_labels"><code class="docutils literal notranslate"><span class="pre">MultiTypeFeaturizer.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.MultiTypeFeaturizer.featurize"><code class="docutils literal notranslate"><span class="pre">MultiTypeFeaturizer.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.MultiTypeFeaturizer.implementors"><code class="docutils literal notranslate"><span class="pre">MultiTypeFeaturizer.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.MultipleFeatureFeaturizer"><code class="docutils literal notranslate"><span class="pre">MultipleFeatureFeaturizer</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.MultipleFeatureFeaturizer.citations"><code class="docutils literal notranslate"><span class="pre">MultipleFeatureFeaturizer.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.MultipleFeatureFeaturizer.feature_labels"><code class="docutils literal notranslate"><span class="pre">MultipleFeatureFeaturizer.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.MultipleFeatureFeaturizer.featurize"><code class="docutils literal notranslate"><span class="pre">MultipleFeatureFeaturizer.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.MultipleFeatureFeaturizer.implementors"><code class="docutils literal notranslate"><span class="pre">MultipleFeatureFeaturizer.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.SingleFeaturizer"><code class="docutils literal notranslate"><span class="pre">SingleFeaturizer</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.SingleFeaturizer.citations"><code class="docutils literal notranslate"><span class="pre">SingleFeaturizer.citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.SingleFeaturizer.feature_labels"><code class="docutils literal notranslate"><span class="pre">SingleFeaturizer.feature_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.SingleFeaturizer.featurize"><code class="docutils literal notranslate"><span class="pre">SingleFeaturizer.featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.SingleFeaturizer.implementors"><code class="docutils literal notranslate"><span class="pre">SingleFeaturizer.implementors()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.SingleFeaturizerMultiArgs"><code class="docutils literal notranslate"><span class="pre">SingleFeaturizerMultiArgs</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.SingleFeaturizerMultiArgs.featurize"><code class="docutils literal notranslate"><span class="pre">SingleFeaturizerMultiArgs.featurize()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.SingleFeaturizerMultiArgsWithPrecheck"><code class="docutils literal notranslate"><span class="pre">SingleFeaturizerMultiArgsWithPrecheck</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.SingleFeaturizerMultiArgsWithPrecheck.precheck"><code class="docutils literal notranslate"><span class="pre">SingleFeaturizerMultiArgsWithPrecheck.precheck()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.SingleFeaturizerWithPrecheck"><code class="docutils literal notranslate"><span class="pre">SingleFeaturizerWithPrecheck</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.SingleFeaturizerWithPrecheck.precheck"><code class="docutils literal notranslate"><span class="pre">SingleFeaturizerWithPrecheck.precheck()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.TestBaseClass"><code class="docutils literal notranslate"><span class="pre">TestBaseClass</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.TestBaseClass.make_test_data"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.make_test_data()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.TestBaseClass.setUp"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.setUp()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.TestBaseClass.test_caching"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_caching()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.TestBaseClass.test_dataframe"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_dataframe()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.TestBaseClass.test_featurize_many"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_featurize_many()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.TestBaseClass.test_fittable"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_fittable()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.TestBaseClass.test_ignore_errors"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_ignore_errors()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.TestBaseClass.test_indices"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_indices()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.TestBaseClass.test_inplace"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_inplace()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.TestBaseClass.test_matrix"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_matrix()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.TestBaseClass.test_multifeature_no_zero_index"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_multifeature_no_zero_index()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.TestBaseClass.test_multifeatures_multiargs"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_multifeatures_multiargs()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.TestBaseClass.test_multiindex_in_multifeaturizer"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_multiindex_in_multifeaturizer()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.TestBaseClass.test_multiindex_inplace"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_multiindex_inplace()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.TestBaseClass.test_multiindex_return"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_multiindex_return()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.TestBaseClass.test_multiple"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_multiple()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.TestBaseClass.test_multiprocessing_df"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_multiprocessing_df()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.TestBaseClass.test_multitype_multifeat"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_multitype_multifeat()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.TestBaseClass.test_precheck"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_precheck()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_base.TestBaseClass.test_stacked_featurizer"><code class="docutils literal notranslate"><span class="pre">TestBaseClass.test_stacked_featurizer()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.tests.html#module-matminer.featurizers.tests.test_conversions">matminer.featurizers.tests.test_conversions module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_conversions.TestConversions"><code class="docutils literal notranslate"><span class="pre">TestConversions</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_conversions.TestConversions.test_ase_conversion"><code class="docutils literal notranslate"><span class="pre">TestConversions.test_ase_conversion()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_conversions.TestConversions.test_composition_to_oxidcomposition"><code class="docutils literal notranslate"><span class="pre">TestConversions.test_composition_to_oxidcomposition()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_conversions.TestConversions.test_composition_to_structurefromMP"><code class="docutils literal notranslate"><span class="pre">TestConversions.test_composition_to_structurefromMP()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_conversions.TestConversions.test_conversion_multiindex"><code class="docutils literal notranslate"><span class="pre">TestConversions.test_conversion_multiindex()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_conversions.TestConversions.test_conversion_multiindex_dynamic"><code class="docutils literal notranslate"><span class="pre">TestConversions.test_conversion_multiindex_dynamic()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_conversions.TestConversions.test_conversion_overwrite"><code class="docutils literal notranslate"><span class="pre">TestConversions.test_conversion_overwrite()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_conversions.TestConversions.test_dict_to_object"><code class="docutils literal notranslate"><span class="pre">TestConversions.test_dict_to_object()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_conversions.TestConversions.test_json_to_object"><code class="docutils literal notranslate"><span class="pre">TestConversions.test_json_to_object()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_conversions.TestConversions.test_pymatgen_general_converter"><code class="docutils literal notranslate"><span class="pre">TestConversions.test_pymatgen_general_converter()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_conversions.TestConversions.test_str_to_composition"><code class="docutils literal notranslate"><span class="pre">TestConversions.test_str_to_composition()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_conversions.TestConversions.test_structure_to_composition"><code class="docutils literal notranslate"><span class="pre">TestConversions.test_structure_to_composition()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_conversions.TestConversions.test_structure_to_oxidstructure"><code class="docutils literal notranslate"><span class="pre">TestConversions.test_structure_to_oxidstructure()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_conversions.TestConversions.test_to_istructure"><code class="docutils literal notranslate"><span class="pre">TestConversions.test_to_istructure()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.tests.html#module-matminer.featurizers.tests.test_dos">matminer.featurizers.tests.test_dos module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_dos.DOSFeaturesTest"><code class="docutils literal notranslate"><span class="pre">DOSFeaturesTest</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_dos.DOSFeaturesTest.setUp"><code class="docutils literal notranslate"><span class="pre">DOSFeaturesTest.setUp()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_dos.DOSFeaturesTest.test_DOSFeaturizer"><code class="docutils literal notranslate"><span class="pre">DOSFeaturesTest.test_DOSFeaturizer()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_dos.DOSFeaturesTest.test_DopingFermi"><code class="docutils literal notranslate"><span class="pre">DOSFeaturesTest.test_DopingFermi()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_dos.DOSFeaturesTest.test_DosAsymmetry"><code class="docutils literal notranslate"><span class="pre">DOSFeaturesTest.test_DosAsymmetry()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_dos.DOSFeaturesTest.test_Hybridization"><code class="docutils literal notranslate"><span class="pre">DOSFeaturesTest.test_Hybridization()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_dos.DOSFeaturesTest.test_SiteDOS"><code class="docutils literal notranslate"><span class="pre">DOSFeaturesTest.test_SiteDOS()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.tests.html#module-matminer.featurizers.tests.test_function">matminer.featurizers.tests.test_function module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_function.TestFunctionFeaturizer"><code class="docutils literal notranslate"><span class="pre">TestFunctionFeaturizer</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_function.TestFunctionFeaturizer.setUp"><code class="docutils literal notranslate"><span class="pre">TestFunctionFeaturizer.setUp()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_function.TestFunctionFeaturizer.test_featurize"><code class="docutils literal notranslate"><span class="pre">TestFunctionFeaturizer.test_featurize()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_function.TestFunctionFeaturizer.test_featurize_labels"><code class="docutils literal notranslate"><span class="pre">TestFunctionFeaturizer.test_featurize_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_function.TestFunctionFeaturizer.test_helper_functions"><code class="docutils literal notranslate"><span class="pre">TestFunctionFeaturizer.test_helper_functions()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.tests.html#matminer.featurizers.tests.test_function.TestFunctionFeaturizer.test_multi_featurizer"><code class="docutils literal notranslate"><span class="pre">TestFunctionFeaturizer.test_multi_featurizer()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.tests.html#module-matminer.featurizers.tests">Module contents</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="matminer.featurizers.utils.html">matminer.featurizers.utils package</a><ul>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.utils.html#subpackages">Subpackages</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.utils.tests.html">matminer.featurizers.utils.tests package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.tests.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.tests.html#module-matminer.featurizers.utils.tests.test_grdf">matminer.featurizers.utils.tests.test_grdf module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.tests.html#module-matminer.featurizers.utils.tests.test_oxidation">matminer.featurizers.utils.tests.test_oxidation module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.tests.html#module-matminer.featurizers.utils.tests.test_stats">matminer.featurizers.utils.tests.test_stats module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.tests.html#module-matminer.featurizers.utils.tests">Module contents</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.utils.html#submodules">Submodules</a></li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.utils.html#module-matminer.featurizers.utils.grdf">matminer.featurizers.utils.grdf module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.grdf.AbstractPairwise"><code class="docutils literal notranslate"><span class="pre">AbstractPairwise</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.grdf.AbstractPairwise.name"><code class="docutils literal notranslate"><span class="pre">AbstractPairwise.name()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.grdf.AbstractPairwise.volume"><code class="docutils literal notranslate"><span class="pre">AbstractPairwise.volume()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.grdf.Bessel"><code class="docutils literal notranslate"><span class="pre">Bessel</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.grdf.Bessel.__init__"><code class="docutils literal notranslate"><span class="pre">Bessel.__init__()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.grdf.Cosine"><code class="docutils literal notranslate"><span class="pre">Cosine</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.grdf.Cosine.__init__"><code class="docutils literal notranslate"><span class="pre">Cosine.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.grdf.Cosine.volume"><code class="docutils literal notranslate"><span class="pre">Cosine.volume()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.grdf.Gaussian"><code class="docutils literal notranslate"><span class="pre">Gaussian</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.grdf.Gaussian.__init__"><code class="docutils literal notranslate"><span class="pre">Gaussian.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.grdf.Gaussian.volume"><code class="docutils literal notranslate"><span class="pre">Gaussian.volume()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.grdf.Histogram"><code class="docutils literal notranslate"><span class="pre">Histogram</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.grdf.Histogram.__init__"><code class="docutils literal notranslate"><span class="pre">Histogram.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.grdf.Histogram.volume"><code class="docutils literal notranslate"><span class="pre">Histogram.volume()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.grdf.Sine"><code class="docutils literal notranslate"><span class="pre">Sine</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.grdf.Sine.__init__"><code class="docutils literal notranslate"><span class="pre">Sine.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.grdf.Sine.volume"><code class="docutils literal notranslate"><span class="pre">Sine.volume()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.grdf.initialize_pairwise_function"><code class="docutils literal notranslate"><span class="pre">initialize_pairwise_function()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.utils.html#module-matminer.featurizers.utils.oxidation">matminer.featurizers.utils.oxidation module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.oxidation.has_oxidation_states"><code class="docutils literal notranslate"><span class="pre">has_oxidation_states()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.utils.html#module-matminer.featurizers.utils.stats">matminer.featurizers.utils.stats module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.stats.PropertyStats"><code class="docutils literal notranslate"><span class="pre">PropertyStats</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.stats.PropertyStats.avg_dev"><code class="docutils literal notranslate"><span class="pre">PropertyStats.avg_dev()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.stats.PropertyStats.calc_stat"><code class="docutils literal notranslate"><span class="pre">PropertyStats.calc_stat()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.stats.PropertyStats.eigenvalues"><code class="docutils literal notranslate"><span class="pre">PropertyStats.eigenvalues()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.stats.PropertyStats.flatten"><code class="docutils literal notranslate"><span class="pre">PropertyStats.flatten()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.stats.PropertyStats.geom_std_dev"><code class="docutils literal notranslate"><span class="pre">PropertyStats.geom_std_dev()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.stats.PropertyStats.holder_mean"><code class="docutils literal notranslate"><span class="pre">PropertyStats.holder_mean()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.stats.PropertyStats.inverse_mean"><code class="docutils literal notranslate"><span class="pre">PropertyStats.inverse_mean()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.stats.PropertyStats.kurtosis"><code class="docutils literal notranslate"><span class="pre">PropertyStats.kurtosis()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.stats.PropertyStats.maximum"><code class="docutils literal notranslate"><span class="pre">PropertyStats.maximum()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.stats.PropertyStats.mean"><code class="docutils literal notranslate"><span class="pre">PropertyStats.mean()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.stats.PropertyStats.minimum"><code class="docutils literal notranslate"><span class="pre">PropertyStats.minimum()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.stats.PropertyStats.mode"><code class="docutils literal notranslate"><span class="pre">PropertyStats.mode()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.stats.PropertyStats.quantile"><code class="docutils literal notranslate"><span class="pre">PropertyStats.quantile()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.stats.PropertyStats.range"><code class="docutils literal notranslate"><span class="pre">PropertyStats.range()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.stats.PropertyStats.skewness"><code class="docutils literal notranslate"><span class="pre">PropertyStats.skewness()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.stats.PropertyStats.sorted"><code class="docutils literal notranslate"><span class="pre">PropertyStats.sorted()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.utils.html#matminer.featurizers.utils.stats.PropertyStats.std_dev"><code class="docutils literal notranslate"><span class="pre">PropertyStats.std_dev()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.utils.html#module-matminer.featurizers.utils">Module contents</a></li>
</ul>
</li>
</ul>
</div>
</section>
<section id="submodules">
<h2>Submodules<a class="headerlink" href="#submodules" title="Permalink to this heading">¶</a></h2>
</section>
<section id="module-matminer.featurizers.bandstructure">
<span id="matminer-featurizers-bandstructure-module"></span><h2>matminer.featurizers.bandstructure module<a class="headerlink" href="#module-matminer.featurizers.bandstructure" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.bandstructure.BandFeaturizer">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.bandstructure.</span></span><span class="sig-name descname"><span class="pre">BandFeaturizer</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">kpoints</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">find_method</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'nearest'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">nbands</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">2</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.bandstructure.BandFeaturizer" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>Featurizes a pymatgen band structure object.</p>
<dl>
<dt>Args:</dt><dd><dl>
<dt>kpoints ([1x3 numpy array]): list of fractional coordinates of</dt><dd><p>k-points at which energy is extracted.</p>
</dd>
<dt>find_method (str): the method for finding or interpolating for energy</dt><dd><p>at given kpoints. It does nothing if kpoints is None.
options are:</p>
<blockquote>
<div><dl class="simple">
<dt>‘nearest’: the energy of the nearest available k-point to</dt><dd><p>the input k-point is returned.</p>
</dd>
</dl>
<p>‘linear’: the result of linear interpolation is returned
see the documentation for scipy.interpolate.griddata</p>
</div></blockquote>
</dd>
</dl>
<p>nbands (int): the number of valence/conduction bands to be featurized</p>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.bandstructure.BandFeaturizer.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">kpoints</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">find_method</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'nearest'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">nbands</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">2</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.bandstructure.BandFeaturizer.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.bandstructure.BandFeaturizer.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.bandstructure.BandFeaturizer.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.bandstructure.BandFeaturizer.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.bandstructure.BandFeaturizer.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.bandstructure.BandFeaturizer.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">bs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.bandstructure.BandFeaturizer.featurize" title="Permalink to this definition">¶</a></dt>
<dd><dl>
<dt>Args:</dt><dd><dl class="simple">
<dt>bs (pymatgen BandStructure or BandStructureSymmLine or their dict):</dt><dd><p>The band structure to featurize. To obtain all features, bs
should include the structure attribute.</p>
</dd>
</dl>
</dd>
<dt>Returns:</dt><dd><blockquote>
<div><dl class="simple">
<dt>([float]): a list of band structure features. If not bs.structure,</dt><dd><p>features that require the structure will be returned as NaN.</p>
</dd>
</dl>
</div></blockquote>
<dl>
<dt>List of currently supported features:</dt><dd><p>band_gap (eV): the difference between the CBM and VBM energy
is_gap_direct (0.0|1.0): whether the band gap is direct or not
direct_gap (eV): the minimum direct distance of the last</p>
<blockquote>
<div><p>valence band and the first conduction band</p>
</div></blockquote>
<dl class="simple">
<dt>p_ex1_norm (float): k-space distance between Gamma point</dt><dd><p>and k-point of VBM</p>
</dd>
<dt>n_ex1_norm (float): k-space distance between Gamma point</dt><dd><p>and k-point of CBM</p>
</dd>
</dl>
<p>p_ex1_degen: degeneracy of VBM
n_ex1_degen: degeneracy of CBM
if kpoints is provided (e.g. for kpoints == [[0.0, 0.0, 0.0]]):</p>
<blockquote>
<div><dl class="simple">
<dt>n_0.0;0.0;0.0_en: (energy of the first conduction band at</dt><dd><p>[0.0, 0.0, 0.0] - CBM energy)</p>
</dd>
<dt>p_0.0;0.0;0.0_en: (energy of the last valence band at</dt><dd><p>[0.0, 0.0, 0.0] - VBM energy)</p>
</dd>
</dl>
</div></blockquote>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.bandstructure.BandFeaturizer.get_bindex_bspin">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">get_bindex_bspin</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">extremum</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">is_cbm</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.bandstructure.BandFeaturizer.get_bindex_bspin" title="Permalink to this definition">¶</a></dt>
<dd><p>Returns the band index and spin of band extremum</p>
<dl>
<dt>Args:</dt><dd><dl class="simple">
<dt>extremum (dict): dictionary containing the CBM/VBM, i.e. output of</dt><dd><p>Bandstructure.get_cbm()</p>
</dd>
</dl>
<p>is_cbm (bool): whether the extremum is the CBM or not</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.bandstructure.BandFeaturizer.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.bandstructure.BandFeaturizer.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.bandstructure.BranchPointEnergy">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.bandstructure.</span></span><span class="sig-name descname"><span class="pre">BranchPointEnergy</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n_vb</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">n_cb</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">calculate_band_edges</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">atol</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1e-05</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.bandstructure.BranchPointEnergy" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>Branch point energy and absolute band edge position.</p>
<p>Calculates the branch point energy and (optionally) an absolute band
edge position assuming the branch point energy is the center of the gap</p>
<dl>
<dt>Args:</dt><dd><p>n_vb (int): number of valence bands to include in BPE calc
n_cb (int): number of conduction bands to include in BPE calc
calculate_band_edges: (bool) whether to also return band edge</p>
<blockquote>
<div><p>positions</p>
</div></blockquote>
<dl class="simple">
<dt>atol (float): absolute tolerance when finding equivalent fractional</dt><dd><p>k-points in irreducible brillouin zone (IBZ) when weights is None</p>
</dd>
</dl>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.bandstructure.BranchPointEnergy.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n_vb</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">n_cb</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">calculate_band_edges</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">atol</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1e-05</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.bandstructure.BranchPointEnergy.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.bandstructure.BranchPointEnergy.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.bandstructure.BranchPointEnergy.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.bandstructure.BranchPointEnergy.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.bandstructure.BranchPointEnergy.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><dl class="simple">
<dt>Returns ([str]): absolute energy levels as provided in the input</dt><dd><p>BandStructure. “absolute” means no reference energy is subtracted
from branch_point_energy, vbm or cbm.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.bandstructure.BranchPointEnergy.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">bs</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">target_gap</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">weights</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.bandstructure.BranchPointEnergy.featurize" title="Permalink to this definition">¶</a></dt>
<dd><dl>
<dt>Args:</dt><dd><p>bs (BandStructure): Uniform (not symm line) band structure
target_gap (float): if set the band gap is scissored to match this</p>
<blockquote>
<div><p>number</p>
</div></blockquote>
<dl class="simple">
<dt>weights ([float]): if set, its length has to be equal to bs.kpoints</dt><dd><p>to explicitly determine the k-point weights when averaging</p>
</dd>
</dl>
</dd>
<dt>Returns:</dt><dd><p>(int) branch point energy on same energy scale as BS eigenvalues</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.bandstructure.BranchPointEnergy.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.bandstructure.BranchPointEnergy.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.base">
<span id="matminer-featurizers-base-module"></span><h2>matminer.featurizers.base module<a class="headerlink" href="#module-matminer.featurizers.base" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.base.BaseFeaturizer">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.base.</span></span><span class="sig-name descname"><span class="pre">BaseFeaturizer</span></span><a class="headerlink" href="#matminer.featurizers.base.BaseFeaturizer" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">BaseEstimator</span></code>, <code class="xref py py-class docutils literal notranslate"><span class="pre">TransformerMixin</span></code>, <a class="reference external" href="https://docs.python.org/3/library/abc.html#abc.ABC" title="(in Python v3.11)"><code class="xref py py-class docutils literal notranslate"><span class="pre">ABC</span></code></a></p>
<p>Abstract class to calculate features from raw materials input data
such a compound formula or a pymatgen crystal structure or
bandstructure object.</p>
<p>## Using a BaseFeaturizer Class</p>
<p>There are multiple ways for running the featurize routines:</p>
<blockquote>
<div><p><cite>featurize</cite>: Featurize a single entry
<cite>featurize_many</cite>: Featurize a list of entries
<cite>featurize_dataframe</cite>: Compute features for many entries, store results</p>
<blockquote>
<div><p>as columns in a dataframe</p>
</div></blockquote>
</div></blockquote>
<p>Some featurizers require first calling the <cite>fit</cite> method before the
featurization methods can function. Generally, you pass the dataset to
fit to determine which features a featurizer should compute. For example,
a featurizer that returns the partial radial distribution function
may need to know which elements are present in a dataset.</p>
<p>You can also use the <cite>precheck</cite> and <cite>precheck_dataframe</cite> methods to
ensure a featurizer is in scope for a given sample (or dataset) before
featurizing.</p>
<p>You can also employ the featurizer as part of a ScikitLearn Pipeline object.
For these cases, ScikitLearn calls the <cite>transform</cite> function of the
<cite>BaseFeaturizer</cite> which is a less-featured wrapper of <cite>featurize_many</cite>. You
would then provide your input data as an array to the Pipeline, which would
output the features as an array.</p>
<p>Beyond the featurizing capability, BaseFeaturizer also includes methods
for retrieving proper references for a featurizer. The <cite>citations</cite> function
returns a list of papers that should be cited. The <cite>implementors</cite> function
returns a list of people who wrote the featurizer, so that you know
who to contact with questions.</p>
<p>## Implementing a New BaseFeaturizer Class</p>
<dl>
<dt>These operations must be implemented for each new featurizer:</dt><dd><dl class="simple">
<dt><cite>featurize</cite> - Takes a single material as input, returns the features of</dt><dd><p>that material.</p>
</dd>
<dt><cite>feature_labels</cite> - Generates a human-meaningful name for each of the</dt><dd><p>features.</p>
</dd>
</dl>
<p><cite>citations</cite> - Returns a list of citations in BibTeX format
<cite>implementors</cite> - Returns a list of people who contributed to writing a</p>
<blockquote>
<div><p>paper.</p>
</div></blockquote>
</dd>
</dl>
<p>None of these operations should change the state of the featurizer. I.e.,
running each method twice should not produce different results, no class
attributes should be changed, and running one operation should not affect
the output of another.</p>
<p>All options of the featurizer must be set by the <cite>__init__</cite> function. All
options must be listed as keyword arguments with default values, and the
value must be saved as a class attribute with the same name (e.g., argument
<cite>n</cite> should be stored in <cite>self.n</cite>). These requirements are necessary for
compatibility with the <cite>get_params</cite> and <cite>set_params</cite> methods of
<cite>BaseEstimator</cite>, which enable easy interoperability with ScikitLearn</p>
<p>Depending on the complexity of your featurizer, it may be worthwhile to
implement a <cite>from_preset</cite> class method. The <cite>from_preset</cite> method takes the
name of a preset and returns an instance of the featurizer with some
hard-coded set of inputs. The <cite>from_preset</cite> option is particularly useful
for defining the settings used by papers in the literature.</p>
<p>Optionally, you can implement the <cite>fit</cite> operation if there are attributes of
your featurizer that must be set for the featurizer to work. Any variables
that are set by fitting should be stored as class attributes that end with
an underscore. (This follows the pattern used by ScikitLearn).</p>
<p>Another option to consider is whether it is worth making any utility
operations for your featurizer. <cite>featurize</cite> must return a list of features,
but this may not be the most natural representation for your features (e.g.,
a <cite>dict</cite> could be better). Making a separate function for computing features
in this natural representation and having the <cite>featurize</cite> function call this
method and then convert the data into a list is a recommended approach.
Users who want to compute the representation in the natural form can use the
utility function and users who want the data in a ML-ready format (list) can
call <cite>featurize</cite>. See <cite>PartialRadialDistributionFunction</cite> for an example of
this concept.</p>
<p>An additional factor to consider is the chunksize for data parallelisation.
For lightweight computational tasks, the overhead associated with passing
data from <cite>multiprocessing.Pool.map()</cite> to the function being parallelized
can increase the time taken for all tasks to be completed. By setting
the <cite>self._chunksize</cite> argument, the overhead associated with passing data
to the tasks can be reduced. Note that there is only an advantage to using
chunksize when the time taken to pass the data from <cite>map</cite> to the function
call is within several orders of magnitude to that of the function call
itself. By default, we allow the Python multiprocessing library to determine
the chunk size automatically based on the size of the list being featurized.
You may want to specify a small chunk size for computationally-expensive
featurizers, which will enable better distribution of tasks across threads.
In contrast, for more lightweight featurizers, it is recommended that
the implementor trial a range of chunksize values to find the optimum.
As a general rule of thumb, if the featurize function takes 0.1 seconds or
less, a chunksize of around 30 will perform best.</p>
<p>## Documenting a BaseFeaturizer</p>
<p>The class documentation for each featurizer must contain a description of
the options and the features that will be computed. The options of the class
must all be defined in the <cite>__init__</cite> function of the class, and we
recommend documenting them using the
[Google style](<a class="reference external" href="https://google.github.io/styleguide/pyguide.html">https://google.github.io/styleguide/pyguide.html</a>).</p>
<p>For auto-generated documentation purposes, the first line of the featurizer
doc should come under the class declaration (not under __init__) and should
be a one line summary of the featurizer.</p>
<p>We recommend starting the class documentation with a high-level overview of
the features. For example, mention what kind of characteristics of the
material they describe and refer the reader to a paper that describes these
features well (use a hyperlink if possible, so that the readthedocs will
link to that paper). Then, describe each of the individual features in a
block named “Features”. It is necessary here to give the user enough
information for user to map a feature name what it means. The objective in
this part is to allow people to understand what each column of their
dataframe is without having to read the Python code. You do not need to
explain all of the math/algorithms behind each feature for them to be able
to reproduce the feature, just to get an idea what it is.</p>
<dl class="py property">
<dt class="sig sig-object py" id="matminer.featurizers.base.BaseFeaturizer.chunksize">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">chunksize</span></span><a class="headerlink" href="#matminer.featurizers.base.BaseFeaturizer.chunksize" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.base.BaseFeaturizer.citations">
<em class="property"><span class="pre">abstract</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.base.BaseFeaturizer.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.base.BaseFeaturizer.feature_labels">
<em class="property"><span class="pre">abstract</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.base.BaseFeaturizer.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.base.BaseFeaturizer.featurize">
<em class="property"><span class="pre">abstract</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.base.BaseFeaturizer.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Main featurizer function, which has to be implemented
in any derived featurizer subclass.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>x: input data to featurize (type depends on featurizer).</p>
</dd>
<dt>Returns:</dt><dd><p>(list) one or more features.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.base.BaseFeaturizer.featurize_dataframe">
<span class="sig-name descname"><span class="pre">featurize_dataframe</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">df</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">col_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ignore_errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">return_errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">inplace</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">multiindex</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pbar</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.base.BaseFeaturizer.featurize_dataframe" title="Permalink to this definition">¶</a></dt>
<dd><p>Compute features for all entries contained in input dataframe.</p>
<dl>
<dt>Args:</dt><dd><p>df (Pandas dataframe): Dataframe containing input data.
col_id (str or list of str): column label containing objects to</p>
<blockquote>
<div><p>featurize. Can be multiple labels if the featurize function
requires multiple inputs.</p>
</div></blockquote>
<dl class="simple">
<dt>ignore_errors (bool): Returns NaN for dataframe rows where</dt><dd><p>exceptions are thrown if True. If False, exceptions
are thrown as normal.</p>
</dd>
<dt>return_errors (bool). Returns the errors encountered for each</dt><dd><p>row in a separate <cite>XFeaturizer errors</cite> column if True. Requires
ignore_errors to be True.</p>
</dd>
<dt>inplace (bool): If True, adds columns to the original object in</dt><dd><p>memory and returns None. Else, returns the updated object.
Should be identical to pandas inplace behavior.</p>
</dd>
<dt>multiindex (bool): If True, use a Featurizer - Feature 2-level</dt><dd><p>index using the MultiIndex capabilities of pandas. If done
inplace, multiindex featurization will overwrite the original
dataframe’s column index.</p>
</dd>
</dl>
<p>pbar (bool): Shows a progress bar if True.</p>
</dd>
<dt>Returns:</dt><dd><p>updated dataframe.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.base.BaseFeaturizer.featurize_many">
<span class="sig-name descname"><span class="pre">featurize_many</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">entries</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ignore_errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">return_errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pbar</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.base.BaseFeaturizer.featurize_many" title="Permalink to this definition">¶</a></dt>
<dd><p>Featurize a list of entries.</p>
<p>If <cite>featurize</cite> takes multiple inputs, supply inputs as a list of tuples.</p>
<p>Featurize_many supports entries as a list, tuple, numpy array,
Pandas Series, or Pandas DataFrame.</p>
<dl>
<dt>Args:</dt><dd><p>entries (list-like object): A list of entries to be featurized.
ignore_errors (bool): Returns NaN for entries where exceptions are</p>
<blockquote>
<div><p>thrown if True. If False, exceptions are thrown as normal.</p>
</div></blockquote>
<dl class="simple">
<dt>return_errors (bool): If True, returns the feature list as</dt><dd><p>determined by ignore_errors with traceback strings added
as an extra ‘feature’. Entries which featurize without
exceptions have this extra feature set to NaN.</p>
</dd>
</dl>
<p>pbar (bool): Show a progress bar for featurization if True.</p>
</dd>
<dt>Returns:</dt><dd><p>(list) features for each entry.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.base.BaseFeaturizer.featurize_wrapper">
<span class="sig-name descname"><span class="pre">featurize_wrapper</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">return_errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ignore_errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.base.BaseFeaturizer.featurize_wrapper" title="Permalink to this definition">¶</a></dt>
<dd><p>An exception wrapper for featurize, used in featurize_many and
featurize_dataframe. featurize_wrapper changes the behavior of featurize
when ignore_errors is True in featurize_many/dataframe.</p>
<dl>
<dt>Args:</dt><dd><p>x: input data to featurize (type depends on featurizer).
ignore_errors (bool): Returns NaN for entries where exceptions are</p>
<blockquote>
<div><p>thrown if True. If False, exceptions are thrown as normal.</p>
</div></blockquote>
<dl class="simple">
<dt>return_errors (bool): If True, returns the feature list as</dt><dd><p>determined by ignore_errors with traceback strings added
as an extra ‘feature’. Entries which featurize without
exceptions have this extra feature set to NaN.</p>
</dd>
</dl>
</dd>
<dt>Returns:</dt><dd><p>(list) one or more features.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.base.BaseFeaturizer.fit">
<span class="sig-name descname"><span class="pre">fit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">X</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">fit_kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.base.BaseFeaturizer.fit" title="Permalink to this definition">¶</a></dt>
<dd><p>Update the parameters of this featurizer based on available data</p>
<dl class="simple">
<dt>Args:</dt><dd><p>X - [list of tuples], training data</p>
</dd>
<dt>Returns:</dt><dd><p>self</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.base.BaseFeaturizer.fit_featurize_dataframe">
<span class="sig-name descname"><span class="pre">fit_featurize_dataframe</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">df</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">col_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fit_args</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.base.BaseFeaturizer.fit_featurize_dataframe" title="Permalink to this definition">¶</a></dt>
<dd><p>The dataframe equivalent of fit_transform. Takes a dataframe and
column id as input, fits the featurizer to that dataframe, and
returns a featurized dataframe. Accepts the same arguments as
featurize_dataframe.</p>
<dl>
<dt>Args:</dt><dd><p>df (Pandas dataframe): Dataframe containing input data.
col_id (str or list of str): column label containing objects to</p>
<blockquote>
<div><p>featurize. Can be multiple labels if the featurize function
requires multiple inputs.</p>
</div></blockquote>
<p>fit_args (list): list of arguments for fit function.</p>
</dd>
<dt>Returns:</dt><dd><p>updated dataframe based on featurizer fitted to that dataframe.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.base.BaseFeaturizer.implementors">
<em class="property"><span class="pre">abstract</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.base.BaseFeaturizer.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="matminer.featurizers.base.BaseFeaturizer.n_jobs">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">n_jobs</span></span><a class="headerlink" href="#matminer.featurizers.base.BaseFeaturizer.n_jobs" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.base.BaseFeaturizer.precheck">
<span class="sig-name descname"><span class="pre">precheck</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.11)"><span class="pre">bool</span></a></span></span><a class="headerlink" href="#matminer.featurizers.base.BaseFeaturizer.precheck" title="Permalink to this definition">¶</a></dt>
<dd><p>Precheck (provide an estimate of whether a featurizer will work or not)
for a single entry (e.g., a single composition). If the entry fails the
precheck, it will most likely fail featurization; if it passes, it is
likely (but not guaranteed) to featurize correctly.</p>
<dl class="simple">
<dt>Prechecks should be:</dt><dd><ul class="simple">
<li><p>accurate (but can be good estimates rather than ground truth)</p></li>
<li><p>fast to evaluate</p></li>
<li><dl class="simple">
<dt>unlikely to be obsolete via changes in the featurizer in the near</dt><dd><p>future</p>
</dd>
</dl>
</li>
</ul>
</dd>
</dl>
<p>This method should be overridden by any featurizer requiring its
use, as by default all entries will pass prechecking. Also, precheck
is a good opportunity to throw warnings about long runtimes (e.g., doing
nearest neighbors computations on a structure with many thousand sites).</p>
<p>See the documentation for precheck_dataframe for more information.</p>
<dl class="simple">
<dt>Args:</dt><dd><dl class="simple">
<dt><a href="#id1"><span class="problematic" id="id2">*</span></a>x (Composition, Structure, etc.): Input to-be-featurized. Can be</dt><dd><p>a single input or multiple inputs.</p>
</dd>
</dl>
</dd>
<dt>Returns:</dt><dd><p>(bool): True, if passes the precheck. False, if fails.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.base.BaseFeaturizer.precheck_dataframe">
<span class="sig-name descname"><span class="pre">precheck_dataframe</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">df</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">col_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">return_frac</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">inplace</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Union" title="(in Python v3.11)"><span class="pre">Union</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.11)"><span class="pre">float</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">DataFrame</span><span class="p"><span class="pre">]</span></span></span></span><a class="headerlink" href="#matminer.featurizers.base.BaseFeaturizer.precheck_dataframe" title="Permalink to this definition">¶</a></dt>
<dd><p>Precheck an entire dataframe. Subclasses wanting to use precheck
functionality should not override this method, they should override
precheck (unless the entire df determines whether single entries pass
or fail a precheck).</p>
<p>Prechecking should be a quick and useful way to check that for a
particular dataframe (set of featurizer inputs), the featurizer is:</p>
<blockquote>
<div><ol class="arabic simple">
<li><p>in scope, and/or…</p></li>
<li><p>robust to errors and/or…</p></li>
<li><dl class="simple">
<dt>any other reason you would not practically want to use this</dt><dd><p>featurizer in on this dataframe.</p>
</dd>
</dl>
</li>
</ol>
</div></blockquote>
<p>By prechecking before featurizing, you can avoid applying featurizers
to data that will ultimately fail, return unreliable numbers, or
are out of scope. Prechecking is also a good time to throw/observe
warnings (such as long runtime warnings!).</p>
<dl>
<dt>Args:</dt><dd><p>df (pd.DataFrame): A dataframe
col_id (str or [str]): column label containing objects to featurize.</p>
<blockquote>
<div><p>Can be multiple labels if the featurize function requires
multiple inputs.</p>
</div></blockquote>
<dl class="simple">
<dt>return_frac (bool): If True, returns the fraction of entries</dt><dd><p>passing the precheck (e.g., 0.5). Else, returns a dataframe.</p>
</dd>
<dt>inplace (bool); Only relevant if return_frac=False. If inplace=True,</dt><dd><p>the input dataframe is modified in memory with a boolean column
for precheck. Otherwise, a new df with this column is returned.</p>
</dd>
</dl>
</dd>
<dt>Returns:</dt><dd><dl class="simple">
<dt>(bool, pd.DataFrame): If return_frac=True, returns the fraction of</dt><dd><p>entries passing the precheck. Else, returns the dataframe with
an extra boolean column added for the precheck.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.base.BaseFeaturizer.set_chunksize">
<span class="sig-name descname"><span class="pre">set_chunksize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">chunksize</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.base.BaseFeaturizer.set_chunksize" title="Permalink to this definition">¶</a></dt>
<dd><p>Set the chunksize used for Pool.map parallelisation.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.base.BaseFeaturizer.set_n_jobs">
<span class="sig-name descname"><span class="pre">set_n_jobs</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n_jobs</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.11)"><span class="pre">int</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.11)"><span class="pre">None</span></a></span></span><a class="headerlink" href="#matminer.featurizers.base.BaseFeaturizer.set_n_jobs" title="Permalink to this definition">¶</a></dt>
<dd><p>Set the number of concurrent jobs to spawn during featurization.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>n_jobs (int): Number of threads in multiprocessing pool.</p>
</dd>
</dl>
<p>Note: It seems multiprocessing can be the cause of out-of-memory (OOM) errors, especially when trying to featurize large structures on HPC nodes with strict memory limits. Using featurizer.set_n_jobs(1) has been known to help as a workaround.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.base.BaseFeaturizer.transform">
<span class="sig-name descname"><span class="pre">transform</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">X</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.base.BaseFeaturizer.transform" title="Permalink to this definition">¶</a></dt>
<dd><p>Compute features for a list of inputs</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.base.MultipleFeaturizer">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.base.</span></span><span class="sig-name descname"><span class="pre">MultipleFeaturizer</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">featurizers</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">iterate_over_entries</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.base.MultipleFeaturizer" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>Class to run multiple featurizers on the same input data.</p>
<p>All featurizers must take the same kind of data as input
to the featurize function.</p>
<dl>
<dt>Args:</dt><dd><p>featurizers (list of BaseFeaturizer): A list of featurizers to run.
iterate_over_entries (bool): Whether to iterate over the entries or</p>
<blockquote>
<div><p>featurizers. Iterating over entries will enable increased caching
but will only display a single progress bar for all featurizers.
If set to False, iteration will be performed over featurizers,
resulting in reduced caching but individual progress bars for each
featurizer.</p>
</div></blockquote>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.base.MultipleFeaturizer.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">featurizers</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">iterate_over_entries</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.base.MultipleFeaturizer.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.base.MultipleFeaturizer.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.base.MultipleFeaturizer.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.base.MultipleFeaturizer.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.base.MultipleFeaturizer.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.base.MultipleFeaturizer.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.base.MultipleFeaturizer.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Main featurizer function, which has to be implemented
in any derived featurizer subclass.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>x: input data to featurize (type depends on featurizer).</p>
</dd>
<dt>Returns:</dt><dd><p>(list) one or more features.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.base.MultipleFeaturizer.featurize_many">
<span class="sig-name descname"><span class="pre">featurize_many</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">entries</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ignore_errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">return_errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pbar</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.base.MultipleFeaturizer.featurize_many" title="Permalink to this definition">¶</a></dt>
<dd><p>Featurize a list of entries.</p>
<p>If <cite>featurize</cite> takes multiple inputs, supply inputs as a list of tuples.</p>
<p>Featurize_many supports entries as a list, tuple, numpy array,
Pandas Series, or Pandas DataFrame.</p>
<dl>
<dt>Args:</dt><dd><p>entries (list-like object): A list of entries to be featurized.
ignore_errors (bool): Returns NaN for entries where exceptions are</p>
<blockquote>
<div><p>thrown if True. If False, exceptions are thrown as normal.</p>
</div></blockquote>
<dl class="simple">
<dt>return_errors (bool): If True, returns the feature list as</dt><dd><p>determined by ignore_errors with traceback strings added
as an extra ‘feature’. Entries which featurize without
exceptions have this extra feature set to NaN.</p>
</dd>
</dl>
<p>pbar (bool): Show a progress bar for featurization if True.</p>
</dd>
<dt>Returns:</dt><dd><p>(list) features for each entry.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.base.MultipleFeaturizer.featurize_wrapper">
<span class="sig-name descname"><span class="pre">featurize_wrapper</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">return_errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ignore_errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.base.MultipleFeaturizer.featurize_wrapper" title="Permalink to this definition">¶</a></dt>
<dd><p>An exception wrapper for featurize, used in featurize_many and
featurize_dataframe. featurize_wrapper changes the behavior of featurize
when ignore_errors is True in featurize_many/dataframe.</p>
<dl>
<dt>Args:</dt><dd><p>x: input data to featurize (type depends on featurizer).
ignore_errors (bool): Returns NaN for entries where exceptions are</p>
<blockquote>
<div><p>thrown if True. If False, exceptions are thrown as normal.</p>
</div></blockquote>
<dl class="simple">
<dt>return_errors (bool): If True, returns the feature list as</dt><dd><p>determined by ignore_errors with traceback strings added
as an extra ‘feature’. Entries which featurize without
exceptions have this extra feature set to NaN.</p>
</dd>
</dl>
</dd>
<dt>Returns:</dt><dd><p>(list) one or more features.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.base.MultipleFeaturizer.fit">
<span class="sig-name descname"><span class="pre">fit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">X</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">fit_kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.base.MultipleFeaturizer.fit" title="Permalink to this definition">¶</a></dt>
<dd><p>Update the parameters of this featurizer based on available data</p>
<dl class="simple">
<dt>Args:</dt><dd><p>X - [list of tuples], training data</p>
</dd>
<dt>Returns:</dt><dd><p>self</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.base.MultipleFeaturizer.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.base.MultipleFeaturizer.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.base.MultipleFeaturizer.set_n_jobs">
<span class="sig-name descname"><span class="pre">set_n_jobs</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n_jobs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.base.MultipleFeaturizer.set_n_jobs" title="Permalink to this definition">¶</a></dt>
<dd><p>Set the number of concurrent jobs to spawn during featurization.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>n_jobs (int): Number of threads in multiprocessing pool.</p>
</dd>
</dl>
<p>Note: It seems multiprocessing can be the cause of out-of-memory (OOM) errors, especially when trying to featurize large structures on HPC nodes with strict memory limits. Using featurizer.set_n_jobs(1) has been known to help as a workaround.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.base.StackedFeaturizer">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.base.</span></span><span class="sig-name descname"><span class="pre">StackedFeaturizer</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">featurizer</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">model</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">class_names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.base.StackedFeaturizer" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>Use the output of a machine learning model as features</p>
<p>For regression models, we use the single output class.</p>
<p>For classification models, we use the probability for the first N-1 classes where N is the
number of classes.</p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.base.StackedFeaturizer.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">featurizer</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">model</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">class_names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.base.StackedFeaturizer.__init__" title="Permalink to this definition">¶</a></dt>
<dd><p>Initialize featurizer</p>
<dl>
<dt>Args:</dt><dd><p>featurizer (BaseFeaturizer): Featurizer used to generate inputs to the model
model (BaseEstimator): Fitted machine learning model to be evaluated
name (str): [Optional] name of model, used when creating feature names</p>
<blockquote>
<div><p>class_names ([str]): Required for classification models, used when creating
feature names (scikit-learn does not specify the number of classes for
a classifier). Class names must be in the same order as the classes in the model
(e.g., class_names[0] must be the name of the class 0)</p>
</div></blockquote>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.base.StackedFeaturizer.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.base.StackedFeaturizer.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.base.StackedFeaturizer.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.base.StackedFeaturizer.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.base.StackedFeaturizer.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.base.StackedFeaturizer.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Main featurizer function, which has to be implemented
in any derived featurizer subclass.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>x: input data to featurize (type depends on featurizer).</p>
</dd>
<dt>Returns:</dt><dd><p>(list) one or more features.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.base.StackedFeaturizer.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.base.StackedFeaturizer.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.conversions">
<span id="matminer-featurizers-conversions-module"></span><h2>matminer.featurizers.conversions module<a class="headerlink" href="#module-matminer.featurizers.conversions" title="Permalink to this heading">¶</a></h2>
<p>This module defines featurizers that can convert between different data formats</p>
<p>Note that these featurizers do not produce machine learning-ready features.
Instead, they should be used to pre-process data, either through a standalone
transformation or as part of a Pipeline.</p>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.ASEAtomstoStructure">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.conversions.</span></span><span class="sig-name descname"><span class="pre">ASEAtomstoStructure</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_col_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'PMG</span> <span class="pre">Structure</span> <span class="pre">from</span> <span class="pre">ASE</span> <span class="pre">Atoms'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">overwrite_data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.ASEAtomstoStructure" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.conversions.ConversionFeaturizer" title="matminer.featurizers.conversions.ConversionFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">ConversionFeaturizer</span></code></a></p>
<p>Convert dataframes of ase structures to pymatgen structures for further use with
matminer.</p>
<dl>
<dt>Args:</dt><dd><p>target_col_id (str): Column to place PMG structures.
overwrite_data (bool): If True, will overwrite target_col_id even if there is</p>
<blockquote>
<div><p>data currently in that column</p>
</div></blockquote>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.ASEAtomstoStructure.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_col_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'PMG</span> <span class="pre">Structure</span> <span class="pre">from</span> <span class="pre">ASE</span> <span class="pre">Atoms'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">overwrite_data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.ASEAtomstoStructure.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.ASEAtomstoStructure.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ase_atoms</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.ASEAtomstoStructure.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Main featurizer function, which has to be implemented
in any derived featurizer subclass.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>x: input data to featurize (type depends on featurizer).</p>
</dd>
<dt>Returns:</dt><dd><p>(list) one or more features.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.ASEAtomstoStructure.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.ASEAtomstoStructure.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.CompositionToOxidComposition">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.conversions.</span></span><span class="sig-name descname"><span class="pre">CompositionToOxidComposition</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_col_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'composition_oxid'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">overwrite_data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">coerce_mixed</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">return_original_on_error</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.CompositionToOxidComposition" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.conversions.ConversionFeaturizer" title="matminer.featurizers.conversions.ConversionFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">ConversionFeaturizer</span></code></a></p>
<p>Utility featurizer to add oxidation states to a pymatgen Composition.</p>
<p>Oxidation states are determined using pymatgen’s guessing routines.
The expected input is a <cite>pymatgen.core.composition.Composition</cite> object.</p>
<p>Note that this Featurizer does not produce machine learning-ready features
but instead can be applied to pre-process data or as part of a Pipeline.</p>
<dl class="simple">
<dt>Args:</dt><dd><dl class="simple">
<dt>target_col_id (str or None): The column in which the converted data will</dt><dd><p>be written. If the column already exists then an error will be
thrown unless <cite>overwrite_data</cite> is set to <cite>True</cite>. If <cite>target_col_id</cite>
begins with an underscore the data will be written to the column:
<cite>“{}_{}”.format(col_id, target_col_id[1:])</cite>, where <cite>col_id</cite> is the
column being featurized. If <cite>target_col_id</cite> is set to None then
the data will be written “in place” to the <cite>col_id</cite> column (this
will only work if <cite>overwrite_data=True</cite>).</p>
</dd>
<dt>overwrite_data (bool): Overwrite any data in <cite>target_column</cite> if it</dt><dd><p>exists.</p>
</dd>
<dt>coerce_mixed (bool): If a composition has both species containing</dt><dd><p>oxid states and not containing oxid states, strips all of the
oxid states and guesses the entire composition’s oxid states.</p>
</dd>
<dt>return_original_on_error: If the oxidation states cannot be</dt><dd><p>guessed and set to True, the composition without oxidation states
will be returned. If set to False, an error will be thrown.</p>
</dd>
<dt><a href="#id3"><span class="problematic" id="id4">**</span></a>kwargs: Parameters to control the settings for</dt><dd><p><cite>pymatgen.io.structure.Structure.add_oxidation_state_by_guess()</cite>.</p>
</dd>
</dl>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.CompositionToOxidComposition.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_col_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'composition_oxid'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">overwrite_data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">coerce_mixed</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">return_original_on_error</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.CompositionToOxidComposition.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.CompositionToOxidComposition.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.CompositionToOxidComposition.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.CompositionToOxidComposition.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">comp</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.CompositionToOxidComposition.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Add oxidation states to a Structure using pymatgen’s guessing routines.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>comp (<cite>pymatgen.core.composition.Composition</cite>): A composition.</p>
</dd>
<dt>Returns:</dt><dd><dl class="simple">
<dt>(<cite>pymatgen.core.composition.Composition</cite>): A Composition object</dt><dd><p>decorated with oxidation states.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.CompositionToOxidComposition.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.CompositionToOxidComposition.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.CompositionToStructureFromMP">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.conversions.</span></span><span class="sig-name descname"><span class="pre">CompositionToStructureFromMP</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_col_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'structure'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">overwrite_data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mapi_key</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.CompositionToStructureFromMP" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.conversions.ConversionFeaturizer" title="matminer.featurizers.conversions.ConversionFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">ConversionFeaturizer</span></code></a></p>
<p>Featurizer to get a Structure object from Materials Project using the
composition alone. The most stable entry from Materials Project is selected,
or NaN if no entry is found in the Materials Project.</p>
<dl>
<dt>Args:</dt><dd><dl class="simple">
<dt>target_col_id (str or None): The column in which the converted data will</dt><dd><p>be written. If the column already exists then an error will be
thrown unless <cite>overwrite_data</cite> is set to <cite>True</cite>. If <cite>target_col_id</cite>
begins with an underscore the data will be written to the column:
<cite>“{}_{}”.format(col_id, target_col_id[1:])</cite>, where <cite>col_id</cite> is the
column being featurized. If <cite>target_col_id</cite> is set to None then
the data will be written “in place” to the <cite>col_id</cite> column (this
will only work if <cite>overwrite_data=True</cite>).</p>
</dd>
<dt>overwrite_data (bool): Overwrite any data in <cite>target_column</cite> if it</dt><dd><p>exists.</p>
</dd>
</dl>
<p>map_key (str): Materials API key</p>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.CompositionToStructureFromMP.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_col_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'structure'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">overwrite_data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mapi_key</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.CompositionToStructureFromMP.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.CompositionToStructureFromMP.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.CompositionToStructureFromMP.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.CompositionToStructureFromMP.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">comp</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.CompositionToStructureFromMP.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Get the most stable structure from Materials Project
Args:</p>
<blockquote>
<div><p>comp (<cite>pymatgen.core.composition.Composition</cite>): A composition.</p>
</div></blockquote>
<dl class="simple">
<dt>Returns:</dt><dd><p>(<cite>pymatgen.core.structure.Structure</cite>): A Structure object.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.CompositionToStructureFromMP.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.CompositionToStructureFromMP.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.ConversionFeaturizer">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.conversions.</span></span><span class="sig-name descname"><span class="pre">ConversionFeaturizer</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_col_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">overwrite_data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.ConversionFeaturizer" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>Abstract class to perform data conversions.</p>
<p>Featurizers subclassing this class do not produce machine learning-ready
features but instead are used to pre-process data. As Featurizers,
the conversion process can take advantage of the parallelisation implemented
in ScikitLearn.</p>
<p>Note that <cite>feature_labels</cite> are set dynamically and may depend on the column
id of the data being featurized. As such, <cite>feature_labels</cite> may differ
before and after featurization.</p>
<p>ConversionFeaturizers differ from other Featurizers in that the user can
can specify the column in which to write the converted data. The output
column is controlled through <cite>target_col_id</cite>. ConversionFeaturizers also
have the ability to overwrite data in existing columns. This is
controlled by the <cite>overwrite_data</cite> option. “in place” conversion of data can
be achieved by setting <cite>target_col_id=None</cite> and <cite>overwrite_data=True</cite>. See
the docstring below for more details.</p>
<dl class="simple">
<dt>Args:</dt><dd><dl class="simple">
<dt>target_col_id (str or None): The column in which the converted data will</dt><dd><p>be written. If the column already exists then an error will be
thrown unless <cite>overwrite_data</cite> is set to <cite>True</cite>. If <cite>target_col_id</cite>
begins with an underscore the data will be written to the column:
<cite>“{}_{}”.format(col_id, target_col_id[1:])</cite>, where <cite>col_id</cite> is the
column being featurized. If <cite>target_col_id</cite> is set to None then
the data will be written “in place” to the <cite>col_id</cite> column (this
will only work if <cite>overwrite_data=True</cite>).</p>
</dd>
<dt>overwrite_data (bool): Overwrite any data in <cite>target_col_id</cite> if it</dt><dd><p>exists.</p>
</dd>
</dl>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.ConversionFeaturizer.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_col_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">overwrite_data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.ConversionFeaturizer.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.ConversionFeaturizer.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.ConversionFeaturizer.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.ConversionFeaturizer.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.ConversionFeaturizer.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.ConversionFeaturizer.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.ConversionFeaturizer.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Main featurizer function, which has to be implemented
in any derived featurizer subclass.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>x: input data to featurize (type depends on featurizer).</p>
</dd>
<dt>Returns:</dt><dd><p>(list) one or more features.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.ConversionFeaturizer.featurize_dataframe">
<span class="sig-name descname"><span class="pre">featurize_dataframe</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">df</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">col_id</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.ConversionFeaturizer.featurize_dataframe" title="Permalink to this definition">¶</a></dt>
<dd><p>Perform the data conversion and set the target column dynamically.</p>
<p><cite>target_col_id</cite>, and accordingly <cite>feature_labels</cite>, may depend on the
column id of the data being featurized. As such, <cite>target_col_id</cite> is
first set dynamically before the <cite>BaseFeaturizer.featurize_dataframe()</cite>
super method is called.</p>
<dl>
<dt>Args:</dt><dd><p>df (Pandas.DataFrame): Dataframe containing input data.
col_id (str or list of str): column label containing objects to</p>
<blockquote>
<div><p>featurize. Can be multiple labels if the featurize function
requires multiple inputs.</p>
</div></blockquote>
<dl class="simple">
<dt><a href="#id5"><span class="problematic" id="id6">**</span></a>kwargs: Additional keyword arguments that will be passed through</dt><dd><p>to <cite>BaseFeaturizer.featurize_dataframe()</cite>.</p>
</dd>
</dl>
</dd>
<dt>Returns:</dt><dd><p>(Pandas.Dataframe): The updated dataframe.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.ConversionFeaturizer.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.ConversionFeaturizer.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.DictToObject">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.conversions.</span></span><span class="sig-name descname"><span class="pre">DictToObject</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_col_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'_object'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">overwrite_data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.DictToObject" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.conversions.ConversionFeaturizer" title="matminer.featurizers.conversions.ConversionFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">ConversionFeaturizer</span></code></a></p>
<p>Utility featurizer to decode a dict to Python object via MSON.</p>
<p>Note that this Featurizer does not produce machine learning-ready features
but instead can be applied to pre-process data or as part of a Pipeline.</p>
<dl class="simple">
<dt>Args:</dt><dd><dl class="simple">
<dt>target_col_id (str or None): The column in which the converted data will</dt><dd><p>be written. If the column already exists then an error will be
thrown unless <cite>overwrite_data</cite> is set to <cite>True</cite>. If <cite>target_col_id</cite>
begins with an underscore the data will be written to the column:
<cite>“{}_{}”.format(col_id, target_col_id[1:])</cite>, where <cite>col_id</cite> is the
column being featurized. If <cite>target_col_id</cite> is set to None then
the data will be written “in place” to the <cite>col_id</cite> column (this
will only work if <cite>overwrite_data=True</cite>).</p>
</dd>
<dt>overwrite_data (bool): Overwrite any data in <cite>target_column</cite> if it</dt><dd><p>exists.</p>
</dd>
</dl>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.DictToObject.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_col_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'_object'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">overwrite_data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.DictToObject.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.DictToObject.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.DictToObject.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.DictToObject.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dict_data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.DictToObject.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Convert a string to a pymatgen Composition.</p>
<dl class="simple">
<dt>Args:</dt><dd><dl class="simple">
<dt>dict_data (dict): A MSONable dictionary. E.g. Produced from</dt><dd><p><cite>pymatgen.core.structure.Structure.as_dict()</cite>.</p>
</dd>
</dl>
</dd>
<dt>Returns:</dt><dd><p>(object): An object with the type specified by <cite>dict_data</cite>.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.DictToObject.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.DictToObject.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.JsonToObject">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.conversions.</span></span><span class="sig-name descname"><span class="pre">JsonToObject</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_col_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'_object'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">overwrite_data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.JsonToObject" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.conversions.ConversionFeaturizer" title="matminer.featurizers.conversions.ConversionFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">ConversionFeaturizer</span></code></a></p>
<p>Utility featurizer to decode json data to a Python object via MSON.</p>
<p>Note that this Featurizer does not produce machine learning-ready features
but instead can be applied to pre-process data or as part of a Pipeline.</p>
<dl class="simple">
<dt>Args:</dt><dd><dl class="simple">
<dt>target_col_id (str or None): The column in which the converted data will</dt><dd><p>be written. If the column already exists then an error will be
thrown unless <cite>overwrite_data</cite> is set to <cite>True</cite>. If <cite>target_col_id</cite>
begins with an underscore the data will be written to the column:
<cite>“{}_{}”.format(col_id, target_col_id[1:])</cite>, where <cite>col_id</cite> is the
column being featurized. If <cite>target_col_id</cite> is set to None then
the data will be written “in place” to the <cite>col_id</cite> column (this
will only work if <cite>overwrite_data=True</cite>).</p>
</dd>
<dt>overwrite_data (bool): Overwrite any data in <cite>target_column</cite> if it</dt><dd><p>exists.</p>
</dd>
</dl>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.JsonToObject.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_col_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'_object'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">overwrite_data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.JsonToObject.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.JsonToObject.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.JsonToObject.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.JsonToObject.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">json_data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.JsonToObject.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Convert a string to a pymatgen Composition.</p>
<dl class="simple">
<dt>Args:</dt><dd><dl class="simple">
<dt>json_data (dict): MSONable json data. E.g. Produced from</dt><dd><p><cite>pymatgen.core.structure.Structure.to_json()</cite>.</p>
</dd>
</dl>
</dd>
<dt>Returns:</dt><dd><p>(object): An object with the type specified by <cite>json_data</cite>.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.JsonToObject.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.JsonToObject.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.PymatgenFunctionApplicator">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.conversions.</span></span><span class="sig-name descname"><span class="pre">PymatgenFunctionApplicator</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">func</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">func_args</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">func_kwargs</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">target_col_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">overwrite_data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.PymatgenFunctionApplicator" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.conversions.ConversionFeaturizer" title="matminer.featurizers.conversions.ConversionFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">ConversionFeaturizer</span></code></a></p>
<p>Featurizer to run any function using on/from pymatgen primitives.</p>
<p>For example, apply</p>
<blockquote>
<div><p>lambda structure: structure.composition.anonymized_formula</p>
</div></blockquote>
<p>To all rows in a dataframe.</p>
<p>And return the results in the specified column.</p>
<dl>
<dt>Args:</dt><dd><p>func (function): Function object or lambda to pass the pmg primitive objects to.
func_args (list): List of args to pass along with the pmg object to func.
func_kwargs (dict): Dict of kwargs to pass along with the pmg object to func,
target_col_id (str): Output column for the results. If not provided, the func name</p>
<blockquote>
<div><p>will be used.</p>
</div></blockquote>
<dl class="simple">
<dt>overwrite_data (bool): If True, will overwrite target_col_id even if there is</dt><dd><p>data currently in that column</p>
</dd>
</dl>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.PymatgenFunctionApplicator.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">func</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">func_args</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">func_kwargs</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">target_col_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">overwrite_data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.PymatgenFunctionApplicator.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.PymatgenFunctionApplicator.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">obj</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.PymatgenFunctionApplicator.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Main featurizer function, which has to be implemented
in any derived featurizer subclass.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>x: input data to featurize (type depends on featurizer).</p>
</dd>
<dt>Returns:</dt><dd><p>(list) one or more features.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.PymatgenFunctionApplicator.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.PymatgenFunctionApplicator.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.StrToComposition">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.conversions.</span></span><span class="sig-name descname"><span class="pre">StrToComposition</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">reduce</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">target_col_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'composition'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">overwrite_data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.StrToComposition" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.conversions.ConversionFeaturizer" title="matminer.featurizers.conversions.ConversionFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">ConversionFeaturizer</span></code></a></p>
<p>Utility featurizer to convert a string to a Composition</p>
<p>The expected input is a composition in string form (e.g. “Fe2O3”).</p>
<p>Note that this Featurizer does not produce machine learning-ready features
but instead can be applied to pre-process data or as part of a Pipeline.</p>
<dl class="simple">
<dt>Args:</dt><dd><dl class="simple">
<dt>reduce (bool): Whether to return a reduced</dt><dd><p><cite>pymatgen.core.composition.Composition</cite> object.</p>
</dd>
<dt>target_col_id (str or None): The column in which the converted data will</dt><dd><p>be written. If the column already exists then an error will be
thrown unless <cite>overwrite_data</cite> is set to <cite>True</cite>. If <cite>target_col_id</cite>
begins with an underscore the data will be written to the column:
<cite>“{}_{}”.format(col_id, target_col_id[1:])</cite>, where <cite>col_id</cite> is the
column being featurized. If <cite>target_col_id</cite> is set to None then
the data will be written “in place” to the <cite>col_id</cite> column (this
will only work if <cite>overwrite_data=True</cite>).</p>
</dd>
<dt>overwrite_data (bool): Overwrite any data in <cite>target_column</cite> if it</dt><dd><p>exists.</p>
</dd>
</dl>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.StrToComposition.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">reduce</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">target_col_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'composition'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">overwrite_data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.StrToComposition.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.StrToComposition.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.StrToComposition.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.StrToComposition.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string_composition</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.StrToComposition.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Convert a string to a pymatgen Composition.</p>
<dl class="simple">
<dt>Args:</dt><dd><dl class="simple">
<dt>string_composition (str): A chemical formula as a string (e.g.</dt><dd><p>“Fe2O3”).</p>
</dd>
</dl>
</dd>
<dt>Returns:</dt><dd><p>(<cite>pymatgen.core.composition.Composition</cite>): A composition object.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.StrToComposition.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.StrToComposition.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.StructureToComposition">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.conversions.</span></span><span class="sig-name descname"><span class="pre">StructureToComposition</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">reduce</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">target_col_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'composition'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">overwrite_data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.StructureToComposition" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.conversions.ConversionFeaturizer" title="matminer.featurizers.conversions.ConversionFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">ConversionFeaturizer</span></code></a></p>
<p>Utility featurizer to convert a Structure to a Composition.</p>
<p>The expected input is a <cite>pymatgen.core.structure.Structure</cite> object.</p>
<p>Note that this Featurizer does not produce machine learning-ready features
but instead can be applied to pre-process data or as part of a Pipeline.</p>
<dl>
<dt>Args:</dt><dd><p>reduce (bool): Whether to return a reduced Composition object.
target_col_id (str or None): The column in which the converted data will</p>
<blockquote>
<div><p>be written. If the column already exists then an error will be
thrown unless <cite>overwrite_data</cite> is set to <cite>True</cite>. If <cite>target_col_id</cite>
begins with an underscore the data will be written to the column:
<cite>“{}_{}”.format(col_id, target_col_id[1:])</cite>, where <cite>col_id</cite> is the
column being featurized. If <cite>target_col_id</cite> is set to None then
the data will be written “in place” to the <cite>col_id</cite> column (this
will only work if <cite>overwrite_data=True</cite>).</p>
</div></blockquote>
<dl class="simple">
<dt>overwrite_data (bool): Overwrite any data in <cite>target_column</cite> if it</dt><dd><p>exists.</p>
</dd>
</dl>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.StructureToComposition.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">reduce</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">target_col_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'composition'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">overwrite_data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.StructureToComposition.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.StructureToComposition.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.StructureToComposition.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.StructureToComposition.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">structure</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.StructureToComposition.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Convert a string to a pymatgen Composition.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>structure (<cite>pymatgen.core.structure.Structure</cite>): A structure.</p>
</dd>
<dt>Returns:</dt><dd><p>(<cite>pymatgen.core.composition.Composition</cite>): A Composition object.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.StructureToComposition.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.StructureToComposition.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.StructureToIStructure">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.conversions.</span></span><span class="sig-name descname"><span class="pre">StructureToIStructure</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_col_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'istructure'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">overwrite_data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.StructureToIStructure" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.conversions.ConversionFeaturizer" title="matminer.featurizers.conversions.ConversionFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">ConversionFeaturizer</span></code></a></p>
<p>Utility featurizer to convert a Structure to an immutable IStructure.</p>
<p>This is useful if you are using features that employ caching.</p>
<p>The expected input is a <cite>pymatgen.core.structure.Structure</cite> object.</p>
<p>Note that this Featurizer does not produce machine learning-ready features
but instead can be applied to pre-process data or as part of a Pipeline.</p>
<dl class="simple">
<dt>Args:</dt><dd><dl class="simple">
<dt>target_col_id (str or None): The column in which the converted data will</dt><dd><p>be written. If the column already exists then an error will be
thrown unless <cite>overwrite_data</cite> is set to <cite>True</cite>. If <cite>target_col_id</cite>
begins with an underscore the data will be written to the column:
<cite>“{}_{}”.format(col_id, target_col_id[1:])</cite>, where <cite>col_id</cite> is the
column being featurized. If <cite>target_col_id</cite> is set to None then
the data will be written “in place” to the <cite>col_id</cite> column (this
will only work if <cite>overwrite_data=True</cite>).</p>
</dd>
<dt>overwrite_data (bool): Overwrite any data in <cite>target_column</cite> if it</dt><dd><p>exists.</p>
</dd>
</dl>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.StructureToIStructure.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_col_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'istructure'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">overwrite_data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.StructureToIStructure.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.StructureToIStructure.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.StructureToIStructure.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.StructureToIStructure.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">structure</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.StructureToIStructure.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Convert a pymatgen Structure to an immutable IStructure,</p>
<dl class="simple">
<dt>Args:</dt><dd><p>structure (<cite>pymatgen.core.structure.Structure</cite>): A structure.</p>
</dd>
<dt>Returns:</dt><dd><dl class="simple">
<dt>(<cite>pymatgen.core.structure.IStructure</cite>): An immutable IStructure</dt><dd><p>object.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.StructureToIStructure.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.StructureToIStructure.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.StructureToOxidStructure">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.conversions.</span></span><span class="sig-name descname"><span class="pre">StructureToOxidStructure</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_col_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'structure_oxid'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">overwrite_data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">return_original_on_error</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.StructureToOxidStructure" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.conversions.ConversionFeaturizer" title="matminer.featurizers.conversions.ConversionFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">ConversionFeaturizer</span></code></a></p>
<p>Utility featurizer to add oxidation states to a pymatgen Structure.</p>
<p>Oxidation states are determined using pymatgen’s guessing routines.
The expected input is a <cite>pymatgen.core.structure.Structure</cite> object.</p>
<p>Note that this Featurizer does not produce machine learning-ready features
but instead can be applied to pre-process data or as part of a Pipeline.</p>
<dl class="simple">
<dt>Args:</dt><dd><dl class="simple">
<dt>target_col_id (str or None): The column in which the converted data will</dt><dd><p>be written. If the column already exists then an error will be
thrown unless <cite>overwrite_data</cite> is set to <cite>True</cite>. If <cite>target_col_id</cite>
begins with an underscore the data will be written to the column:
<cite>“{}_{}”.format(col_id, target_col_id[1:])</cite>, where <cite>col_id</cite> is the
column being featurized. If <cite>target_col_id</cite> is set to None then
the data will be written “in place” to the <cite>col_id</cite> column (this
will only work if <cite>overwrite_data=True</cite>).</p>
</dd>
<dt>overwrite_data (bool): Overwrite any data in <cite>target_column</cite> if it</dt><dd><p>exists.</p>
</dd>
<dt>return_original_on_error: If the oxidation states cannot be</dt><dd><p>guessed and set to True, the structure without oxidation states will
be returned. If set to False, an error will be thrown.</p>
</dd>
<dt><a href="#id7"><span class="problematic" id="id8">**</span></a>kwargs: Parameters to control the settings for</dt><dd><p><cite>pymatgen.io.structure.Structure.add_oxidation_state_by_guess()</cite>.</p>
</dd>
</dl>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.StructureToOxidStructure.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_col_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'structure_oxid'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">overwrite_data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">return_original_on_error</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.StructureToOxidStructure.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.StructureToOxidStructure.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.StructureToOxidStructure.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.StructureToOxidStructure.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">structure</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.StructureToOxidStructure.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Add oxidation states to a Structure using pymatgen’s guessing routines.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>structure (<cite>pymatgen.core.structure.Structure</cite>): A structure.</p>
</dd>
<dt>Returns:</dt><dd><dl class="simple">
<dt>(<cite>pymatgen.core.structure.Structure</cite>): A Structure object decorated</dt><dd><p>with oxidation states.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.conversions.StructureToOxidStructure.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.conversions.StructureToOxidStructure.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.dos">
<span id="matminer-featurizers-dos-module"></span><h2>matminer.featurizers.dos module<a class="headerlink" href="#module-matminer.featurizers.dos" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.dos.DOSFeaturizer">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.dos.</span></span><span class="sig-name descname"><span class="pre">DOSFeaturizer</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">contributors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">decay_length</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sampling_resolution</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">100</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">gaussian_smear</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.05</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.DOSFeaturizer" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>Significant character and contribution of the density of state from a
CompleteDos, object. Contributors are the atomic orbitals from each site
within the structure. This underlines the importance of dos.structure.</p>
<dl>
<dt>Args:</dt><dd><dl class="simple">
<dt>contributors (int):</dt><dd><p>Sets the number of top contributors to the DOS that are
returned as features. (i.e. contributors=1 will only return the
main cb and main vb orbital)</p>
</dd>
<dt>decay_length (float in eV):</dt><dd><p>The dos is sampled by an exponential decay function. this parameter
sets the decay length of the exponential. Three times the decay
length corresponds to 10% sampling strength. There is a hard cutoff
at five times the decay length (1% sampling strength)</p>
</dd>
<dt>sampling_resolution (int):</dt><dd><p>Number of points to sample DOS</p>
</dd>
<dt>gaussian_smear (float in eV):</dt><dd><p>Gaussian smearing (sigma) around each sampled point in the DOS</p>
</dd>
</dl>
</dd>
<dt>Returns (featurize returns [float] and featurize_labels returns [str]):</dt><dd><p>xbm_score_i (float): fractions of ith contributor orbital
xbm_location_i (str): fractional coordinate of ith contributor/site
xbm_character_i (str): character of ith contributor (s, p, d, f)
xbm_specie_i (str): elemental specie of ith contributor (ex: ‘Ti’)
xbm_hybridization (int): the amount of hybridization at the band edge</p>
<blockquote>
<div><p>characterized by an entropy score (x ln x). the hybridization score
is larger for a greater number of significant contributors</p>
</div></blockquote>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.dos.DOSFeaturizer.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">contributors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">decay_length</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sampling_resolution</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">100</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">gaussian_smear</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.05</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.DOSFeaturizer.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.dos.DOSFeaturizer.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.DOSFeaturizer.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.dos.DOSFeaturizer.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.DOSFeaturizer.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><dl class="simple">
<dt>Returns ([str]): list of names of the features. See the docs for the</dt><dd><p>featurize method for more information.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.dos.DOSFeaturizer.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dos</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.DOSFeaturizer.featurize" title="Permalink to this definition">¶</a></dt>
<dd><dl class="simple">
<dt>Args:</dt><dd><dl class="simple">
<dt>dos (pymatgen CompleteDos or their dict):</dt><dd><p>The density of states to featurize. Must be a complete DOS,
(i.e. contains PDOS and structure, in addition to total DOS)
and must contain the structure.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.dos.DOSFeaturizer.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.DOSFeaturizer.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.dos.DopingFermi">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.dos.</span></span><span class="sig-name descname"><span class="pre">DopingFermi</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dopings</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">eref</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'midgap'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">T</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">300</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">return_eref</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.DopingFermi" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>The fermi level (w.r.t. selected reference energy) associated with a
specified carrier concentration (1/cm3) and temperature. This featurizar
requires the total density of states and structure. The Structure
as dos.structure (e.g. in CompleteDos) is required by FermiDos class.</p>
<dl>
<dt>Args:</dt><dd><dl class="simple">
<dt>dopings ([float]): list of doping concentrations 1/cm3. Note that a</dt><dd><p>negative concentration is treated as electron majority carrier
(n-type) and positive for holes (p-type)</p>
</dd>
<dt>eref (str or int or float): energy alignment reference. Defaults</dt><dd><p>to midgap (equilibrium fermi). A fixed number can also be used.
str options: “midgap”, “vbm”, “cbm”, “dos_fermi”, “band_center”</p>
</dd>
</dl>
<p>T (float): absolute temperature in Kelvin
return_eref: if True, instead of aligning the fermi levels based</p>
<blockquote>
<div><p>on eref, it (eref) will be explicitly returned as a feature</p>
</div></blockquote>
</dd>
<dt>Returns (featurize returns [float] and featurize_labels returns [str]):</dt><dd><dl class="simple">
<dt>examples:</dt><dd><dl class="simple">
<dt>fermi_c-1e+20T300 (float): the fermi level for the electron</dt><dd><p>concentration of 1e20 and the temperature of 300K.</p>
</dd>
<dt>fermi_c1e+18T600 (float): fermi level for the hole concentration</dt><dd><p>of 1e18 and the temperature of 600K.</p>
</dd>
<dt>midgap eref (float): if return_eref==True then eref (midgap here)</dt><dd><p>energy is returned. In this case, fermi levels are absolute as
opposed to relative to eref (i.e. if not return_eref)</p>
</dd>
</dl>
</dd>
</dl>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.dos.DopingFermi.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dopings</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">eref</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'midgap'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">T</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">300</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">return_eref</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.DopingFermi.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.dos.DopingFermi.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.DopingFermi.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.dos.DopingFermi.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.DopingFermi.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><dl class="simple">
<dt>Returns ([str]): list of names of the features generated by featurize</dt><dd><p>example: “fermi_c-1e+20T300” that is the fermi level for the
electron concentration of 1e20 (c-1e+20) and temperature of 300K.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.dos.DopingFermi.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dos</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bandgap</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.DopingFermi.featurize" title="Permalink to this definition">¶</a></dt>
<dd><dl>
<dt>Args:</dt><dd><p>dos (pymatgen Dos, CompleteDos or FermiDos):
bandgap (float): for example the experimentally measured band gap</p>
<blockquote>
<div><p>or one that is calculated via more accurate methods than the
one used to generate dos. dos will be scissored to have the
same electronic band gap as bandgap.</p>
</div></blockquote>
</dd>
<dt>Returns ([float]): features are fermi levels in eV at the given</dt><dd><p>concentrations and temperature + eref in eV if return_eref</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.dos.DopingFermi.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.DopingFermi.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.dos.DosAsymmetry">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.dos.</span></span><span class="sig-name descname"><span class="pre">DosAsymmetry</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">decay_length</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.5</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sampling_resolution</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">100</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">gaussian_smear</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.05</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.DosAsymmetry" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>Quantifies the asymmetry of the DOS near the Fermi level.</p>
<p>The DOS asymmetry is defined the natural logarithm of the quotient of the
total DOS above the Fermi level and the total DOS below the Fermi level. A
positive number indicates that there are more states directly above the
Fermi level than below the Fermi level. This featurizer is only meant for
metals and semi-metals.</p>
<dl class="simple">
<dt>Args:</dt><dd><dl class="simple">
<dt>decay_length (float in eV):</dt><dd><p>The dos is sampled by an exponential decay function. this parameter
sets the decay length of the exponential. Three times the decay
length corresponds to 10% sampling strength. There is a hard cutoff
at five times the decay length (1% sampling strength)</p>
</dd>
<dt>sampling_resolution (int):</dt><dd><p>Number of points to sample DOS</p>
</dd>
<dt>gaussian_smear (float in eV):</dt><dd><p>Gaussian smearing (sigma) around each sampled point in the DOS</p>
</dd>
</dl>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.dos.DosAsymmetry.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">decay_length</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.5</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sampling_resolution</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">100</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">gaussian_smear</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.05</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.DosAsymmetry.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.dos.DosAsymmetry.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.DosAsymmetry.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.dos.DosAsymmetry.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.DosAsymmetry.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Returns the labels for each of the features.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.dos.DosAsymmetry.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dos</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.DosAsymmetry.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Calculates the DOS asymmetry.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>dos (Dos): A pymatgen Dos object.</p>
</dd>
<dt>Returns:</dt><dd><p>A float describing the asymmetry of the DOS.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.dos.DosAsymmetry.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.DosAsymmetry.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.dos.Hybridization">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.dos.</span></span><span class="sig-name descname"><span class="pre">Hybridization</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">decay_length</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sampling_resolution</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">100</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">gaussian_smear</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.05</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">species</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.Hybridization" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>quantify s/p/d/f orbital character and their hybridizations at band edges</p>
<dl>
<dt>Args:</dt><dd><dl class="simple">
<dt>decay_length (float in eV):</dt><dd><p>The dos is sampled by an exponential decay function. this parameter
sets the decay length of the exponential. Three times the decay
length corresponds to 10% sampling strength. There is a hard cutoff
at five times the decay length (1% sampling strength)</p>
</dd>
<dt>sampling_resolution (int):</dt><dd><p>Number of points to sample DOS</p>
</dd>
<dt>gaussian_smear (float in eV):</dt><dd><p>Gaussian smearing (sigma) around each sampled point in the DOS</p>
</dd>
<dt>species ([str]): the species for which orbital contributions are</dt><dd><p>separately returned.</p>
</dd>
</dl>
</dd>
<dt>Returns (featurize returns [float] and featurize_labels returns [str]):</dt><dd><p>set of orbitals contributions and hybridizations. If species, then also
individual contributions from given species. Examples:</p>
<blockquote>
<div><p>cbm_s (float): s-orbital character of the cbm up to energy_cutoff
vbm_sp (float): sp-hybridization at the vbm edge. Minimum is 0</p>
<blockquote>
<div><p>or no hybridization (e.g. all s or vbm_s==1) and 1.0 is
maximum hybridization (i.e. vbm_s==0.5, vbm_p==0.5)</p>
</div></blockquote>
<p>cbm_Si_p (float): p-orbital character of Si</p>
</div></blockquote>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.dos.Hybridization.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">decay_length</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sampling_resolution</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">100</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">gaussian_smear</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.05</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">species</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.Hybridization.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.dos.Hybridization.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.Hybridization.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.dos.Hybridization.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.Hybridization.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Returns ([str]): feature names starting with the extrema (cbm or vbm)
followed by either s,p,d,f orbital to show normalized contribution
or a pair showing their hybridization or contribution of an element.
See the class docs for examples.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.dos.Hybridization.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dos</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">decay_length</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.Hybridization.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>takes in the density of state and return the orbitals contributions
and hybridizations.</p>
<dl>
<dt>Args:</dt><dd><p>dos (pymatgen CompleteDos): note that dos.structure is required
decay_length (float or None): if set, it overrides the instance</p>
<blockquote>
<div><p>variable self.decay_length.</p>
</div></blockquote>
</dd>
</dl>
<p>Returns ([float]): features, see class doc for more info</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.dos.Hybridization.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.Hybridization.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.dos.SiteDOS">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.dos.</span></span><span class="sig-name descname"><span class="pre">SiteDOS</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">decay_length</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sampling_resolution</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">100</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">gaussian_smear</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.05</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.SiteDOS" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>report the fractional s/p/d/f dos for a particular site. a CompleteDos
object is required because knowledge of the structure is needed. this
featurizer will work for metals as well as semiconductors. if the dos is a
semiconductor, cbm and vbm will correspond to the two respective band
edges. if the dos is a metal, then cbm and vbm correspond to above and
below the fermi level, respectively.</p>
<dl>
<dt>Args:</dt><dd><dl class="simple">
<dt>decay_length (float in eV):</dt><dd><p>the dos is sampled by an exponential decay function. this parameter
sets the decay length of the exponential. three times the
decay_length corresponds to 10% sampling strength. there is a hard
cutoff at five times the decay length (1% sampling strength)</p>
</dd>
<dt>sampling_resolution (int):</dt><dd><p>number of points to sample dos</p>
</dd>
<dt>gaussian_smear (float in eV):</dt><dd><p>Gaussian smearing (sigma) around each sampled point in dos</p>
</dd>
</dl>
</dd>
<dt>Returns (list of floats):</dt><dd><p>cbm_score_i (float): fractional score for i in {s,p,d,f}
cbm_score_total (float): the total sum of all the {s,p,d,f} scores</p>
<blockquote>
<div><p>this is useful information when comparing the relative
contributions from multiples sites</p>
</div></blockquote>
<p>vbm_score_i (float): fractional score for i in {s,p,d,f}
vbm_score_total (float): the total sum of all the {s,p,d,f} scores</p>
<blockquote>
<div><p>this is useful information when comparing the relative
contributions from multiples sites</p>
</div></blockquote>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.dos.SiteDOS.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">decay_length</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sampling_resolution</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">100</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">gaussian_smear</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.05</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.SiteDOS.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.dos.SiteDOS.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.SiteDOS.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.dos.SiteDOS.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.SiteDOS.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><dl class="simple">
<dt>Returns (list of str): list of names of the features. See the docs for</dt><dd><p>the featurizer class for more information.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.dos.SiteDOS.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dos</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">idx</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.SiteDOS.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>get dos scores for given site index</p>
<dl>
<dt>Args:</dt><dd><dl class="simple">
<dt>dos (pymatgen CompleteDos or their dict):</dt><dd><p>dos to featurize, must contain pdos and structure</p>
</dd>
</dl>
<p>idx (int): index of target site in structure.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.dos.SiteDOS.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.SiteDOS.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.featurizers.dos.get_cbm_vbm_scores">
<span class="sig-prename descclassname"><span class="pre">matminer.featurizers.dos.</span></span><span class="sig-name descname"><span class="pre">get_cbm_vbm_scores</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dos</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">decay_length</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sampling_resolution</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">gaussian_smear</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.get_cbm_vbm_scores" title="Permalink to this definition">¶</a></dt>
<dd><p>Quantifies the contribution of all atomic orbitals (s/p/d/f) from all
crystal sites to the conduction band minimum (CBM) and the valence band
maximum (VBM). An exponential decay function is used to sample the DOS.
An example use may be sorting the output based on cbm_score or vbm_score.</p>
<dl class="simple">
<dt>Args:</dt><dd><dl class="simple">
<dt>dos (pymatgen CompleteDos or their dict):</dt><dd><p>The density of states to featurize. Must be a complete DOS,
(i.e. contains PDOS and structure, in addition to total DOS)</p>
</dd>
<dt>decay_length (float in eV):</dt><dd><p>The dos is sampled by an exponential decay function. this parameter
sets the decay length of the exponential. Three times the decay
length corresponds to 10% sampling strength. There is a hard cutoff
at five times the decay length (1% sampling strength)</p>
</dd>
<dt>sampling_resolution (int):</dt><dd><p>Number of points to sample DOS</p>
</dd>
<dt>gaussian_smear (float in eV):</dt><dd><p>Gaussian smearing (sigma) around each sampled point in the DOS</p>
</dd>
</dl>
</dd>
<dt>Returns:</dt><dd><dl class="simple">
<dt>orbital_scores [(dict)]:</dt><dd><p>A list of how much each orbital contributes to the partial
density of states near the band edge. Dictionary items are:
.. cbm_score: (float) fractional contribution to conduction band
.. vbm_score: (float) fractional contribution to valence band
.. species: (pymatgen Specie) the Specie of the orbital
.. character: (str) is the orbital character s, p, d, or f
.. location: [(float)] fractional coordinates of the orbital</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.featurizers.dos.get_site_dos_scores">
<span class="sig-prename descclassname"><span class="pre">matminer.featurizers.dos.</span></span><span class="sig-name descname"><span class="pre">get_site_dos_scores</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dos</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">idx</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">decay_length</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sampling_resolution</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">gaussian_smear</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.dos.get_site_dos_scores" title="Permalink to this definition">¶</a></dt>
<dd><p>Quantifies the contribution of all atomic orbitals (s/p/d/f) from a
particular crystal site to the conduction band minimum (CBM) and the
valence band maximum (VBM). An exponential decay function is used to sample
the DOS. if the dos is a metal, then CBM and VBM indicate the orbital
scores above and below the fermi energy, respectively.</p>
<dl>
<dt>Args:</dt><dd><dl class="simple">
<dt>dos (pymatgen CompleteDos or their dict):</dt><dd><p>The density of states to featurize. Must be a complete DOS,
(i.e. contains PDOS and structure, in addition to total DOS)</p>
</dd>
<dt>decay_length (float in eV):</dt><dd><p>The dos is sampled by an exponential decay function. this parameter
sets the decay length of the exponential. Three times the decay
length corresponds to 10% sampling strength. There is a hard cutoff
at five times the decay length (1% sampling strength)</p>
</dd>
<dt>sampling_resolution (int):</dt><dd><p>Number of points to sample DOS</p>
</dd>
<dt>gaussian_smear (float in eV):</dt><dd><p>Gaussian smearing (sigma) around each sampled point in the DOS</p>
</dd>
<dt>idx (int):</dt><dd><p>site index for which to gather dos s/p/d/f scores</p>
</dd>
</dl>
</dd>
<dt>Returns:</dt><dd><dl>
<dt>orbital_scores (dict):</dt><dd><p>a dictionary of the fractional s/p/d/f orbital scores from the
total dos accumulated from that site. dictionary structure:</p>
<blockquote>
<div><dl class="simple">
<dt>{cbm: {s: (float), …, f: (float), total: (float)},</dt><dd><p>vbm: {s: (float), …, f: (float), total: (float)}}</p>
</dd>
</dl>
</div></blockquote>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</section>
<section id="module-matminer.featurizers.function">
<span id="matminer-featurizers-function-module"></span><h2>matminer.featurizers.function module<a class="headerlink" href="#module-matminer.featurizers.function" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.function.FunctionFeaturizer">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.function.</span></span><span class="sig-name descname"><span class="pre">FunctionFeaturizer</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">expressions</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">multi_feature_depth</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">postprocess</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">combo_function</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">latexify_labels</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.function.FunctionFeaturizer" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>Features from functions applied to existing features, e.g. “1/x”</p>
<p>This featurizer must be fit either by calling .fit_featurize_dataframe
or by calling .fit followed by featurize_dataframe.</p>
<p>This class featurizes a dataframe according to a set
of expressions representing functions to apply to
existing features. The approach here has uses a sympy-based
parsing of string expressions, rather than explicit
python functions.  The primary reason this has been
done is to provide for better support for book-keeping
(e. g. with feature labels), substitution, and elimination
of symbolic redundancy, which sympy is well-suited for.</p>
<p>Note original feature names in the resulting feature set
will have their sympy-illegal characters substituted with
underscores. For example:</p>
<p>“exp(-MagpieData_avg_dev_NfValence)/sqrt(MagpieData_range_Number)”</p>
<p>Where the original feature names were</p>
<p>“MagpieData avg_dev NfValence”  and “MagpieData range Number”</p>
<dl class="simple">
<dt>Args:</dt><dd><dl class="simple">
<dt>expressions ([str]): list of sympy-parseable expressions</dt><dd><p>representing a function of a single variable x, e. g.
[“1 / x”, “x ** 2”], defaults to the list above</p>
</dd>
<dt>multi_feature_depth (int): how many features to include if using</dt><dd><p>multiple fields for functionalization, e. g. 2 will
include pairwise combined features</p>
</dd>
<dt>postprocess (function or type): type to cast functional outputs</dt><dd><p>to, if, for example, you want to include the possibility of
complex numbers in your outputs, use postprocess=np.complex128,
defaults to float</p>
</dd>
<dt>combo_function (function): function to combine multi-features,</dt><dd><p>defaults to np.prod (i.e. cumulative product of expressions),
note that a combo function must cleanly process sympy
expressions and <strong>takes a list of arbitrary length as input</strong>,
other options include np.sum</p>
</dd>
<dt>latexify_labels (bool): whether to render labels in latex,</dt><dd><p>defaults to False</p>
</dd>
</dl>
</dd>
</dl>
<dl class="py attribute">
<dt class="sig sig-object py" id="matminer.featurizers.function.FunctionFeaturizer.ILLEGAL_CHARACTERS">
<span class="sig-name descname"><span class="pre">ILLEGAL_CHARACTERS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">['|',</span> <span class="pre">'</span> <span class="pre">',</span> <span class="pre">'/',</span> <span class="pre">'\\',</span> <span class="pre">'?',</span> <span class="pre">'&#64;',</span> <span class="pre">'#',</span> <span class="pre">'$',</span> <span class="pre">'%']</span></em><a class="headerlink" href="#matminer.featurizers.function.FunctionFeaturizer.ILLEGAL_CHARACTERS" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.function.FunctionFeaturizer.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">expressions</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">multi_feature_depth</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">postprocess</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">combo_function</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">latexify_labels</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.function.FunctionFeaturizer.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.function.FunctionFeaturizer.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.function.FunctionFeaturizer.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="matminer.featurizers.function.FunctionFeaturizer.exp_dict">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">exp_dict</span></span><a class="headerlink" href="#matminer.featurizers.function.FunctionFeaturizer.exp_dict" title="Permalink to this definition">¶</a></dt>
<dd><p>Generates a dictionary of expressions keyed by number of
variables in each expression</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>Dictionary of expressions keyed by number of variables</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.function.FunctionFeaturizer.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.function.FunctionFeaturizer.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><dl class="simple">
<dt>Returns:</dt><dd><p>Set of feature labels corresponding to expressions</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.function.FunctionFeaturizer.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.function.FunctionFeaturizer.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Main featurizer function, essentially iterates over all
of the functions in self.function_list to generate
features for each argument.</p>
<dl class="simple">
<dt>Args:</dt><dd><dl class="simple">
<dt><a href="#id9"><span class="problematic" id="id10">*</span></a>args: list of numbers to generate functional output</dt><dd><p>features</p>
</dd>
</dl>
</dd>
<dt>Returns:</dt><dd><p>list of functional outputs corresponding to input args</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.function.FunctionFeaturizer.fit">
<span class="sig-name descname"><span class="pre">fit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">X</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">fit_kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.function.FunctionFeaturizer.fit" title="Permalink to this definition">¶</a></dt>
<dd><p>Sets the feature labels.  Not intended to be used by a user,
only intended to be invoked as part of featurize_dataframe</p>
<dl class="simple">
<dt>Args:</dt><dd><p>X (DataFrame or array-like): data to fit to</p>
</dd>
<dt>Returns:</dt><dd><p>Set of feature labels corresponding to expressions</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.function.FunctionFeaturizer.generate_string_expressions">
<span class="sig-name descname"><span class="pre">generate_string_expressions</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">input_variable_names</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.function.FunctionFeaturizer.generate_string_expressions" title="Permalink to this definition">¶</a></dt>
<dd><p>Method to generate string expressions for input strings,
mainly used to generate columns names for featurize_dataframe</p>
<dl class="simple">
<dt>Args:</dt><dd><dl class="simple">
<dt>input_variable_names ([str]): strings corresponding to</dt><dd><p>functional input variable names</p>
</dd>
</dl>
</dd>
<dt>Returns:</dt><dd><p>List of string expressions generated by substitution of
variable names into functions</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.function.FunctionFeaturizer.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.function.FunctionFeaturizer.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.featurizers.function.generate_expressions_combinations">
<span class="sig-prename descclassname"><span class="pre">matminer.featurizers.function.</span></span><span class="sig-name descname"><span class="pre">generate_expressions_combinations</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">expressions</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">combo_depth=2</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">combo_function=&lt;function</span> <span class="pre">prod&gt;</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.function.generate_expressions_combinations" title="Permalink to this definition">¶</a></dt>
<dd><p>This function takes a list of strings representing functions
of x, converts them to sympy expressions, and combines
them according to the combo_depth parameter.  Also filters
resultant expressions for any redundant ones determined
by sympy expression equivalence.</p>
<dl>
<dt>Args:</dt><dd><dl class="simple">
<dt>expressions (strings): all of the sympy-parseable strings</dt><dd><p>to be converted to expressions and combined, e. g.
[“1 / x”, “x ** 2”], must be functions of x</p>
</dd>
</dl>
<p>combo_depth (int): the number of independent variables to consider
combo_function (method): the function which combines the</p>
<blockquote>
<div><p>the respective expressions provided, defaults to np.prod,
i. e. the cumulative product of the expressions</p>
</div></blockquote>
</dd>
<dt>Returns:</dt><dd><dl class="simple">
<dt>list of unique non-trivial expressions for featurization</dt><dd><p>of inputs</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</section>
<section id="module-matminer.featurizers">
<span id="module-contents"></span><h2>Module contents<a class="headerlink" href="#module-matminer.featurizers" title="Permalink to this heading">¶</a></h2>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">matminer.featurizers package</a><ul>
<li><a class="reference internal" href="#subpackages">Subpackages</a></li>
<li><a class="reference internal" href="#submodules">Submodules</a></li>
<li><a class="reference internal" href="#module-matminer.featurizers.bandstructure">matminer.featurizers.bandstructure module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.bandstructure.BandFeaturizer"><code class="docutils literal notranslate"><span class="pre">BandFeaturizer</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.bandstructure.BandFeaturizer.__init__"><code class="docutils literal notranslate"><span class="pre">BandFeaturizer.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.bandstructure.BandFeaturizer.citations"><code class="docutils literal notranslate"><span class="pre">BandFeaturizer.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.bandstructure.BandFeaturizer.feature_labels"><code class="docutils literal notranslate"><span class="pre">BandFeaturizer.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.bandstructure.BandFeaturizer.featurize"><code class="docutils literal notranslate"><span class="pre">BandFeaturizer.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.bandstructure.BandFeaturizer.get_bindex_bspin"><code class="docutils literal notranslate"><span class="pre">BandFeaturizer.get_bindex_bspin()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.bandstructure.BandFeaturizer.implementors"><code class="docutils literal notranslate"><span class="pre">BandFeaturizer.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.bandstructure.BranchPointEnergy"><code class="docutils literal notranslate"><span class="pre">BranchPointEnergy</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.bandstructure.BranchPointEnergy.__init__"><code class="docutils literal notranslate"><span class="pre">BranchPointEnergy.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.bandstructure.BranchPointEnergy.citations"><code class="docutils literal notranslate"><span class="pre">BranchPointEnergy.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.bandstructure.BranchPointEnergy.feature_labels"><code class="docutils literal notranslate"><span class="pre">BranchPointEnergy.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.bandstructure.BranchPointEnergy.featurize"><code class="docutils literal notranslate"><span class="pre">BranchPointEnergy.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.bandstructure.BranchPointEnergy.implementors"><code class="docutils literal notranslate"><span class="pre">BranchPointEnergy.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.base">matminer.featurizers.base module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.base.BaseFeaturizer"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.base.BaseFeaturizer.chunksize"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.chunksize</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.base.BaseFeaturizer.citations"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.base.BaseFeaturizer.feature_labels"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.base.BaseFeaturizer.featurize"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.base.BaseFeaturizer.featurize_dataframe"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.featurize_dataframe()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.base.BaseFeaturizer.featurize_many"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.featurize_many()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.base.BaseFeaturizer.featurize_wrapper"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.featurize_wrapper()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.base.BaseFeaturizer.fit"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.fit()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.base.BaseFeaturizer.fit_featurize_dataframe"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.fit_featurize_dataframe()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.base.BaseFeaturizer.implementors"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.implementors()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.base.BaseFeaturizer.n_jobs"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.n_jobs</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.base.BaseFeaturizer.precheck"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.precheck()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.base.BaseFeaturizer.precheck_dataframe"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.precheck_dataframe()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.base.BaseFeaturizer.set_chunksize"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.set_chunksize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.base.BaseFeaturizer.set_n_jobs"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.set_n_jobs()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.base.BaseFeaturizer.transform"><code class="docutils literal notranslate"><span class="pre">BaseFeaturizer.transform()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.base.MultipleFeaturizer"><code class="docutils literal notranslate"><span class="pre">MultipleFeaturizer</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.base.MultipleFeaturizer.__init__"><code class="docutils literal notranslate"><span class="pre">MultipleFeaturizer.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.base.MultipleFeaturizer.citations"><code class="docutils literal notranslate"><span class="pre">MultipleFeaturizer.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.base.MultipleFeaturizer.feature_labels"><code class="docutils literal notranslate"><span class="pre">MultipleFeaturizer.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.base.MultipleFeaturizer.featurize"><code class="docutils literal notranslate"><span class="pre">MultipleFeaturizer.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.base.MultipleFeaturizer.featurize_many"><code class="docutils literal notranslate"><span class="pre">MultipleFeaturizer.featurize_many()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.base.MultipleFeaturizer.featurize_wrapper"><code class="docutils literal notranslate"><span class="pre">MultipleFeaturizer.featurize_wrapper()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.base.MultipleFeaturizer.fit"><code class="docutils literal notranslate"><span class="pre">MultipleFeaturizer.fit()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.base.MultipleFeaturizer.implementors"><code class="docutils literal notranslate"><span class="pre">MultipleFeaturizer.implementors()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.base.MultipleFeaturizer.set_n_jobs"><code class="docutils literal notranslate"><span class="pre">MultipleFeaturizer.set_n_jobs()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.base.StackedFeaturizer"><code class="docutils literal notranslate"><span class="pre">StackedFeaturizer</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.base.StackedFeaturizer.__init__"><code class="docutils literal notranslate"><span class="pre">StackedFeaturizer.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.base.StackedFeaturizer.citations"><code class="docutils literal notranslate"><span class="pre">StackedFeaturizer.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.base.StackedFeaturizer.feature_labels"><code class="docutils literal notranslate"><span class="pre">StackedFeaturizer.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.base.StackedFeaturizer.featurize"><code class="docutils literal notranslate"><span class="pre">StackedFeaturizer.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.base.StackedFeaturizer.implementors"><code class="docutils literal notranslate"><span class="pre">StackedFeaturizer.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.conversions">matminer.featurizers.conversions module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.conversions.ASEAtomstoStructure"><code class="docutils literal notranslate"><span class="pre">ASEAtomstoStructure</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.conversions.ASEAtomstoStructure.__init__"><code class="docutils literal notranslate"><span class="pre">ASEAtomstoStructure.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.ASEAtomstoStructure.featurize"><code class="docutils literal notranslate"><span class="pre">ASEAtomstoStructure.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.ASEAtomstoStructure.implementors"><code class="docutils literal notranslate"><span class="pre">ASEAtomstoStructure.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.CompositionToOxidComposition"><code class="docutils literal notranslate"><span class="pre">CompositionToOxidComposition</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.conversions.CompositionToOxidComposition.__init__"><code class="docutils literal notranslate"><span class="pre">CompositionToOxidComposition.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.CompositionToOxidComposition.citations"><code class="docutils literal notranslate"><span class="pre">CompositionToOxidComposition.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.CompositionToOxidComposition.featurize"><code class="docutils literal notranslate"><span class="pre">CompositionToOxidComposition.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.CompositionToOxidComposition.implementors"><code class="docutils literal notranslate"><span class="pre">CompositionToOxidComposition.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.CompositionToStructureFromMP"><code class="docutils literal notranslate"><span class="pre">CompositionToStructureFromMP</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.conversions.CompositionToStructureFromMP.__init__"><code class="docutils literal notranslate"><span class="pre">CompositionToStructureFromMP.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.CompositionToStructureFromMP.citations"><code class="docutils literal notranslate"><span class="pre">CompositionToStructureFromMP.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.CompositionToStructureFromMP.featurize"><code class="docutils literal notranslate"><span class="pre">CompositionToStructureFromMP.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.CompositionToStructureFromMP.implementors"><code class="docutils literal notranslate"><span class="pre">CompositionToStructureFromMP.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.ConversionFeaturizer"><code class="docutils literal notranslate"><span class="pre">ConversionFeaturizer</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.conversions.ConversionFeaturizer.__init__"><code class="docutils literal notranslate"><span class="pre">ConversionFeaturizer.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.ConversionFeaturizer.citations"><code class="docutils literal notranslate"><span class="pre">ConversionFeaturizer.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.ConversionFeaturizer.feature_labels"><code class="docutils literal notranslate"><span class="pre">ConversionFeaturizer.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.ConversionFeaturizer.featurize"><code class="docutils literal notranslate"><span class="pre">ConversionFeaturizer.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.ConversionFeaturizer.featurize_dataframe"><code class="docutils literal notranslate"><span class="pre">ConversionFeaturizer.featurize_dataframe()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.ConversionFeaturizer.implementors"><code class="docutils literal notranslate"><span class="pre">ConversionFeaturizer.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.DictToObject"><code class="docutils literal notranslate"><span class="pre">DictToObject</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.conversions.DictToObject.__init__"><code class="docutils literal notranslate"><span class="pre">DictToObject.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.DictToObject.citations"><code class="docutils literal notranslate"><span class="pre">DictToObject.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.DictToObject.featurize"><code class="docutils literal notranslate"><span class="pre">DictToObject.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.DictToObject.implementors"><code class="docutils literal notranslate"><span class="pre">DictToObject.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.JsonToObject"><code class="docutils literal notranslate"><span class="pre">JsonToObject</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.conversions.JsonToObject.__init__"><code class="docutils literal notranslate"><span class="pre">JsonToObject.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.JsonToObject.citations"><code class="docutils literal notranslate"><span class="pre">JsonToObject.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.JsonToObject.featurize"><code class="docutils literal notranslate"><span class="pre">JsonToObject.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.JsonToObject.implementors"><code class="docutils literal notranslate"><span class="pre">JsonToObject.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.PymatgenFunctionApplicator"><code class="docutils literal notranslate"><span class="pre">PymatgenFunctionApplicator</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.conversions.PymatgenFunctionApplicator.__init__"><code class="docutils literal notranslate"><span class="pre">PymatgenFunctionApplicator.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.PymatgenFunctionApplicator.featurize"><code class="docutils literal notranslate"><span class="pre">PymatgenFunctionApplicator.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.PymatgenFunctionApplicator.implementors"><code class="docutils literal notranslate"><span class="pre">PymatgenFunctionApplicator.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.StrToComposition"><code class="docutils literal notranslate"><span class="pre">StrToComposition</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.conversions.StrToComposition.__init__"><code class="docutils literal notranslate"><span class="pre">StrToComposition.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.StrToComposition.citations"><code class="docutils literal notranslate"><span class="pre">StrToComposition.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.StrToComposition.featurize"><code class="docutils literal notranslate"><span class="pre">StrToComposition.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.StrToComposition.implementors"><code class="docutils literal notranslate"><span class="pre">StrToComposition.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.StructureToComposition"><code class="docutils literal notranslate"><span class="pre">StructureToComposition</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.conversions.StructureToComposition.__init__"><code class="docutils literal notranslate"><span class="pre">StructureToComposition.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.StructureToComposition.citations"><code class="docutils literal notranslate"><span class="pre">StructureToComposition.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.StructureToComposition.featurize"><code class="docutils literal notranslate"><span class="pre">StructureToComposition.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.StructureToComposition.implementors"><code class="docutils literal notranslate"><span class="pre">StructureToComposition.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.StructureToIStructure"><code class="docutils literal notranslate"><span class="pre">StructureToIStructure</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.conversions.StructureToIStructure.__init__"><code class="docutils literal notranslate"><span class="pre">StructureToIStructure.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.StructureToIStructure.citations"><code class="docutils literal notranslate"><span class="pre">StructureToIStructure.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.StructureToIStructure.featurize"><code class="docutils literal notranslate"><span class="pre">StructureToIStructure.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.StructureToIStructure.implementors"><code class="docutils literal notranslate"><span class="pre">StructureToIStructure.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.StructureToOxidStructure"><code class="docutils literal notranslate"><span class="pre">StructureToOxidStructure</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.conversions.StructureToOxidStructure.__init__"><code class="docutils literal notranslate"><span class="pre">StructureToOxidStructure.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.StructureToOxidStructure.citations"><code class="docutils literal notranslate"><span class="pre">StructureToOxidStructure.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.StructureToOxidStructure.featurize"><code class="docutils literal notranslate"><span class="pre">StructureToOxidStructure.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.conversions.StructureToOxidStructure.implementors"><code class="docutils literal notranslate"><span class="pre">StructureToOxidStructure.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.dos">matminer.featurizers.dos module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.dos.DOSFeaturizer"><code class="docutils literal notranslate"><span class="pre">DOSFeaturizer</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.dos.DOSFeaturizer.__init__"><code class="docutils literal notranslate"><span class="pre">DOSFeaturizer.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.dos.DOSFeaturizer.citations"><code class="docutils literal notranslate"><span class="pre">DOSFeaturizer.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.dos.DOSFeaturizer.feature_labels"><code class="docutils literal notranslate"><span class="pre">DOSFeaturizer.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.dos.DOSFeaturizer.featurize"><code class="docutils literal notranslate"><span class="pre">DOSFeaturizer.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.dos.DOSFeaturizer.implementors"><code class="docutils literal notranslate"><span class="pre">DOSFeaturizer.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.dos.DopingFermi"><code class="docutils literal notranslate"><span class="pre">DopingFermi</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.dos.DopingFermi.__init__"><code class="docutils literal notranslate"><span class="pre">DopingFermi.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.dos.DopingFermi.citations"><code class="docutils literal notranslate"><span class="pre">DopingFermi.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.dos.DopingFermi.feature_labels"><code class="docutils literal notranslate"><span class="pre">DopingFermi.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.dos.DopingFermi.featurize"><code class="docutils literal notranslate"><span class="pre">DopingFermi.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.dos.DopingFermi.implementors"><code class="docutils literal notranslate"><span class="pre">DopingFermi.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.dos.DosAsymmetry"><code class="docutils literal notranslate"><span class="pre">DosAsymmetry</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.dos.DosAsymmetry.__init__"><code class="docutils literal notranslate"><span class="pre">DosAsymmetry.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.dos.DosAsymmetry.citations"><code class="docutils literal notranslate"><span class="pre">DosAsymmetry.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.dos.DosAsymmetry.feature_labels"><code class="docutils literal notranslate"><span class="pre">DosAsymmetry.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.dos.DosAsymmetry.featurize"><code class="docutils literal notranslate"><span class="pre">DosAsymmetry.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.dos.DosAsymmetry.implementors"><code class="docutils literal notranslate"><span class="pre">DosAsymmetry.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.dos.Hybridization"><code class="docutils literal notranslate"><span class="pre">Hybridization</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.dos.Hybridization.__init__"><code class="docutils literal notranslate"><span class="pre">Hybridization.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.dos.Hybridization.citations"><code class="docutils literal notranslate"><span class="pre">Hybridization.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.dos.Hybridization.feature_labels"><code class="docutils literal notranslate"><span class="pre">Hybridization.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.dos.Hybridization.featurize"><code class="docutils literal notranslate"><span class="pre">Hybridization.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.dos.Hybridization.implementors"><code class="docutils literal notranslate"><span class="pre">Hybridization.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.dos.SiteDOS"><code class="docutils literal notranslate"><span class="pre">SiteDOS</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.dos.SiteDOS.__init__"><code class="docutils literal notranslate"><span class="pre">SiteDOS.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.dos.SiteDOS.citations"><code class="docutils literal notranslate"><span class="pre">SiteDOS.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.dos.SiteDOS.feature_labels"><code class="docutils literal notranslate"><span class="pre">SiteDOS.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.dos.SiteDOS.featurize"><code class="docutils literal notranslate"><span class="pre">SiteDOS.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.dos.SiteDOS.implementors"><code class="docutils literal notranslate"><span class="pre">SiteDOS.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.dos.get_cbm_vbm_scores"><code class="docutils literal notranslate"><span class="pre">get_cbm_vbm_scores()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.dos.get_site_dos_scores"><code class="docutils literal notranslate"><span class="pre">get_site_dos_scores()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.function">matminer.featurizers.function module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.function.FunctionFeaturizer"><code class="docutils literal notranslate"><span class="pre">FunctionFeaturizer</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.function.FunctionFeaturizer.ILLEGAL_CHARACTERS"><code class="docutils literal notranslate"><span class="pre">FunctionFeaturizer.ILLEGAL_CHARACTERS</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.function.FunctionFeaturizer.__init__"><code class="docutils literal notranslate"><span class="pre">FunctionFeaturizer.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.function.FunctionFeaturizer.citations"><code class="docutils literal notranslate"><span class="pre">FunctionFeaturizer.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.function.FunctionFeaturizer.exp_dict"><code class="docutils literal notranslate"><span class="pre">FunctionFeaturizer.exp_dict</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.function.FunctionFeaturizer.feature_labels"><code class="docutils literal notranslate"><span class="pre">FunctionFeaturizer.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.function.FunctionFeaturizer.featurize"><code class="docutils literal notranslate"><span class="pre">FunctionFeaturizer.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.function.FunctionFeaturizer.fit"><code class="docutils literal notranslate"><span class="pre">FunctionFeaturizer.fit()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.function.FunctionFeaturizer.generate_string_expressions"><code class="docutils literal notranslate"><span class="pre">FunctionFeaturizer.generate_string_expressions()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.function.FunctionFeaturizer.implementors"><code class="docutils literal notranslate"><span class="pre">FunctionFeaturizer.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.function.generate_expressions_combinations"><code class="docutils literal notranslate"><span class="pre">generate_expressions_combinations()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers">Module contents</a></li>
</ul>
</li>
</ul>

  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/matminer.featurizers.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer.featurizers package</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2015, Anubhav Jain.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.3.0.
    </div>

  </body>
</html>