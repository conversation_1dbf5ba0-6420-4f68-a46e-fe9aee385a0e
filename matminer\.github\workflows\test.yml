# This workflow runs only on Ubuntu and aims to be more complete than the Mac and Windows workflows.
# In particular, Openbabel and many of the external command line dependencies are included for testing.defaults:
# The ext package is also only tested in this workflow. Coverage is also computed based on this platform.
name: Testing

on:
  push:
    branches:
      - main
    paths:
      - matminer/**
      - requirements/**

  pull_request:
    branches:
      - main
    paths:
      - matminer/**
      - requirements/**

  workflow_dispatch:
    inputs:
      fullTest:
        description: "run full test"
        required: true
        default: false
        type: boolean

jobs:

  test:
    strategy:
      matrix:
        python-version: ["3.8", "3.9", "3.10"]
        mongodb-version: ['4.0']

    runs-on: ubuntu-latest

    env:
      PMG_MAPI_KEY: ${{ secrets.PMG_MAPI_KEY }}
      MPDS_KEY: ${{ secrets.MPDS_KEY }}
      CITRINATION_API_KEY: ${{ secrets.CITRINATION_API_KEY }}
      RUNNING_ON_GHACTIONS: "True"
      MPLBACKEND: "Agg"
      MATMINER_DATASET_FULL_TEST: ${{ inputs.fullTest }}

    services:
      mongo:
        image: mongo:4
        ports:
          - 27017:27017

    steps:
    - uses: actions/checkout@v3
      with:
        fetch-depth: 0
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        cache: "pip"
        cache-dependency-path: '**/setup.py'

    - name: Install Python dependencies
      run: |
        python${{ matrix.python-version }} -m pip install --upgrade pip pip-tools setuptools setuptools_scm
        python${{ matrix.python-version }} -m piptools sync --user requirements/ubuntu-latest_py${{ matrix.python-version }}_extras.txt
        # Using non-editable install for testing building of MANIFEST files
        python${{ matrix.python-version }} -m pip install --no-deps .
        python${{ matrix.python-version }} -m pip install pre-commit

    - name: linting
      run: |
        pre-commit run --all-files

    - name: Run tests
      run: |
        python${{ matrix.python-version }} -m pytest --cov=matminer matminer --durations=0 --timeout=360

    - name: Build package
      if: matrix.python-version == 3.9
      run: |
        python${{ matrix.python-version }} -m pip install --upgrade pip build setuptools setuptools_scm wheel
        python${{ matrix.python-version }} -m build

    - name: Publish distribution 📦s to Test PyPI
      if: matrix.python-version == 3.9
      uses: pypa/gh-action-pypi-publish@release/v1.5
      with:
        skip_existing: true
        repository_url: https://test.pypi.org/legacy/
        user: __token__
        password: ${{ secrets.TEST_PYPI_TOKEN }}

  auto-gen-release:
    needs:
      - test
    runs-on: ubuntu-latest
    env:
      GITHUB_TOKEN: ${{ secrets.PAT }}
    steps:
      - uses: rymndhng/release-on-push-action@v0.25.0
        with:
          bump_version_scheme: norelease
