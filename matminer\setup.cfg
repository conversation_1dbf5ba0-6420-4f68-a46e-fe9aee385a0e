[build_sphinx]
source-dir = docs/
build-dir  = docs/_build
all_files  = 1

[upload_sphinx]
upload-dir = docs/_build/html

[aliases]
upload_docs = upload_docs --upload-dir=docs/_build/html
release = register sdist upload

[pycodestyle]
count = true
ignore = E121,E123,E126,E133,E226,E241,E242,E704,W503,W504,W505,E741,W605,W293,W291,W292,E203,E231
max-line-length = 120
statistics = true
exclude=docs_rst/*.py

[flake8]
exclude = .git,__pycache__,docs_rst/conf.py,tests
# max-complexity = 10
extend-ignore = E741,W291,W293,E501,E231,E203
max-line-length = 120
per-file-ignores =
  # F401: imported but unused
  __init__.py: F401

[pydocstyle]
ignore = D105,D2,D4
match-dir=(?!(tests)).*

[coverage.report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    @deprecated

[mypy]
ignore_missing_imports = true

[isort]
profile = black

[autoflake]
in-place = true
remove-unused-variables = true
remove-all-unused-imports = true
expand-star-imports = true
ignore-init-module-imports = true
