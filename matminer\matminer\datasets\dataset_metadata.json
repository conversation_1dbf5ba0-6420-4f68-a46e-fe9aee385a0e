{"boltztrap_mp": {"bibtex_refs": ["@Article{Ricci2017,\nauthor={<PERSON><PERSON><PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON><PERSON>\nand <PERSON>, <PERSON><PERSON>\nand <PERSON>, <PERSON><PERSON><PERSON><PERSON>\nand <PERSON>, <PERSON><PERSON><PERSON><PERSON>\nand <PERSON><PERSON><PERSON>, <PERSON><PERSON>},\ntitle={An ab initio electronic transport database for inorganic materials},\njournal={Scientific Data},\nyear={2017},\nmonth={Jul},\nday={04},\npublisher={The Author(s)},\nvolume={4},\npages={170085},\nnote={Data Descriptor},\nurl={http://dx.doi.org/10.1038/sdata.2017.85}\n}", "@misc{dryad_gn001,\ntitle = {Data from: An ab initio electronic transport database for inorganic materials},\nauthor = {<PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON><PERSON>, <PERSON> and Snyder, <PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, G},\nyear = {2017},\njournal = {Scientific Data},\nURL = {https://doi.org/10.5061/dryad.gn001},\ndoi = {doi:10.5061/dryad.gn001},\npublisher = {Dryad Digital Repository}\n}"], "columns": {"formula": "Chemical formula of the entry", "m_n": "n-type/conduction band effective mass. Units: m_e where m_e is the mass of an electron; i.e. m_n is a unitless ratio", "m_p": "p-type/valence band effective mass.", "mpid": "Materials Project identifier", "pf_n": "n-type thermoelectric power factor in uW/cm2.K where uW is microwatts and a constant relaxation time of 1e-14 assumed.", "pf_p": "p-type power factor in uW/cm2.K", "s_n": "n-type Seebeck coefficient in micro Volts per Kelvin", "s_p": "p-type Seebeck coefficient in micro Volts per Kelvin", "structure": "pymatgen Structure object describing the crystal structure of the material"}, "description": "Effective mass and thermoelectric properties of 8924 compounds in The  Materials Project database that are calculated by the BoltzTraP software package run on the GGA-PBE or GGA+U density functional theory calculation results. The properties are reported at the temperature of 300 Kelvin and the carrier concentration of 1e18 1/cm3.", "file_type": "json.gz", "hash": "02c8ea59a3805cdd50aef6f6f55c3d0148eda9e5cab871ff37d2fa46fb5941d7", "num_entries": 8924, "reference": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> et al. An ab initio electronic transport database for inorganic materials. Sci. Data 4:170085 doi: 10.1038/sdata.2017.85 (2017).\n<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> (2017) Data from: An ab initio electronic transport database for inorganic materials. Dryad Digital Repository. https://doi.org/10.5061/dryad.gn001", "url": "https://ndownloader.figshare.com/files/13297511"}, "brgoch_superhard_training": {"bibtex_refs": ["@article{doi:10.1021/jacs.8b02717,\nauthor = {<PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>},\ntitle = {Machine Learning Directed Search for Ultraincompressible, Superhard Materials},\njournal = {Journal of the American Chemical Society},\nvolume = {140},\nnumber = {31},\npages = {9844-9853},\nyear = {2018},\ndoi = {10.1021/jacs.8b02717},\nnote ={PMID: 30010335},\nURL = {\nhttps://doi.org/10.1021/jacs.8b02717\n},\neprint = {\nhttps://doi.org/10.1021/jacs.8b02717\n}\n}"], "columns": {"brgoch_feats": "features used in brgoch study compressed to a dictionary", "bulk_modulus": "VRH bulk modulus", "composition": "pymatgen composition object", "formula": "Chemical formula as a string", "material_id": "materials project id", "structure": "pymatgen structure object", "shear_modulus": "VRH shear modulus", "suspect_value": "True if bulk or shear value did not closely match (within 5%/1GPa of MP) materials project value at time of cross reference or if no material could be found"}, "description": "2574 materials used for training regressors that predict shear and bulk modulus.", "file_type": "json.gz", "hash": "c1b228ba4b54429937e3ed6b0ce8e90284099bf38f55fadf183816ddd45f3b40", "num_entries": 2574, "reference": "Machine Learning Directed Search for Ultraincompressible, Superhard Materials\nAria <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and Jakoah Brgoch\nJournal of the American Chemical Society 2018 140 (31), 9844-9853\nDOI: 10.1021/jacs.8b02717", "url": "https://ndownloader.figshare.com/files/13858931"}, "castelli_perovskites": {"bibtex_refs": ["@Article{C2EE22341D,\nauthor =\"<PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON>\",\ntitle  =\"New cubic perovskites for one- and two-photon water splitting using the computational materials repository\",\njournal  =\"Energy Environ. Sci.\",\nyear  =\"2012\",\nvolume  =\"5\",\nissue  =\"10\",\npages  =\"9034-9043\",\npublisher  =\"The Royal Society of Chemistry\",\ndoi  =\"10.1039/C2EE22341D\",\nurl  =\"http://dx.doi.org/10.1039/C2EE22341D\",\nabstract  =\"A new efficient photoelectrochemical cell (PEC) is one of the possible solutions to the energy and climate problems of our time. Such a device requires development of new semiconducting materials with tailored properties with respect to stability and light absorption. Here we perform computational screening of around 19 000 oxides{,} oxynitrides{,} oxysulfides{,} oxyfluorides{,} and oxyfluoronitrides in the cubic perovskite structure with PEC applications in mind. We address three main applications: light absorbers for one- and two-photon water splitting and high-stability transparent shields to protect against corrosion. We end up with 20{,} 12{,} and 15 different combinations of oxides{,} oxynitrides and oxyfluorides{,} respectively{,} inviting further experimental investigation.\"}"], "columns": {"cbm": "similar to vbm but for conduction band", "e_form": "heat of formation in eV, Note the reference state for oxygen was computed from oxygen's chemical potential in water vapor, not as oxygen molecules, to reflect the application which these perovskites were studied for.", "fermi level": "the thermodynamic work required to add one electron to the body in eV", "fermi width": "fermi bandwidth", "formula": "Chemical formula of the material", "gap gllbsc": "electronic band gap in eV calculated via gllbsc functional", "gap is direct": "boolean indicator for direct gap", "mu_b": "magnetic moment in terms of Bohr magneton", "structure": "crystal structure represented by pymatgen Structure object", "vbm": "absolute value of valence band edge calculated via gllbsc"}, "description": "18,928 perovskites generated with ABX combinatorics, calculating gllbsc band gap and pbe structure, and also reporting absolute band edge positions and heat of formation.", "file_type": "json.gz", "hash": "fc8b44d7213515f9bc2ef3a5a5d3e4b946b321c1311f14c8be31f857b61b10d7", "num_entries": 18928, "reference": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> and <PERSON><PERSON> (2012) New cubic perovskites for one- and two-photon water splitting using the computational materials repository. Energy Environ. Sci., 2012,5, 9034-9043 https://doi.org/10.1039/C2EE22341D", "url": "https://ndownloader.figshare.com/files/13284197"}, "citrine_thermal_conductivity": {"bibtex_refs": ["@misc{Citrine Informatics,\ntitle = {Citrination},\nhowpublished = {\\url{https://www.citrination.com/}},\n}"], "columns": {"formula": "Chemical formula of the dataset entry", "k-units": "units of thermal conductivity", "k_condition": "Temperature description of testing conditions", "k_condition_units": "units of testing condition temperature representation", "k_expt": "the experimentally measured thermal conductivity in SI units of W/m.K"}, "description": "Thermal conductivity of 872 compounds measured experimentally and retrieved from Citrine database from various references. The reported values are measured at various temperatures of which 295 are at room temperature.", "file_type": "json.gz", "hash": "8490997195d6ec912705a5b2322726e53a72c26143a7e63fe0f962ccc6232b2c", "num_entries": 872, "reference": "https://www.citrination.com", "url": "https://ndownloader.figshare.com/files/13314470"}, "dielectric_constant": {"bibtex_refs": ["@Article{Petousis2017,\nauthor={<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>\nand <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON>\nand <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON>},\ntitle={High-throughput screening of inorganic compounds for the\ndiscovery of novel dielectric and optical materials},\njournal={Scientific Data},\nyear={2017},\nmonth={Jan},\nday={31},\npublisher={The Author(s)},\nvolume={4},\npages={160134},\nnote={Data Descriptor},\nurl={http://dx.doi.org/10.1038/sdata.2016.134}\n}"], "columns": {"band_gap": "Measure of the conductivity of a material", "cif": "optional: Description string for structure", "e_electronic": "electronic contribution to dielectric tensor", "e_total": "Total dielectric tensor incorporating both electronic and ionic contributions", "formula": "Chemical formula of the material", "material_id": "Materials Project ID of the material", "meta": "optional, metadata descriptor of the datapoint", "n": "Refractive Index", "nsites": "The \\# of atoms in the unit cell of the calculation.", "poly_electronic": "the average of the eigenvalues of the electronic contribution to the dielectric tensor", "poly_total": "the average of the eigenvalues of the total (electronic and ionic) contributions to the dielectric tensor", "poscar": "optional: Poscar metadata", "pot_ferroelectric": "Whether the material is potentially ferroelectric", "space_group": "Integer specifying the crystallographic structure of the material", "structure": "pandas Series defining the structure of the material", "volume": "Volume of the unit cell in cubic angstroms, For supercell calculations, this quantity refers to the volume of the full supercell. "}, "description": "1,056 structures with dielectric properties, calculated with DFPT-PBE.", "file_type": "json.gz", "hash": "8eb24812148732786cd7c657eccfc6b5ee66533429c2cfbcc4f0059c0295e8b6", "num_entries": 1056, "reference": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>,\n<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, T<PERSON>, <PERSON>, K. A. & Prinz, F. B.\nHigh-throughput screening of inorganic compounds for the discovery\nof novel dielectric and optical materials. Sci. Data 4, 160134 (2017).", "url": "https://ndownloader.figshare.com/files/13213475"}, "double_perovskites_gap": {"bibtex_refs": ["@Article{Pilania2016,\nauthor={<PERSON><PERSON><PERSON>, <PERSON><PERSON>\nand <PERSON><PERSON>, A.\nand <PERSON>ber<PERSON>, B. P.\nand <PERSON>, <PERSON><PERSON>\nand <PERSON><PERSON>, <PERSON>. <PERSON>\nand <PERSON>, T.},\ntitle={Machine learning bandgaps of double perovskites},\njournal={Scientific Reports},\nyear={2016},\nmonth={Jan},\nday={19},\npublisher={The Author(s)},\nvolume={6},\npages={19375},\nnote={Article},\nurl={http://dx.doi.org/10.1038/srep19375}\n}", "@misc{Computational Materials Repository,\ntitle = {Computational Materials Repository},\nhowpublished = {\\url{https://cmr.fysik.dtu.dk/}},\n}"], "columns": {"a_1": "Species occupying the a1 perovskite site", "a_2": "Species occupying the a2 site", "b_1": "Species occupying the b1 site", "b_2": "Species occupying the b2 site", "formula": "Chemical formula of the entry", "gap gllbsc": "electronic band gap (in eV) calculated via gllbsc"}, "description": "Band gap of 1306 double perovskites (a_1-b_1-a_2-b_2-O6) calculated using ﻿<PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON>ere<PERSON> potential (gllbsc) in GPAW.", "file_type": "json.gz", "hash": "746072db995ec46f44b67c3f55fccdc24a7ec6233be1b96cc52dab8db4bf0ba4", "num_entries": 1306, "reference": "Dataset discussed in:\n<PERSON><PERSON><PERSON>, <PERSON><PERSON> et al. Machine learning bandgaps of double perovskites. Sci. Rep. 6, 19375; doi: 10.1038/srep19375 (2016).\nDataset sourced from:\nhttps://cmr.fysik.dtu.dk/", "url": "https://ndownloader.figshare.com/files/13309157"}, "double_perovskites_gap_lumo": {"bibtex_refs": ["@Article{Pilania2016,\nauthor={<PERSON><PERSON><PERSON>, <PERSON><PERSON>\nand <PERSON><PERSON>, A.\nand <PERSON>ber<PERSON>, B. P.\nand <PERSON>, <PERSON><PERSON>\nand <PERSON><PERSON>, <PERSON>. <PERSON>\nand <PERSON>, T.},\ntitle={Machine learning bandgaps of double perovskites},\njournal={Scientific Reports},\nyear={2016},\nmonth={Jan},\nday={19},\npublisher={The Author(s)},\nvolume={6},\npages={19375},\nnote={Article},\nurl={http://dx.doi.org/10.1038/srep19375}\n}", "@misc{Computational Materials Repository,\ntitle = {Computational Materials Repository},\nhowpublished = {\\url{https://cmr.fysik.dtu.dk/}},\n}"], "columns": {"atom": "Name of the atom whos lumo is listed", "lumo": "Lowest unoccupied molecular obital energy level (in eV)"}, "description": "Supplementary lumo data of 55 atoms for the double_perovskites_gap dataset.", "file_type": "json.gz", "hash": "6d57bcec55a748ab9da2224195d4c5fb78edfabbbfe606d11cb04ae63b385808", "num_entries": 55, "reference": "Dataset discussed in:\n<PERSON><PERSON><PERSON>, <PERSON><PERSON> et al. Machine learning bandgaps of double perovskites. Sci. Rep. 6, 19375; doi: 10.1038/srep19375 (2016).\nDataset sourced from:\nhttps://cmr.fysik.dtu.dk/", "url": "https://ndownloader.figshare.com/files/13309154"}, "elastic_tensor_2015": {"bibtex_refs": ["@Article{deJong2015,\nauthor={<PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>\nand <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON>\nand <PERSON>, <PERSON> and <PERSON>, Chaitanya\nand <PERSON> der <PERSON>, Sybrand and Plata, <PERSON> and <PERSON>, Cormac\nand Curtarolo, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON>\nand <PERSON><PERSON>, <PERSON>},\ntitle={Charting the complete elastic properties\nof inorganic crystalline compounds},\njournal={Scientific Data},\nyear={2015},\nmonth={Mar},\nday={17},\npublisher={The Author(s)},\nvolume={2},\npages={150009},\nnote={Data Descriptor},\nurl={http://dx.doi.org/10.1038/sdata.2015.9}\n}"], "columns": {"G_Reuss": "Lower bound on shear modulus for polycrystalline material", "G_VRH": "Average of <PERSON><PERSON><PERSON><PERSON> and <PERSON>_Voigt", "G_Voigt": "Upper bound on shear modulus for polycrystalline material", "K_Reuss": "Lower bound on bulk modulus for polycrystalline material", "K_VRH": "Average of <PERSON><PERSON><PERSON><PERSON> and <PERSON>_<PERSON>oigt", "K_Voigt": "Upper bound on bulk modulus for polycrystalline material", "cif": "optional: Description string for structure", "compliance_tensor": "Tensor describing elastic behavior", "elastic_anisotropy": "measure of directional dependence of the materials elasticity, metric is always >= 0", "elastic_tensor": "Tensor describing elastic behavior corresponding to IEEE orientation, symmetrized to crystal structure ", "elastic_tensor_original": "Tensor describing elastic behavior, unsymmetrized, corresponding to POSCAR conventional standard cell orientation", "formula": "Chemical formula of the material", "kpoint_density": "optional: Sampling parameter from calculation", "material_id": "Materials Project ID of the material", "nsites": "The \\# of atoms in the unit cell of the calculation.", "poisson_ratio": "Describes lateral response to loading", "poscar": "optional: Poscar metadata", "space_group": "Integer specifying the crystallographic structure of the material", "structure": "pandas Series defining the structure of the material", "volume": "Volume of the unit cell in cubic angstroms, For supercell calculations, this quantity refers to the volume of the full supercell. "}, "description": "1,181 structures with elastic properties calculated with DFT-PBE.", "file_type": "json.gz", "hash": "8c3b342f75da7e7baa1b769b59554485fd647f4cb1da9318d0d1ba3b3b838183", "num_entries": 1181, "reference": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>,\n<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>. <PERSON>, <PERSON>, <PERSON><PERSON>, Plata, J. <PERSON>, <PERSON>,\n<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON>, <PERSON>, \"Charting\nthe complete elastic properties of inorganic crystalline compounds\",\nScientific Data volume 2, Article number: 150009 (2015)", "url": "https://ndownloader.figshare.com/files/13220603"}, "expt_formation_enthalpy": {"bibtex_refs": ["@Article{Kim2017,\nauthor={<PERSON>, <PERSON>\nand <PERSON>, S. V<PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>},\ntitle={Experimental formation enthalpies for intermetallic phases and other inorganic compounds},\njournal={Scientific Data},\nyear={2017},\nmonth={Oct},\nday={24},\npublisher={The Author(s)},\nvolume={4},\npages={170162},\nnote={Data Descriptor},\nurl={https://doi.org/10.1038/sdata.2017.162}}", " @misc{kim_meschel_nash_chen_2017, title={Experimental formation enthalpies for intermetallic phases and other inorganic compounds}, url={https://figshare.com/collections/Experimental_formation_enthalpies_for_intermetallic_phases_and_other_inorganic_compounds/3822835/1}, DOI={10.6084/m9.figshare.c.3822835.v1}, abstractNote={The standard enthalpy of formation of a compound is the energy associated with the reaction to form the compound from its component elements. The standard enthalpy of formation is a fundamental thermodynamic property that determines its phase stability, which can be coupled with other thermodynamic data to calculate phase diagrams. Calorimetry provides the only direct method by which the standard enthalpy of formation is experimentally measured. However, the measurement is often a time and energy intensive process. We present a dataset of enthalpies of formation measured by high-temperature calorimetry. The phases measured in this dataset include intermetallic compounds with transition metal and rare-earth elements, metal borides, metal carbides, and metallic silicides. These measurements were collected from over 50 years of calorimetric experiments. The dataset contains 1,276 entries on experimental enthalpy of formation values and structural information. Most of the entries are for binary compounds but ternary and quaternary compounds are being added as they become available. The dataset also contains predictions of enthalpy of formation from first-principles calculations for comparison.}, publisher={figshare}, author={<PERSON>, <PERSON> and <PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>}, year={2017}, month={Oct}}"], "columns": {"e_form expt": "experimental formation enthalpy (in eV/atom)", "e_form mp": "formation enthalpy from Materials Project (in eV/atom)", "e_form oqmd": "formation enthalpy from OQMD (in eV/atom)", "formula": "chemical formula", "mpid": "materials project id", "oqmdid": "OQMD id", "pearson symbol": "pearson symbol of the structure", "space group": "space group of the structure"}, "description": "Experimental formation enthalpies for inorganic compounds, collected from years of calorimetric experiments. There are 1,276 entries in this dataset, mostly binary compounds. Matching mpids or oqmdids as well as the DFT-computed formation energies are also added (if any).", "file_type": "json.gz", "hash": "cb0e1bba0de70ca977a3a6b3c0020cc986fe32affc90fc36752beb61aaf6f5b4", "num_entries": 1276, "reference": "https://www.nature.com/articles/sdata2017162", "url": "https://ndownloader.figshare.com/files/13464467"}, "expt_gap": {"bibtex_refs": ["@article{doi:10.1021/acs.jpclett.8b00124,\nauthor = {<PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, Jak<PERSON><PERSON>},\ntitle = {Predicting the Band Gaps of Inorganic Solids by Machine Learning},\njournal = {The Journal of Physical Chemistry Letters},\nvolume = {9},\nnumber = {7},\npages = {1668-1673},\nyear = {2018},\ndoi = {10.1021/acs.jpclett.8b00124},\nnote ={PMID: 29532658},\neprint = {\nhttps://doi.org/10.1021/acs.jpclett.8b00124\n\n}}"], "columns": {"formula": "chemical formula", "gap expt": "band gap (in eV) measured experimentally"}, "description": "Experimental band gap of 6354 inorganic semiconductors.", "file_type": "json.gz", "hash": "2d0980e3533c1ba6ad6e392a88f08cfcf2d311d4b7fe6eb0b0c8e876211dfda3", "num_entries": 6354, "reference": "https://pubs.acs.org/doi/suppl/10.1021/acs.jpclett.8b00124", "url": "https://ndownloader.figshare.com/files/13464434"}, "flla": {"bibtex_refs": ["@article{doi:10.1002/qua.24917,\nauthor = {<PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, Rickard},\ntitle = {Crystal structure representations for machine learning models of formation energies},\njournal = {International Journal of Quantum Chemistry},\nvolume = {115},\nnumber = {16},\npages = {1094-1101},\nkeywords = {machine learning, formation energies, representations,\ncrystal structure, periodic systems},\ndoi = {10.1002/qua.24917},\nurl = {https://onlinelibrary.wiley.com/doi/abs/10.1002/qua.24917},\neprint = {https://onlinelibrary.wiley.com/doi/pdf/10.1002/qua.24917},\nabstract = {We introduce and evaluate a set of feature vector\nrepresentations of crystal structures for machine learning (ML)\nmodels of formation energies of solids. ML models of atomization\nenergies of organic molecules have been successful using a Coulomb\nmatrix representation of the molecule. We consider three ways to\ngeneralize such representations to periodic systems: (i) a matrix\nwhere each element is related to the Ewald sum of the electrostatic\ninteraction between two different atoms in the unit cell repeated\nover the lattice; (ii) an extended Coulomb-like matrix that takes\ninto account a number of neighboring unit cells; and (iii) an\nansatz that mimics the periodicity and the basic features of the\nelements in the Ewald sum matrix using a sine function of the\ncrystal coordinates of the atoms. The representations are compared\nfor a Laplacian kernel with Manhattan norm, trained to reproduce\nformation energies using a dataset of 3938 crystal structures\nobtained from the Materials Project. For training sets consisting\nof 3000 crystals, the generalization error in predicting formation\nenergies of new structures corresponds to (i) 0.49, (ii) 0.64, and\n(iii) for the respective representations. © 2015 Wiley Periodicals,\nInc.}\n}", "@article{doi:10.1063/1.4812323,\nauthor = {<PERSON>,<PERSON><PERSON><PERSON><PERSON>  and <PERSON>,<PERSON><PERSON><PERSON>  and <PERSON>,<PERSON><PERSON>\nand <PERSON>,<PERSON>  and <PERSON>,<PERSON>  and <PERSON>,<PERSON>\nand <PERSON>,<PERSON><PERSON><PERSON><PERSON>  <PERSON> <PERSON><PERSON>,<PERSON>  and <PERSON>,<PERSON>\nand <PERSON>,<PERSON><PERSON><PERSON>  and <PERSON>,<PERSON><PERSON> },\ntitle = {Commentary: The Materials Project: A materials genome\napproach to accelerating materials innovation},\njournal = {APL Materials},\nvolume = {1},\nnumber = {1},\npages = {011002},\nyear = {2013},\ndoi = {10.1063/1.4812323},\nURL = {https://doi.org/10.1063/1.4812323},\neprint = {https://doi.org/10.1063/1.4812323}\n}"], "columns": {"e_above_hull": "The energy of decomposition of this material into the set of most stable materials at this chemical composition, in eV/atom.", "formation_energy": "Computed formation energy at 0K, 0atm using a reference state of zero for the pure elements.", "formation_energy_per_atom": "See formation_energy", "formula": "Chemical formula of the material", "material_id": "Materials Project ID of the material", "nsites": "The \\# of atoms in the unit cell of the calculation.", "structure": "pandas Series defining the structure of the material"}, "description": "3938 structures and computed formation energies from \"Crystal Structure Representations for Machine Learning Models of Formation Energies.\"", "file_type": "json.gz", "hash": "a7f1649c3b9f5186e8440b163e0421cbfdf61973ec7e45101f6dab641f1e2170", "num_entries": 3938, "reference": "1) <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>,\n\"Crystal structure representations for machine learning models of\nformation energies\", Int. J. Quantum Chem. 115 (2015) 1094–1101.\ndoi:10.1002/qua.24917.\n\n(raw data)\n2) <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>,\n<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> & <PERSON>,\nK. A. Commentary: The Materials Project: A materials genome approach\nto accelerating materials innovation. APL Mater. 1, 11002 (2013).", "url": "https://ndownloader.figshare.com/files/13220597"}, "glass_binary": {"bibtex_refs": ["@article{doi:10.1021/acs.jpclett.7b01046,\nauthor = {<PERSON>, Y. <PERSON>. and <PERSON>, <PERSON>. Y. and <PERSON>, <PERSON><PERSON> and <PERSON>, W. H.},\ntitle = {Machine Learning Approach for Prediction and Understanding of Glass-Forming Ability},\njournal = {The Journal of Physical Chemistry Letters},\nvolume = {8},\nnumber = {14},\npages = {3434-3439},\nyear = {2017},\ndoi = {10.1021/acs.jpclett.7b01046},\nnote ={PMID: 28697303},\neprint = {\nhttps://doi.org/10.1021/acs.jpclett.7b01046\n\n}}"], "columns": {"formula": "chemical formula", "gfa": "glass forming ability, correlated with the phase column, designating whether the composition can form monolithic glass or not, 1: glass forming (\"AM\"), 0: non-full-forming(\"CR\")"}, "description": "Metallic glass formation data for binary alloys, collected from various experimental techniques such as melt-spinning or mechanical alloying. This dataset covers all compositions with an interval of 5 at. % in 59 binary systems, containing a total of 5959 alloys in the dataset. The target property of this dataset is the glass forming ability (GFA), i.e. whether the composition can form monolithic glass or not, which is either 1 for glass forming or 0 for non-full glass forming.", "file_type": "json.gz", "hash": "76ad5994074eeca1caf6161a3f6e9aeaf3bdd8f1e2a044ac4939bc11310cb48a", "num_entries": 5959, "reference": "https://pubs.acs.org/doi/10.1021/acs.jpclett.7b01046", "url": "https://ndownloader.figshare.com/files/13464368"}, "glass_binary_v2": {"bibtex_refs": ["@article{doi:10.1021/acs.jpclett.7b01046,\nauthor = {<PERSON>, Y. <PERSON>. and <PERSON>, <PERSON>. Y. and <PERSON>, <PERSON><PERSON> and <PERSON>, W. H.},\ntitle = {Machine Learning Approach for Prediction and Understanding of Glass-Forming Ability},\njournal = {The Journal of Physical Chemistry Letters},\nvolume = {8},\nnumber = {14},\npages = {3434-3439},\nyear = {2017},\ndoi = {10.1021/acs.jpclett.7b01046},\nnote ={PMID: 28697303},\neprint = {\nhttps://doi.org/10.1021/acs.jpclett.7b01046\n\n}}"], "columns": {"formula": "chemical formula", "gfa": "glass forming ability, correlated with the phase column, designating whether the composition can form monolithic glass or not, 1: glass forming (\"AM\"), 0: non-full-forming(\"CR\")"}, "description": "Identical to glass_binary dataset, but with duplicate entries merged. If there was a disagreement in gfa when merging the class was defaulted to 1.", "file_type": "json.gz", "hash": "70ef83f6c7130615b759ae52eb9c48ec77503bcc34b00aec328de372d9f19c90", "num_entries": 5483, "reference": "https://pubs.acs.org/doi/10.1021/acs.jpclett.7b01046", "url": "https://ndownloader.figshare.com/files/13553498"}, "glass_ternary_hipt": {"bibtex_refs": ["@article {Reneaaq1566,\nauthor = {<PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON>-<PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON>},\ntitle = {Accelerated discovery of metallic glasses through iteration of machine learning and high-throughput experiments},\nvolume = {4},\nnumber = {4},\nyear = {2018},\ndoi = {10.1126/sciadv.aaq1566},\npublisher = {American Association for the Advancement of Science},\nabstract = {With more than a hundred elements in the periodic table, a large number of potential new materials exist to address the technological and societal challenges we face today; however, without some guidance, searching through this vast combinatorial space is frustratingly slow and expensive, especially for materials strongly influenced by processing. We train a machine learning (ML) model on previously reported observations, parameters from physiochemical theories, and make it synthesis method{\\textendash}dependent to guide high-throughput (HiTp) experiments to find a new system of metallic glasses in the Co-V-Zr ternary. Experimental observations are in good agreement with the predictions of the model, but there are quantitative discrepancies in the precise compositions predicted. We use these discrepancies to retrain the ML model. The refined model has significantly improved accuracy not only for the Co-V-Zr system but also across all other available validation data. We then use the refined model to guide the discovery of metallic glasses in two additional previously unreported ternaries. Although our approach of iterative use of ML and HiTp experiments has guided us to rapid discovery of three new glass-forming systems, it has also provided us with a quantitatively accurate, synthesis method{\\textendash}sensitive predictor for metallic glasses that improves performance with use and thus promises to greatly accelerate discovery of many new metallic glasses. We believe that this discovery paradigm is applicable to a wider range of materials and should prove equally powerful for other materials and properties that are synthesis path{\\textendash}dependent and that current physiochemical theories find challenging to predict.},\nURL = {http://advances.sciencemag.org/content/4/4/eaaq1566},\neprint = {http://advances.sciencemag.org/content/4/4/eaaq1566.full.pdf},\njournal = {Science Advances}\n}"], "columns": {"formula": "Chemical formula of the entry", "gfa": "Glass forming ability: 1 means glass forming and coresponds to AM, 0 means non glass forming and corresponds to CR", "phase": "AM: amorphous phase or CR: crystalline phase", "processing": "How the point was processed, always sputtering for this dataset", "system": "System of dataset experiment, one of: CoFeZr, CoTiZr, CoVZr, or FeTiNb"}, "description": "Metallic glass formation dataset for ternary alloys, collected from the high-throughput sputtering experiments measuring whether it is possible to form a glass using sputtering. The hipt experimental data are of the Co-Fe-Zr, Co-Ti-Zr, Co-V-Zr and Fe-Ti-Nb ternary systems.", "file_type": "json.gz", "hash": "4a4c60ef227891203570dbc3decb606649b877f6b75289afd4e78fe8e8095e63", "num_entries": 5170, "reference": "Accelerated discovery of metallic glasses through iteration of machine learning and high-throughput experiments\nBy <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Apurva Mehta\nScience Advances 13 Apr 2018 : eaaq1566", "url": "https://ndownloader.figshare.com/files/13298018"}, "glass_ternary_landolt": {"bibtex_refs": ["@Misc{LandoltBornstein1997:sm_lbs_978-3-540-47679-5_2,\nauthor=\"<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>\nand <PERSON>, <PERSON><PERSON>\nand <PERSON><PERSON>, A<PERSON><PERSON><PERSON><PERSON>\nand <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>\nand <PERSON><PERSON>, T.\",\neditor=\"<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>\nand <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>\nand <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>\nand <PERSON><PERSON><PERSON>, T.\",\ntitle=\"Nonequilibrium Phase Diagrams of Ternary Amorphous Alloys {\\textperiodcentered} 1 Introduction: Datasheet from Landolt-B{\\\"o}rnstein - Group III Condensed Matter {\\textperiodcentered} Volume 37A: ``Nonequilibrium Phase Diagrams of Ternary Amorphous Alloys'' in SpringerMaterials (https://dx.doi.org/10.1007/10510374{\\_}2)\",\npublisher=\"Springer-Verlag Berlin Heidelberg\",\nnote=\"Copyright 1997 Springer-Verlag Berlin Heidelberg\",\nnote=\"Part of SpringerMaterials\",\nnote=\"accessed 2018-10-23\",\ndoi=\"10.1007/10510374_2\",\nurl=\"https://materials.springer.com/lb/docs/sm_lbs_978-3-540-47679-5_2\"\n}", "@Article{Ward2016,\nauthor={<PERSON>, <PERSON>\nand <PERSON>, Anki<PERSON>\nand Choudhary, Alok\nand Wolverton, Christopher},\ntitle={A general-purpose machine learning framework for predicting properties of inorganic materials},\njournal={Npj Computational Materials},\nyear={2016},\nmonth={Aug},\nday={26},\npublisher={The Author(s)},\nvolume={2},\npages={16028},\nnote={Article},\nurl={http://dx.doi.org/10.1038/npjcompumats.2016.28}\n}"], "columns": {"formula": "Chemical formula of the entry", "gfa": "Glass forming ability: 1 means glass forming and corresponds to AM, 0 means non full glass forming and corresponds to CR AC or QC", "phase": "\"AM\": amorphous phase. \"CR\": crystalline phase. \"AC\": amorphous-crystalline composite phase. \"QC\": quasi-crystalline phase. Phases obtained from glass producing experiments", "processing": "processing method, meltspin or sputtering"}, "description": "Metallic glass formation dataset for ternary alloys, collected from the \"Nonequilibrium Phase Diagrams of Ternary Amorphous Alloys,’ a volume of the Landolt– Börnstein collection. This dataset contains experimental measurements of whether it is possible to form a glass using a variety of processing techniques at thousands of compositions from hundreds of ternary systems. The processing techniques are designated in the \"processing\" column. There are originally 7191 experiments in this dataset, will be reduced to 6203 after deduplicated, and will be further reduced to 6118 if combining multiple data for one composition. There are originally 6780 melt-spinning experiments in this dataset, will be reduced to 5800 if deduplicated, and will be further reduced to 5736 if combining multiple experimental data for one composition.", "file_type": "json.gz", "hash": "748efd0bd85da497ebcb894b2681bc7c16aed52295344432ca501216832aa260", "num_entries": 7191, "reference": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> (1997) <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> (ed.) SpringerMaterials\nNonequilibrium Phase Diagrams of Ternary Amorphous Alloys · 1 Introduction Landolt-Börnstein - Group III Condensed Matter 37A (Nonequilibrium Phase Diagrams of Ternary Amorphous Alloys) https://www.springer.com/gp/book/9783540605072 (Springer-Verlag Berlin Heidelberg © 1997) Accessed: 03-09-2019", "url": "https://ndownloader.figshare.com/files/13314428"}, "heusler_magnetic": {"bibtex_refs": ["@misc{Citrine Informatics,\ntitle = {University of Alabama Heusler database},\nhowpublished = {\\url{https://citrination.com/datasets/150561/}},\n}"], "columns": {"e_form": "Formation energy in eV/atom", "formula": "Chemical formula of the entry", "heusler type": "Full, Half, or Inverse Heusler", "latt const": "La<PERSON>ce constant", "mu_b": "Magnetic moment", "mu_b saturation": "Saturation magnetization in emu/cc", "num_electron": "Number of electrons per formula unit", "pol fermi": "Polarization at Fermi level in %", "struct type": "Structure type", "tetragonality": "Tetragonality, i.e. c/a"}, "description": "1153 Heusler alloys with DFT-calculated magnetic and electronic properties. The 1153 alloys include 576 full, 449 half and 128 inverse Heusler alloys. The data are extracted and cleaned (including de-duplicating) from Citrine.", "file_type": "json.gz", "hash": "0ae0b837201979afb6004451c8a79b30c0045f0daf4ec11355c3db5953ff9520", "num_entries": 1153, "reference": "https://citrination.com/datasets/150561/", "url": "https://ndownloader.figshare.com/files/13354670"}, "jarvis_dft_2d": {"bibtex_refs": ["@Article{Cho<PERSON><PERSON>y2017,\nauthor={<PERSON><PERSON><PERSON><PERSON>, <PERSON>\nand <PERSON><PERSON>, <PERSON><PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>},\ntitle={High-throughput Identification and Characterization of Two-dimensional Materials using Density functional theory},\njournal={Scientific Reports},\nyear={2017},\nvolume={7},\nnumber={1},\npages={5179},\nabstract={We introduce a simple criterion to identify two-dimensional (2D) materials based on the comparison between experimental lattice constants and lattice constants mainly obtained from Materials-Project (MP) density functional theory (DFT) calculation repository. Specifically, if the relative difference between the two lattice constants for a specific material is greater than or equal to 5%, we predict them to be good candidates for 2D materials. We have predicted at least 1356 such 2D materials. For all the systems satisfying our criterion, we manually create single layer systems and calculate their energetics, structural, electronic, and elastic properties for both the bulk and the single layer cases. Currently the database consists of 1012 bulk and 430 single layer materials, of which 371 systems are common to bulk and single layer. The rest of calculations are underway. To validate our criterion, we calculated the exfoliation energy of the suggested layered materials, and we found that in 88.9% of the cases the currently accepted criterion for exfoliation was satisfied. Also, using molybdenum telluride as a test case, we performed X-ray diffraction and Raman scattering experiments to benchmark our calculations and understand their applicability and limitations. The data is publicly available at the website http://www.ctcms.nist.gov/{\textasciitilde}knc6/JVASP.html.},\nissn={2045-2322},\ndoi={10.1038/s41598-017-05402-0},\nurl={https://doi.org/10.1038/s41598-017-05402-0}\n}", "@misc{choudhary__2018, title={jdft_2d-7-7-2018.json}, url={https://figshare.com/articles/jdft_2d-7-7-2018_json/6815705/1}, DOI={10.6084/m9.figshare.6815705.v1}, abstractNote={2D materials}, publisher={figshare}, author={choudhary, kamal and https://orcid.org/0000-0001-9737-8074}, year={2018}, month={Jul}}"], "columns": {"composition": "A Pymatgen Composition descriptor of the composition of the material", "e_form": "formation energy per atom, in eV/atom", "epsilon_x opt": "Static dielectric function in x direction calculated with OptB88vDW functional.", "epsilon_x tbmbj": "Static dielectric function in x direction calculuated with TBMBJ functional.", "epsilon_y opt": "Static dielectric function in y direction calculated with OptB88vDW functional.", "epsilon_y tbmbj": "Static dielectric function in y direction calculuated with TBMBJ functional.", "epsilon_z opt": "Static dielectric function in z direction calculated with OptB88vDW functional.", "epsilon_z tbmbj": "Static dielectric function in z direction calculuated with TBMBJ functional.", "exfoliation_en": "Exfoliation energy (monolayer formation E) in meV/atom", "gap opt": "Band gap calculated with OptB88vDW functional, in eV", "gap tbmbj": "Band gap calculated with TBMBJ functional, in eV", "jid": "JARVIS ID", "mpid": "Materials Project ID", "structure": "A description of the crystal structure of the material", "structure initial": "Initial structure description of the crystal structure of the material"}, "description": "Various properties of 636 2D materials computed with the OptB88vdW and TBmBJ functionals taken from the JARVIS DFT database.", "file_type": "json.gz", "hash": "6994355a15ae960e3e204a1208db68eb490b5be363b15b97b36d2e0cd09b6d99", "num_entries": 636, "reference": "2D Dataset discussed in:\nHigh-throughput Identification and Characterization of Two dimensional Materials using Density functional theory <PERSON>, <PERSON><PERSON>, <PERSON> & <PERSON> Scientific Reports volume 7, Article number: 5179 (2017)\nOriginal 2D Data file sourced from:\nch<PERSON><PERSON><PERSON>, kamal; https://orcid.org/0000-0001-9737-8074 (2018): jdft_2d-7-7-2018.json. figshare. Dataset.", "url": "https://ndownloader.figshare.com/files/13376504"}, "jarvis_dft_3d": {"bibtex_refs": ["@article{PhysRevB.98.014107,\ntitle = {Elastic properties of bulk and low-dimensional materials using van der Waals density functional},\nauthor = {<PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, Francesca},\njournal = {Phys. Rev. B},\nvolume = {98},\nissue = {1},\npages = {014107},\nnumpages = {12},\nyear = {2018},\nmonth = {Jul},\npublisher = {American Physical Society},\ndoi = {10.1103/PhysRevB.98.014107},\nurl = {https://link.aps.org/doi/10.1103/PhysRevB.98.014107}\n}", "@misc{choudhary__2018, title={jdft_3d.json}, url={https://figshare.com/articles/jdft_3d-7-7-2018_json/6815699/2}, DOI={10.6084/m9.figshare.6815699.v2}, abstractNote={https://jarvis.nist.gov/\nThe Density functional theory section of JARVIS (JARVIS-DFT) consists of thousands of VASP based calculations for 3D-bulk, single layer (2D), nanowire (1D) and molecular (0D) systems. Most of the calculations are carried out with optB88vDW functional. JARVIS-DFT includes materials data such as: energetics, diffraction pattern, radial distribution function, band-structure, density of states, carrier effective mass, temperature and carrier concentration dependent thermoelectric properties, elastic constants and gamma-point phonons.}, publisher={figshare}, author={choudhary, kamal and https://orcid.org/0000-0001-9737-8074}, year={2018}, month={Jul}}"], "columns": {"bulk modulus": "VRH average calculation of bulk modulus", "composition": "A Pymatgen Composition descriptor of the composition of the material", "e_form": "formation energy per atom, in eV/atom", "epsilon_x opt": "Static dielectric function in x direction calculated with OptB88vDW functional.", "epsilon_x tbmbj": "Static dielectric function in x direction calculuated with TBMBJ functional.", "epsilon_y opt": "Static dielectric function in y direction calculated with OptB88vDW functional.", "epsilon_y tbmbj": "Static dielectric function in y direction calculuated with TBMBJ functional.", "epsilon_z opt": "Static dielectric function in z direction calculated with OptB88vDW functional.", "epsilon_z tbmbj": "Static dielectric function in z direction calculuated with TBMBJ functional.", "gap opt": "Band gap calculated with OptB88vDW functional, in eV", "gap tbmbj": "Band gap calculated with TBMBJ functional, in eV", "jid": "JARVIS ID", "mpid": "Materials Project ID", "shear modulus": "VRH average calculation of shear modulus", "structure": "A description of the crystal structure of the material", "structure initial": "Initial structure description of the crystal structure of the material"}, "description": "Various properties of 25,923 bulk materials computed with the OptB88vdW and TBmBJ functionals taken from the JARVIS DFT database.", "file_type": "json.gz", "hash": "3d0fc1315b74cf84071f16aa25bb93434dafbc83867beb75872064ac245bd747", "num_entries": 25923, "reference": "3D Dataset discussed in:\nElastic properties of bulk and low-dimensional materials using van der <PERSON>s density functional <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Rev. B 98, 014107\nOriginal 3D Data file sourced from:\nch<PERSON><PERSON><PERSON>, kamal; https://orcid.org/0000-0001-9737-8074 (2018): jdft_3d.json. figshare. Dataset.", "url": "https://ndownloader.figshare.com/files/13376507"}, "jarvis_ml_dft_training": {"bibtex_refs": ["@article{PhysRevMaterials.2.083801,\ntitle = {Machine learning with force-field-inspired descriptors for materials: Fast screening and mapping energy landscape},\nauthor = {<PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, Francesca},\njournal = {Phys. Rev. Materials},\nvolume = {2},\nissue = {8},\npages = {083801},\nnumpages = {8},\nyear = {2018},\nmonth = {Aug},\npublisher = {American Physical Society},\ndoi = {10.1103/PhysRevMaterials.2.083801},\nurl = {https://link.aps.org/doi/10.1103/PhysRevMaterials.2.083801}\n}", "@misc{choudhary_2018, title={JARVIS-ML-CFID-descriptors and material properties}, url={https://figshare.com/articles/JARVIS-ML-CFID-descriptors_and_material_properties/6870101/1}, DOI={10.6084/m9.figshare.6870101.v1}, abstractNote={Classical force-field inspired descriptors (CFID) for more than 25000 materials and their material properties such as bandgap, formation energies, modulus of elasticity etc. See JARVIS-ML: https://jarvis.nist.gov/}, publisher={figshare}, author={choudhary, kamal}, year={2018}, month={Jul}}"], "columns": {"bulk modulus": "VRH average calculation of bulk modulus", "composition": "A descriptor of the composition of the material", "e mass_x": "Effective electron mass in x direction (BoltzTraP)", "e mass_y": "Effective electron mass in y direction (BoltzTraP)", "e mass_z": "Effective electron mass in z direction (BoltzTraP)", "e_exfol": "exfoliation energy per atom in eV/atom", "e_form": "formation energy per atom, in eV/atom", "epsilon_x opt": "Static dielectric function in x direction calculated with OptB88vDW functional.", "epsilon_x tbmbj": "Static dielectric function in x direction calculated with TBMBJ functional.", "epsilon_y opt": "Static dielectric function in y direction calculated with OptB88vDW functional.", "epsilon_y tbmbj": "Static dielectric function in y direction calculated with TBMBJ functional.", "epsilon_z opt": "Static dielectric function in z direction calculated with OptB88vDW functional.", "epsilon_z tbmbj": "Static dielectric function in z direction calculated with TBMBJ functional.", "gap opt": "Band gap calculated with OptB88vDW functional, in eV", "gap tbmbj": "Band gap calculated with TBMBJ functional, in eV", "hole mass_x": "Effective hole mass in x direction (BoltzTraP)", "hole mass_y": "Effective hole mass in y direction (BoltzTraP)", "hole mass_z": "Effective hole mass in z direction (BoltzTraP)", "jid": "JARVIS ID", "mpid": "Materials Project ID", "mu_b": "Magnetic moment, in Bohr Magneton", "shear modulus": "VRH average calculation of shear modulus", "structure": "A Pymatgen Structure object describing the crystal structure of the material"}, "description": "Various properties of 24,759 bulk and 2D materials computed with the OptB88vdW and TBmBJ functionals taken from the JARVIS DFT database.", "file_type": "json.gz", "hash": "e7b147bb2135018e38050acc479230adb5dceb5ce3ae1eacb323e1ca1d44770b", "num_entries": 24759, "reference": "Dataset discussed in:\nMachine learning with force-field-inspired descriptors for materials: Fast screening and mapping energy landscape <PERSON>, <PERSON>, and <PERSON>. Rev. Materials 2, 083801\n\nOriginal Data file sourced from:\n<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> (2018): JARVIS-ML-CFID-descriptors and material properties. figshare. Dataset.", "url": "https://ndownloader.figshare.com/files/13376159"}, "m2ax": {"bibtex_refs": ["@article{M F Cover,\nauthor={<PERSON> and <PERSON> and <PERSON> <PERSON> and <PERSON>},\ntitle={A comprehensive survey of M 2 AX phase elastic properties},\njournal={Journal of Physics: Condensed Matter},\nvolume={21},\nnumber={30},\npages={305403},\nurl={http://stacks.iop.org/0953-8984/21/i=30/a=305403},\nyear={2009},\nabstract={M 2 AX phases are a family of nanolaminate, ternary alloys that are composed of slabs of transition metal carbide or nitride (M 2 X) separated by single atomic layers of a main group element. In this combination, they manifest many of the beneficial properties of both ceramic and metallic compounds, making them attractive for many technological applications. We report here the results of a large scale computational survey of the elastic properties of all 240 elemental combinations using first-principles density functional theory calculations. We found correlations revealing the governing role of the A element and its interaction with the M element on the c axis compressibility and shearability of the material. The role of the X element is relatively minor, with the strongest effect seen in the in-plane constants C 11 and C 12 . We identify several elemental compositions with extremal properties such as W 2 SnC, which has by far the lowest value of C 44 , suggesting potential applications as a...}}"], "columns": {"a": "Lattice parameter a, in A (angstrom)", "bulk modulus": "In GPa", "c": "lattice parameter c, in A (angstrom)", "c11": "Elastic constants of the M2AX material. These are specific to hexagonal materials.", "c12": "Elastic constants of the M2AX material. These are specific to hexagonal materials.", "c13": "Elastic constants of the M2AX material. These are specific to hexagonal materials.", "c33": "Elastic constants of the M2AX material. These are specific to hexagonal materials.", "c44": "Elastic constants of the M2AX material. These are specific to hexagonal materials.", "d_ma": "distance from the M atom to the A atom", "d_mx": "distance from the M atom to the X atom", "elastic modulus": "In GPa", "formula": "chemical formula", "shear modulus": "In GPa"}, "description": "Elastic properties of 223 stable M2AX compounds from \"A comprehensive survey of M2AX phase elastic properties\" by Cover et al. Calculations are PAW PW91.", "file_type": "json.gz", "hash": "b8dff06867b04805ef7c3f2fcbf21cb11c6bc3d0a78f5efab2472e9d269f49c0", "num_entries": 223, "reference": "http://iopscience.iop.org/article/10.1088/0953-8984/21/30/305403/meta", "url": "https://ndownloader.figshare.com/files/13464404"}, "mp_all_20181018": {"bibtex_refs": ["@article{Jain2013,\nauthor = {<PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON> a.},\ndoi = {10.1063/1.4812323},\nissn = {2166532X},\njournal = {APL Materials},\nnumber = {1},\npages = {011002},\ntitle = {{The Materials Project: A materials genome approach to accelerating materials innovation}},\nurl = {http://link.aip.org/link/AMPADS/v1/i1/p011002/s1\\&Agg=doi},\nvolume = {1},\nyear = {2013}\n}"], "columns": {"bulk modulus": "in GPa, average of <PERSON>oi<PERSON>, <PERSON><PERSON>, and Hill", "e_form": "Formation energy per atom (eV)", "e_hull": "The calculated energy above the convex hull, in eV per atom", "elastic anisotropy": "The ratio of elastic anisotropy.", "formula": "The chemical formula of the MP entry", "gap pbe": "The band gap in eV calculated with PBE-DFT functional", "initial structure": "A Pymatgen Structure object describing the material crystal structure prior to relaxation", "mpid": "(input): The Materials Project mpid, as a string.", "mu_b": "The total magnetization of the unit cell.", "shear modulus": "in GPa, average of <PERSON>oi<PERSON>, <PERSON><PERSON>, and Hill", "structure": "A Pymatgen Structure object describing the material crystal structure"}, "description": "A complete copy of the Materials Project database as of 10/18/2018. mp_all files contain structure data for each material while mp_nostruct does not.", "file_type": "json.gz", "hash": "1f3de2dc7c68959647240921b841293fce918ea1708e6f3760ba35a9cdfe0500", "num_entries": 83989, "reference": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> (*=equal contributions)\nThe Materials Project: A materials genome approach to accelerating materials innovation\nAPL Materials, 2013, 1(1), 011002.\ndoi:10.1063/1.4812323", "url": "https://ndownloader.figshare.com/files/13309250"}, "mp_nostruct_20181018": {"bibtex_refs": ["@article{Jain2013,\nauthor = {<PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON> a.},\ndoi = {10.1063/1.4812323},\nissn = {2166532X},\njournal = {APL Materials},\nnumber = {1},\npages = {011002},\ntitle = {{The Materials Project: A materials genome approach to accelerating materials innovation}},\nurl = {http://link.aip.org/link/AMPADS/v1/i1/p011002/s1\\&Agg=doi},\nvolume = {1},\nyear = {2013}\n}"], "columns": {"bulk modulus": "in GPa, average of <PERSON>oi<PERSON>, <PERSON><PERSON>, and Hill", "e_form": "Formation energy per atom (eV)", "e_hull": "The calculated energy above the convex hull, in eV per atom", "elastic anisotropy": "The ratio of elastic anisotropy.", "formula": "The chemical formula of the MP entry", "gap pbe": "The band gap in eV calculated with PBE-DFT functional", "mpid": "(input): The Materials Project mpid, as a string.", "mu_b": "The total magnetization of the unit cell.", "shear modulus": "in GPa, average of <PERSON>oi<PERSON>, <PERSON><PERSON>, and Hill"}, "description": "A complete copy of the Materials Project database as of 10/18/2018. mp_all files contain structure data for each material while mp_nostruct does not.", "file_type": "json.gz", "hash": "48481abc390e0772defbbfa68d7db3b9bdef2519d407b82a581f2298f7df080f", "num_entries": 83989, "reference": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> (*=equal contributions)\nThe Materials Project: A materials genome approach to accelerating materials innovation\nAPL Materials, 2013, 1(1), 011002.\ndoi:10.1063/1.4812323", "url": "https://ndownloader.figshare.com/files/13309253"}, "phonon_dielectric_mp": {"bibtex_refs": ["@Article{Petretto2018,\nauthor={<PERSON><PERSON><PERSON>, <PERSON>\nand <PERSON>, <PERSON><PERSON><PERSON>\nand <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>\nand <PERSON>, <PERSON>\nand <PERSON><PERSON><PERSON>, <PERSON>\nand <PERSON>, <PERSON><PERSON><PERSON>\nand <PERSON><PERSON><PERSON>, <PERSON>\nand <PERSON>, <PERSON><PERSON>\nand <PERSON><PERSON>, <PERSON><PERSON>\nand <PERSON>, <PERSON><PERSON><PERSON><PERSON>},\ntitle={High-throughput density-functional perturbation theory phonons for inorganic materials},\njournal={Scientific Data},\nyear={2018},\nmonth={May},\nday={01},\npublisher={The Author(s)},\nvolume={5},\npages={180065},\nnote={Data Descriptor},\nurl={http://dx.doi.org/10.1038/sdata.2018.65}\n}", "@misc{petret<PERSON>_<PERSON><PERSON><PERSON><PERSON>_miranda_winston_giant<PERSON><PERSON>_rignan<PERSON>_van set<PERSON>_gonze_persson_hautier_2018, title={High-throughput Density-Functional Perturbation Theory phonons for inorganic materials}, url={https://figshare.com/collections/High-throughput_Density-Functional_Perturbation_Theory_phonons_for_inorganic_materials/3938023/1}, DOI={10.6084/m9.figshare.c.3938023.v1}, abstractNote={The knowledge of the vibrational properties of a material is of key importance to understand physical phenomena such as thermal conductivity, superconductivity, and ferroelectricity among others. However, detailed experimental phonon spectra are available only for a limited number of materials which hinders the large-scale analysis of vibrational properties and their derived quantities. In this work, we perform ab initio calculations of the full phonon dispersion and vibrational density of states for 1521 semiconductor compounds in the harmonic approximation based on density functional perturbation theory. The data is collected along with derived dielectric and thermodynamic properties. We present the procedure used to obtain the results, the details of the provided database and a validation based on the comparison with experimental data.}, publisher={figshare}, author={<PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON>-<PERSON> and <PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON>. and <PERSON><PERSON>, <PERSON> and <PERSON><PERSON>, <PERSON><PERSON> <PERSON> and <PERSON><PERSON><PERSON>, <PERSON><PERSON>}, year={2018}, month={<PERSON>}}"], "columns": {"eps_electronic": "A target variable of the dataset, electronic contribution to the calculated dielectric constant; unitless.", "eps_total": "A target variable of the dataset, total calculated dielectric constant. Unitless: it is a ratio over the dielectric constant at vacuum.", "formula": "The chemical formula of the material", "last phdos peak": "A target variable of the dataset, the frequency of the last calculated phonon density of states in 1/cm; may be used as an estimation of dominant longitudinal optical phonon frequency, a descriptor.", "mpid": "The Materials Project identifier for the material", "structure": "A pymatgen Structure object describing the chemical strucutre of the material"}, "description": "Phonon (lattice/atoms vibrations) and dielectric properties of 1296 compounds computed via ABINIT software package in the harmonic approximation based on density functional perturbation theory.", "file_type": "json.gz", "hash": "53044ba423c06b5c08e8d3991c6c2140a2391160f90af03377d4e1734ce475f9", "num_entries": 1296, "reference": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> et al. High-throughput density functional perturbation theory phonons for inorganic materials. Sci. Data 5:180065 doi: 10.1038/sdata.2018.65 (2018).\n<PERSON><PERSON><PERSON>, <PERSON><PERSON> et al. High-throughput density functional perturbation theory phonons for inorganic materials. (2018). figshare. Collection.", "url": "https://ndownloader.figshare.com/files/13297571"}, "piezoelectric_tensor": {"bibtex_refs": ["@Article{deJong2015,\nauthor={<PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>\nand <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON>},\ntitle={A database to enable discovery and design of piezoelectric\nmaterials},\njournal={Scientific Data},\nyear={2015},\nmonth={Sep},\nday={29},\npublisher={The Author(s)},\nvolume={2},\npages={150053},\nnote={Data Descriptor},\nurl={http://dx.doi.org/10.1038/sdata.2015.53}\n}"], "columns": {"cif": "optional: Description string for structure", "eij_max": "Piezoelectric modulus", "formula": "Chemical formula of the material", "material_id": "Materials Project ID of the material", "meta": "optional, metadata descriptor of the datapoint", "nsites": "The \\# of atoms in the unit cell of the calculation.", "piezoelectric_tensor": "Tensor describing the piezoelectric properties of the material", "point_group": "Descriptor of crystallographic structure of the material", "poscar": "optional: Poscar metadata", "space_group": "Integer specifying the crystallographic structure of the material", "structure": "pandas Series defining the structure of the material", "v_max": "Crystallographic direction", "volume": "Volume of the unit cell in cubic angstroms, For supercell calculations, this quantity refers to the volume of the full supercell. "}, "description": "941 structures with piezoelectric properties, calculated with DFT-PBE.", "file_type": "json.gz", "hash": "dc9d04836f7f91ecb4ef6dc23e42468571be857f0fccafdfb51a5a40f19db898", "num_entries": 941, "reference": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, M<PERSON> & <PERSON>, K. A.\nA database to enable discovery and design of piezoelectric materials.\nSci. Data 2, 150053 (2015)", "url": "https://ndownloader.figshare.com/files/13220621"}, "ricci_boltztrap_mp_tabular": {"bibtex_refs": ["@Article{Ricci2017,\nauthor={<PERSON><PERSON><PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON><PERSON>\nand <PERSON>, <PERSON><PERSON>\nand <PERSON>, <PERSON><PERSON><PERSON><PERSON>\nand <PERSON>, <PERSON><PERSON><PERSON><PERSON>\nand <PERSON><PERSON><PERSON>, <PERSON><PERSON>},\ntitle={An ab initio electronic transport database for inorganic materials},\njournal={Scientific Data},\nyear={2017},\nmonth={Jul},\nday={04},\npublisher={The Author(s)},\nvolume={4},\npages={170085},\nnote={Data Descriptor},\nurl={http://dx.doi.org/10.1038/sdata.2017.85}\n}", "@misc{dryad_gn001,\ntitle = {Data from: An ab initio electronic transport database for inorganic materials},\nauthor = {<PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON><PERSON>, <PERSON> and Snyder, <PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, G},\nyear = {2017},\njournal = {Scientific Data},\nURL = {https://doi.org/10.5061/dryad.gn001},\ndoi = {doi:10.5061/dryad.gn001},\npublisher = {Dryad Digital Repository}\n}"], "columns": {"task": "Materials project task_id.", "functional": "Type of DFT functional (GGA=generalized gradient approximation, GGA+U=GGA + U approximation)", "is_metal": "If True, crystal is a metal.", "ΔE [eV]": "Band gap, in eV.", "V [Å³]": "Unit cell volume, in cubic angstrom.", "S.p [µV/K]": "Average eigenvalue of the Seebeck coefficient with hole concentration of 10^-18 carriers/cm^-3 (p-type) at 300K, in microVolts/Kelvin.", "S.n [µV/K]": "Average eigenvalue of the Seebeck coefficient with electron concentration of 10^-18 carriers/cm^-3 (n-type) at 300K, in microVolts/Kelvin.", "Sᵉ.p.v [µV/K]": "Value of p-type Seebeck coefficient at maximum average eigenvalue of Seebeck coefficient chosen among temperatures 100-1300K, doping levels 10^16-10^21cm^-3.", "Sᵉ.p.T [K]": "Temperature corresponding to Sᵉ.p.v [µV/K] (max p-type Seebeck), in Kelvin.", "Sᵉ.p.c [cm⁻³]": "Carrier concentration corresponding to Sᵉ.p.v [µV/K] (max p-type Seebeck), in cm^-3", "Sᵉ.n.v [µV/K]": "Value of n-type Seebeck coefficient at maximum average eigenvalue of Seebeck coefficient chosen among temperatures 100-1300K, doping levels 10^16-10^21cm^-3.", "Sᵉ.n.T [K]": "Temperature corresponding to Sᵉ.n.v [µV/K] (max n-type Seebeck), in Kelvin.", "Sᵉ.n.c [cm⁻³]": "Carrier concentration corresponding to Sᵉ.n.v [µV/K] (max n-type Seebeck), in cm^-3", "σ.p [1/Ω/m/s]": "Average eigenvalue of the conductivity with hole concentration of 10^-18 carriers/cm^-3 (p-type) at 300K, in 1/Ω/m/s.", "σ.n [1/Ω/m/s]": "Average eigenvalue of the conductivity with electron concentration of 10^-18 carriers/cm^-3 (n-type) at 300K, in 1/Ω/m/s.", "PF.p [µW/cm/K²/s]": "Average eigenvalue of the power factor with hole concentration of 10^-18 carriers/cm^-3 (p-type) at 300K, in µW/cm/K²/s.", "PF.n [µW/cm/K²/s]": "Average eigenvalue of the power factor with electron concentration of 10^-18 carriers/cm^-3 (n-type) at 300K, in µW/cm/K²/s.", "σᵉ.p.v [1/Ω/m/s]": "Value of p-type conductivity at maximum average eigenvalue of conductivity chosen among temperatures 100-1300K, doping levels 10^16-10^21cm^-3.", "σᵉ.p.T [K]": "Temperature corresponding to σᵉ.p.T [1/Ω/m/s], in Kelvin.", "σᵉ.p.c [cm⁻³]": "Carrier concentration corresponding to σᵉ.p.T [1/Ω/m/s], in cm^-3.", "σᵉ.n.v [1/Ω/m/s]": "Value of n-type conductivity at maximum average eigenvalue of conductivity chosen among temperatures 100-1300K, doping levels 10^16-10^21cm^-3.", "σᵉ.n.T [K]": "Temperature corresponding to σᵉ.n.T [1/Ω/m/s], in Kelvin.", "σᵉ.n.c [cm⁻³]": "Carrier concentration corresponding to σᵉ.n.T [1/Ω/m/s], in cm^-3.", "PFᵉ.p.v [µW/cm/K²/s]": "Value of p-type power factor at maximum average eigenvalue of power factor chosen among temperatures 100-1300K, doping levels 10^16-10^21cm^-3.", "PFᵉ.p.T [K]": "Temperature corresponding to PFᵉ.p.v [µW/cm/K²/s], in Kelvin.", "PFᵉ.p.c [cm⁻³]": "Carrier concentration corresponding to PFᵉ.p.v [µW/cm/K²/s], in cm^-3.", "PFᵉ.n.v [µW/cm/K²/s]": "Value of n-type power factor at maximum average eigenvalue of power factor chosen among temperatures 100-1300K, doping levels 10^16-10^21cm^-3.", "PFᵉ.n.T [K]": "Temperature corresponding to PFᵉ.n.v [µW/cm/K²/s], in Kelvin.", "PFᵉ.n.c [cm⁻³]": "Carrier concentration corresponding to PFᵉ.n.v [µW/cm/K²/s], in cm^-3.", "κₑ.p [W/K/m/s]": "Average eigenvalue of electrical thermal conductivity with hole concentration of 10^-18 carriers/cm^-3 (p-type) at 300K, in [W/K/m/s].", "κₑ.n [W/K/m/s]": "Average eigenvalue of electrical thermal conductivity with electron concentration of 10^-18 carriers/cm^-3 (n-type) at 300K, in [W/K/m/s].", "κₑᵉ.p.v [W/K/m/s]": "Value of p-type electrical thermal conductivity at maximum average eigenvalue of electrical thermal conductivity chosen among temperatures 100-1300K, doping levels 10^16-10^21cm^-3.", "κₑᵉ.p.T [K]": "Temperature corresponding to κₑᵉ.p.v [W/K/m/s], in Kelvin.", "κₑᵉ.p.c [cm⁻³]": "Carrier concentration corresponding to κₑᵉ.p.v [W/K/m/s], in cm^-3.", "κₑᵉ.n.v [W/K/m/s]": "Value of n-type electrical thermal conductivity at maximum average eigenvalue of electrical thermal conductivity chosen among temperatures 100-1300K, doping levels 10^16-10^21cm^-3.", "κₑᵉ.n.T [K]": "Temperature corresponding to κₑᵉ.n.v [W/K/m/s], in Kelvin.", "κₑᵉ.n.c [cm⁻³]": "Carrier concentration corresponding to κₑᵉ.n.v [W/K/m/s], in cm^-3.", "mₑᶜ.p.ε̄ [mₑ]": "Average (ε̄) eigenvalue of conductivity effective mass with hole concentration of 10^-18 carriers/cm^-3 (p-type) at 300K.", "mₑᶜ.p.ε₁ [mₑ]": "1st eigenvalue of conductivity effective mass with hole concentration of 10^-18 carriers/cm^-3 (p-type) at 300K.", "mₑᶜ.p.ε₂ [mₑ]": "2nd eigenvalue of conductivity effective mass with hole concentration of 10^-18 carriers/cm^-3 (p-type) at 300K.", "mₑᶜ.p.ε₃ [mₑ]": "3rd eigenvalue of conductivity effective mass with hole concentration of 10^-18 carriers/cm^-3 (p-type) at 300K.", "mₑᶜ.n.ε̄ [mₑ]": "Average (ε̄) eigenvalue of conductivity effective mass with electron concentration of 10^-18 carriers/cm^-3 (n-type) at 300K.", "mₑᶜ.n.ε₁ [mₑ]": "1st eigenvalue of conductivity effective mass with electron concentration of 10^-18 carriers/cm^-3 (n-type) at 300K.", "mₑᶜ.n.ε₂ [mₑ]": "2nd eigenvalue of conductivity effective mass with electron concentration of 10^-18 carriers/cm^-3 (n-type) at 300K.", "mₑᶜ.n.ε₃ [mₑ]": "3rd eigenvalue of conductivity effective mass with electron concentration of 10^-18 carriers/cm^-3 (n-type) at 300K.", "structure": "Pymatgen structure, taken from Materials Project April 2021", "pretty_formula": "Formula for composition corresponding to MPID."}, "description": "Ab-initio electronic transport database for inorganic materials. Complex multivariable BoltzTraP simulation data is condensed down into tabular form of two main motifs: average eigenvalues at set moderate carrier concentrations and temperatures, and optimal values among all carrier concentrations and temperatures within certain ranges. Here are reported the average of the eigenvalues of conductivity effective mass (mₑᶜᵒⁿᵈ), the Seebeck coefficient (S), the conductivity (σ), the electronic thermal conductivity (κₑ), and the Power Factor (PF) at a doping level of 10¹⁸ cm⁻³ and at a temperature of 300 K for n- and p-type. Also, the maximum values for S, σ, PF, and the minimum value for κₑ chosen among the temperatures [100, 1300] K, the doping levels [10¹⁶, 10²¹] cm⁻³, and doping types are reported. The properties that depend on the relaxation time are reported divided by the constant value 10⁻¹⁴. The average of the eigenvalues for all the properties at all the temperatures, doping levels, and doping types are reported in the tables for each entry. Data is indexed by materials project id (mpid)", "file_type": "json.gz", "hash": "b45b0806d4ee5e027323bb8021b157c1e91f8b19083342f83d2c84acaee356ef", "num_entries": 47737, "reference": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> et al. An ab initio electronic transport database for inorganic materials. Sci. Data 4:170085 doi: 10.1038/sdata.2017.85 (2017).\n<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> (2017) Data from: An ab initio electronic transport database for inorganic materials. Dryad Digital Repository. https://doi.org/10.5061/dryad.gn001", "url": "https://ndownloader.figshare.com/files/28231785"}, "steel_strength": {"bibtex_refs": ["@misc{Citrine Informatics,\ntitle = {Mechanical properties of some steels},\nhowpublished = {\\url{https://citrination.com/datasets/153092/},\n}"], "columns": {"al": "weight percent of Al", "c": "weight percent of C", "co": "weight percent of Co", "cr": "weight percent of Cr", "elongation": "elongation in %", "formula": "Chemical formula of the entry", "mn": "weight percent of Mn", "mo": "weight percent of Mo", "n": "weight percent of N", "nb": "weight percent of Nb", "ni": "weight percent of Ni", "si": "weight percent of Si", "tensile strength": "ultimate tensile strength in MPa", "ti": "weight percent of Ti", "v": "weight percent of V", "w": "weight percent of W", "yield strength": "yield strength in MPa"}, "description": "312 steels with experimental yield strength and ultimate tensile strength, extracted and cleaned (including de-duplicating) from Citrine.", "file_type": "json.gz", "hash": "e36501d7057cd833223bb8ed9948668b5ac90fd585d29a749f45af51c1d7f6ad", "num_entries": 312, "reference": "https://citrination.com/datasets/153092/", "url": "https://ndownloader.figshare.com/files/13354691"}, "wolverton_oxides": {"bibtex_refs": ["@Article{Emery2017,\nauthor=<PERSON><PERSON>, <PERSON>\nand <PERSON><PERSON><PERSON>, <PERSON>},\ntitle={High-throughput DFT calculations of formation energy, stability and oxygen vacancy formation energy of ABO3 perovskites},\njournal={Scientific Data},\nyear={2017},\nmonth={Oct},\nday={17},\npublisher={The Author(s)},\nvolume={4},\npages={170153},\nnote={Data Descriptor},\nurl={http://dx.doi.org/10.1038/sdata.2017.153}\n}", "@misc{emery_2017, title={High-throughput DFT calculations of formation energy, stability and oxygen vacancy formation energy of ABO3 perovskites}, url={https://figshare.com/articles/High-throughput_DFT_calculations_of_formation_energy_stability_and_oxygen_vacancy_formation_energy_of_ABO3_perovskites/5334142/1}, DOI={10.6084/m9.figshare.5334142.v1}, abstractNote={ABO3 perovskites are oxide materials that are used for a variety of applications such as solid oxide fuel cells, piezo-, ferro-electricity and water splitting. Due to their remarkable stability with respect to cation substitution, new compounds for such applications potentially await discovery. In this work, we present an exhaustive dataset of formation energies of 5,329 cubic and distorted perovskites that were calculated using first-principles density functional theory. In addition to formation energies, several additional properties such as oxidation states, band gap, oxygen vacancy formation energy, and thermodynamic stability with respect to all phases in the Open Quantum Materials Database are also made publicly available. This large dataset for this ubiquitous crystal structure type contains 395 perovskites that are predicted to be thermodynamically stable, of which many have not yet been experimentally reported, and therefore represent theoretical predictions. The dataset thus opens avenues for future use, including materials discovery in many research-active areas.}, publisher={figshare}, author={<PERSON>, <PERSON>}, year={2017}, month={Aug}}"], "columns": {"a": "Lattice parameter a, in A (angstrom)", "alpha": "Lattice angle alpha, in degrees", "atom a": "The atom in the 'A' site of the pervoskite.", "atom b": "The atom in the 'B' site of the perovskite.", "b": "Lattice parameter b, in A (angstrom)", "beta": "Lattice angle beta, in degrees", "c": "Lattice parameter c, in A (angstrom)", "e_form": "Formation energy in eV", "e_form oxygen": "Formation energy of oxygen vacancy (eV)", "e_hull": "Energy above convex hull, wrt. OQMD db (eV)", "formula": "Chemical formula of the entry", "gamma": "Lattice angle gamma, in degrees", "gap pbe": "Bandgap in eV from PBE calculations", "lowest distortion": "Local distortion crystal structure with lowest energy among all considered distortions.", "mu_b": "Magnetic moment", "vpa": "Volume per atom (A^3/atom)"}, "description": "4,914 perovskite oxides containing composition data, lattice constants, and formation + vacancy formation energies. All perovskites are of the form ABO3. Adapted from a dataset presented by <PERSON> and <PERSON>ol<PERSON>.", "file_type": "json.gz", "hash": "3e66c2a5bd23dac335f045210258ca14babf730824bf989b0127640c24bcb78b", "num_entries": 4914, "reference": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON> & <PERSON>, C. High-throughput DFT calculations of formation energy, stability and oxygen vacancy formation energy of ABO3 perovskites. Sci. Data 4:170153 doi: 10.1038/sdata.2017.153 (2017).\nEmery, A. A., & <PERSON>, C. <PERSON> http://dx.doi.org/10.6084/m9.figshare.5334142 (2017)", "url": "https://ndownloader.figshare.com/files/13354616"}, "matbench_mp_is_metal": {"bibtex_refs": ["@Article{Dunn2020,\nauthor={<PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>, Anubha<PERSON>},\ntitle={Benchmarking materials property prediction methods: the Matbench test set and Automatminer reference algorithm},\njournal={npj Computational Materials},\nyear={2020},\nmonth={Sep},\nday={15},\nvolume={6},\nnumber={1},\npages={138},\nabstract={We present a benchmark test suite and an automated machine learning procedure for evaluating supervised machine learning (ML) models for predicting properties of inorganic bulk materials. The test suite, Matbench, is a set of 13{\\thinspace}ML tasks that range in size from 312 to 132k samples and contain data from 10 density functional theory-derived and experimental sources. Tasks include predicting optical, thermal, electronic, thermodynamic, tensile, and elastic properties given a material's composition and/or crystal structure. The reference algorithm, Automatminer, is a highly-extensible, fully automated ML pipeline for predicting materials properties from materials primitives (such as composition and crystal structure) without user intervention or hyperparameter tuning. We test Automatminer on the Matbench test suite and compare its predictive power with state-of-the-art crystal graph neural networks and a traditional descriptor-based Random Forest model. We find <PERSON>mat<PERSON><PERSON> achieves the best performance on 8 of 13 tasks in the benchmark. We also show our test suite is capable of exposing predictive advantages of each algorithm---namely, that crystal graph methods appear to outperform traditional machine learning methods given {\\textasciitilde}104 or greater data points. We encourage evaluating materials ML algorithms on the Matbench benchmark and comparing them against the latest version of Automatminer.},\nissn={2057-3960},\ndoi={10.1038/s41524-020-00406-3},\nurl={https://doi.org/10.1038/s41524-020-00406-3}\n}\n", "@article{Jain2013,\nauthor = {<PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON> a.},\ndoi = {10.1063/1.4812323},\nissn = {2166532X},\njournal = {APL Materials},\nnumber = {1},\npages = {011002},\ntitle = {{The Materials Project: A materials genome approach to accelerating materials innovation}},\nurl = {http://link.aip.org/link/AMPADS/v1/i1/p011002/s1\\&Agg=doi},\nvolume = {1},\nyear = {2013}\n}"], "columns": {"is_metal": "Target variable. 1 if the compound is a metal, 0 if the compound is not a metal. Metallicity determined with pymatgen", "structure": "Pymatgen Structure of the material."}, "description": "Matbench v0.1 test dataset for predicting DFT metallicity from structure. Adapted from Materials Project database. Removed entries having a formation energy (or energy above the convex hull) more than 150meV and those containing noble gases. Retrieved April 2, 2019. For benchmarking w/ nested cross validation, the order of the dataset must be identical to the retrieved data; refer to the Automatminer/Matbench publication for more details.", "file_type": "json.gz", "num_entries": 106113, "reference": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> (*=equal contributions)\nThe Materials Project: A materials genome approach to accelerating materials innovation\nAPL Materials, 2013, 1(1), 011002.\ndoi:10.1063/1.4812323", "url": "https://ml.materialsproject.org/projects/matbench_mp_is_metal.json.gz", "hash": "9a028ed5750a4c76ca36e9f3c8d48fe0bf3fb21b76ec2289e58ae7048d527919"}, "matbench_mp_gap": {"bibtex_refs": ["@Article{Dunn2020,\nauthor={<PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>, Anubha<PERSON>},\ntitle={Benchmarking materials property prediction methods: the Matbench test set and Automatminer reference algorithm},\njournal={npj Computational Materials},\nyear={2020},\nmonth={Sep},\nday={15},\nvolume={6},\nnumber={1},\npages={138},\nabstract={We present a benchmark test suite and an automated machine learning procedure for evaluating supervised machine learning (ML) models for predicting properties of inorganic bulk materials. The test suite, Matbench, is a set of 13{\\thinspace}ML tasks that range in size from 312 to 132k samples and contain data from 10 density functional theory-derived and experimental sources. Tasks include predicting optical, thermal, electronic, thermodynamic, tensile, and elastic properties given a material's composition and/or crystal structure. The reference algorithm, Automatminer, is a highly-extensible, fully automated ML pipeline for predicting materials properties from materials primitives (such as composition and crystal structure) without user intervention or hyperparameter tuning. We test Automatminer on the Matbench test suite and compare its predictive power with state-of-the-art crystal graph neural networks and a traditional descriptor-based Random Forest model. We find <PERSON>mat<PERSON><PERSON> achieves the best performance on 8 of 13 tasks in the benchmark. We also show our test suite is capable of exposing predictive advantages of each algorithm---namely, that crystal graph methods appear to outperform traditional machine learning methods given {\\textasciitilde}104 or greater data points. We encourage evaluating materials ML algorithms on the Matbench benchmark and comparing them against the latest version of Automatminer.},\nissn={2057-3960},\ndoi={10.1038/s41524-020-00406-3},\nurl={https://doi.org/10.1038/s41524-020-00406-3}\n}\n", "@article{Jain2013,\nauthor = {<PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON> a.},\ndoi = {10.1063/1.4812323},\nissn = {2166532X},\njournal = {APL Materials},\nnumber = {1},\npages = {011002},\ntitle = {{The Materials Project: A materials genome approach to accelerating materials innovation}},\nurl = {http://link.aip.org/link/AMPADS/v1/i1/p011002/s1\\&Agg=doi},\nvolume = {1},\nyear = {2013}\n}"], "columns": {"gap pbe": "Target variable. The band gap as calculated by PBE DFT from the Materials Project, in eV.", "structure": "Pymatgen Structure of the material."}, "description": "Matbench v0.1 test dataset for predicting DFT PBE band gap from structure. Adapted from Materials Project database. Removed entries having a formation energy (or energy above the convex hull) more than 150meV and those containing noble gases. Retrieved April 2, 2019. For benchmarking w/ nested cross validation, the order of the dataset must be identical to the retrieved data; refer to the Automatminer/Matbench publication for more details.", "file_type": "json.gz", "num_entries": 106113, "reference": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> (*=equal contributions)\nThe Materials Project: A materials genome approach to accelerating materials innovation\nAPL Materials, 2013, 1(1), 011002.\ndoi:10.1063/1.4812323", "url": "https://ml.materialsproject.org/projects/matbench_mp_gap.json.gz", "hash": "58b65746bd88329986ed66031a2ac1369c7c522f7bc9f9081528e07097c2c057"}, "matbench_mp_e_form": {"bibtex_refs": ["@Article{Dunn2020,\nauthor={<PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>, Anubha<PERSON>},\ntitle={Benchmarking materials property prediction methods: the Matbench test set and Automatminer reference algorithm},\njournal={npj Computational Materials},\nyear={2020},\nmonth={Sep},\nday={15},\nvolume={6},\nnumber={1},\npages={138},\nabstract={We present a benchmark test suite and an automated machine learning procedure for evaluating supervised machine learning (ML) models for predicting properties of inorganic bulk materials. The test suite, Matbench, is a set of 13{\\thinspace}ML tasks that range in size from 312 to 132k samples and contain data from 10 density functional theory-derived and experimental sources. Tasks include predicting optical, thermal, electronic, thermodynamic, tensile, and elastic properties given a material's composition and/or crystal structure. The reference algorithm, Automatminer, is a highly-extensible, fully automated ML pipeline for predicting materials properties from materials primitives (such as composition and crystal structure) without user intervention or hyperparameter tuning. We test Automatminer on the Matbench test suite and compare its predictive power with state-of-the-art crystal graph neural networks and a traditional descriptor-based Random Forest model. We find <PERSON>mat<PERSON><PERSON> achieves the best performance on 8 of 13 tasks in the benchmark. We also show our test suite is capable of exposing predictive advantages of each algorithm---namely, that crystal graph methods appear to outperform traditional machine learning methods given {\\textasciitilde}104 or greater data points. We encourage evaluating materials ML algorithms on the Matbench benchmark and comparing them against the latest version of Automatminer.},\nissn={2057-3960},\ndoi={10.1038/s41524-020-00406-3},\nurl={https://doi.org/10.1038/s41524-020-00406-3}\n}\n", "@article{Jain2013,\nauthor = {<PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON> a.},\ndoi = {10.1063/1.4812323},\nissn = {2166532X},\njournal = {APL Materials},\nnumber = {1},\npages = {011002},\ntitle = {{The Materials Project: A materials genome approach to accelerating materials innovation}},\nurl = {http://link.aip.org/link/AMPADS/v1/i1/p011002/s1\\&Agg=doi},\nvolume = {1},\nyear = {2013}\n}"], "columns": {"e_form": "Target variable. Formation energy in eV as calculated by the Materials Project.", "structure": "Pymatgen Structure of the material."}, "description": "Matbench v0.1 test dataset for predicting DFT formation energy from structure. Adapted from Materials Project database. Removed entries having formation energy more than 2.5eV and those containing noble gases. Retrieved April 2, 2019. For benchmarking w/ nested cross validation, the order of the dataset must be identical to the retrieved data; refer to the Automatminer/Matbench publication for more details.", "file_type": "json.gz", "num_entries": 132752, "reference": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> (*=equal contributions)\nThe Materials Project: A materials genome approach to accelerating materials innovation\nAPL Materials, 2013, 1(1), 011002.\ndoi:10.1063/1.4812323", "url": "https://ml.materialsproject.org/projects/matbench_mp_e_form.json.gz", "hash": "dedcb1d4ba2e3e50dbdd45ba5bc647a00e9c2bcf8f8bf556dc8e92caa39eb21f"}, "matbench_log_gvrh": {"bibtex_refs": ["@Article{Dunn2020,\nauthor={<PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>, Anubha<PERSON>},\ntitle={Benchmarking materials property prediction methods: the Matbench test set and Automatminer reference algorithm},\njournal={npj Computational Materials},\nyear={2020},\nmonth={Sep},\nday={15},\nvolume={6},\nnumber={1},\npages={138},\nabstract={We present a benchmark test suite and an automated machine learning procedure for evaluating supervised machine learning (ML) models for predicting properties of inorganic bulk materials. The test suite, Matbench, is a set of 13{\\thinspace}ML tasks that range in size from 312 to 132k samples and contain data from 10 density functional theory-derived and experimental sources. Tasks include predicting optical, thermal, electronic, thermodynamic, tensile, and elastic properties given a material's composition and/or crystal structure. The reference algorithm, Automatminer, is a highly-extensible, fully automated ML pipeline for predicting materials properties from materials primitives (such as composition and crystal structure) without user intervention or hyperparameter tuning. We test Automatminer on the Matbench test suite and compare its predictive power with state-of-the-art crystal graph neural networks and a traditional descriptor-based Random Forest model. We find <PERSON>mat<PERSON><PERSON> achieves the best performance on 8 of 13 tasks in the benchmark. We also show our test suite is capable of exposing predictive advantages of each algorithm---namely, that crystal graph methods appear to outperform traditional machine learning methods given {\\textasciitilde}104 or greater data points. We encourage evaluating materials ML algorithms on the Matbench benchmark and comparing them against the latest version of Automatminer.},\nissn={2057-3960},\ndoi={10.1038/s41524-020-00406-3},\nurl={https://doi.org/10.1038/s41524-020-00406-3}\n}\n", "@Article{deJong2015,\nauthor={<PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>\nand <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON>\nand <PERSON>, <PERSON> and <PERSON>, Chaitanya\nand <PERSON> der <PERSON>, Sybrand and Plata, <PERSON> and <PERSON>, Cormac\nand Curtarolo, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON>\nand <PERSON><PERSON>, <PERSON>},\ntitle={Charting the complete elastic properties\nof inorganic crystalline compounds},\njournal={Scientific Data},\nyear={2015},\nmonth={Mar},\nday={17},\npublisher={The Author(s)},\nvolume={2},\npages={150009},\nnote={Data Descriptor},\nurl={http://dx.doi.org/10.1038/sdata.2015.9}\n}"], "columns": {"log10(G_VRH)": "Target variable. Base 10 logarithm of the DFT Voigt-Reuss-Hill average shear moduli in GPa", "structure": "Pymatgen Structure of the material."}, "description": "Matbench v0.1 test dataset for predicting DFT log10 VRH-average shear modulus from structure. Adapted from Materials Project database. Removed entries having a formation energy (or energy above the convex hull) more than 150meV and those having negative G_Voigt, <PERSON>_<PERSON><PERSON>, <PERSON>_VR<PERSON>, K_Voigt, <PERSON><PERSON><PERSON><PERSON>, or K_VRH and those failing <PERSON><PERSON><PERSON><PERSON> <= G_VRH <= G_Voigt or <PERSON>_<PERSON><PERSON> <= K_VRH <= K_Voigt and those containing noble gases. Retrieved April 2, 2019. For benchmarking w/ nested cross validation, the order of the dataset must be identical to the retrieved data; refer to the Automatminer/Matbench publication for more details.", "file_type": "json.gz", "num_entries": 10987, "reference": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>,\n<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>. <PERSON>, <PERSON>, <PERSON><PERSON>, Plata, J. <PERSON>, <PERSON>,\n<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON>, <PERSON>, \"Charting\nthe complete elastic properties of inorganic crystalline compounds\",\nScientific Data volume 2, Article number: 150009 (2015)", "url": "https://ml.materialsproject.org/projects/matbench_log_gvrh.json.gz", "hash": "098af941f4c663270f1fe21abf20ffad6fb85ecbfcba5786ceac03983ac29da7"}, "matbench_log_kvrh": {"bibtex_refs": ["@Article{Dunn2020,\nauthor={<PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>, Anubha<PERSON>},\ntitle={Benchmarking materials property prediction methods: the Matbench test set and Automatminer reference algorithm},\njournal={npj Computational Materials},\nyear={2020},\nmonth={Sep},\nday={15},\nvolume={6},\nnumber={1},\npages={138},\nabstract={We present a benchmark test suite and an automated machine learning procedure for evaluating supervised machine learning (ML) models for predicting properties of inorganic bulk materials. The test suite, Matbench, is a set of 13{\\thinspace}ML tasks that range in size from 312 to 132k samples and contain data from 10 density functional theory-derived and experimental sources. Tasks include predicting optical, thermal, electronic, thermodynamic, tensile, and elastic properties given a material's composition and/or crystal structure. The reference algorithm, Automatminer, is a highly-extensible, fully automated ML pipeline for predicting materials properties from materials primitives (such as composition and crystal structure) without user intervention or hyperparameter tuning. We test Automatminer on the Matbench test suite and compare its predictive power with state-of-the-art crystal graph neural networks and a traditional descriptor-based Random Forest model. We find <PERSON>mat<PERSON><PERSON> achieves the best performance on 8 of 13 tasks in the benchmark. We also show our test suite is capable of exposing predictive advantages of each algorithm---namely, that crystal graph methods appear to outperform traditional machine learning methods given {\\textasciitilde}104 or greater data points. We encourage evaluating materials ML algorithms on the Matbench benchmark and comparing them against the latest version of Automatminer.},\nissn={2057-3960},\ndoi={10.1038/s41524-020-00406-3},\nurl={https://doi.org/10.1038/s41524-020-00406-3}\n}\n", "@Article{deJong2015,\nauthor={<PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>\nand <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON>\nand <PERSON>, <PERSON> and <PERSON>, Chaitanya\nand <PERSON> der <PERSON>, Sybrand and Plata, <PERSON> and <PERSON>, Cormac\nand Curtarolo, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON>\nand <PERSON><PERSON>, <PERSON>},\ntitle={Charting the complete elastic properties\nof inorganic crystalline compounds},\njournal={Scientific Data},\nyear={2015},\nmonth={Mar},\nday={17},\npublisher={The Author(s)},\nvolume={2},\npages={150009},\nnote={Data Descriptor},\nurl={http://dx.doi.org/10.1038/sdata.2015.9}\n}"], "columns": {"log10(K_VRH)": "Target variable. Base 10 logarithm of the DFT Voigt-Reuss-Hill average bulk moduli in GPa.", "structure": "Pymatgen Structure of the material."}, "description": "Matbench v0.1 test dataset for predicting DFT log10 VRH-average bulk modulus from structure. Adapted from Materials Project database. Removed entries having a formation energy (or energy above the convex hull) more than 150meV and those having negative G_Voigt, <PERSON>_<PERSON><PERSON>, <PERSON>_VR<PERSON>, K_Voigt, <PERSON><PERSON><PERSON><PERSON>, or K_VRH and those failing <PERSON><PERSON><PERSON><PERSON> <= G_VRH <= G_Voigt or <PERSON>_<PERSON><PERSON> <= K_VRH <= K_Voigt and those containing noble gases. Retrieved April 2, 2019. For benchmarking w/ nested cross validation, the order of the dataset must be identical to the retrieved data; refer to the Automatminer/Matbench publication for more details.", "file_type": "json.gz", "num_entries": 10987, "reference": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>,\n<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>. <PERSON>, <PERSON>, <PERSON><PERSON>, Plata, J. <PERSON>, <PERSON>,\n<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON>, <PERSON>, \"Charting\nthe complete elastic properties of inorganic crystalline compounds\",\nScientific Data volume 2, Article number: 150009 (2015)", "url": "https://ml.materialsproject.org/projects/matbench_log_kvrh.json.gz", "hash": "44b113ddb7e23aa18731a62c74afa7e5aa654199e0db5f951c8248a00955c9cd"}, "matbench_dielectric": {"bibtex_refs": ["@Article{Dunn2020,\nauthor={<PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>, Anubha<PERSON>},\ntitle={Benchmarking materials property prediction methods: the Matbench test set and Automatminer reference algorithm},\njournal={npj Computational Materials},\nyear={2020},\nmonth={Sep},\nday={15},\nvolume={6},\nnumber={1},\npages={138},\nabstract={We present a benchmark test suite and an automated machine learning procedure for evaluating supervised machine learning (ML) models for predicting properties of inorganic bulk materials. The test suite, Matbench, is a set of 13{\\thinspace}ML tasks that range in size from 312 to 132k samples and contain data from 10 density functional theory-derived and experimental sources. Tasks include predicting optical, thermal, electronic, thermodynamic, tensile, and elastic properties given a material's composition and/or crystal structure. The reference algorithm, Automatminer, is a highly-extensible, fully automated ML pipeline for predicting materials properties from materials primitives (such as composition and crystal structure) without user intervention or hyperparameter tuning. We test Automatminer on the Matbench test suite and compare its predictive power with state-of-the-art crystal graph neural networks and a traditional descriptor-based Random Forest model. We find <PERSON>mat<PERSON><PERSON> achieves the best performance on 8 of 13 tasks in the benchmark. We also show our test suite is capable of exposing predictive advantages of each algorithm---namely, that crystal graph methods appear to outperform traditional machine learning methods given {\\textasciitilde}104 or greater data points. We encourage evaluating materials ML algorithms on the Matbench benchmark and comparing them against the latest version of Automatminer.},\nissn={2057-3960},\ndoi={10.1038/s41524-020-00406-3},\nurl={https://doi.org/10.1038/s41524-020-00406-3}\n}\n", "@article{Jain2013,\nauthor = {<PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON> a.},\ndoi = {10.1063/1.4812323},\nissn = {2166532X},\njournal = {APL Materials},\nnumber = {1},\npages = {011002},\ntitle = {{The Materials Project: A materials genome approach to accelerating materials innovation}},\nurl = {http://link.aip.org/link/AMPADS/v1/i1/p011002/s1\\&Agg=doi},\nvolume = {1},\nyear = {2013}\n}", "@article{Petousis2017,\nauthor={<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>\nand <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON>\nand <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON>},\ntitle={High-throughput screening of inorganic compounds for the\ndiscovery of novel dielectric and optical materials},\njournal={Scientific Data},\nyear={2017},\nmonth={Jan},\nday={31},\npublisher={The Author(s)},\nvolume={4},\npages={160134},\nnote={Data Descriptor},\nurl={http://dx.doi.org/10.1038/sdata.2016.134}\n}"], "columns": {"n": "Target variable. Refractive index (unitless).", "structure": "Pymatgen Structure of the material."}, "description": "Matbench v0.1 test dataset for predicting refractive index from structure. Adapted from Materials Project database. Removed entries having a formation energy (or energy above the convex hull) more than 150meV and those having refractive indices less than 1 and those containing noble gases. Retrieved April 2, 2019. For benchmarking w/ nested cross validation, the order of the dataset must be identical to the retrieved data; refer to the Automatminer/Matbench publication for more details.", "file_type": "json.gz", "num_entries": 4764, "url": "https://ml.materialsproject.org/projects/matbench_dielectric.json.gz", "hash": "83befa09bc2ec2f4b6143afc413157827a90e5e2e42c1eb507ccfa01bf26a1d6", "reference": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>,\n<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, T<PERSON>, <PERSON>, K. A. & Prinz, F. B.\nHigh-throughput screening of inorganic compounds for the discovery\nof novel dielectric and optical materials. Sci. Data 4, 160134 (2017)."}, "matbench_jdft2d": {"bibtex_refs": ["@Article{Dunn2020,\nauthor={<PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>, Anubha<PERSON>},\ntitle={Benchmarking materials property prediction methods: the Matbench test set and Automatminer reference algorithm},\njournal={npj Computational Materials},\nyear={2020},\nmonth={Sep},\nday={15},\nvolume={6},\nnumber={1},\npages={138},\nabstract={We present a benchmark test suite and an automated machine learning procedure for evaluating supervised machine learning (ML) models for predicting properties of inorganic bulk materials. The test suite, Matbench, is a set of 13{\\thinspace}ML tasks that range in size from 312 to 132k samples and contain data from 10 density functional theory-derived and experimental sources. Tasks include predicting optical, thermal, electronic, thermodynamic, tensile, and elastic properties given a material's composition and/or crystal structure. The reference algorithm, Automatminer, is a highly-extensible, fully automated ML pipeline for predicting materials properties from materials primitives (such as composition and crystal structure) without user intervention or hyperparameter tuning. We test Automatminer on the Matbench test suite and compare its predictive power with state-of-the-art crystal graph neural networks and a traditional descriptor-based Random Forest model. We find <PERSON>mat<PERSON><PERSON> achieves the best performance on 8 of 13 tasks in the benchmark. We also show our test suite is capable of exposing predictive advantages of each algorithm---namely, that crystal graph methods appear to outperform traditional machine learning methods given {\\textasciitilde}104 or greater data points. We encourage evaluating materials ML algorithms on the Matbench benchmark and comparing them against the latest version of Automatminer.},\nissn={2057-3960},\ndoi={10.1038/s41524-020-00406-3},\nurl={https://doi.org/10.1038/s41524-020-00406-3}\n}\n", "@Article{Cho<PERSON><PERSON>y2017,\nauthor={<PERSON><PERSON><PERSON><PERSON>, <PERSON>\nand <PERSON><PERSON>, <PERSON><PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>},\ntitle={High-throughput Identification and Characterization of Two-dimensional Materials using Density functional theory},\njournal={Scientific Reports},\nyear={2017},\nvolume={7},\nnumber={1},\npages={5179},\nabstract={We introduce a simple criterion to identify two-dimensional (2D) materials based on the comparison between experimental lattice constants and lattice constants mainly obtained from Materials-Project (MP) density functional theory (DFT) calculation repository. Specifically, if the relative difference between the two lattice constants for a specific material is greater than or equal to 5%, we predict them to be good candidates for 2D materials. We have predicted at least 1356 such 2D materials. For all the systems satisfying our criterion, we manually create single layer systems and calculate their energetics, structural, electronic, and elastic properties for both the bulk and the single layer cases. Currently the database consists of 1012 bulk and 430 single layer materials, of which 371 systems are common to bulk and single layer. The rest of calculations are underway. To validate our criterion, we calculated the exfoliation energy of the suggested layered materials, and we found that in 88.9% of the cases the currently accepted criterion for exfoliation was satisfied. Also, using molybdenum telluride as a test case, we performed X-ray diffraction and Raman scattering experiments to benchmark our calculations and understand their applicability and limitations. The data is publicly available at the website http://www.ctcms.nist.gov/{\textasciitilde}knc6/JVASP.html.},\nissn={2045-2322},\ndoi={10.1038/s41598-017-05402-0},\nurl={https://doi.org/10.1038/s41598-017-05402-0}\n}", "@misc{choudhary__2018, title={jdft_2d-7-7-2018.json}, url={https://figshare.com/articles/jdft_2d-7-7-2018_json/6815705/1}, DOI={10.6084/m9.figshare.6815705.v1}, abstractNote={2D materials}, publisher={figshare}, author={choudhary, kamal and https://orcid.org/0000-0001-9737-8074}, year={2018}, month={Jul}}"], "columns": {"exfoliation_en": "Target variable. Exfoliation energy (meV/atom).", "structure": "Pymatgen Structure of the material."}, "description": "Matbench v0.1 test dataset for predicting exfoliation energies from crystal structure (computed with the OptB88vdW and TBmBJ functionals). Adapted from the JARVIS DFT database. For benchmarking w/ nested cross validation, the order of the dataset must be identical to the retrieved data; refer to the Automatminer/Matbench publication for more details.", "file_type": "json.gz", "num_entries": 636, "reference": "2D Dataset discussed in:\nHigh-throughput Identification and Characterization of Two dimensional Materials using Density functional theory <PERSON>, <PERSON><PERSON>, <PERSON> & <PERSON> Scientific Reports volume 7, Article number: 5179 (2017)\nOriginal 2D Data file sourced from:\nch<PERSON><PERSON><PERSON>, kamal; https://orcid.org/0000-0001-9737-8074 (2018): jdft_2d-7-7-2018.json. figshare. Dataset.", "url": "https://ml.materialsproject.org/projects/matbench_jdft2d.json.gz", "hash": "26057dc4524e193e32abffb296ce819b58b6e11d1278cae329a2f97817a4eddf"}, "matbench_perovskites": {"bibtex_refs": ["@Article{Dunn2020,\nauthor={<PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>, Anubha<PERSON>},\ntitle={Benchmarking materials property prediction methods: the Matbench test set and Automatminer reference algorithm},\njournal={npj Computational Materials},\nyear={2020},\nmonth={Sep},\nday={15},\nvolume={6},\nnumber={1},\npages={138},\nabstract={We present a benchmark test suite and an automated machine learning procedure for evaluating supervised machine learning (ML) models for predicting properties of inorganic bulk materials. The test suite, Matbench, is a set of 13{\\thinspace}ML tasks that range in size from 312 to 132k samples and contain data from 10 density functional theory-derived and experimental sources. Tasks include predicting optical, thermal, electronic, thermodynamic, tensile, and elastic properties given a material's composition and/or crystal structure. The reference algorithm, Automatminer, is a highly-extensible, fully automated ML pipeline for predicting materials properties from materials primitives (such as composition and crystal structure) without user intervention or hyperparameter tuning. We test Automatminer on the Matbench test suite and compare its predictive power with state-of-the-art crystal graph neural networks and a traditional descriptor-based Random Forest model. We find <PERSON>mat<PERSON><PERSON> achieves the best performance on 8 of 13 tasks in the benchmark. We also show our test suite is capable of exposing predictive advantages of each algorithm---namely, that crystal graph methods appear to outperform traditional machine learning methods given {\\textasciitilde}104 or greater data points. We encourage evaluating materials ML algorithms on the Matbench benchmark and comparing them against the latest version of Automatminer.},\nissn={2057-3960},\ndoi={10.1038/s41524-020-00406-3},\nurl={https://doi.org/10.1038/s41524-020-00406-3}\n}\n", "@Article{C2EE22341D,\nauthor =\"<PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON>\",\ntitle  =\"New cubic perovskites for one- and two-photon water splitting using the computational materials repository\",\njournal  =\"Energy Environ. Sci.\",\nyear  =\"2012\",\nvolume  =\"5\",\nissue  =\"10\",\npages  =\"9034-9043\",\npublisher  =\"The Royal Society of Chemistry\",\ndoi  =\"10.1039/C2EE22341D\",\nurl  =\"http://dx.doi.org/10.1039/C2EE22341D\",\nabstract  =\"A new efficient photoelectrochemical cell (PEC) is one of the possible solutions to the energy and climate problems of our time. Such a device requires development of new semiconducting materials with tailored properties with respect to stability and light absorption. Here we perform computational screening of around 19 000 oxides{,} oxynitrides{,} oxysulfides{,} oxyfluorides{,} and oxyfluoronitrides in the cubic perovskite structure with PEC applications in mind. We address three main applications: light absorbers for one- and two-photon water splitting and high-stability transparent shields to protect against corrosion. We end up with 20{,} 12{,} and 15 different combinations of oxides{,} oxynitrides and oxyfluorides{,} respectively{,} inviting further experimental investigation.\"}"], "columns": {"e_form": "Target variable. Heat of formation of the entire 5-atom perovskite cell, in eV as calculated by RPBE GGA-DFT. Note the reference state for oxygen was computed from oxygen's chemical potential in water vapor, not as oxygen molecules, to reflect the application which these perovskites were studied for.", "structure": "Pymatgen Structure of the material."}, "description": "Matbench v0.1 test dataset for predicting formation energy from crystal structure. Adapted from an original dataset generated by <PERSON> et al. For benchmarking w/ nested cross validation, the order of the dataset must be identical to the retrieved data; refer to the Automatminer/Matbench publication for more details.", "file_type": "json.gz", "num_entries": 18928, "reference": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> and <PERSON><PERSON> (2012) New cubic perovskites for one- and two-photon water splitting using the computational materials repository. Energy Environ. Sci., 2012,5, 9034-9043 https://doi.org/10.1039/C2EE22341D", "url": "https://ml.materialsproject.org/projects/matbench_perovskites.json.gz", "hash": "4641e2417f8ec8b50096d2230864468dfa08278dc9d257c327f65d0305278483"}, "matbench_glass": {"bibtex_refs": ["@Article{Dunn2020,\nauthor={<PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>, Anubha<PERSON>},\ntitle={Benchmarking materials property prediction methods: the Matbench test set and Automatminer reference algorithm},\njournal={npj Computational Materials},\nyear={2020},\nmonth={Sep},\nday={15},\nvolume={6},\nnumber={1},\npages={138},\nabstract={We present a benchmark test suite and an automated machine learning procedure for evaluating supervised machine learning (ML) models for predicting properties of inorganic bulk materials. The test suite, Matbench, is a set of 13{\\thinspace}ML tasks that range in size from 312 to 132k samples and contain data from 10 density functional theory-derived and experimental sources. Tasks include predicting optical, thermal, electronic, thermodynamic, tensile, and elastic properties given a material's composition and/or crystal structure. The reference algorithm, Automatminer, is a highly-extensible, fully automated ML pipeline for predicting materials properties from materials primitives (such as composition and crystal structure) without user intervention or hyperparameter tuning. We test Automatminer on the Matbench test suite and compare its predictive power with state-of-the-art crystal graph neural networks and a traditional descriptor-based Random Forest model. We find <PERSON>mat<PERSON><PERSON> achieves the best performance on 8 of 13 tasks in the benchmark. We also show our test suite is capable of exposing predictive advantages of each algorithm---namely, that crystal graph methods appear to outperform traditional machine learning methods given {\\textasciitilde}104 or greater data points. We encourage evaluating materials ML algorithms on the Matbench benchmark and comparing them against the latest version of Automatminer.},\nissn={2057-3960},\ndoi={10.1038/s41524-020-00406-3},\nurl={https://doi.org/10.1038/s41524-020-00406-3}\n}\n", "@Misc{LandoltBornstein1997:sm_lbs_978-3-540-47679-5_2,\nauthor=\"<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>\nand <PERSON>, <PERSON><PERSON>\nand <PERSON><PERSON>, A<PERSON><PERSON><PERSON><PERSON>\nand <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>\nand <PERSON><PERSON>, T.\",\neditor=\"<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>\nand <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>\nand <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>\nand <PERSON><PERSON><PERSON>, T.\",\ntitle=\"Nonequilibrium Phase Diagrams of Ternary Amorphous Alloys {\\textperiodcentered} 1 Introduction: Datasheet from Landolt-B{\\\"o}rnstein - Group III Condensed Matter {\\textperiodcentered} Volume 37A: ``Nonequilibrium Phase Diagrams of Ternary Amorphous Alloys'' in SpringerMaterials (https://dx.doi.org/10.1007/10510374{\\_}2)\",\npublisher=\"Springer-Verlag Berlin Heidelberg\",\nnote=\"Copyright 1997 Springer-Verlag Berlin Heidelberg\",\nnote=\"Part of SpringerMaterials\",\nnote=\"accessed 2018-10-23\",\ndoi=\"10.1007/10510374_2\",\nurl=\"https://materials.springer.com/lb/docs/sm_lbs_978-3-540-47679-5_2\"\n}", "@Article{Ward2016,\nauthor={<PERSON>, <PERSON>\nand <PERSON>, Anki<PERSON>\nand Choudhary, Alok\nand Wolverton, Christopher},\ntitle={A general-purpose machine learning framework for predicting properties of inorganic materials},\njournal={Npj Computational Materials},\nyear={2016},\nmonth={Aug},\nday={26},\npublisher={The Author(s)},\nvolume={2},\npages={16028},\nnote={Article},\nurl={http://dx.doi.org/10.1038/npjcompumats.2016.28}\n}"], "columns": {"composition": "Chemical formula.", "gfa": "Target variable. Glass forming ability: 1 means glass forming and corresponds to amorphous, 0 means non full glass forming."}, "description": "Matbench v0.1 test dataset for predicting full bulk metallic glass formation ability from chemical formula. Retrieved from \"Nonequilibrium Phase Diagrams of Ternary Amorphous Alloys,’ a volume of the Landolt– Börnstein collection. Deduplicated according to composition, ensuring no compositions were reported as both GFA and not GFA (i.e., all reports agreed on the classification designation). For benchmarking w/ nested cross validation, the order of the dataset must be identical to the retrieved data; refer to the Automatminer/Matbench publication for more details.", "file_type": "json.gz", "num_entries": 5680, "reference": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> (1997) <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> (ed.) SpringerMaterials\nNonequilibrium Phase Diagrams of Ternary Amorphous Alloys · 1 Introduction Landolt-Börnstein - Group III Condensed Matter 37A (Nonequilibrium Phase Diagrams of Ternary Amorphous Alloys) https://www.springer.com/gp/book/9783540605072 (Springer-Verlag Berlin Heidelberg © 1997) Accessed: 03-09-2019", "url": "https://ml.materialsproject.org/projects/matbench_glass.json.gz", "hash": "36beb654e2a463ee2a6572105bea0ca2961eee7c7b26a25377bff2c3b338e53a"}, "expt_formation_enthalpy_kingsbury": {"bibtex_refs": ["@article{Kim2017,doi={10.1038/sdata.2017.162},url={https://doi.org/10.1038/sdata.2017.162},year={2017},month=oct,publisher={Springer Science and Business Media {LLC}}, volume = {4},  number = {1},  author = {<PERSON> and <PERSON><PERSON> <PERSON><PERSON> and <PERSON> and <PERSON>},title ={Experimental formation enthalpies for intermetallic phases and other inorganic compounds},journal={Scientific Data}}", "@misc{kim_meschel_nash_chen_2017, title={Experimental formation enthalpies for intermetallic phases and other inorganic compounds}, url={https://springernature.figshare.com/collections/Experimental_formation_enthalpies_for_intermetallic_phases_and_other_inorganic_compounds/3822835/1}, DOI={10.6084/m9.figshare.c.3822835.v1}, publisher={figshare},author={<PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>}, year={2017}, month={Oct} }", "@article{Kim2017, doi = {10.1038/sdata.2017.162}, url = {https://doi.org/10.1038/sdata.2017.162}, year = {2017}, month = oct, publisher = {Springer Science and Business Media LLC}}, volume = {4}, number = {1},author = {<PERSON> and <PERSON><PERSON> <PERSON><PERSON> and <PERSON> and <PERSON>},title = {Experimental formation enthalpies for intermetallic phases and other inorganic compounds},journal = {Scientific Data}}", "@book{<PERSON><PERSON><PERSON><PERSON>1993,author={<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, C<PERSON> and <PERSON>, P.J.},edition={6th},isbn={0080418880},publisher={Pergamon Press},title={{Materials Thermochemistry}},year = {1993}}", "@misc{NIST,doi = {10.18434/T42S31},url = {http://kinetics.nist.gov/janaf/},author = {<PERSON>}, title = {NIST-JANAF Thermochemical Tables}, publisher = {National Institute of Standards and Technology},  year = {1998},  url={https://janaf.nist.org}}", "@article{RZYMAN2000309,title = {Enthalpies of formation of AlFe: Experiment versus theory},journal = {Calphad},volume = {24},number = {3},pages = {309-318},year = {2000},      issn = {0364-5916},doi = {https://doi.org/10.1016/S0364-5916(01)00007-4}, url = {https://www.sciencedirect.com/science/article/pii/S0364591601000074}, author = {<PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>}}", "@book{CRC2007,asin = {0849304881},author = {{CRC Handbook}},dewey = {530},ean = {9780849304880},edition = 88,interhash = {da6394e1a9c5f450ed705c32ec82bb08},intrahash = {5ff8f541915536461697300e8727f265},isbn = {0849304881},keywords = {crc_handbook},publisher = {CRC Press},title = {CRC Handbook of Chemistry and Physics, 88th Edition},        year = 2007}", "@article{Grindy2013,author = {<PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, C.},doi = {10.1103/PhysRevB.87.075150},issn = {10980121},journal = {Physical Review B - Condensed Matter and Materials Physics},number = {7},pages = {1--8},title = {{Approaching chemical accuracy with density functional calculations: Diatomic energy corrections}},volume = {87},year = {2013}}"], "columns": {"formula": "Chemical formula.", "expt_form_e": "Experimental standard formation enthalpy (298 K), in eV/atom.", "uncertainty": "Uncertainty reported in the experimental formation energy, in eV/atom.", "phaseinfo": "Description of the material's crystal structure or space group.", "reference": "Reference to the original data source.", "likely_mpid": "Materials Project database ID (mp-id) most likely associated with each material."}, "description": "Dataset containing experimental standard formation enthalpies for solids. Formation enthalpies were compiled primarily from <PERSON> et al., <PERSON><PERSON><PERSON><PERSON>, and the NIST JANAF tables (see references). Elements, liquids, and gases were excluded. Data were deduplicated such that each material is associated with a single formation enthalpy value. Refer to <PERSON> et al. (see references) for a complete desciption of the methods used. Materials Project database IDs (mp-ids) were assigned to materials from among computed materials in the Materials Project database (version 2021.03.22) that were 1) not marked 'theoretical', 2) had structures matching at least one ICSD material, and 3) were within 200 meV of the DFT-computed stable energy hull (e_above_hull < 0.2 eV). Among these candidates, we chose the mp-id with the lowest e_above_hull that matched the reported spacegroup (where available).", "file_type": "json.gz", "num_entries": 2135, "reference": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, S.P., <PERSON>, S., <PERSON>, K. A framework for quantifying uncertainty in DFT energy corrections. ChemRxiv. Preprint. https://doi.org/10.26434/chemrxiv.14593476.v1", "url": "https://ndownloader.figshare.com/files/28232538", "hash": "a2d2ced98d40349abd2041f169d9ed9c7f49453e86a77f82cab8c61c70dcb7ca"}, "expt_gap_kingsbury": {"bibtex_refs": ["@Article{Dunn2020,\nauthor={<PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>, Anubha<PERSON>},\ntitle={Benchmarking materials property prediction methods: the Matbench test set and Automatminer reference algorithm},\njournal={npj Computational Materials},\nyear={2020},\nmonth={Sep},\nday={15},\nvolume={6},\nnumber={1},\npages={138},\nabstract={We present a benchmark test suite and an automated machine learning procedure for evaluating supervised machine learning (ML) models for predicting properties of inorganic bulk materials. The test suite, Matbench, is a set of 13{\\thinspace}ML tasks that range in size from 312 to 132k samples and contain data from 10 density functional theory-derived and experimental sources. Tasks include predicting optical, thermal, electronic, thermodynamic, tensile, and elastic properties given a material's composition and/or crystal structure. The reference algorithm, Automatminer, is a highly-extensible, fully automated ML pipeline for predicting materials properties from materials primitives (such as composition and crystal structure) without user intervention or hyperparameter tuning. We test Automatminer on the Matbench test suite and compare its predictive power with state-of-the-art crystal graph neural networks and a traditional descriptor-based Random Forest model. We find <PERSON>mat<PERSON><PERSON> achieves the best performance on 8 of 13 tasks in the benchmark. We also show our test suite is capable of exposing predictive advantages of each algorithm---namely, that crystal graph methods appear to outperform traditional machine learning methods given {\\textasciitilde}104 or greater data points. We encourage evaluating materials ML algorithms on the Matbench benchmark and comparing them against the latest version of Automatminer.},\nissn={2057-3960},\ndoi={10.1038/s41524-020-00406-3},\nurl={https://doi.org/10.1038/s41524-020-00406-3}\n}\n", "@article{doi:10.1021/acs.jpclett.8b00124,\nauthor = {<PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, Jak<PERSON><PERSON>},\ntitle = {Predicting the Band Gaps of Inorganic Solids by Machine Learning},\njournal = {The Journal of Physical Chemistry Letters},\nvolume = {9},\nnumber = {7},\npages = {1668-1673},\nyear = {2018},\ndoi = {10.1021/acs.jpclett.8b00124},\nnote ={PMID: 29532658},\neprint = {\nhttps://doi.org/10.1021/acs.jpclett.8b00124\n\n}}"], "columns": {"formula": "Chemical formula.", "expt_gap": "Experimentally measured bandgap, in eV.", "likely_mpid": "Materials Project database ID (mp-id) most likely associated with each material."}, "description": "Identical to the matbench_expt_gap dataset, except that Materials Project database IDs (mp-ids) have been associated with each material using the same method as described for the expt_formation_enthalpy_kingsbury dataset. Columns have also been renamed for consistency with the formation enthalpy data.", "file_type": "json.gz", "num_entries": 4604, "reference": "Kings<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, S<PERSON>, <PERSON><PERSON>, <PERSON><PERSON> of r$^2$SCAN and SCAN metaGGA functionals via an automated, high-throughput computational workflow. In preparation.", "url": "https://ndownloader.figshare.com/files/28232598", "hash": "b36bf5bfe3782c2a2372422c64b8a86fd1d30709559a6dc0c30e4c8ac5ab3e96"}, "matbench_expt_gap": {"bibtex_refs": ["@Article{Dunn2020,\nauthor={<PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>, Anubha<PERSON>},\ntitle={Benchmarking materials property prediction methods: the Matbench test set and Automatminer reference algorithm},\njournal={npj Computational Materials},\nyear={2020},\nmonth={Sep},\nday={15},\nvolume={6},\nnumber={1},\npages={138},\nabstract={We present a benchmark test suite and an automated machine learning procedure for evaluating supervised machine learning (ML) models for predicting properties of inorganic bulk materials. The test suite, Matbench, is a set of 13{\\thinspace}ML tasks that range in size from 312 to 132k samples and contain data from 10 density functional theory-derived and experimental sources. Tasks include predicting optical, thermal, electronic, thermodynamic, tensile, and elastic properties given a material's composition and/or crystal structure. The reference algorithm, Automatminer, is a highly-extensible, fully automated ML pipeline for predicting materials properties from materials primitives (such as composition and crystal structure) without user intervention or hyperparameter tuning. We test Automatminer on the Matbench test suite and compare its predictive power with state-of-the-art crystal graph neural networks and a traditional descriptor-based Random Forest model. We find <PERSON>mat<PERSON><PERSON> achieves the best performance on 8 of 13 tasks in the benchmark. We also show our test suite is capable of exposing predictive advantages of each algorithm---namely, that crystal graph methods appear to outperform traditional machine learning methods given {\\textasciitilde}104 or greater data points. We encourage evaluating materials ML algorithms on the Matbench benchmark and comparing them against the latest version of Automatminer.},\nissn={2057-3960},\ndoi={10.1038/s41524-020-00406-3},\nurl={https://doi.org/10.1038/s41524-020-00406-3}\n}\n", "@article{doi:10.1021/acs.jpclett.8b00124,\nauthor = {<PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, Jak<PERSON><PERSON>},\ntitle = {Predicting the Band Gaps of Inorganic Solids by Machine Learning},\njournal = {The Journal of Physical Chemistry Letters},\nvolume = {9},\nnumber = {7},\npages = {1668-1673},\nyear = {2018},\ndoi = {10.1021/acs.jpclett.8b00124},\nnote ={PMID: 29532658},\neprint = {\nhttps://doi.org/10.1021/acs.jpclett.8b00124\n\n}}"], "columns": {"composition": "Chemical formula.", "gap expt": "Target variable. Experimentally measured gap, in eV."}, "description": "Matbench v0.1 test dataset for predicting experimental band gap from composition alone. Retrieved from <PERSON><PERSON> et al. supplementary information. Deduplicated according to composition, removing compositions with reported band gaps spanning more than a 0.1eV range; remaining compositions were assigned values based on the closest experimental value to the mean experimental value for that composition among all reports. For benchmarking w/ nested cross validation, the order of the dataset must be identical to the retrieved data; refer to the Automatminer/Matbench publication for more details.", "file_type": "json.gz", "num_entries": 4604, "reference": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> (2018) Predicting the Band Gaps of Inorganic Solids by Machine Learning J. Phys. Chem. Lett. 2018, 9, 7, 1668-1673 https:doi.org/10.1021/acs.jpclett.8b00124.", "url": "https://ml.materialsproject.org/projects/matbench_expt_gap.json.gz", "hash": "783e7d1461eb83b00b2f2942da4b95fda5e58a0d1ae26b581c24cf8a82ca75b2"}, "matbench_expt_is_metal": {"bibtex_refs": ["@Article{Dunn2020,\nauthor={<PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>, Anubha<PERSON>},\ntitle={Benchmarking materials property prediction methods: the Matbench test set and Automatminer reference algorithm},\njournal={npj Computational Materials},\nyear={2020},\nmonth={Sep},\nday={15},\nvolume={6},\nnumber={1},\npages={138},\nabstract={We present a benchmark test suite and an automated machine learning procedure for evaluating supervised machine learning (ML) models for predicting properties of inorganic bulk materials. The test suite, Matbench, is a set of 13{\\thinspace}ML tasks that range in size from 312 to 132k samples and contain data from 10 density functional theory-derived and experimental sources. Tasks include predicting optical, thermal, electronic, thermodynamic, tensile, and elastic properties given a material's composition and/or crystal structure. The reference algorithm, Automatminer, is a highly-extensible, fully automated ML pipeline for predicting materials properties from materials primitives (such as composition and crystal structure) without user intervention or hyperparameter tuning. We test Automatminer on the Matbench test suite and compare its predictive power with state-of-the-art crystal graph neural networks and a traditional descriptor-based Random Forest model. We find <PERSON>mat<PERSON><PERSON> achieves the best performance on 8 of 13 tasks in the benchmark. We also show our test suite is capable of exposing predictive advantages of each algorithm---namely, that crystal graph methods appear to outperform traditional machine learning methods given {\\textasciitilde}104 or greater data points. We encourage evaluating materials ML algorithms on the Matbench benchmark and comparing them against the latest version of Automatminer.},\nissn={2057-3960},\ndoi={10.1038/s41524-020-00406-3},\nurl={https://doi.org/10.1038/s41524-020-00406-3}\n}\n", "@article{doi:10.1021/acs.jpclett.8b00124,\nauthor = {<PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, Jak<PERSON><PERSON>},\ntitle= {Predicting the Band Gaps of Inorganic Solids by Machine Learning},\njournal = {The Journal of Physical Chemistry Letters},\nvolume = {9},\nnumber = {7},\npages = {1668-1673},\nyear = {2018},\ndoi = {10.1021/acs.jpclett.8b00124},\nnote ={PMID: 29532658},\neprint = {\nhttps://doi.org/10.1021/acs.jpclett.8b00124\n\n}}"], "columns": {"composition": "Chemical formula.", "is_metal": "Target variable. 1 if is a metal, 0 if nonmetal."}, "description": "Matbench v0.1 test dataset for classifying metallicity from composition alone. Retrieved from <PERSON><PERSON> et al. supplementary information. Deduplicated according to composition, ensuring no conflicting reports were entered for any compositions (i.e., no reported compositions were both metal and nonmetal). For benchmarking w/ nested cross validation, the order of the dataset must be identical to the retrieved data; refer to the Automatminer/Matbench publication for more details.", "file_type": "json.gz", "num_entries": 4921, "reference": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> (2018) Predicting the Band Gaps of Inorganic Solids by Machine Learning J. Phys. Chem. Lett. 2018, 9, 7, 1668-1673 \n https//:doi.org/10.1021/acs.jpclett.8b00124.", "url": "https://ml.materialsproject.org/projects/matbench_expt_is_metal.json.gz", "hash": "8f2a4f9bacdcbc5c2c73615629ee7986f09d39bed40ba7db52b61b2889730887"}, "matbench_phonons": {"bibtex_refs": ["@Article{Dunn2020,\nauthor={<PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>, Anubha<PERSON>},\ntitle={Benchmarking materials property prediction methods: the Matbench test set and Automatminer reference algorithm},\njournal={npj Computational Materials},\nyear={2020},\nmonth={Sep},\nday={15},\nvolume={6},\nnumber={1},\npages={138},\nabstract={We present a benchmark test suite and an automated machine learning procedure for evaluating supervised machine learning (ML) models for predicting properties of inorganic bulk materials. The test suite, Matbench, is a set of 13{\\thinspace}ML tasks that range in size from 312 to 132k samples and contain data from 10 density functional theory-derived and experimental sources. Tasks include predicting optical, thermal, electronic, thermodynamic, tensile, and elastic properties given a material's composition and/or crystal structure. The reference algorithm, Automatminer, is a highly-extensible, fully automated ML pipeline for predicting materials properties from materials primitives (such as composition and crystal structure) without user intervention or hyperparameter tuning. We test Automatminer on the Matbench test suite and compare its predictive power with state-of-the-art crystal graph neural networks and a traditional descriptor-based Random Forest model. We find <PERSON>mat<PERSON><PERSON> achieves the best performance on 8 of 13 tasks in the benchmark. We also show our test suite is capable of exposing predictive advantages of each algorithm---namely, that crystal graph methods appear to outperform traditional machine learning methods given {\\textasciitilde}104 or greater data points. We encourage evaluating materials ML algorithms on the Matbench benchmark and comparing them against the latest version of Automatminer.},\nissn={2057-3960},\ndoi={10.1038/s41524-020-00406-3},\nurl={https://doi.org/10.1038/s41524-020-00406-3}\n}\n", "@Article{Petretto2018,\nauthor={<PERSON><PERSON><PERSON>, <PERSON>\nand <PERSON>, <PERSON><PERSON><PERSON>\nand <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>\nand <PERSON>, <PERSON>\nand <PERSON><PERSON><PERSON>, <PERSON>\nand <PERSON>, <PERSON><PERSON><PERSON>\nand <PERSON><PERSON><PERSON>, <PERSON>\nand <PERSON>, <PERSON><PERSON>\nand <PERSON><PERSON>, <PERSON><PERSON>\nand <PERSON>, <PERSON><PERSON><PERSON><PERSON>},\ntitle={High-throughput density-functional perturbation theory phonons for inorganic materials},\njournal={Scientific Data},\nyear={2018},\nmonth={May},\nday={01},\npublisher={The Author(s)},\nvolume={5},\npages={180065},\nnote={Data Descriptor},\nurl={http://dx.doi.org/10.1038/sdata.2018.65}\n}", "@misc{petret<PERSON>_<PERSON><PERSON><PERSON><PERSON>_miranda_winston_giant<PERSON><PERSON>_rignan<PERSON>_van set<PERSON>_gonze_persson_hautier_2018, title={High-throughput Density-Functional Perturbation Theory phonons for inorganic materials}, url={https://figshare.com/collections/High-throughput_Density-Functional_Perturbation_Theory_phonons_for_inorganic_materials/3938023/1}, DOI={10.6084/m9.figshare.c.3938023.v1}, abstractNote={The knowledge of the vibrational properties of a material is of key importance to understand physical phenomena such as thermal conductivity, superconductivity, and ferroelectricity among others. However, detailed experimental phonon spectra are available only for a limited number of materials which hinders the large-scale analysis of vibrational properties and their derived quantities. In this work, we perform ab initio calculations of the full phonon dispersion and vibrational density of states for 1521 semiconductor compounds in the harmonic approximation based on density functional perturbation theory. The data is collected along with derived dielectric and thermodynamic properties. We present the procedure used to obtain the results, the details of the provided database and a validation based on the comparison with experimental data.}, publisher={figshare}, author={<PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON>-<PERSON> and <PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON>. and <PERSON><PERSON>, <PERSON> and <PERSON><PERSON>, <PERSON><PERSON> <PERSON> and <PERSON><PERSON><PERSON>, <PERSON><PERSON>}, year={2018}, month={<PERSON>}}"], "columns": {"last phdos peak": "Target variable. Frequency of the highest frequency optical phonon mode peak, in units of 1/cm; ; may be used as an estimation of dominant longitudinal optical phonon frequency.", "structure": "Pymatgen Structure of the material."}, "description": "Matbench v0.1 test dataset for predicting vibration properties from crystal structure. Original data retrieved from <PERSON><PERSON><PERSON> et al. Original calculations done via ABINIT in the harmonic approximation based on density functional perturbation theory. Removed entries having a formation energy (or energy above the convex hull) more than 150meV. For benchmarking w/ nested cross validation, the order of the dataset must be identical to the retrieved data; refer to the Automatminer/Matbench publication for more details.", "file_type": "json.gz", "num_entries": 1265, "reference": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> et al. High-throughput density functional perturbation theory phonons for inorganic materials. Sci. Data 5:180065 doi: 10.1038/sdata.2018.65 (2018).\n<PERSON><PERSON><PERSON>, <PERSON><PERSON> et al. High-throughput density functional perturbation theory phonons for inorganic materials. (2018). figshare. Collection.", "url": "https://ml.materialsproject.org/projects/matbench_phonons.json.gz", "hash": "4db551f21ec5f577e6202725f10e34dfc509aa7df3a6bdaac497da7f6dbbb9b3"}, "matbench_steels": {"bibtex_refs": ["@Article{Dunn2020,\nauthor={<PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>\nand <PERSON>, <PERSON>, Anubha<PERSON>},\ntitle={Benchmarking materials property prediction methods: the Matbench test set and Automatminer reference algorithm},\njournal={npj Computational Materials},\nyear={2020},\nmonth={Sep},\nday={15},\nvolume={6},\nnumber={1},\npages={138},\nabstract={We present a benchmark test suite and an automated machine learning procedure for evaluating supervised machine learning (ML) models for predicting properties of inorganic bulk materials. The test suite, Matbench, is a set of 13{\\thinspace}ML tasks that range in size from 312 to 132k samples and contain data from 10 density functional theory-derived and experimental sources. Tasks include predicting optical, thermal, electronic, thermodynamic, tensile, and elastic properties given a material's composition and/or crystal structure. The reference algorithm, Automatminer, is a highly-extensible, fully automated ML pipeline for predicting materials properties from materials primitives (such as composition and crystal structure) without user intervention or hyperparameter tuning. We test Automatminer on the Matbench test suite and compare its predictive power with state-of-the-art crystal graph neural networks and a traditional descriptor-based Random Forest model. We find <PERSON>mat<PERSON><PERSON> achieves the best performance on 8 of 13 tasks in the benchmark. We also show our test suite is capable of exposing predictive advantages of each algorithm---namely, that crystal graph methods appear to outperform traditional machine learning methods given {\\textasciitilde}104 or greater data points. We encourage evaluating materials ML algorithms on the Matbench benchmark and comparing them against the latest version of Automatminer.},\nissn={2057-3960},\ndoi={10.1038/s41524-020-00406-3},\nurl={https://doi.org/10.1038/s41524-020-00406-3}\n}\n", "@misc{Citrine Informatics,\ntitle = {Mechanical properties of some steels},\nhowpublished = {\\url{https://citrination.com/datasets/153092/},\n}"], "columns": {"composition": "Chemical formula.", "yield strength": "Target variable. Experimentally measured steel yield strengths, in MPa."}, "description": "Matbench v0.1 test dataset for predicting steel yield strengths from chemical composition alone. Retrieved from Citrine informatics. Deduplicated. For benchmarking w/ nested cross validation, the order of the dataset must be identical to the retrieved data; refer to the Automatminer/Matbench publication for more details.", "file_type": "json.gz", "num_entries": 312, "reference": "https://citrination.com/datasets/153092/", "url": "https://ml.materialsproject.org/projects/matbench_steels.json.gz", "hash": "473bc4957b2ea5e6465aef84bc29bb48ac34db27d69ea4ec5f508745c6fae252"}, "superconductivity2018": {"bibtex_refs": ["@article{Stanev2018,\n  doi = {10.1038/s41524-018-0085-8},\n  url = {https://doi.org/10.1038/s41524-018-0085-8},\n  year = {2018},\n  month = jun,\n  publisher = {Springer Science and Business Media {LLC}},\n  volume = {4},\n  number = {1},\n  author = {<PERSON><PERSON> and <PERSON> and <PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON> and <PERSON><PERSON><PERSON>},\n  title = {Machine learning modeling of superconducting critical temperature},\n  journal = {npj Computational Materials}\n}", "@misc{NIMSSuperCon,\nhowpublished={http://supercon.nims.go.jp/index_en.html}, \ntitle={SuperCon},\nauthor={National Institute of Materials Science, Materials Information Station}}\n"], "columns": {"composition": "Chemical formula.", "Tc": "Experimental superconducting temperature, in K."}, "description": "Dataset of ~16,000 experimental superconductivity records (critical temperatures) from <PERSON><PERSON> et al., originally from the Japanese National Institute for Materials Science. Does not include structural data. Includes ~300 measurements from materials found without superconductivity (Tc=0). No modifications were made to the core dataset, aside from basic file type change to json for (un)packaging with matminer. Reproduced under the Creative Commons 4.0 license, which can be found here: http://creativecommons.org/licenses/by/4.0/.", "file_type": "json.gz", "num_entries": 16414, "reference": "https://doi.org/10.1038/s41524-018-0085-8", "url": "https://figshare.com/ndownloader/files/31614956", "hash": "03976fc80f39b3d9c7bf2c12f4d045513cdad17a3a2e559f6290fc7245154418"}, "ucsb_thermoelectrics": {"bibtex_refs": ["@article{Gaultois2013,\n  doi = {10.1021/cm400893e},\n  url = {https://doi.org/10.1021/cm400893e},\n  year = {2013},\n  month = may,\n  publisher = {American Chemical Society ({ACS})},\n  volume = {25},\n  number = {15},\n  pages = {2911--2920},\n  author = {<PERSON> and <PERSON> and <PERSON> and <PERSON> and <PERSON> and <PERSON>},\n  title = {Data-Driven Review of Thermoelectric Materials: Performance and Resource Considerations},\n  journal = {Chemistry of Materials}\n}", "@misc{Citrine Informatics,\ntitle = {UCSB Thermoelectrics Database},\nhowpublished = {\\url{https://citrination.com/datasets/150557/},\n}"], "columns": {"composition": "Chemical formula.", "crystallinity": "Either single crystal, polycrystalline, or nanoparticles.", "synthesis": "Brief string describing the synthesis method", "spacegroup": "Spacegroup number, if available", "rho (ohm.cm)": "Electrical resistivity, in ohm.cm", "S [muV/K]": "Seebeck coefficient, in microVolts/K, if available", "PF [W/mK^2]": "Thermoelectric power factor, conductivity * Seebeck^2, in [W/mK^2] if available", "zT": "Thermoelectric figure of merit, PF * T/K, unitless, if available", "kappa [W/mK]": "Thermal conductivity in Watt/ meter * Kelvin, if available", "sigma [S/cm]": "Electrical conductivity, in Siemens/cm, if available", "T [K]": "Temperature in Kelvin at which these properties were obtained, if available", "src": "Original source of the recording. To cite the aggregator of the data, see the bibtext_refs section of this metadata."}, "description": "Database of ~1,100 experimental thermoelectric materials from UCSB aggregated from 108 source publications and personal communications. Downloaded from Citrine. Source UCSB webpage is http://www.mrl.ucsb.edu:8080/datamine/thermoelectric.jsp. See reference for more information on original data aggregation. No duplicate entries are present, but each src may result in multiple measurements of the same materials' properties at different temperatures or conditions.", "file_type": "json.gz", "num_entries": 1093, "reference": "https://citrination.com/datasets/150557/", "url": "https://ndownloader.figshare.com/files/28333845", "hash": "7d0c9c74fd52995b876bd251a7732b5498314702441fa463197fb492be68ecc0"}, "tholander_nitrides": {"bibtex_refs": ["@article{tholander2016strong,\n  title={Strong piezoelectric response in stable TiZnN2, ZrZnN2, and HfZnN2 found by ab initio high-throughput approach},\n  author={<PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, CBA and Armiento, <PERSON><PERSON> and <PERSON>, <PERSON>ren<PERSON> and Alling, Bj{\\\"o}rn},\n  journal={Journal of Applied Physics},\n  volume={120},\n  number={22},\n  pages={225102},\n  year={2016},\n  publisher={AIP Publishing LLC}\n}"], "columns": {"material_id": "Human readable identifier for each material.", "ht_id": "Unique identifier to track the calculation in httk", "initial_structure": "A pymatgen structure object representing the structure before relaxation.", "final_structure": "A pymatgen structure object representing the structure after relaxation.", "E_vasp_per_atom": "The VASP calculated energy per atom for the final structure, in eV/atom", "chemical_system": "The chemical system represented by the atoms actually contained in the structure"}, "description": "A challenging data set for quantum machine learning containing a diverse set of 12.8k polymorphs in the Zn-Ti-N, Zn-Zr-N and Zn-Hf-N chemical systems. The phase diagrams of the Ti-Zn-N, Zr-Zn-N, and Hf-Zn-N systems are determined using large-scale high-throughput density functional calculations (DFT-GGA) (PBE). In total 12,815 relaxed structures are shared alongside their energy calculated using the VASP DFT code. The High-Throughput Toolkit was used to manage the calculations. Data adapted and deduplicated from the original data on Zenodo at https://zenodo.org/record/5530535#.YjJ3ZhDMJLQ, published under MIT licence. Collated from separate files of chemical systems and deduplicated according to identical structures matching ht_ids. Prepared in collaboration with <PERSON>.", "file_type": "json.gz", "num_entries": 12815, "reference": "https://zenodo.org/record/5530535#.YjJ3ZhDMJLQ", "url": "https://figshare.com/ndownloader/files/34423997", "hash": "6b3f9f5f4d2564fcf39dc5797bbc4eeffc033ce382b7c467386bd0a1050a0872"}}