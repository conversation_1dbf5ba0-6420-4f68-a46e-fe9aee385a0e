root_path: ${oc.env:PROJECT_ROOT}/data/perov_5
prop: heat_ref
num_targets: 1
# prop: scaled_lattice
# num_targets: 6
niggli: true
primitive: false
graph_method: crystalnn
lattice_scale_method: scale_length
preprocess_workers: 30
readout: mean
max_atoms: 20
otf_graph: false
eval_model_name: perovskite


train_max_epochs: 3000
early_stopping_patience: 100000
teacher_forcing_max_epoch: 1500


datamodule:
  _target_: cdvae.pl_data.datamodule.CrystDataModule

  datasets:
    train:
      _target_: cdvae.pl_data.dataset.CrystDataset
      name: Formation energy train
      path: ${data.root_path}/train.csv
      prop: ${data.prop}
      niggli: ${data.niggli}
      primitive: ${data.primitive}
      graph_method: ${data.graph_method}
      lattice_scale_method: ${data.lattice_scale_method}
      preprocess_workers: ${data.preprocess_workers}

    val:
      - _target_: cdvae.pl_data.dataset.CrystDataset
        name: Formation energy val
        path: ${data.root_path}/val.csv
        prop: ${data.prop}
        niggli: ${data.niggli}
        primitive: ${data.primitive}
        graph_method: ${data.graph_method}
        lattice_scale_method: ${data.lattice_scale_method}
        preprocess_workers: ${data.preprocess_workers}

    test:
      - _target_: cdvae.pl_data.dataset.CrystDataset
        name: Formation energy test
        path: ${data.root_path}/test.csv
        prop: ${data.prop}
        niggli: ${data.niggli}
        primitive: ${data.primitive}
        graph_method: ${data.graph_method}
        lattice_scale_method: ${data.lattice_scale_method}
        preprocess_workers: ${data.preprocess_workers}

  num_workers:
    train: 0
    val: 0
    test: 0

  batch_size:
    train: 512
    val: 256
    test: 256
