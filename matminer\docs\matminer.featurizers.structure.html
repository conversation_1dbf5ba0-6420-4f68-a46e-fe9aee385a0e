
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

    <title>matminer.featurizers.structure package &#8212; matminer 0.9.0 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="_static/nature.css" />
    <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="_static/doctools.js"></script>
    <script src="_static/sphinx_highlight.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
 
<link href='https://fonts.googleapis.com/css?family=Lato:400,700' rel='stylesheet' type='text/css'>

  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer.featurizers.structure package</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="matminer-featurizers-structure-package">
<h1>matminer.featurizers.structure package<a class="headerlink" href="#matminer-featurizers-structure-package" title="Permalink to this heading">¶</a></h1>
<section id="subpackages">
<h2>Subpackages<a class="headerlink" href="#subpackages" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="matminer.featurizers.structure.tests.html">matminer.featurizers.structure.tests package</a><ul>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.structure.tests.html#submodules">Submodules</a></li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.structure.tests.html#module-matminer.featurizers.structure.tests.base">matminer.featurizers.structure.tests.base module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.base.StructureFeaturesTest"><code class="docutils literal notranslate"><span class="pre">StructureFeaturesTest</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.base.StructureFeaturesTest.setUp"><code class="docutils literal notranslate"><span class="pre">StructureFeaturesTest.setUp()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.structure.tests.html#module-matminer.featurizers.structure.tests.test_bonding">matminer.featurizers.structure.tests.test_bonding module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_bonding.BondingStructureTest"><code class="docutils literal notranslate"><span class="pre">BondingStructureTest</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_bonding.BondingStructureTest.test_GlobalInstabilityIndex"><code class="docutils literal notranslate"><span class="pre">BondingStructureTest.test_GlobalInstabilityIndex()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_bonding.BondingStructureTest.test_bob"><code class="docutils literal notranslate"><span class="pre">BondingStructureTest.test_bob()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_bonding.BondingStructureTest.test_bondfractions"><code class="docutils literal notranslate"><span class="pre">BondingStructureTest.test_bondfractions()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_bonding.BondingStructureTest.test_min_relative_distances"><code class="docutils literal notranslate"><span class="pre">BondingStructureTest.test_min_relative_distances()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_bonding.BondingStructureTest.test_ward_prb_2017_strhet"><code class="docutils literal notranslate"><span class="pre">BondingStructureTest.test_ward_prb_2017_strhet()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.structure.tests.html#module-matminer.featurizers.structure.tests.test_composite">matminer.featurizers.structure.tests.test_composite module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_composite.CompositeStructureFeaturesTest"><code class="docutils literal notranslate"><span class="pre">CompositeStructureFeaturesTest</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_composite.CompositeStructureFeaturesTest.test_jarvisCFID"><code class="docutils literal notranslate"><span class="pre">CompositeStructureFeaturesTest.test_jarvisCFID()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.structure.tests.html#module-matminer.featurizers.structure.tests.test_matrix">matminer.featurizers.structure.tests.test_matrix module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_matrix.MatrixStructureFeaturesTest"><code class="docutils literal notranslate"><span class="pre">MatrixStructureFeaturesTest</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_matrix.MatrixStructureFeaturesTest.test_coulomb_matrix"><code class="docutils literal notranslate"><span class="pre">MatrixStructureFeaturesTest.test_coulomb_matrix()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_matrix.MatrixStructureFeaturesTest.test_orbital_field_matrix"><code class="docutils literal notranslate"><span class="pre">MatrixStructureFeaturesTest.test_orbital_field_matrix()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_matrix.MatrixStructureFeaturesTest.test_sine_coulomb_matrix"><code class="docutils literal notranslate"><span class="pre">MatrixStructureFeaturesTest.test_sine_coulomb_matrix()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.structure.tests.html#module-matminer.featurizers.structure.tests.test_misc">matminer.featurizers.structure.tests.test_misc module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_misc.MiscStructureFeaturesTest"><code class="docutils literal notranslate"><span class="pre">MiscStructureFeaturesTest</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_misc.MiscStructureFeaturesTest.test_composition_features"><code class="docutils literal notranslate"><span class="pre">MiscStructureFeaturesTest.test_composition_features()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_misc.MiscStructureFeaturesTest.test_ewald"><code class="docutils literal notranslate"><span class="pre">MiscStructureFeaturesTest.test_ewald()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_misc.MiscStructureFeaturesTest.test_xrd_powderPattern"><code class="docutils literal notranslate"><span class="pre">MiscStructureFeaturesTest.test_xrd_powderPattern()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.structure.tests.html#module-matminer.featurizers.structure.tests.test_order">matminer.featurizers.structure.tests.test_order module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_order.OrderStructureFeaturesTest"><code class="docutils literal notranslate"><span class="pre">OrderStructureFeaturesTest</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_order.OrderStructureFeaturesTest.test_density_features"><code class="docutils literal notranslate"><span class="pre">OrderStructureFeaturesTest.test_density_features()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_order.OrderStructureFeaturesTest.test_ordering_param"><code class="docutils literal notranslate"><span class="pre">OrderStructureFeaturesTest.test_ordering_param()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_order.OrderStructureFeaturesTest.test_packing_efficiency"><code class="docutils literal notranslate"><span class="pre">OrderStructureFeaturesTest.test_packing_efficiency()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_order.OrderStructureFeaturesTest.test_structural_complexity"><code class="docutils literal notranslate"><span class="pre">OrderStructureFeaturesTest.test_structural_complexity()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.structure.tests.html#module-matminer.featurizers.structure.tests.test_rdf">matminer.featurizers.structure.tests.test_rdf module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_rdf.StructureRDFTest"><code class="docutils literal notranslate"><span class="pre">StructureRDFTest</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_rdf.StructureRDFTest.test_get_rdf_bin_labels"><code class="docutils literal notranslate"><span class="pre">StructureRDFTest.test_get_rdf_bin_labels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_rdf.StructureRDFTest.test_prdf"><code class="docutils literal notranslate"><span class="pre">StructureRDFTest.test_prdf()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_rdf.StructureRDFTest.test_rdf_and_peaks"><code class="docutils literal notranslate"><span class="pre">StructureRDFTest.test_rdf_and_peaks()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_rdf.StructureRDFTest.test_redf"><code class="docutils literal notranslate"><span class="pre">StructureRDFTest.test_redf()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.structure.tests.html#module-matminer.featurizers.structure.tests.test_sites">matminer.featurizers.structure.tests.test_sites module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_sites.PartialStructureSitesFeaturesTest"><code class="docutils literal notranslate"><span class="pre">PartialStructureSitesFeaturesTest</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_sites.PartialStructureSitesFeaturesTest.test_partialsitestatsfingerprint"><code class="docutils literal notranslate"><span class="pre">PartialStructureSitesFeaturesTest.test_partialsitestatsfingerprint()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_sites.PartialStructureSitesFeaturesTest.test_ward_prb_2017_efftcn"><code class="docutils literal notranslate"><span class="pre">PartialStructureSitesFeaturesTest.test_ward_prb_2017_efftcn()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_sites.PartialStructureSitesFeaturesTest.test_ward_prb_2017_lpd"><code class="docutils literal notranslate"><span class="pre">PartialStructureSitesFeaturesTest.test_ward_prb_2017_lpd()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_sites.StructureSitesFeaturesTest"><code class="docutils literal notranslate"><span class="pre">StructureSitesFeaturesTest</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_sites.StructureSitesFeaturesTest.test_sitestatsfingerprint"><code class="docutils literal notranslate"><span class="pre">StructureSitesFeaturesTest.test_sitestatsfingerprint()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_sites.StructureSitesFeaturesTest.test_ward_prb_2017_efftcn"><code class="docutils literal notranslate"><span class="pre">StructureSitesFeaturesTest.test_ward_prb_2017_efftcn()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_sites.StructureSitesFeaturesTest.test_ward_prb_2017_lpd"><code class="docutils literal notranslate"><span class="pre">StructureSitesFeaturesTest.test_ward_prb_2017_lpd()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.structure.tests.html#module-matminer.featurizers.structure.tests.test_symmetry">matminer.featurizers.structure.tests.test_symmetry module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_symmetry.StructureSymmetryFeaturesTest"><code class="docutils literal notranslate"><span class="pre">StructureSymmetryFeaturesTest</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_symmetry.StructureSymmetryFeaturesTest.test_dimensionality"><code class="docutils literal notranslate"><span class="pre">StructureSymmetryFeaturesTest.test_dimensionality()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.structure.tests.html#matminer.featurizers.structure.tests.test_symmetry.StructureSymmetryFeaturesTest.test_global_symmetry"><code class="docutils literal notranslate"><span class="pre">StructureSymmetryFeaturesTest.test_global_symmetry()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.featurizers.structure.tests.html#module-matminer.featurizers.structure.tests">Module contents</a></li>
</ul>
</li>
</ul>
</div>
</section>
<section id="submodules">
<h2>Submodules<a class="headerlink" href="#submodules" title="Permalink to this heading">¶</a></h2>
</section>
<section id="module-matminer.featurizers.structure.bonding">
<span id="matminer-featurizers-structure-bonding-module"></span><h2>matminer.featurizers.structure.bonding module<a class="headerlink" href="#module-matminer.featurizers.structure.bonding" title="Permalink to this heading">¶</a></h2>
<p>Structure featurizers based on bonding.</p>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.BagofBonds">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.bonding.</span></span><span class="sig-name descname"><span class="pre">BagofBonds</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">coulomb_matrix</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">SineCoulombMatrix(flatten=False)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">token</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'</span> <span class="pre">-</span> <span class="pre">'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.BagofBonds" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>Compute a Bag of Bonds vector, as first described by Hansen et al. (2015).</p>
<p>The Bag of Bonds approach is based creating an even-length vector from a
Coulomb matrix output. Practically, it represents the Coloumbic interactions
between each possible set of sites in a structure as a vector.</p>
<p>BagofBonds must be fit to an iterable of structures using the “fit” method
before featurization can occur. This is because the bags and the maximum
lengths of each bag must be set prior to featurization. We recommend
fitting and featurizing on the same data to maintain consistency
between generated feature sets. This can be done using the fit_transform
method (for lists of structures) or the fit_featurize_dataframe method
(for dataframes).</p>
<p>BagofBonds is based on a method by Hansen et. al “Machine Learning
Predictions of Molecular Properties: Accurate Many-Body Potentials and
Nonlocality in Chemical Space” (2015).</p>
<dl class="simple">
<dt>Args:</dt><dd><dl class="simple">
<dt>coulomb_matrix (BaseFeaturizer): A featurizer object containing a</dt><dd><p>“featurize” method which returns a matrix of size nsites x nsites.
Good choices are CoulombMatrix() or SineCoulombMatrix(), with the
flatten=False parameter set.</p>
</dd>
<dt>token (str): The string used to separate species in a bond, including</dt><dd><p>spaces. The token must contain at least one space and cannot have
alphabetic characters in it, and should be padded by spaces. For
example, for the bond Cs+ - Cl-, the token is ‘ - ‘. This determines
how bonds are represented in the dataframe.</p>
</dd>
</dl>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.BagofBonds.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">coulomb_matrix</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">SineCoulombMatrix(flatten=False)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">token</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'</span> <span class="pre">-</span> <span class="pre">'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.BagofBonds.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.BagofBonds.bag">
<span class="sig-name descname"><span class="pre">bag</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">return_baglens</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.BagofBonds.bag" title="Permalink to this definition">¶</a></dt>
<dd><p>Convert a structure into a bag of bonds, where each bag has no padded
zeros. using this function will give the ‘raw’ bags, which when
concatenated, will have different lengths.</p>
<dl class="simple">
<dt>Args:</dt><dd><dl class="simple">
<dt>s (Structure): A pymatgen Structure or IStructure object. May also</dt><dd><p>work with a</p>
</dd>
<dt>return_baglens (bool): If True, returns the bag of bonds with as</dt><dd><p>a dictionary with the number of bonds as values in place
of the vectors of coulomb matrix vals. If False, calculates
Coulomb matrix values and returns ‘raw’ bags.</p>
</dd>
</dl>
</dd>
<dt>Returns:</dt><dd><dl class="simple">
<dt>(dict) A bag of bonds, where the keys are sorted tuples of pymatgen</dt><dd><p>Site objects representing bonds or sites, and the values are the
Coulomb matrix values for that bag.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.BagofBonds.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.BagofBonds.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.BagofBonds.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.BagofBonds.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.BagofBonds.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.BagofBonds.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Featurizes a structure according to the bag of bonds method.
Specifically, each structure is first bagged by flattening the
Coulomb matrix for the structure. Then, it is zero-padded according to
the maximum number of bonds in each bag, for the set of bags that
BagofBonds was fit with.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>s (Structure): A pymatgen structure object</p>
</dd>
<dt>Returns:</dt><dd><p>(list): The Bag of Bonds vector for the input structure</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.BagofBonds.fit">
<span class="sig-name descname"><span class="pre">fit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">X</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.BagofBonds.fit" title="Permalink to this definition">¶</a></dt>
<dd><p>Define the bags using a list of structures.</p>
<p>Both the names of the bags (e.g., Cs-Cl) and the maximum lengths of
the bags are set with fit.</p>
<dl>
<dt>Args:</dt><dd><dl class="simple">
<dt>X (Series/list): An iterable of pymatgen Structure</dt><dd><p>objects which will be used to determine the allowed bond
types and bag lengths.</p>
</dd>
</dl>
<p>y : unused (added for consistency with overridden method signature)</p>
</dd>
<dt>Returns:</dt><dd><p>self</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.BagofBonds.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.BagofBonds.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.BondFractions">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.bonding.</span></span><span class="sig-name descname"><span class="pre">BondFractions</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">nn=&lt;pymatgen.analysis.local_env.CrystalNN</span> <span class="pre">object&gt;</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bbv=0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">no_oxi=False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">approx_bonds=False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">token='</span> <span class="pre">-</span> <span class="pre">'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">allowed_bonds=None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.BondFractions" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>Compute the fraction of each bond in a structure, based on NearestNeighbors.</p>
<p>For example, in a structure with 2 Li-O bonds and 3 Li-P bonds:</p>
<p>Li-0: 0.4
Li-P: 0.6</p>
<p>Features:</p>
<p>BondFractions must be fit with iterable of structures before featurization in
order to define the allowed bond types (features). To do this, pass a list
of allowed_bonds. Otherwise, fit based on a list of structures. If
allowed_bonds is defined and BondFractions is also fit, the intersection
of the two lists of possible bonds is used.</p>
<p>For dataframes containing structures of various compositions, a unified
dataframe is returned which has the collection of all possible bond types
gathered from all structures as columns. To approximate bonds based on
chemical rules (ie, for a structure which you’d like to featurize but has
bonds not in the allowed set), use approx_bonds = True.</p>
<p>BondFractions is based on the “sum over bonds” in the Bag of Bonds approach,
based on a method by Hansen et. al “Machine Learning Predictions of Molecular
Properties: Accurate Many-Body Potentials and Nonlocality in Chemical Space”
(2015).</p>
<dl class="simple">
<dt>Args:</dt><dd><dl class="simple">
<dt>nn (NearestNeighbors): A Pymatgen nearest neighbors derived object. For</dt><dd><p>example, pymatgen.analysis.local_env.VoronoiNN().</p>
</dd>
<dt>bbv (float): The ‘bad bond values’, values substituted for</dt><dd><p>structure-bond combinations which can not physically exist, but
exist in the unified dataframe. For example, if a dataframe contains
structures of BaLiP and BaTiO3, determines the value to place in
the Li-P column for the BaTiO3 row; by default, is 0.</p>
</dd>
<dt>no_oxi (bool): If True, the featurizer will be agnostic to oxidation</dt><dd><p>states, which prevents oxidation states from  differentiating
bonds. For example, if True, Ca - O is identical to Ca2+ - O2-,
Ca3+ - O-, etc., and all of them will be included in Ca - O column.</p>
</dd>
<dt>approx_bonds (bool): If True, approximates the fractions of bonds not</dt><dd><p>in allowed_bonds (forbidden bonds) with similar allowed bonds.
Chemical rules are used to determine which bonds are most ‘similar’;
particularly, the Euclidean distance between the 2-tuples of the
bonds in Mendeleev no. space is minimized for the approximate
bond chosen.</p>
</dd>
<dt>token (str): The string used to separate species in a bond, including</dt><dd><p>spaces. The token must contain at least one space and cannot have
alphabetic characters in it, and should be padded by spaces. For
example, for the bond Cs+ - Cl-, the token is ‘ - ‘. This determines
how bonds are represented in the dataframe.</p>
</dd>
<dt>allowed_bonds ([str]): A listlike object containing bond types as</dt><dd><p>strings. For example, Cs - Cl, or Li+ - O2-. Ions and elements
will still have distinct bonds if (1) the bonds list originally
contained them and (2) no_oxi is False. These must match the
token specified.</p>
</dd>
</dl>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.BondFractions.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">nn=&lt;pymatgen.analysis.local_env.CrystalNN</span> <span class="pre">object&gt;</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bbv=0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">no_oxi=False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">approx_bonds=False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">token='</span> <span class="pre">-</span> <span class="pre">'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">allowed_bonds=None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.BondFractions.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.BondFractions.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.BondFractions.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.BondFractions.enumerate_all_bonds">
<span class="sig-name descname"><span class="pre">enumerate_all_bonds</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">structures</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.BondFractions.enumerate_all_bonds" title="Permalink to this definition">¶</a></dt>
<dd><p>Identify all the unique, possible bonds types of all structures present,
and create the ‘unified’ bonds list.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>structures (list/ndarray): List of pymatgen Structures</p>
</dd>
<dt>Returns:</dt><dd><p>A tuple of unique, possible bond types for an entire list of
structures. This tuple is used to form the unified feature labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.BondFractions.enumerate_bonds">
<span class="sig-name descname"><span class="pre">enumerate_bonds</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.BondFractions.enumerate_bonds" title="Permalink to this definition">¶</a></dt>
<dd><p>Lists out all the bond possibilities in a single structure.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>s (Structure): A pymatgen structure</p>
</dd>
<dt>Returns:</dt><dd><p>A list of bond types in ‘Li-O’ form, where the order of the
elements in each bond type is alphabetic.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.BondFractions.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.BondFractions.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Returns the list of allowed bonds. Throws an error if the featurizer
has not been fit.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.BondFractions.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.BondFractions.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Quantify the fractions of each bond type in a structure.</p>
<p>For collections of structures, bonds types which are not found in a
particular structure (e.g., Li-P in BaTiO3) are represented as NaN.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>s (Structure): A pymatgen Structure object</p>
</dd>
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) The feature list of bond fractions, in the order of the</dt><dd><p>alphabetized corresponding bond names.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.BondFractions.fit">
<span class="sig-name descname"><span class="pre">fit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">X</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.BondFractions.fit" title="Permalink to this definition">¶</a></dt>
<dd><p>Define the bond types allowed to be returned during each featurization.
Bonds found during featurization which are not allowed will be omitted
from the returned dataframe or matrix.</p>
<p>Fit BondFractions by either passing an iterable of structures to
training_data or by defining the bonds explicitly with allowed_bonds
in __init__.</p>
<dl>
<dt>Args:</dt><dd><dl class="simple">
<dt>X (Series/list): An iterable of pymatgen Structure</dt><dd><p>objects which will be used to determine the allowed bond
types.</p>
</dd>
</dl>
<p>y : unused (added for consistency with overridden method signature)</p>
</dd>
<dt>Returns:</dt><dd><p>self</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.BondFractions.from_preset">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">from_preset</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">preset</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.BondFractions.from_preset" title="Permalink to this definition">¶</a></dt>
<dd><p>Use one of the standard instances of a given NearNeighbor class.
Pass args to __init__, such as allowed_bonds, using this method as well.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>preset (str): preset type (“CrystalNN”, “VoronoiNN”, “JmolNN”,
“MiniumDistanceNN”, “MinimumOKeeffeNN”, or “MinimumVIRENN”).</p>
</dd>
<dt>Returns:</dt><dd><p>CoordinationNumber from a preset.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.BondFractions.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.BondFractions.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.GlobalInstabilityIndex">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.bonding.</span></span><span class="sig-name descname"><span class="pre">GlobalInstabilityIndex</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">r_cut</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">4.0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">disordered_pymatgen</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.GlobalInstabilityIndex" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>The global instability index of a structure.</p>
<p>The default is to use IUCr 2016 bond valence parameters for computing
bond valence sums. If the structure has disordered site occupancies
or non-integer valences on sites, pymatgen’s bond valence sum method
can be used instead.</p>
<p>Note that pymatgen’s bond valence sum method is prone to error unless
the correct scale factor is supplied. A scale factor based on testing
with perovskites is used here.
TODO: Use scipy to optimize scale factor for minimizing GII</p>
<p>Based on the following publication:</p>
<dl>
<dt>‘Structural characterization of R2BaCuO5 (R = Y, Lu, Yb, Tm, Er, Ho,</dt><dd><p>Dy, Gd, Eu and Sm) oxides by X-ray and neutron diffraction’,
A.Salinas-Sanchez, J.L.Garcia-Muñoz, J.Rodriguez-Carvajal,
R.Saez-Puche, and J.L.Martinez, Journal of Solid State Chemistry,
100, 201-211 (1992),
<a class="reference external" href="https://doi.org/10.1016/0022-4596(92)90094-C">https://doi.org/10.1016/0022-4596(92)90094-C</a></p>
</dd>
<dt>Args:</dt><dd><p>r_cut: Float, how far to search for neighbors when computing bond valences
disordered_pymatgen: Boolean, whether to fall back on pymatgen’s bond</p>
<blockquote>
<div><p>valence sum method for disordered structures</p>
</div></blockquote>
</dd>
<dt>Features:</dt><dd><dl class="simple">
<dt>The global instability index is the square root of the sum of squared</dt><dd><p>differences of the bond valence sums from the formal valences
averaged over all atoms in the unit cell.</p>
</dd>
</dl>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.GlobalInstabilityIndex.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">r_cut</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">4.0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">disordered_pymatgen</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.GlobalInstabilityIndex.calc_bv_sum">
<span class="sig-name descname"><span class="pre">calc_bv_sum</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">site_val</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">site_el</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">neighbor_list</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.calc_bv_sum" title="Permalink to this definition">¶</a></dt>
<dd><p>Computes bond valence sum for site.
Args:</p>
<blockquote>
<div><p>site_val (Integer): valence of site
site_el (String): element name
neighbor_list (List): List of neighboring sites and their distances</p>
</div></blockquote>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.GlobalInstabilityIndex.calc_gii_iucr">
<span class="sig-name descname"><span class="pre">calc_gii_iucr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.calc_gii_iucr" title="Permalink to this definition">¶</a></dt>
<dd><p>Computes global instability index using tabulated bv params.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>s: Pymatgen Structure object</p>
</dd>
<dt>Returns:</dt><dd><p>gii: Float, the global instability index</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.GlobalInstabilityIndex.calc_gii_pymatgen">
<span class="sig-name descname"><span class="pre">calc_gii_pymatgen</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">struct</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">scale_factor</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.965</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.calc_gii_pymatgen" title="Permalink to this definition">¶</a></dt>
<dd><p>Calculates global instability index using Pymatgen’s bond valence sum
Args:</p>
<blockquote>
<div><p>struct: Pymatgen Structure object
scale_factor: Float, tunable scale factor for bond valence</p>
</div></blockquote>
<dl class="simple">
<dt>Returns:</dt><dd><p>gii: Float, global instability index</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.GlobalInstabilityIndex.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.GlobalInstabilityIndex.compute_bv">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">compute_bv</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">params</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dist</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.compute_bv" title="Permalink to this definition">¶</a></dt>
<dd><p>Compute bond valence from parameters.
Args:</p>
<blockquote>
<div><p>params: Dataframe with Ro and B parameters
dist: Float, distance to neighboring atom</p>
</div></blockquote>
<dl class="simple">
<dt>Returns:</dt><dd><p>bv: Float, bond valence</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.GlobalInstabilityIndex.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.GlobalInstabilityIndex.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">struct</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Get global instability index.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>struct: Pymatgen Structure object</p>
</dd>
<dt>Returns:</dt><dd><p>[gii]: Length 1 list with float value</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.GlobalInstabilityIndex.get_bv_params">
<span class="sig-name descname"><span class="pre">get_bv_params</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cation</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">anion</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cat_val</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">an_val</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.get_bv_params" title="Permalink to this definition">¶</a></dt>
<dd><p>Lookup bond valence parameters from IUPAC table.
Args:</p>
<blockquote>
<div><p>cation: String, cation element
anion: String, anion element
cat_val: Integer, cation formal valence
an_val: Integer, anion formal valence</p>
</div></blockquote>
<dl class="simple">
<dt>Returns:</dt><dd><p>bond_val_list: dataframe of bond valence parameters</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.GlobalInstabilityIndex.get_equiv_sites">
<span class="sig-name descname"><span class="pre">get_equiv_sites</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">site</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.get_equiv_sites" title="Permalink to this definition">¶</a></dt>
<dd><p>Find identical sites from analyzing space group symmetry.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.GlobalInstabilityIndex.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.GlobalInstabilityIndex.precheck">
<span class="sig-name descname"><span class="pre">precheck</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">struct</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.precheck" title="Permalink to this definition">¶</a></dt>
<dd><p>Bond valence methods require atom pairs with oxidation states.</p>
<p>Additionally, check if at least the first and last site’s species
have a entry in the bond valence parameters.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>struct: Pymatgen Structure</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.MinimumRelativeDistances">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.bonding.</span></span><span class="sig-name descname"><span class="pre">MinimumRelativeDistances</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cutoff</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">10.0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flatten</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_distances</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_species</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.MinimumRelativeDistances" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>Determines the relative distance of each site to its closest neighbor.</p>
<p>We use the relative distance,
f_ij = r_ij / (r^atom_i + r^atom_j), as a measure rather than the
absolute distances, r_ij, to account for the fact that different
atoms/species have different sizes.  The function uses the
valence-ionic radius estimator implemented in Pymatgen.</p>
<p>The features can be flattened so a uniform-length vector is returned for
each material, regardless of the number of sites in each structure.
Returning flat output REQUIRES fitting (using self.fit(…)). If fit,
structures having fewer sites than the max sites among the fitting
structures are extended with NaNs; structures with more sites are truncated.
To return non-flat (i.e., requiring further processing) features so that
no features are NaN and no distances are truncated, use flatten=False.</p>
<dl>
<dt>Features:</dt><dd><p>If using flatten=True:
site #{number} min. rel. dist. (float): The minimum relative distance of</p>
<blockquote>
<div><p>site {number}</p>
</div></blockquote>
<dl class="simple">
<dt>site #{number} specie (str): The string representing the specie at site</dt><dd><p>{number}</p>
</dd>
<dt>site #{number} neighbor specie(s) (str, tuple(str)): The neighbor specie</dt><dd><p>used to determine the minimum relative distance with respect to site
{number}. If multiple neighbor sites have equivalent minimum
relative distances,all these sites are listed in a tuple.</p>
</dd>
</dl>
<p>If using flatten=False:
minimum relative distance of each site ([float]): List of the minimum</p>
<blockquote>
<div><p>relative distance for each site. Structures with different numbers
of sites will return a different length vector.</p>
</div></blockquote>
</dd>
<dt>Args:</dt><dd><dl class="simple">
<dt>cutoff (float): (absolute) distance up to which tentative closest</dt><dd><p>neighbors (on the basis of relative distances) are to be determined.</p>
</dd>
<dt>flatten (bool): If True, returns a uniform length feature vector for</dt><dd><p>each structure regardless of the number of sites in the structure.
If True, you must call .fit() before featurizing.</p>
</dd>
<dt>include_distances (bool): Include the numerical minimum relative</dt><dd><p>distance in the returned features. Only used if flatten=True.</p>
</dd>
<dt>include_species (bool): Include the species for each site and the</dt><dd><p>species of the neighbor (as determined by minimum rel. distance).
Only used as flatten=True.</p>
</dd>
</dl>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.MinimumRelativeDistances.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cutoff</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">10.0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flatten</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_distances</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_species</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.MinimumRelativeDistances.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.MinimumRelativeDistances.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.MinimumRelativeDistances.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.MinimumRelativeDistances.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.MinimumRelativeDistances.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.MinimumRelativeDistances.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.MinimumRelativeDistances.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Get minimum relative distances of all sites of the input structure.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>s: Pymatgen Structure object.</p>
</dd>
<dt>Returns:</dt><dd><dl class="simple">
<dt>dists_relative_min: (list of floats) list of all minimum relative</dt><dd><p>distances (i.e., for all sites).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.MinimumRelativeDistances.fit">
<span class="sig-name descname"><span class="pre">fit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">X</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.MinimumRelativeDistances.fit" title="Permalink to this definition">¶</a></dt>
<dd><p>Fit the MRD featurizer to a list of structures.
Args:</p>
<blockquote>
<div><p>X ([Structure]): A list of pymatgen structures.
y : unused (added for consistency with overridden method signature)</p>
</div></blockquote>
<dl class="simple">
<dt>Returns:</dt><dd><p>self</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.MinimumRelativeDistances.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.MinimumRelativeDistances.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.StructuralHeterogeneity">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.bonding.</span></span><span class="sig-name descname"><span class="pre">StructuralHeterogeneity</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">weight</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'area'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stats</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">('minimum',</span> <span class="pre">'maximum',</span> <span class="pre">'range',</span> <span class="pre">'mean',</span> <span class="pre">'avg_dev')</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.StructuralHeterogeneity" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>Variance in the bond lengths and atomic volumes in a structure</p>
<p>These features are based on several statistics derived from the Voronoi
tessellation of a structure. The first set of features relate to the
variance in the average bond length across all atoms in the structure.
The second relate to the variance of bond lengths between each neighbor
of each atom. The final feature is the variance in Voronoi cell sizes
across the structure.</p>
<p>We define the ‘average bond length’ of a site as the weighted average of
the bond lengths for all neighbors. By default, the weight is the
area of the face between the sites.</p>
<p>The ‘neighbor distance variation’ is defined as the weighted mean absolute
deviation in both length for all neighbors of a particular site. As before,
the weight is according to face area by default. For this statistic, we
divide the mean absolute deviation by the mean neighbor distance for that
site.</p>
<dl class="simple">
<dt>Features:</dt><dd><dl class="simple">
<dt>mean absolute deviation in relative bond length - Mean absolute deviation</dt><dd><p>in the average bond lengths for all sites, divided by the
mean average bond length</p>
</dd>
<dt>max relative bond length - Maximum average bond length, divided by the</dt><dd><p>mean average bond length</p>
</dd>
<dt>min relative bond length - Minimum average bond length, divided by the</dt><dd><p>mean average bond length</p>
</dd>
<dt>[stat] neighbor distance variation - Statistic (e.g., mean) of the</dt><dd><p>neighbor distance variation</p>
</dd>
<dt>mean absolute deviation in relative cell size - Mean absolute deviation</dt><dd><p>in the Voronoi cell volume across all sites in the structure.
Divided by the mean Voronoi cell volume.</p>
</dd>
</dl>
</dd>
<dt>References:</dt><dd><p><a class="reference external" href="http://link.aps.org/doi/10.1103/PhysRevB.96.024104">Ward et al. _PRB_ 2017</a></p>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.StructuralHeterogeneity.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">weight</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'area'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stats</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">('minimum',</span> <span class="pre">'maximum',</span> <span class="pre">'range',</span> <span class="pre">'mean',</span> <span class="pre">'avg_dev')</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.StructuralHeterogeneity.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.StructuralHeterogeneity.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.StructuralHeterogeneity.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.StructuralHeterogeneity.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.StructuralHeterogeneity.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.StructuralHeterogeneity.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">strc</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.StructuralHeterogeneity.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Main featurizer function, which has to be implemented
in any derived featurizer subclass.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>x: input data to featurize (type depends on featurizer).</p>
</dd>
<dt>Returns:</dt><dd><p>(list) one or more features.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.bonding.StructuralHeterogeneity.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.bonding.StructuralHeterogeneity.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.structure.composite">
<span id="matminer-featurizers-structure-composite-module"></span><h2>matminer.featurizers.structure.composite module<a class="headerlink" href="#module-matminer.featurizers.structure.composite" title="Permalink to this heading">¶</a></h2>
<p>Structure featurizers producing more than one kind of structure feature data.</p>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.composite.JarvisCFID">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.composite.</span></span><span class="sig-name descname"><span class="pre">JarvisCFID</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">use_cell</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_chem</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_chg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_rdf</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_adf</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_ddf</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_nn</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.composite.JarvisCFID" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>Classical Force-Field Inspired Descriptors (CFID) from Jarvis-ML.</p>
<p>Chemo-structural descriptors from five different sub-methods, including
pairwise radial, nearest neighbor, bond-angle, dihedral-angle and
core-charge distributions. With all descriptors enabled, there are 1,557
features per structure.</p>
<p>Adapted from the nist/jarvis package hosted at:
<a class="reference external" href="https://github.com/usnistgov/jarvis">https://github.com/usnistgov/jarvis</a></p>
<dl>
<dt>Find details at: <a class="reference external" href="https://journals.aps.org/prmaterials/abstract/10.1103/">https://journals.aps.org/prmaterials/abstract/10.1103/</a></dt><dd><p>PhysRevMaterials.2.083801</p>
</dd>
<dt>Args/Features:</dt><dd><dl class="simple">
<dt>use_cell (bool): Use structure cell descriptors (4 features, based</dt><dd><p>on DensityFeatures and log volume per atom).</p>
</dd>
</dl>
<p>use_chem (bool): Use chemical composition descriptors (438 features)
use_chg (bool): Use core charge descriptors (378 features)
use_adf (bool): Use angular distribution function (179 features x 2, one</p>
<blockquote>
<div><p>set of features for each cutoff).</p>
</div></blockquote>
<p>use_rdf (bool): Use radial distribution function (100 features)
use_ddf (bool): Use dihedral angle distribution function (179 features)
use_nn (bool): Use nearest neighbors (100 descriptors)</p>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.composite.JarvisCFID.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">use_cell</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_chem</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_chg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_rdf</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_adf</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_ddf</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_nn</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.composite.JarvisCFID.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.composite.JarvisCFID.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.composite.JarvisCFID.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.composite.JarvisCFID.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.composite.JarvisCFID.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.composite.JarvisCFID.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.composite.JarvisCFID.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Get chemo-structural CFID descriptors</p>
<dl class="simple">
<dt>Args:</dt><dd><p>s: Structure object</p>
</dd>
<dt>Returns:</dt><dd><p>(np.ndarray) Final descriptors</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.composite.JarvisCFID.get_chem">
<span class="sig-name descname"><span class="pre">get_chem</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">element</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.composite.JarvisCFID.get_chem" title="Permalink to this definition">¶</a></dt>
<dd><p>Get chemical descriptors for an element</p>
<dl class="simple">
<dt>Args:</dt><dd><p>element: element name</p>
</dd>
<dt>Returns:</dt><dd><p>arr: descriptor array value</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.composite.JarvisCFID.get_chg">
<span class="sig-name descname"><span class="pre">get_chg</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">element</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.composite.JarvisCFID.get_chg" title="Permalink to this definition">¶</a></dt>
<dd><p>Get charge descriptors for an element</p>
<dl class="simple">
<dt>Args:</dt><dd><p>element: element name</p>
</dd>
<dt>Returns:</dt><dd><p>arr: descriptor array values</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.composite.JarvisCFID.get_distributions">
<span class="sig-name descname"><span class="pre">get_distributions</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">structure</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">c_size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">10.0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_cut</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">5.0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.composite.JarvisCFID.get_distributions" title="Permalink to this definition">¶</a></dt>
<dd><p>Get radial and angular distribution functions</p>
<dl class="simple">
<dt>Args:</dt><dd><p>structure: Structure object
c_size: max. cell size
max_cut: max. bond cut-off for angular distribution</p>
</dd>
<dt>Returns:</dt><dd><p>adfa, adfb, ddf, rdf, bondo
Angular distribution up to first cut-off
Angular distribution up to second cut-off
Dihedral angle distribution up to first cut-off
Radial distribution function
Bond order distribution</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.composite.JarvisCFID.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.composite.JarvisCFID.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.structure.matrix">
<span id="matminer-featurizers-structure-matrix-module"></span><h2>matminer.featurizers.structure.matrix module<a class="headerlink" href="#module-matminer.featurizers.structure.matrix" title="Permalink to this heading">¶</a></h2>
<p>Structure featurizers generating a matrix for each structure.</p>
<p>Most matrix structure featurizers contain the ability to flatten matrices to be dataframe-friendly.</p>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.matrix.CoulombMatrix">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.matrix.</span></span><span class="sig-name descname"><span class="pre">CoulombMatrix</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">diag_elems</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flatten</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.matrix.CoulombMatrix" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>The Coulomb matrix, a representation of nuclear coulombic interaction.</p>
<p>Generate the Coulomb matrix, M, of the input structure (or molecule). The
Coulomb matrix was put forward by Rupp et al. (Phys. Rev. Lett. 108, 058301,
2012) and is defined by off-diagonal elements M_ij = Z_i*Z_j/<a href="#id14"><span class="problematic" id="id15">|R_i-R_j|</span></a> and
diagonal elements 0.5*Z_i^2.4, where Z_i and R_i denote the nuclear charge
and the position of atom i, respectively.</p>
<p>Coulomb Matrix features are flattened (for ML-readiness) by default. Use
fit before featurizing to use flattened features. To return the matrix form,
set flatten=False.</p>
<dl class="simple">
<dt>Args:</dt><dd><dl class="simple">
<dt>diag_elems (bool): flag indication whether (True, default) to use</dt><dd><p>the original definition of the diagonal elements; if set to False,
the diagonal elements are set to 0</p>
</dd>
<dt>flatten (bool): If True, returns a flattened vector based on eigenvalues</dt><dd><p>of the matrix form. Otherwise, returns a matrix object (single
feature), which will likely need to be processed further.</p>
</dd>
</dl>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.matrix.CoulombMatrix.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">diag_elems</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flatten</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.matrix.CoulombMatrix.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.matrix.CoulombMatrix.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.matrix.CoulombMatrix.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.matrix.CoulombMatrix.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.matrix.CoulombMatrix.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.matrix.CoulombMatrix.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.matrix.CoulombMatrix.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Get Coulomb matrix of input structure.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>s: input Structure (or Molecule) object.</p>
</dd>
<dt>Returns:</dt><dd><p>m: (Nsites x Nsites matrix) Coulomb matrix.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.matrix.CoulombMatrix.fit">
<span class="sig-name descname"><span class="pre">fit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">X</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.matrix.CoulombMatrix.fit" title="Permalink to this definition">¶</a></dt>
<dd><p>Fit the Coulomb Matrix to a list of structures.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>X ([Structure]): A list of pymatgen structures.
y : unused (added for consistency with overridden method signature)</p>
</dd>
<dt>Returns:</dt><dd><p>self</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.matrix.CoulombMatrix.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.matrix.CoulombMatrix.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.matrix.OrbitalFieldMatrix">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.matrix.</span></span><span class="sig-name descname"><span class="pre">OrbitalFieldMatrix</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">period_tag</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flatten</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.matrix.OrbitalFieldMatrix" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>Representation based on the valence shell electrons of neighboring atoms.</p>
<p>Each atom is described by a 32-element vector (or 39-element vector, see
period tag for details) uniquely representing the valence subshell.
A 32x32 (39x39) matrix is formed by multiplying two atomic vectors.
An OFM for an atomic environment is the sum of these matrices for each atom
the center atom coordinates with multiplied by a distance function
(In this case, 1/r times the weight of the coordinating atom in the Voronoi</p>
<blockquote>
<div><p>Polyhedra method). The OFM of a structure or molecule is the average of the
OFMs for all the sites in the structure.</p>
</div></blockquote>
<dl class="simple">
<dt>Args:</dt><dd><dl class="simple">
<dt>period_tag (bool): In the original OFM, an element is represented</dt><dd><p>by a vector of length 32, where each element is 1 or 0,
which represents the valence subshell of the element.
With period_tag=True, the vector size is increased
to 39, where the 7 extra elements represent the period
of the element. Note lanthanides are treated as period 6,
actinides as period 7. Default False as in the original paper.</p>
</dd>
<dt>flatten (bool): Flatten the avg OFM to a 1024-vector (if period_tag</dt><dd><p>False) or a 1521-vector (if period_tag=True).</p>
</dd>
</dl>
</dd>
<dt>…attribute:: size</dt><dd><p>Either 32 or 39, the size of the vectors used to describe elements.</p>
</dd>
<dt>Reference:</dt><dd><p><cite>Pham et al. _Sci Tech Adv Mat_. 2017 &lt;http://dx.doi.org/10.1080/14686996.2017.1378060&gt;_</cite></p>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.matrix.OrbitalFieldMatrix.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">period_tag</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flatten</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.__init__" title="Permalink to this definition">¶</a></dt>
<dd><p>Initialize the featurizer</p>
<dl class="simple">
<dt>Args:</dt><dd><dl class="simple">
<dt>period_tag (bool): In the original OFM, an element is represented</dt><dd><p>by a vector of length 32, where each element is 1 or 0,
which represents the valence subshell of the element.
With period_tag=True, the vector size is increased
to 39, where the 7 extra elements represent the period
of the element. Note lanthanides are treated as period 6,
actinides as period 7. Default False as in the original paper.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.matrix.OrbitalFieldMatrix.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.matrix.OrbitalFieldMatrix.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.matrix.OrbitalFieldMatrix.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Makes a supercell for structure s (to protect sites
from coordinating with themselves), and then finds the mean
of the orbital field matrices of each site to characterize
a structure</p>
<dl class="simple">
<dt>Args:</dt><dd><p>s (Structure): structure to characterize</p>
</dd>
<dt>Returns:</dt><dd><dl class="simple">
<dt>mean_ofm (size X size matrix): orbital field matrix</dt><dd><p>characterizing s</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.matrix.OrbitalFieldMatrix.get_atom_ofms">
<span class="sig-name descname"><span class="pre">get_atom_ofms</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">struct</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">symm</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.get_atom_ofms" title="Permalink to this definition">¶</a></dt>
<dd><p>Calls get_single_ofm for every site in struct. If symm=True,
get_single_ofm is called for symmetrically distinct sites, and
counts is constructed such that ofms[i] occurs counts[i] times
in the structure</p>
<dl>
<dt>Args:</dt><dd><p>struct (Structure): structure for find ofms for
symm (bool): whether to calculate ofm for only symmetrically</p>
<blockquote>
<div><p>distinct sites</p>
</div></blockquote>
</dd>
<dt>Returns:</dt><dd><p>ofms ([size X size matrix] X len(struct)): ofms for struct
if symm:</p>
<blockquote>
<div><dl class="simple">
<dt>ofms ([size X size matrix] X number of symmetrically distinct sites):</dt><dd><p>ofms for struct</p>
</dd>
</dl>
<p>counts: number of identical sites for each ofm</p>
</div></blockquote>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.matrix.OrbitalFieldMatrix.get_mean_ofm">
<span class="sig-name descname"><span class="pre">get_mean_ofm</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ofms</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">counts</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.get_mean_ofm" title="Permalink to this definition">¶</a></dt>
<dd><p>Averages a list of ofms, weights by counts</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.matrix.OrbitalFieldMatrix.get_ohv">
<span class="sig-name descname"><span class="pre">get_ohv</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sp</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">period_tag</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.get_ohv" title="Permalink to this definition">¶</a></dt>
<dd><p>Get the “one-hot-vector” for pymatgen Element sp. This 32 or 39-length
vector represents the valence shell of the given element.
Args:</p>
<blockquote>
<div><p>sp (Element): element whose ohv should be returned
period_tag (bool): If true, the vector contains items</p>
<blockquote>
<div><p>corresponding to the period of the element</p>
</div></blockquote>
</div></blockquote>
<dl class="simple">
<dt>Returns:</dt><dd><p>my_ohv (numpy array length 39 if period_tag, else 32): ohv for sp</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.matrix.OrbitalFieldMatrix.get_single_ofm">
<span class="sig-name descname"><span class="pre">get_single_ofm</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">site</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">site_dict</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.get_single_ofm" title="Permalink to this definition">¶</a></dt>
<dd><p>Gets the orbital field matrix for a single chemical environment,
where site is the center atom whose environment is characterized and
site_dict is a dictionary of site : weight, where the weights are the
Voronoi Polyhedra weights of the corresponding coordinating sites.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>site (Site): center atom
site_dict (dict of Site:float): chemical environment</p>
</dd>
<dt>Returns:</dt><dd><p>atom_ofm (size X size numpy matrix): ofm for site</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.matrix.OrbitalFieldMatrix.get_structure_ofm">
<span class="sig-name descname"><span class="pre">get_structure_ofm</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">struct</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.get_structure_ofm" title="Permalink to this definition">¶</a></dt>
<dd><p>Calls get_mean_ofm on the results of get_atom_ofms
to give a size X size matrix characterizing a structure</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.matrix.OrbitalFieldMatrix.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.matrix.SineCoulombMatrix">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.matrix.</span></span><span class="sig-name descname"><span class="pre">SineCoulombMatrix</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">diag_elems</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flatten</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.matrix.SineCoulombMatrix" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>A variant of the Coulomb matrix developed for periodic crystals.</p>
<p>This function generates a variant of the Coulomb matrix developed
for periodic crystals by Faber et al. (Inter. J. Quantum Chem.
115, 16, 2015). It is identical to the Coulomb matrix, except
that the inverse distance function is replaced by the inverse of a
sin**2 function of the vector between the sites which is periodic
in the dimensions of the structure lattice. See paper for details.</p>
<p>Coulomb Matrix features are flattened (for ML-readiness) by default. Use
fit before featurizing to use flattened features. To return the matrix form,
set flatten=False.</p>
<dl class="simple">
<dt>Args:</dt><dd><dl class="simple">
<dt>diag_elems (bool): flag indication whether (True, default) to use</dt><dd><p>the original definition of the diagonal elements; if set to False,
the diagonal elements are set to 0</p>
</dd>
<dt>flatten (bool): If True, returns a flattened vector based on eigenvalues</dt><dd><p>of the matrix form. Otherwise, returns a matrix object (single
feature), which will likely need to be processed further.</p>
</dd>
</dl>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.matrix.SineCoulombMatrix.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">diag_elems</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flatten</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.matrix.SineCoulombMatrix.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.matrix.SineCoulombMatrix.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.matrix.SineCoulombMatrix.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.matrix.SineCoulombMatrix.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.matrix.SineCoulombMatrix.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.matrix.SineCoulombMatrix.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.matrix.SineCoulombMatrix.featurize" title="Permalink to this definition">¶</a></dt>
<dd><dl class="simple">
<dt>Args:</dt><dd><p>s (Structure or Molecule): input structure (or molecule)</p>
</dd>
<dt>Returns:</dt><dd><p>(Nsites x Nsites matrix) Sine matrix or</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.matrix.SineCoulombMatrix.fit">
<span class="sig-name descname"><span class="pre">fit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">X</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.matrix.SineCoulombMatrix.fit" title="Permalink to this definition">¶</a></dt>
<dd><p>Fit the Sine Coulomb Matrix to a list of structures.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>X ([Structure]): A list of pymatgen structures.
y : unused (added for consistency with overridden method signature)</p>
</dd>
<dt>Returns:</dt><dd><p>self</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.matrix.SineCoulombMatrix.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.matrix.SineCoulombMatrix.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.structure.misc">
<span id="matminer-featurizers-structure-misc-module"></span><h2>matminer.featurizers.structure.misc module<a class="headerlink" href="#module-matminer.featurizers.structure.misc" title="Permalink to this heading">¶</a></h2>
<p>Miscellaneous structure featurizers.</p>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.misc.EwaldEnergy">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.misc.</span></span><span class="sig-name descname"><span class="pre">EwaldEnergy</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">accuracy</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">4</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">per_atom</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.misc.EwaldEnergy" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>Compute the energy from Coulombic interactions.</p>
<p>Note: The energy is computed using _charges already defined for the <a href="#id16"><span class="problematic" id="id17">structure_</span></a>.</p>
<dl class="simple">
<dt>Features:</dt><dd><p>ewald_energy - Coulomb interaction energy of the structure</p>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.misc.EwaldEnergy.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">accuracy</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">4</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">per_atom</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.misc.EwaldEnergy.__init__" title="Permalink to this definition">¶</a></dt>
<dd><dl class="simple">
<dt>Args:</dt><dd><p>accuracy (int): Accuracy of Ewald summation, number of decimal places</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.misc.EwaldEnergy.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.misc.EwaldEnergy.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.misc.EwaldEnergy.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.misc.EwaldEnergy.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.misc.EwaldEnergy.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">strc</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.misc.EwaldEnergy.featurize" title="Permalink to this definition">¶</a></dt>
<dd><dl class="simple">
<dt>Args:</dt><dd><p>(Structure) - Structure being analyzed</p>
</dd>
<dt>Returns:</dt><dd><p>([float]) - Electrostatic energy of the structure</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.misc.EwaldEnergy.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.misc.EwaldEnergy.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.misc.StructureComposition">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.misc.</span></span><span class="sig-name descname"><span class="pre">StructureComposition</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">featurizer</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.misc.StructureComposition" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>Features related to the composition of a structure</p>
<p>This class is just a wrapper that calls a composition-based featurizer
on the composition of a Structure</p>
<dl class="simple">
<dt>Features:</dt><dd><ul class="simple">
<li><p>Depends on the featurizer</p></li>
</ul>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.misc.StructureComposition.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">featurizer</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.misc.StructureComposition.__init__" title="Permalink to this definition">¶</a></dt>
<dd><p>Initialize the featurizer</p>
<dl class="simple">
<dt>Args:</dt><dd><p>featurizer (BaseFeaturizer) - Composition-based featurizer</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.misc.StructureComposition.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.misc.StructureComposition.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.misc.StructureComposition.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.misc.StructureComposition.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.misc.StructureComposition.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">strc</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.misc.StructureComposition.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Main featurizer function, which has to be implemented
in any derived featurizer subclass.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>x: input data to featurize (type depends on featurizer).</p>
</dd>
<dt>Returns:</dt><dd><p>(list) one or more features.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.misc.StructureComposition.fit">
<span class="sig-name descname"><span class="pre">fit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">X</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">fit_kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.misc.StructureComposition.fit" title="Permalink to this definition">¶</a></dt>
<dd><p>Update the parameters of this featurizer based on available data</p>
<dl class="simple">
<dt>Args:</dt><dd><p>X - [list of tuples], training data</p>
</dd>
<dt>Returns:</dt><dd><p>self</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.misc.StructureComposition.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.misc.StructureComposition.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.misc.XRDPowderPattern">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.misc.</span></span><span class="sig-name descname"><span class="pre">XRDPowderPattern</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">two_theta_range</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">(0,</span> <span class="pre">127)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bw_method</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.05</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pattern_length</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.misc.XRDPowderPattern" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>1D array representing powder diffraction of a structure as calculated by
pymatgen. The powder is smeared / normalized according to gaussian_kde.</p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.misc.XRDPowderPattern.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">two_theta_range</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">(0,</span> <span class="pre">127)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bw_method</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.05</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pattern_length</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.misc.XRDPowderPattern.__init__" title="Permalink to this definition">¶</a></dt>
<dd><p>Initialize the featurizer.</p>
<dl>
<dt>Args:</dt><dd><dl class="simple">
<dt>two_theta_range ([float of length 2]): Tuple for range of</dt><dd><p>two_thetas to calculate in degrees. Defaults to (0, 90). Set to
None if you want all diffracted beams within the limiting
sphere of radius 2 / wavelength.</p>
</dd>
</dl>
<p>bw_method (float): how much to smear the XRD pattern
pattern_length (float): length of final array; defaults to one value</p>
<blockquote>
<div><p>per degree (i.e. two_theta_range + 1)</p>
</div></blockquote>
<dl class="simple">
<dt><a href="#id1"><span class="problematic" id="id2">**</span></a>kwargs: any other arguments to pass into pymatgen’s XRDCalculator,</dt><dd><p>such as the type of radiation.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.misc.XRDPowderPattern.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.misc.XRDPowderPattern.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.misc.XRDPowderPattern.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.misc.XRDPowderPattern.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.misc.XRDPowderPattern.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">strc</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.misc.XRDPowderPattern.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Main featurizer function, which has to be implemented
in any derived featurizer subclass.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>x: input data to featurize (type depends on featurizer).</p>
</dd>
<dt>Returns:</dt><dd><p>(list) one or more features.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.misc.XRDPowderPattern.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.misc.XRDPowderPattern.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.structure.order">
<span id="matminer-featurizers-structure-order-module"></span><h2>matminer.featurizers.structure.order module<a class="headerlink" href="#module-matminer.featurizers.structure.order" title="Permalink to this heading">¶</a></h2>
<p>Structure featurizers based on packing or ordering.</p>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.order.ChemicalOrdering">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.order.</span></span><span class="sig-name descname"><span class="pre">ChemicalOrdering</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">shells</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">(1,</span> <span class="pre">2,</span> <span class="pre">3)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">weight</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'area'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.order.ChemicalOrdering" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>How much the ordering of species in the structure differs from random</p>
<p>These parameters describe how much the ordering of all species in a
structure deviates from random using a Warren-Cowley-like ordering
parameter. The first step of this calculation is to determine the nearest
neighbor shells of each site. Then, for each shell a degree of order for
each type is determined by computing:</p>
<p><span class="math">\alpha (t,s) = 1 - \frac{\sum_n w_n \delta (t - t_n)}{x_t \sum_n w_n}</span></p>
<p>where <span class="math">w_n</span> is the weight associated with a certain neighbor,
<span class="math">t_p</span> is the type of the neighbor, and <span class="math">x_t</span> is the fraction
of type t in the structure. For atoms that are randomly dispersed in a
structure, this formula yields 0 for all types. For structures where
each site is surrounded only by atoms of another type, this formula
yields large values of <span class="math">alpha</span>.</p>
<p>The mean absolute value of this parameter across all sites is used
as a feature.</p>
<dl class="simple">
<dt>Features:</dt><dd><dl class="simple">
<dt>mean ordering parameter shell [n] - Mean ordering parameter for</dt><dd><p>atoms in the n&lt;sup&gt;th&lt;/sup&gt; neighbor shell</p>
</dd>
</dl>
</dd>
<dt>References:</dt><dd><p><a class="reference external" href="http://link.aps.org/doi/10.1103/PhysRevB.96.024104">Ward et al. _PRB_ 2017</a></p>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.order.ChemicalOrdering.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">shells</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">(1,</span> <span class="pre">2,</span> <span class="pre">3)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">weight</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'area'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.order.ChemicalOrdering.__init__" title="Permalink to this definition">¶</a></dt>
<dd><p>Initialize the featurizer</p>
<dl class="simple">
<dt>Args:</dt><dd><p>shells ([int]) - Which neighbor shells to evaluate
weight (str) - Attribute used to weigh neighbor contributions</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.order.ChemicalOrdering.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.order.ChemicalOrdering.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.order.ChemicalOrdering.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.order.ChemicalOrdering.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.order.ChemicalOrdering.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">strc</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.order.ChemicalOrdering.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Main featurizer function, which has to be implemented
in any derived featurizer subclass.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>x: input data to featurize (type depends on featurizer).</p>
</dd>
<dt>Returns:</dt><dd><p>(list) one or more features.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.order.ChemicalOrdering.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.order.ChemicalOrdering.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.order.DensityFeatures">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.order.</span></span><span class="sig-name descname"><span class="pre">DensityFeatures</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">desired_features</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.order.DensityFeatures" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>Calculates density and density-like features</p>
<dl class="simple">
<dt>Features:</dt><dd><ul class="simple">
<li><p>density</p></li>
<li><p>volume per atom</p></li>
<li><p>(“vpa”), and packing fraction</p></li>
</ul>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.order.DensityFeatures.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">desired_features</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.order.DensityFeatures.__init__" title="Permalink to this definition">¶</a></dt>
<dd><dl class="simple">
<dt>Args:</dt><dd><dl class="simple">
<dt>desired_features: [str] - choose from “density”, “vpa”,</dt><dd><p>“packing fraction”</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.order.DensityFeatures.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.order.DensityFeatures.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.order.DensityFeatures.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.order.DensityFeatures.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.order.DensityFeatures.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.order.DensityFeatures.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Main featurizer function, which has to be implemented
in any derived featurizer subclass.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>x: input data to featurize (type depends on featurizer).</p>
</dd>
<dt>Returns:</dt><dd><p>(list) one or more features.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.order.DensityFeatures.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.order.DensityFeatures.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.order.DensityFeatures.precheck">
<span class="sig-name descname"><span class="pre">precheck</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Structure</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.11)"><span class="pre">bool</span></a></span></span><a class="headerlink" href="#matminer.featurizers.structure.order.DensityFeatures.precheck" title="Permalink to this definition">¶</a></dt>
<dd><p>Precheck a single entry. DensityFeatures does not work for disordered
structures. To precheck an entire dataframe (qnd automatically gather
the fraction of structures that will pass the precheck), please use
precheck_dataframe.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>s (pymatgen.Structure): The structure to precheck.</p>
</dd>
<dt>Returns:</dt><dd><p>(bool): If True, s passed the precheck; otherwise, it failed.</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.order.MaximumPackingEfficiency">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.order.</span></span><span class="sig-name descname"><span class="pre">MaximumPackingEfficiency</span></span><a class="headerlink" href="#matminer.featurizers.structure.order.MaximumPackingEfficiency" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>Maximum possible packing efficiency of this structure</p>
<p>Uses a Voronoi tessellation to determine the largest radius each atom
can have before any atoms touches any one of their neighbors. Given the
maximum radius size, this class computes the maximum packing efficiency
of the structure as a feature.</p>
<dl class="simple">
<dt>Features:</dt><dd><p>max packing efficiency - Maximum possible packing efficiency</p>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.order.MaximumPackingEfficiency.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.order.MaximumPackingEfficiency.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.order.MaximumPackingEfficiency.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.order.MaximumPackingEfficiency.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.order.MaximumPackingEfficiency.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">strc</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.order.MaximumPackingEfficiency.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Main featurizer function, which has to be implemented
in any derived featurizer subclass.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>x: input data to featurize (type depends on featurizer).</p>
</dd>
<dt>Returns:</dt><dd><p>(list) one or more features.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.order.MaximumPackingEfficiency.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.order.MaximumPackingEfficiency.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.order.StructuralComplexity">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.order.</span></span><span class="sig-name descname"><span class="pre">StructuralComplexity</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symprec</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.1</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.order.StructuralComplexity" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>Shannon information entropy of a structure.</p>
<p>This descriptor treat a structure as a message
to evaluate structural complexity (<span class="math">S</span>)
using the following equation:</p>
<p><span class="math">S = - v \sum_{i=1}^{k} p_i \log_2 p_i</span></p>
<p><span class="math">p_i = m_i / v</span></p>
<p>where <span class="math">v</span> is the total number of atoms in the unit cell,
<span class="math">p_i</span> is the probability mass function,
<span class="math">k</span> is the number of symmetrically inequivalent sites, and
<span class="math">m_i</span> is the number of sites classified in <span class="math">i</span> th
symmetrically inequivalent site.</p>
<dl class="simple">
<dt>Features:</dt><dd><ul class="simple">
<li><p>information entropy (bits/atom)</p></li>
<li><p>information entropy (bits/unit cell)</p></li>
</ul>
</dd>
<dt>Args:</dt><dd><p>symprec: precision for symmetrizing a structure</p>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.order.StructuralComplexity.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">symprec</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.1</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.order.StructuralComplexity.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.order.StructuralComplexity.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.order.StructuralComplexity.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.order.StructuralComplexity.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.order.StructuralComplexity.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.order.StructuralComplexity.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">struct</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.order.StructuralComplexity.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Main featurizer function, which has to be implemented
in any derived featurizer subclass.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>x: input data to featurize (type depends on featurizer).</p>
</dd>
<dt>Returns:</dt><dd><p>(list) one or more features.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.order.StructuralComplexity.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.order.StructuralComplexity.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.structure.rdf">
<span id="matminer-featurizers-structure-rdf-module"></span><h2>matminer.featurizers.structure.rdf module<a class="headerlink" href="#module-matminer.featurizers.structure.rdf" title="Permalink to this heading">¶</a></h2>
<p>Structure featurizers implementing radial distribution functions.</p>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.rdf.</span></span><span class="sig-name descname"><span class="pre">ElectronicRadialDistributionFunction</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cutoff</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">20</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dr</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.05</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>Calculate the inherent electronic radial distribution function (ReDF)</p>
<p>The ReDF is defined according to Willighagen et al., Acta Cryst., 2005, B61,
29-36.</p>
<p>The ReDF is a structure-integral RDF (i.e., summed over
all sites) in which the positions of neighboring sites
are weighted by electrostatic interactions inferred
from atomic partial charges. Atomic charges are obtained
from the ValenceIonicRadiusEvaluator class.</p>
<p>WARNING: The ReDF needs oxidation states to work correctly.</p>
<dl>
<dt>Args:</dt><dd><dl class="simple">
<dt>cutoff: (float) distance up to which the ReDF is to be</dt><dd><p>calculated.</p>
</dd>
</dl>
<p>dr: (float) width of bins (“x”-axis) of ReDF (default: 0.05 A).</p>
</dd>
<dt>Attributes:</dt><dd><p>distances (np.ndarray): The distances at which each bin begins.</p>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cutoff</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">20</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dr</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.05</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Get ReDF of input structure.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>s: input Structure object.</p>
</dd>
</dl>
<p>Returns: (list) the ReDF</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.precheck">
<span class="sig-name descname"><span class="pre">precheck</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.11)"><span class="pre">bool</span></a></span></span><a class="headerlink" href="#matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.precheck" title="Permalink to this definition">¶</a></dt>
<dd><p>Check the structure to ensure the ReDF can be run.
Args:</p>
<blockquote>
<div><p>s (pymatgen. Structure): Structure to precheck</p>
</div></blockquote>
<dl class="simple">
<dt>Returns:</dt><dd><p>(bool)</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.rdf.PartialRadialDistributionFunction">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.rdf.</span></span><span class="sig-name descname"><span class="pre">PartialRadialDistributionFunction</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cutoff</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">20.0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bin_size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_elems</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">exclude_elems</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.rdf.PartialRadialDistributionFunction" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>Compute the partial radial distribution function (PRDF) of an xtal structure</p>
<p>The PRDF of a crystal structure is the radial distribution function broken
down for each pair of atom types.  The PRDF was proposed as a structural
descriptor by [Schutt <em>et al.</em>]
(<a class="reference external" href="https://journals.aps.org/prb/abstract/10.1103/PhysRevB.89.205118">https://journals.aps.org/prb/abstract/10.1103/PhysRevB.89.205118</a>)</p>
<dl class="simple">
<dt>Args:</dt><dd><p>cutoff: (float) distance up to which to calculate the RDF.
bin_size: (float) size of each bin of the (discrete) RDF.
include_elems: (list of string), list of elements that must be included in PRDF
exclude_elems: (list of string), list of elements that should not be included in PRDF</p>
</dd>
<dt>Features:</dt><dd><p>Each feature corresponds to the density of number of bonds
for a certain pair of elements at a certain range of
distances. For example, “Al-Al PRDF r=1.00-1.50” corresponds
to the density of Al-Al bonds between 1 and 1.5 distance units
By default, this featurizer generates RDFs for each pair
of elements in the training set.</p>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cutoff</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">20.0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bin_size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_elems</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">exclude_elems</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.compute_prdf">
<span class="sig-name descname"><span class="pre">compute_prdf</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.compute_prdf" title="Permalink to this definition">¶</a></dt>
<dd><p>Compute the PRDF for a structure</p>
<dl>
<dt>Args:</dt><dd><p>s: (Structure), structure to be evaluated</p>
</dd>
<dt>Returns:</dt><dd><p>dist_bins - float, start of each of the bins
prdf - dict, where the keys is a pair of elements (strings),</p>
<blockquote>
<div><p>and the value is the radial distribution function for those paris of elements</p>
</div></blockquote>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Get PRDF of the input structure.
Args:</p>
<blockquote>
<div><p>s: Pymatgen Structure object.</p>
</div></blockquote>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>prdf, dist: (tuple of arrays) the first element is a</dt><dd><p>dictionary where keys are tuples of element
names and values are PRDFs.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.fit">
<span class="sig-name descname"><span class="pre">fit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">X</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.fit" title="Permalink to this definition">¶</a></dt>
<dd><p>Define the list of elements to be included in the PRDF. By default,
the PRDF will include all of the elements in <cite>X</cite></p>
<dl>
<dt>Args:</dt><dd><dl class="simple">
<dt>X: (numpy array nx1) structures used in the training set. Each entry</dt><dd><p>must be Pymatgen Structure objects.</p>
</dd>
</dl>
<p>y: <em>Not used</em>
fit_kwargs: <em>not used</em></p>
</dd>
<dt>Returns:</dt><dd><p>self</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.precheck">
<span class="sig-name descname"><span class="pre">precheck</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.precheck" title="Permalink to this definition">¶</a></dt>
<dd><p>Precheck the structure is ordered.
Args:</p>
<blockquote>
<div><p>s: (pymatgen.Structure)</p>
</div></blockquote>
<dl class="simple">
<dt>Returns:</dt><dd><p>(bool): True if passing precheck, false if failing</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.rdf.RadialDistributionFunction">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.rdf.</span></span><span class="sig-name descname"><span class="pre">RadialDistributionFunction</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cutoff</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">20.0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bin_size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.1</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.rdf.RadialDistributionFunction" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>Calculate the radial distribution function (RDF) of a crystal structure.</p>
<dl class="simple">
<dt>Features:</dt><dd><ul class="simple">
<li><p>Radial distribution function. Each feature is the “density” of the
distribution at a certain radius.</p></li>
</ul>
</dd>
<dt>Args:</dt><dd><p>cutoff: (float) Angstrom distance up to which to calculate the RDF.
bin_size: (float) size in Angstrom of each bin of the (discrete) RDF.</p>
</dd>
<dt>Attributes:</dt><dd><dl class="simple">
<dt>bin_distances (np.Ndarray): The distances each bin represents. Can be</dt><dd><p>used for graphing the RDF.</p>
</dd>
</dl>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.rdf.RadialDistributionFunction.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cutoff</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">20.0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bin_size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.1</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.rdf.RadialDistributionFunction.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.rdf.RadialDistributionFunction.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.rdf.RadialDistributionFunction.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.rdf.RadialDistributionFunction.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.rdf.RadialDistributionFunction.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.rdf.RadialDistributionFunction.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.rdf.RadialDistributionFunction.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Get RDF of the input structure.
Args:</p>
<blockquote>
<div><p>s (Structure): Pymatgen Structure object.</p>
</div></blockquote>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>rdf: (iterable) the first element is the</dt><dd><p>normalized RDF, whereas the second element is
the inner radius of the RDF bin.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.rdf.RadialDistributionFunction.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.rdf.RadialDistributionFunction.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.rdf.RadialDistributionFunction.precheck">
<span class="sig-name descname"><span class="pre">precheck</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.rdf.RadialDistributionFunction.precheck" title="Permalink to this definition">¶</a></dt>
<dd><p>Precheck the structure is ordered.
Args:</p>
<blockquote>
<div><p>s: (pymatgen.Structure)</p>
</div></blockquote>
<dl class="simple">
<dt>Returns:</dt><dd><p>(bool): True if passing precheck, false if failing</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.featurizers.structure.rdf.get_rdf_bin_labels">
<span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.rdf.</span></span><span class="sig-name descname"><span class="pre">get_rdf_bin_labels</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">bin_distances</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cutoff</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.rdf.get_rdf_bin_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Common function for getting bin labels given the distances at which each
bin begins and the ending cutoff.
Args:</p>
<blockquote>
<div><p>bin_distances (np.ndarray): The distances at which each bin begins.
cutoff (float): The final cutoff value.</p>
</div></blockquote>
<dl class="simple">
<dt>Returns:</dt><dd><p>[str]: The feature labels for the <a href="#id4"><span class="problematic" id="id5">*</span></a>RDF</p>
</dd>
</dl>
</dd></dl>

</section>
<section id="module-matminer.featurizers.structure.sites">
<span id="matminer-featurizers-structure-sites-module"></span><h2>matminer.featurizers.structure.sites module<a class="headerlink" href="#module-matminer.featurizers.structure.sites" title="Permalink to this heading">¶</a></h2>
<p>Structure featurizers based on aggregating site features.</p>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.sites.</span></span><span class="sig-name descname"><span class="pre">PartialsSiteStatsFingerprint</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">site_featurizer</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stats</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">('mean',</span> <span class="pre">'std_dev')</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">min_oxi</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_oxi</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">covariance</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_elems</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">exclude_elems</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.structure.sites.SiteStatsFingerprint" title="matminer.featurizers.structure.sites.SiteStatsFingerprint"><code class="xref py py-class docutils literal notranslate"><span class="pre">SiteStatsFingerprint</span></code></a></p>
<p>Computes statistics of properties across all sites in a structure, and
breaks these down by element. This featurizer first uses a site featurizer
class (see site.py for options) to compute features of each site of a
specific element in a structure, and then computes features of the entire
structure by measuring statistics of each attribute.
Features:</p>
<blockquote>
<div><ul class="simple">
<li><p>Returns each statistic of each site feature, broken down by element</p></li>
</ul>
</div></blockquote>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">site_featurizer</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stats</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">('mean',</span> <span class="pre">'std_dev')</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">min_oxi</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_oxi</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">covariance</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_elems</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">exclude_elems</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.__init__" title="Permalink to this definition">¶</a></dt>
<dd><dl>
<dt>Args:</dt><dd><p>site_featurizer (BaseFeaturizer): a site-based featurizer
stats ([str]): list of weighted statistics to compute for each feature.</p>
<blockquote>
<div><p>If stats is None, a list is returned for each features
that contains the calculated feature for each site in the
structure.
<a href="#id6"><span class="problematic" id="id7">*</span></a>Note for nth mode, stat must be ‘n*_mode’; e.g. stat=’2nd_mode’</p>
</div></blockquote>
<dl class="simple">
<dt>min_oxi (int): minimum site oxidation state for inclusion (e.g.,</dt><dd><p>zero means metals/cations only)</p>
</dd>
</dl>
<p>max_oxi (int): maximum site oxidation state for inclusion
covariance (bool): Whether to compute the covariance of site features</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.compute_pssf">
<span class="sig-name descname"><span class="pre">compute_pssf</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">e</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.compute_pssf" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Get PSSF of the input structure.
Args:</p>
<blockquote>
<div><p>s: Pymatgen Structure object.</p>
</div></blockquote>
<dl class="simple">
<dt>Returns:</dt><dd><p>pssf: 1D array of each element’s ssf</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.fit">
<span class="sig-name descname"><span class="pre">fit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">X</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.fit" title="Permalink to this definition">¶</a></dt>
<dd><p>Define the list of elements to be included in the PRDF. By default,
the PRDF will include all of the elements in <cite>X</cite>
Args:</p>
<blockquote>
<div><dl class="simple">
<dt>X: (numpy array nx1) structures used in the training set. Each entry</dt><dd><p>must be Pymatgen Structure objects.</p>
</dd>
</dl>
<p>y: <em>Not used</em>
fit_kwargs: <em>not used</em></p>
</div></blockquote>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.sites.SiteStatsFingerprint">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.sites.</span></span><span class="sig-name descname"><span class="pre">SiteStatsFingerprint</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">site_featurizer</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stats</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">('mean',</span> <span class="pre">'std_dev')</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">min_oxi</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_oxi</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">covariance</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.sites.SiteStatsFingerprint" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>Computes statistics of properties across all sites in a structure.</p>
<p>This featurizer first uses a site featurizer class (see site.py for
options) to compute features of each site in a structure, and then computes
features of the entire structure by measuring statistics of each attribute.
Can optionally compute the statistics of only sites with certain ranges
of oxidation states (e.g., only anions).</p>
<dl class="simple">
<dt>Features:</dt><dd><ul class="simple">
<li><p>Returns each statistic of each site feature</p></li>
</ul>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.sites.SiteStatsFingerprint.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">site_featurizer</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stats</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">('mean',</span> <span class="pre">'std_dev')</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">min_oxi</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_oxi</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">covariance</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.sites.SiteStatsFingerprint.__init__" title="Permalink to this definition">¶</a></dt>
<dd><dl>
<dt>Args:</dt><dd><p>site_featurizer (BaseFeaturizer): a site-based featurizer
stats ([str]): list of weighted statistics to compute for each feature.</p>
<blockquote>
<div><p>If stats is None, a list is returned for each features
that contains the calculated feature for each site in the
structure.
<a href="#id8"><span class="problematic" id="id9">*</span></a>Note for nth mode, stat must be ‘n*_mode’; e.g. stat=’2nd_mode’</p>
</div></blockquote>
<dl class="simple">
<dt>min_oxi (int): minimum site oxidation state for inclusion (e.g.,</dt><dd><p>zero means metals/cations only)</p>
</dd>
</dl>
<p>max_oxi (int): maximum site oxidation state for inclusion
covariance (bool): Whether to compute the covariance of site features</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.sites.SiteStatsFingerprint.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.sites.SiteStatsFingerprint.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.sites.SiteStatsFingerprint.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.sites.SiteStatsFingerprint.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.sites.SiteStatsFingerprint.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.sites.SiteStatsFingerprint.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Main featurizer function, which has to be implemented
in any derived featurizer subclass.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>x: input data to featurize (type depends on featurizer).</p>
</dd>
<dt>Returns:</dt><dd><p>(list) one or more features.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.sites.SiteStatsFingerprint.fit">
<span class="sig-name descname"><span class="pre">fit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">X</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">fit_kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.sites.SiteStatsFingerprint.fit" title="Permalink to this definition">¶</a></dt>
<dd><p>Fit the SiteStatsFeaturizer using the fitting function of the underlying
site featurizer. Only applicable if the site featurizer is fittable.
See the “.fit()” method of the site_featurizer used to construct the
class for more information.
Args:</p>
<blockquote>
<div><p>X (Iterable):
y (optional, Iterable):
<a href="#id10"><span class="problematic" id="id11">**</span></a>fit_kwargs: Keyword arguments used by the fit function of the</p>
<blockquote>
<div><p>site featurizer class.</p>
</div></blockquote>
</div></blockquote>
<dl class="simple">
<dt>Returns:</dt><dd><p>self (SiteStatsFeaturizer)</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.sites.SiteStatsFingerprint.from_preset">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">from_preset</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">preset</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.sites.SiteStatsFingerprint.from_preset" title="Permalink to this definition">¶</a></dt>
<dd><p>Create a SiteStatsFingerprint class according to a preset</p>
<dl class="simple">
<dt>Args:</dt><dd><p>preset (str) - Name of preset
kwargs - Options for SiteStatsFingerprint</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.sites.SiteStatsFingerprint.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.sites.SiteStatsFingerprint.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.structure.symmetry">
<span id="matminer-featurizers-structure-symmetry-module"></span><h2>matminer.featurizers.structure.symmetry module<a class="headerlink" href="#module-matminer.featurizers.structure.symmetry" title="Permalink to this heading">¶</a></h2>
<p>Structure featurizers based on symmetry.</p>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.symmetry.Dimensionality">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.symmetry.</span></span><span class="sig-name descname"><span class="pre">Dimensionality</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">nn_method=&lt;pymatgen.analysis.local_env.CrystalNN</span> <span class="pre">object&gt;</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.symmetry.Dimensionality" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>Returns dimensionality of structure: 1 means linear chains of atoms OR
isolated atoms/no bonds, 2 means layered, 3 means 3D connected
structure. This feature is sensitive to bond length tables that you use.</p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.symmetry.Dimensionality.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">nn_method=&lt;pymatgen.analysis.local_env.CrystalNN</span> <span class="pre">object&gt;</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.symmetry.Dimensionality.__init__" title="Permalink to this definition">¶</a></dt>
<dd><dl class="simple">
<dt>Args:</dt><dd><dl class="simple">
<dt><a href="#id12"><span class="problematic" id="id13">**</span></a>nn_method: The nearest neighbor method used to determine atomic</dt><dd><p>connectivity.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.symmetry.Dimensionality.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.symmetry.Dimensionality.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.symmetry.Dimensionality.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.symmetry.Dimensionality.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.symmetry.Dimensionality.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.symmetry.Dimensionality.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Main featurizer function, which has to be implemented
in any derived featurizer subclass.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>x: input data to featurize (type depends on featurizer).</p>
</dd>
<dt>Returns:</dt><dd><p>(list) one or more features.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.symmetry.Dimensionality.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.symmetry.Dimensionality.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.structure.symmetry.</span></span><span class="sig-name descname"><span class="pre">GlobalSymmetryFeatures</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">desired_features</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="matminer.featurizers.html#matminer.featurizers.base.BaseFeaturizer" title="matminer.featurizers.base.BaseFeaturizer"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseFeaturizer</span></code></a></p>
<p>Determines symmetry features, e.g. spacegroup number and  crystal system</p>
<dl class="simple">
<dt>Features:</dt><dd><ul class="simple">
<li><p>Spacegroup number</p></li>
<li><p>Crystal system (1 of 7)</p></li>
<li><p>Centrosymmetry (has inversion symmetry)</p></li>
<li><p>Number of symmetry ops, obtained from the spacegroup</p></li>
</ul>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">desired_features</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.__init__" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.all_features">
<span class="sig-name descname"><span class="pre">all_features</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">['spacegroup_num',</span> <span class="pre">'crystal_system',</span> <span class="pre">'crystal_system_int',</span> <span class="pre">'is_centrosymmetric',</span> <span class="pre">'n_symmetry_ops']</span></em><a class="headerlink" href="#matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.all_features" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.citations">
<span class="sig-name descname"><span class="pre">citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Citation(s) and reference(s) for this feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should be a string citation,</dt><dd><p>ideally in BibTeX format.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.crystal_idx">
<span class="sig-name descname"><span class="pre">crystal_idx</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">{'cubic':</span> <span class="pre">1,</span> <span class="pre">'hexagonal':</span> <span class="pre">2,</span> <span class="pre">'monoclinic':</span> <span class="pre">6,</span> <span class="pre">'orthorhombic':</span> <span class="pre">5,</span> <span class="pre">'tetragonal':</span> <span class="pre">4,</span> <span class="pre">'triclinic':</span> <span class="pre">7,</span> <span class="pre">'trigonal':</span> <span class="pre">3}</span></em><a class="headerlink" href="#matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.crystal_idx" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.feature_labels">
<span class="sig-name descname"><span class="pre">feature_labels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.feature_labels" title="Permalink to this definition">¶</a></dt>
<dd><p>Generate attribute names.</p>
<dl class="simple">
<dt>Returns:</dt><dd><p>([str]) attribute labels.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.featurize">
<span class="sig-name descname"><span class="pre">featurize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.featurize" title="Permalink to this definition">¶</a></dt>
<dd><p>Main featurizer function, which has to be implemented
in any derived featurizer subclass.</p>
<dl class="simple">
<dt>Args:</dt><dd><p>x: input data to featurize (type depends on featurizer).</p>
</dd>
<dt>Returns:</dt><dd><p>(list) one or more features.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.implementors">
<span class="sig-name descname"><span class="pre">implementors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.implementors" title="Permalink to this definition">¶</a></dt>
<dd><p>List of implementors of the feature.</p>
<dl class="simple">
<dt>Returns:</dt><dd><dl class="simple">
<dt>(list) each element should either be a string with author name (e.g.,</dt><dd><p>“Anubhav Jain”) or a dictionary  with required key “name” and other
keys like “email” or “institution” (e.g., {“name”: “Anubhav
Jain”, “email”: “<a class="reference external" href="mailto:ajain&#37;&#52;&#48;lbl&#46;gov">ajain<span>&#64;</span>lbl<span>&#46;</span>gov</a>”, “institution”: “LBNL”}).</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.structure">
<span id="module-contents"></span><h2>Module contents<a class="headerlink" href="#module-matminer.featurizers.structure" title="Permalink to this heading">¶</a></h2>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">matminer.featurizers.structure package</a><ul>
<li><a class="reference internal" href="#subpackages">Subpackages</a></li>
<li><a class="reference internal" href="#submodules">Submodules</a></li>
<li><a class="reference internal" href="#module-matminer.featurizers.structure.bonding">matminer.featurizers.structure.bonding module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.BagofBonds"><code class="docutils literal notranslate"><span class="pre">BagofBonds</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.BagofBonds.__init__"><code class="docutils literal notranslate"><span class="pre">BagofBonds.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.BagofBonds.bag"><code class="docutils literal notranslate"><span class="pre">BagofBonds.bag()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.BagofBonds.citations"><code class="docutils literal notranslate"><span class="pre">BagofBonds.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.BagofBonds.feature_labels"><code class="docutils literal notranslate"><span class="pre">BagofBonds.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.BagofBonds.featurize"><code class="docutils literal notranslate"><span class="pre">BagofBonds.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.BagofBonds.fit"><code class="docutils literal notranslate"><span class="pre">BagofBonds.fit()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.BagofBonds.implementors"><code class="docutils literal notranslate"><span class="pre">BagofBonds.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.BondFractions"><code class="docutils literal notranslate"><span class="pre">BondFractions</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.BondFractions.__init__"><code class="docutils literal notranslate"><span class="pre">BondFractions.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.BondFractions.citations"><code class="docutils literal notranslate"><span class="pre">BondFractions.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.BondFractions.enumerate_all_bonds"><code class="docutils literal notranslate"><span class="pre">BondFractions.enumerate_all_bonds()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.BondFractions.enumerate_bonds"><code class="docutils literal notranslate"><span class="pre">BondFractions.enumerate_bonds()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.BondFractions.feature_labels"><code class="docutils literal notranslate"><span class="pre">BondFractions.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.BondFractions.featurize"><code class="docutils literal notranslate"><span class="pre">BondFractions.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.BondFractions.fit"><code class="docutils literal notranslate"><span class="pre">BondFractions.fit()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.BondFractions.from_preset"><code class="docutils literal notranslate"><span class="pre">BondFractions.from_preset()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.BondFractions.implementors"><code class="docutils literal notranslate"><span class="pre">BondFractions.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.GlobalInstabilityIndex"><code class="docutils literal notranslate"><span class="pre">GlobalInstabilityIndex</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.__init__"><code class="docutils literal notranslate"><span class="pre">GlobalInstabilityIndex.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.calc_bv_sum"><code class="docutils literal notranslate"><span class="pre">GlobalInstabilityIndex.calc_bv_sum()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.calc_gii_iucr"><code class="docutils literal notranslate"><span class="pre">GlobalInstabilityIndex.calc_gii_iucr()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.calc_gii_pymatgen"><code class="docutils literal notranslate"><span class="pre">GlobalInstabilityIndex.calc_gii_pymatgen()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.citations"><code class="docutils literal notranslate"><span class="pre">GlobalInstabilityIndex.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.compute_bv"><code class="docutils literal notranslate"><span class="pre">GlobalInstabilityIndex.compute_bv()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.feature_labels"><code class="docutils literal notranslate"><span class="pre">GlobalInstabilityIndex.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.featurize"><code class="docutils literal notranslate"><span class="pre">GlobalInstabilityIndex.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.get_bv_params"><code class="docutils literal notranslate"><span class="pre">GlobalInstabilityIndex.get_bv_params()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.get_equiv_sites"><code class="docutils literal notranslate"><span class="pre">GlobalInstabilityIndex.get_equiv_sites()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.implementors"><code class="docutils literal notranslate"><span class="pre">GlobalInstabilityIndex.implementors()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.GlobalInstabilityIndex.precheck"><code class="docutils literal notranslate"><span class="pre">GlobalInstabilityIndex.precheck()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.MinimumRelativeDistances"><code class="docutils literal notranslate"><span class="pre">MinimumRelativeDistances</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.MinimumRelativeDistances.__init__"><code class="docutils literal notranslate"><span class="pre">MinimumRelativeDistances.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.MinimumRelativeDistances.citations"><code class="docutils literal notranslate"><span class="pre">MinimumRelativeDistances.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.MinimumRelativeDistances.feature_labels"><code class="docutils literal notranslate"><span class="pre">MinimumRelativeDistances.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.MinimumRelativeDistances.featurize"><code class="docutils literal notranslate"><span class="pre">MinimumRelativeDistances.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.MinimumRelativeDistances.fit"><code class="docutils literal notranslate"><span class="pre">MinimumRelativeDistances.fit()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.MinimumRelativeDistances.implementors"><code class="docutils literal notranslate"><span class="pre">MinimumRelativeDistances.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.StructuralHeterogeneity"><code class="docutils literal notranslate"><span class="pre">StructuralHeterogeneity</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.StructuralHeterogeneity.__init__"><code class="docutils literal notranslate"><span class="pre">StructuralHeterogeneity.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.StructuralHeterogeneity.citations"><code class="docutils literal notranslate"><span class="pre">StructuralHeterogeneity.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.StructuralHeterogeneity.feature_labels"><code class="docutils literal notranslate"><span class="pre">StructuralHeterogeneity.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.StructuralHeterogeneity.featurize"><code class="docutils literal notranslate"><span class="pre">StructuralHeterogeneity.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.bonding.StructuralHeterogeneity.implementors"><code class="docutils literal notranslate"><span class="pre">StructuralHeterogeneity.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.structure.composite">matminer.featurizers.structure.composite module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.composite.JarvisCFID"><code class="docutils literal notranslate"><span class="pre">JarvisCFID</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.composite.JarvisCFID.__init__"><code class="docutils literal notranslate"><span class="pre">JarvisCFID.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.composite.JarvisCFID.citations"><code class="docutils literal notranslate"><span class="pre">JarvisCFID.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.composite.JarvisCFID.feature_labels"><code class="docutils literal notranslate"><span class="pre">JarvisCFID.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.composite.JarvisCFID.featurize"><code class="docutils literal notranslate"><span class="pre">JarvisCFID.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.composite.JarvisCFID.get_chem"><code class="docutils literal notranslate"><span class="pre">JarvisCFID.get_chem()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.composite.JarvisCFID.get_chg"><code class="docutils literal notranslate"><span class="pre">JarvisCFID.get_chg()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.composite.JarvisCFID.get_distributions"><code class="docutils literal notranslate"><span class="pre">JarvisCFID.get_distributions()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.composite.JarvisCFID.implementors"><code class="docutils literal notranslate"><span class="pre">JarvisCFID.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.structure.matrix">matminer.featurizers.structure.matrix module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.matrix.CoulombMatrix"><code class="docutils literal notranslate"><span class="pre">CoulombMatrix</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.matrix.CoulombMatrix.__init__"><code class="docutils literal notranslate"><span class="pre">CoulombMatrix.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.matrix.CoulombMatrix.citations"><code class="docutils literal notranslate"><span class="pre">CoulombMatrix.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.matrix.CoulombMatrix.feature_labels"><code class="docutils literal notranslate"><span class="pre">CoulombMatrix.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.matrix.CoulombMatrix.featurize"><code class="docutils literal notranslate"><span class="pre">CoulombMatrix.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.matrix.CoulombMatrix.fit"><code class="docutils literal notranslate"><span class="pre">CoulombMatrix.fit()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.matrix.CoulombMatrix.implementors"><code class="docutils literal notranslate"><span class="pre">CoulombMatrix.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.structure.matrix.OrbitalFieldMatrix"><code class="docutils literal notranslate"><span class="pre">OrbitalFieldMatrix</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.__init__"><code class="docutils literal notranslate"><span class="pre">OrbitalFieldMatrix.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.citations"><code class="docutils literal notranslate"><span class="pre">OrbitalFieldMatrix.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.feature_labels"><code class="docutils literal notranslate"><span class="pre">OrbitalFieldMatrix.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.featurize"><code class="docutils literal notranslate"><span class="pre">OrbitalFieldMatrix.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.get_atom_ofms"><code class="docutils literal notranslate"><span class="pre">OrbitalFieldMatrix.get_atom_ofms()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.get_mean_ofm"><code class="docutils literal notranslate"><span class="pre">OrbitalFieldMatrix.get_mean_ofm()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.get_ohv"><code class="docutils literal notranslate"><span class="pre">OrbitalFieldMatrix.get_ohv()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.get_single_ofm"><code class="docutils literal notranslate"><span class="pre">OrbitalFieldMatrix.get_single_ofm()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.get_structure_ofm"><code class="docutils literal notranslate"><span class="pre">OrbitalFieldMatrix.get_structure_ofm()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.matrix.OrbitalFieldMatrix.implementors"><code class="docutils literal notranslate"><span class="pre">OrbitalFieldMatrix.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.structure.matrix.SineCoulombMatrix"><code class="docutils literal notranslate"><span class="pre">SineCoulombMatrix</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.matrix.SineCoulombMatrix.__init__"><code class="docutils literal notranslate"><span class="pre">SineCoulombMatrix.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.matrix.SineCoulombMatrix.citations"><code class="docutils literal notranslate"><span class="pre">SineCoulombMatrix.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.matrix.SineCoulombMatrix.feature_labels"><code class="docutils literal notranslate"><span class="pre">SineCoulombMatrix.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.matrix.SineCoulombMatrix.featurize"><code class="docutils literal notranslate"><span class="pre">SineCoulombMatrix.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.matrix.SineCoulombMatrix.fit"><code class="docutils literal notranslate"><span class="pre">SineCoulombMatrix.fit()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.matrix.SineCoulombMatrix.implementors"><code class="docutils literal notranslate"><span class="pre">SineCoulombMatrix.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.structure.misc">matminer.featurizers.structure.misc module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.misc.EwaldEnergy"><code class="docutils literal notranslate"><span class="pre">EwaldEnergy</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.misc.EwaldEnergy.__init__"><code class="docutils literal notranslate"><span class="pre">EwaldEnergy.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.misc.EwaldEnergy.citations"><code class="docutils literal notranslate"><span class="pre">EwaldEnergy.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.misc.EwaldEnergy.feature_labels"><code class="docutils literal notranslate"><span class="pre">EwaldEnergy.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.misc.EwaldEnergy.featurize"><code class="docutils literal notranslate"><span class="pre">EwaldEnergy.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.misc.EwaldEnergy.implementors"><code class="docutils literal notranslate"><span class="pre">EwaldEnergy.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.structure.misc.StructureComposition"><code class="docutils literal notranslate"><span class="pre">StructureComposition</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.misc.StructureComposition.__init__"><code class="docutils literal notranslate"><span class="pre">StructureComposition.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.misc.StructureComposition.citations"><code class="docutils literal notranslate"><span class="pre">StructureComposition.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.misc.StructureComposition.feature_labels"><code class="docutils literal notranslate"><span class="pre">StructureComposition.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.misc.StructureComposition.featurize"><code class="docutils literal notranslate"><span class="pre">StructureComposition.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.misc.StructureComposition.fit"><code class="docutils literal notranslate"><span class="pre">StructureComposition.fit()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.misc.StructureComposition.implementors"><code class="docutils literal notranslate"><span class="pre">StructureComposition.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.structure.misc.XRDPowderPattern"><code class="docutils literal notranslate"><span class="pre">XRDPowderPattern</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.misc.XRDPowderPattern.__init__"><code class="docutils literal notranslate"><span class="pre">XRDPowderPattern.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.misc.XRDPowderPattern.citations"><code class="docutils literal notranslate"><span class="pre">XRDPowderPattern.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.misc.XRDPowderPattern.feature_labels"><code class="docutils literal notranslate"><span class="pre">XRDPowderPattern.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.misc.XRDPowderPattern.featurize"><code class="docutils literal notranslate"><span class="pre">XRDPowderPattern.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.misc.XRDPowderPattern.implementors"><code class="docutils literal notranslate"><span class="pre">XRDPowderPattern.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.structure.order">matminer.featurizers.structure.order module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.order.ChemicalOrdering"><code class="docutils literal notranslate"><span class="pre">ChemicalOrdering</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.order.ChemicalOrdering.__init__"><code class="docutils literal notranslate"><span class="pre">ChemicalOrdering.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.order.ChemicalOrdering.citations"><code class="docutils literal notranslate"><span class="pre">ChemicalOrdering.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.order.ChemicalOrdering.feature_labels"><code class="docutils literal notranslate"><span class="pre">ChemicalOrdering.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.order.ChemicalOrdering.featurize"><code class="docutils literal notranslate"><span class="pre">ChemicalOrdering.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.order.ChemicalOrdering.implementors"><code class="docutils literal notranslate"><span class="pre">ChemicalOrdering.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.structure.order.DensityFeatures"><code class="docutils literal notranslate"><span class="pre">DensityFeatures</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.order.DensityFeatures.__init__"><code class="docutils literal notranslate"><span class="pre">DensityFeatures.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.order.DensityFeatures.citations"><code class="docutils literal notranslate"><span class="pre">DensityFeatures.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.order.DensityFeatures.feature_labels"><code class="docutils literal notranslate"><span class="pre">DensityFeatures.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.order.DensityFeatures.featurize"><code class="docutils literal notranslate"><span class="pre">DensityFeatures.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.order.DensityFeatures.implementors"><code class="docutils literal notranslate"><span class="pre">DensityFeatures.implementors()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.order.DensityFeatures.precheck"><code class="docutils literal notranslate"><span class="pre">DensityFeatures.precheck()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.structure.order.MaximumPackingEfficiency"><code class="docutils literal notranslate"><span class="pre">MaximumPackingEfficiency</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.order.MaximumPackingEfficiency.citations"><code class="docutils literal notranslate"><span class="pre">MaximumPackingEfficiency.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.order.MaximumPackingEfficiency.feature_labels"><code class="docutils literal notranslate"><span class="pre">MaximumPackingEfficiency.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.order.MaximumPackingEfficiency.featurize"><code class="docutils literal notranslate"><span class="pre">MaximumPackingEfficiency.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.order.MaximumPackingEfficiency.implementors"><code class="docutils literal notranslate"><span class="pre">MaximumPackingEfficiency.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.structure.order.StructuralComplexity"><code class="docutils literal notranslate"><span class="pre">StructuralComplexity</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.order.StructuralComplexity.__init__"><code class="docutils literal notranslate"><span class="pre">StructuralComplexity.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.order.StructuralComplexity.citations"><code class="docutils literal notranslate"><span class="pre">StructuralComplexity.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.order.StructuralComplexity.feature_labels"><code class="docutils literal notranslate"><span class="pre">StructuralComplexity.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.order.StructuralComplexity.featurize"><code class="docutils literal notranslate"><span class="pre">StructuralComplexity.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.order.StructuralComplexity.implementors"><code class="docutils literal notranslate"><span class="pre">StructuralComplexity.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.structure.rdf">matminer.featurizers.structure.rdf module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction"><code class="docutils literal notranslate"><span class="pre">ElectronicRadialDistributionFunction</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.__init__"><code class="docutils literal notranslate"><span class="pre">ElectronicRadialDistributionFunction.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.citations"><code class="docutils literal notranslate"><span class="pre">ElectronicRadialDistributionFunction.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.feature_labels"><code class="docutils literal notranslate"><span class="pre">ElectronicRadialDistributionFunction.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.featurize"><code class="docutils literal notranslate"><span class="pre">ElectronicRadialDistributionFunction.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.implementors"><code class="docutils literal notranslate"><span class="pre">ElectronicRadialDistributionFunction.implementors()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.precheck"><code class="docutils literal notranslate"><span class="pre">ElectronicRadialDistributionFunction.precheck()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.structure.rdf.PartialRadialDistributionFunction"><code class="docutils literal notranslate"><span class="pre">PartialRadialDistributionFunction</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.__init__"><code class="docutils literal notranslate"><span class="pre">PartialRadialDistributionFunction.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.citations"><code class="docutils literal notranslate"><span class="pre">PartialRadialDistributionFunction.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.compute_prdf"><code class="docutils literal notranslate"><span class="pre">PartialRadialDistributionFunction.compute_prdf()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.feature_labels"><code class="docutils literal notranslate"><span class="pre">PartialRadialDistributionFunction.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.featurize"><code class="docutils literal notranslate"><span class="pre">PartialRadialDistributionFunction.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.fit"><code class="docutils literal notranslate"><span class="pre">PartialRadialDistributionFunction.fit()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.implementors"><code class="docutils literal notranslate"><span class="pre">PartialRadialDistributionFunction.implementors()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.precheck"><code class="docutils literal notranslate"><span class="pre">PartialRadialDistributionFunction.precheck()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.structure.rdf.RadialDistributionFunction"><code class="docutils literal notranslate"><span class="pre">RadialDistributionFunction</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.rdf.RadialDistributionFunction.__init__"><code class="docutils literal notranslate"><span class="pre">RadialDistributionFunction.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.rdf.RadialDistributionFunction.citations"><code class="docutils literal notranslate"><span class="pre">RadialDistributionFunction.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.rdf.RadialDistributionFunction.feature_labels"><code class="docutils literal notranslate"><span class="pre">RadialDistributionFunction.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.rdf.RadialDistributionFunction.featurize"><code class="docutils literal notranslate"><span class="pre">RadialDistributionFunction.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.rdf.RadialDistributionFunction.implementors"><code class="docutils literal notranslate"><span class="pre">RadialDistributionFunction.implementors()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.rdf.RadialDistributionFunction.precheck"><code class="docutils literal notranslate"><span class="pre">RadialDistributionFunction.precheck()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.structure.rdf.get_rdf_bin_labels"><code class="docutils literal notranslate"><span class="pre">get_rdf_bin_labels()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.structure.sites">matminer.featurizers.structure.sites module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint"><code class="docutils literal notranslate"><span class="pre">PartialsSiteStatsFingerprint</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.__init__"><code class="docutils literal notranslate"><span class="pre">PartialsSiteStatsFingerprint.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.compute_pssf"><code class="docutils literal notranslate"><span class="pre">PartialsSiteStatsFingerprint.compute_pssf()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.feature_labels"><code class="docutils literal notranslate"><span class="pre">PartialsSiteStatsFingerprint.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.featurize"><code class="docutils literal notranslate"><span class="pre">PartialsSiteStatsFingerprint.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.fit"><code class="docutils literal notranslate"><span class="pre">PartialsSiteStatsFingerprint.fit()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.implementors"><code class="docutils literal notranslate"><span class="pre">PartialsSiteStatsFingerprint.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.structure.sites.SiteStatsFingerprint"><code class="docutils literal notranslate"><span class="pre">SiteStatsFingerprint</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.sites.SiteStatsFingerprint.__init__"><code class="docutils literal notranslate"><span class="pre">SiteStatsFingerprint.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.sites.SiteStatsFingerprint.citations"><code class="docutils literal notranslate"><span class="pre">SiteStatsFingerprint.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.sites.SiteStatsFingerprint.feature_labels"><code class="docutils literal notranslate"><span class="pre">SiteStatsFingerprint.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.sites.SiteStatsFingerprint.featurize"><code class="docutils literal notranslate"><span class="pre">SiteStatsFingerprint.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.sites.SiteStatsFingerprint.fit"><code class="docutils literal notranslate"><span class="pre">SiteStatsFingerprint.fit()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.sites.SiteStatsFingerprint.from_preset"><code class="docutils literal notranslate"><span class="pre">SiteStatsFingerprint.from_preset()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.sites.SiteStatsFingerprint.implementors"><code class="docutils literal notranslate"><span class="pre">SiteStatsFingerprint.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.structure.symmetry">matminer.featurizers.structure.symmetry module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.symmetry.Dimensionality"><code class="docutils literal notranslate"><span class="pre">Dimensionality</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.symmetry.Dimensionality.__init__"><code class="docutils literal notranslate"><span class="pre">Dimensionality.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.symmetry.Dimensionality.citations"><code class="docutils literal notranslate"><span class="pre">Dimensionality.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.symmetry.Dimensionality.feature_labels"><code class="docutils literal notranslate"><span class="pre">Dimensionality.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.symmetry.Dimensionality.featurize"><code class="docutils literal notranslate"><span class="pre">Dimensionality.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.symmetry.Dimensionality.implementors"><code class="docutils literal notranslate"><span class="pre">Dimensionality.implementors()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures"><code class="docutils literal notranslate"><span class="pre">GlobalSymmetryFeatures</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.__init__"><code class="docutils literal notranslate"><span class="pre">GlobalSymmetryFeatures.__init__()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.all_features"><code class="docutils literal notranslate"><span class="pre">GlobalSymmetryFeatures.all_features</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.citations"><code class="docutils literal notranslate"><span class="pre">GlobalSymmetryFeatures.citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.crystal_idx"><code class="docutils literal notranslate"><span class="pre">GlobalSymmetryFeatures.crystal_idx</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.feature_labels"><code class="docutils literal notranslate"><span class="pre">GlobalSymmetryFeatures.feature_labels()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.featurize"><code class="docutils literal notranslate"><span class="pre">GlobalSymmetryFeatures.featurize()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.implementors"><code class="docutils literal notranslate"><span class="pre">GlobalSymmetryFeatures.implementors()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.structure">Module contents</a></li>
</ul>
</li>
</ul>

  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/matminer.featurizers.structure.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer.featurizers.structure package</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2015, Anubhav Jain.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.3.0.
    </div>

  </body>
</html>