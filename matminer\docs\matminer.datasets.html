
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

    <title>matminer.datasets package &#8212; matminer 0.9.0 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="_static/nature.css" />
    <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="_static/doctools.js"></script>
    <script src="_static/sphinx_highlight.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
 
<link href='https://fonts.googleapis.com/css?family=Lato:400,700' rel='stylesheet' type='text/css'>

  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer.datasets package</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="matminer-datasets-package">
<h1>matminer.datasets package<a class="headerlink" href="#matminer-datasets-package" title="Permalink to this heading">¶</a></h1>
<section id="subpackages">
<h2>Subpackages<a class="headerlink" href="#subpackages" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="matminer.datasets.tests.html">matminer.datasets.tests package</a><ul>
<li class="toctree-l2"><a class="reference internal" href="matminer.datasets.tests.html#submodules">Submodules</a></li>
<li class="toctree-l2"><a class="reference internal" href="matminer.datasets.tests.html#module-matminer.datasets.tests.base">matminer.datasets.tests.base module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.base.DatasetTest"><code class="docutils literal notranslate"><span class="pre">DatasetTest</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.base.DatasetTest.setUp"><code class="docutils literal notranslate"><span class="pre">DatasetTest.setUp()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.datasets.tests.html#module-matminer.datasets.tests.test_convenience_loaders">matminer.datasets.tests.test_convenience_loaders module</a></li>
<li class="toctree-l2"><a class="reference internal" href="matminer.datasets.tests.html#module-matminer.datasets.tests.test_dataset_retrieval">matminer.datasets.tests.test_dataset_retrieval module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest"><code class="docutils literal notranslate"><span class="pre">DataRetrievalTest</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_all_dataset_info"><code class="docutils literal notranslate"><span class="pre">DataRetrievalTest.test_get_all_dataset_info()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_attribute"><code class="docutils literal notranslate"><span class="pre">DataRetrievalTest.test_get_dataset_attribute()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_citations"><code class="docutils literal notranslate"><span class="pre">DataRetrievalTest.test_get_dataset_citations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_column_descriptions"><code class="docutils literal notranslate"><span class="pre">DataRetrievalTest.test_get_dataset_column_descriptions()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_columns"><code class="docutils literal notranslate"><span class="pre">DataRetrievalTest.test_get_dataset_columns()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_description"><code class="docutils literal notranslate"><span class="pre">DataRetrievalTest.test_get_dataset_description()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_num_entries"><code class="docutils literal notranslate"><span class="pre">DataRetrievalTest.test_get_dataset_num_entries()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_reference"><code class="docutils literal notranslate"><span class="pre">DataRetrievalTest.test_get_dataset_reference()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_load_dataset"><code class="docutils literal notranslate"><span class="pre">DataRetrievalTest.test_load_dataset()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_print_available_datasets"><code class="docutils literal notranslate"><span class="pre">DataRetrievalTest.test_print_available_datasets()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.datasets.tests.html#module-matminer.datasets.tests.test_datasets">matminer.datasets.tests.test_datasets module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.DataSetsTest"><code class="docutils literal notranslate"><span class="pre">DataSetsTest</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.DataSetsTest.universal_dataset_check"><code class="docutils literal notranslate"><span class="pre">DataSetsTest.universal_dataset_check()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatbenchDatasetsTest"><code class="docutils literal notranslate"><span class="pre">MatbenchDatasetsTest</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatbenchDatasetsTest.test_matbench_v0_1"><code class="docutils literal notranslate"><span class="pre">MatbenchDatasetsTest.test_matbench_v0_1()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_boltztrap_mp"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_boltztrap_mp()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_brgoch_superhard_training"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_brgoch_superhard_training()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_castelli_perovskites"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_castelli_perovskites()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_citrine_thermal_conductivity"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_citrine_thermal_conductivity()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_dielectric_constant"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_dielectric_constant()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_double_perovskites_gap"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_double_perovskites_gap()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_double_perovskites_gap_lumo"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_double_perovskites_gap_lumo()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_elastic_tensor_2015"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_elastic_tensor_2015()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_expt_formation_enthalpy"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_expt_formation_enthalpy()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_expt_formation_enthalpy_kingsbury"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_expt_formation_enthalpy_kingsbury()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_expt_gap"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_expt_gap()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_expt_gap_kingsbury"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_expt_gap_kingsbury()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_flla"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_flla()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_glass_binary"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_glass_binary()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_glass_binary_v2"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_glass_binary_v2()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_glass_ternary_hipt"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_glass_ternary_hipt()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_glass_ternary_landolt"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_glass_ternary_landolt()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_heusler_magnetic"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_heusler_magnetic()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_jarvis_dft_2d"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_jarvis_dft_2d()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_jarvis_dft_3d"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_jarvis_dft_3d()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_jarvis_ml_dft_training"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_jarvis_ml_dft_training()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_m2ax"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_m2ax()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_mp_all_20181018"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_mp_all_20181018()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_mp_nostruct_20181018"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_mp_nostruct_20181018()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_phonon_dielectric_mp"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_phonon_dielectric_mp()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_piezoelectric_tensor"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_piezoelectric_tensor()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_ricci_boltztrap_mp_tabular"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_ricci_boltztrap_mp_tabular()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_steel_strength"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_steel_strength()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_superconductivity2018"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_superconductivity2018()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_tholander_nitrides_e_form"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_tholander_nitrides_e_form()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_ucsb_thermoelectrics"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_ucsb_thermoelectrics()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_wolverton_oxides"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_wolverton_oxides()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.datasets.tests.html#module-matminer.datasets.tests.test_utils">matminer.datasets.tests.test_utils module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_utils.UtilsTest"><code class="docutils literal notranslate"><span class="pre">UtilsTest</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_utils.UtilsTest.test_fetch_external_dataset"><code class="docutils literal notranslate"><span class="pre">UtilsTest.test_fetch_external_dataset()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_utils.UtilsTest.test_get_data_home"><code class="docutils literal notranslate"><span class="pre">UtilsTest.test_get_data_home()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_utils.UtilsTest.test_get_file_sha256_hash"><code class="docutils literal notranslate"><span class="pre">UtilsTest.test_get_file_sha256_hash()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_utils.UtilsTest.test_load_dataset_dict"><code class="docutils literal notranslate"><span class="pre">UtilsTest.test_load_dataset_dict()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_utils.UtilsTest.test_read_dataframe_from_file"><code class="docutils literal notranslate"><span class="pre">UtilsTest.test_read_dataframe_from_file()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.tests.html#matminer.datasets.tests.test_utils.UtilsTest.test_validate_dataset"><code class="docutils literal notranslate"><span class="pre">UtilsTest.test_validate_dataset()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.datasets.tests.html#module-matminer.datasets.tests">Module contents</a></li>
</ul>
</li>
</ul>
</div>
</section>
<section id="submodules">
<h2>Submodules<a class="headerlink" href="#submodules" title="Permalink to this heading">¶</a></h2>
</section>
<section id="module-matminer.datasets.convenience_loaders">
<span id="matminer-datasets-convenience-loaders-module"></span><h2>matminer.datasets.convenience_loaders module<a class="headerlink" href="#module-matminer.datasets.convenience_loaders" title="Permalink to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.convenience_loaders.load_boltztrap_mp">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.convenience_loaders.</span></span><span class="sig-name descname"><span class="pre">load_boltztrap_mp</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data_home</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">download_if_missing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.convenience_loaders.load_boltztrap_mp" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for loading the boltztrap_mp dataset.</p>
<dl>
<dt>Args:</dt><dd><p>data_home (str, None): Where to look for and store the loaded dataset</p>
<dl class="simple">
<dt>download_if_missing (bool): Whether or not to download the dataset if</dt><dd><p>it isn’t on disk</p>
</dd>
</dl>
</dd>
</dl>
<p>Returns: (pd.DataFrame)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.convenience_loaders.load_brgoch_superhard_training">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.convenience_loaders.</span></span><span class="sig-name descname"><span class="pre">load_brgoch_superhard_training</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">subset</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'all'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">drop_suspect</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data_home</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">download_if_missing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.convenience_loaders.load_brgoch_superhard_training" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for loading the expt_formation_enthalpy dataset.</p>
<dl>
<dt>Args:</dt><dd><dl>
<dt>subset (str): Identifier for subset of data to return,</dt><dd><dl class="simple">
<dt>all: all possible columns including metadata, engineered features,</dt><dd><p>and basic descriptors</p>
</dd>
</dl>
<p>brgoch_features: only features from reference paper and targets
basic_descriptors: only composition/structure columns and targets</p>
</dd>
<dt>drop_suspect (bool): Whether to drop values with possibly incorrect</dt><dd><p>elastic data and materials that could not be verified</p>
</dd>
</dl>
<p>data_home (str, None): Where to look for and store the loaded dataset</p>
<dl class="simple">
<dt>download_if_missing (bool): Whether or not to download the dataset if</dt><dd><p>it isn’t on disk</p>
</dd>
</dl>
</dd>
</dl>
<p>Returns: (pd.DataFrame)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.convenience_loaders.load_castelli_perovskites">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.convenience_loaders.</span></span><span class="sig-name descname"><span class="pre">load_castelli_perovskites</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data_home</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">download_if_missing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.convenience_loaders.load_castelli_perovskites" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for loading the castelli_perovskites dataset.</p>
<dl>
<dt>Args:</dt><dd><p>data_home (str, None): Where to look for and store the loaded dataset</p>
<dl class="simple">
<dt>download_if_missing (bool): Whether or not to download the dataset if</dt><dd><p>it isn’t on disk</p>
</dd>
</dl>
</dd>
</dl>
<p>Returns: (pd.DataFrame)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.convenience_loaders.load_citrine_thermal_conductivity">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.convenience_loaders.</span></span><span class="sig-name descname"><span class="pre">load_citrine_thermal_conductivity</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">room_temperature</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data_home</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">download_if_missing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.convenience_loaders.load_citrine_thermal_conductivity" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for loading the citrine thermal conductivity dataset.</p>
<dl>
<dt>Args:</dt><dd><dl class="simple">
<dt>room_temperature (bool) Whether or not to only return items with room</dt><dd><p>temperature k_condition. True by default.</p>
</dd>
</dl>
<p>data_home (str, None): Where to look for and store the loaded dataset</p>
<dl class="simple">
<dt>download_if_missing (bool): Whether or not to download the dataset if</dt><dd><p>it isn’t on disk</p>
</dd>
</dl>
</dd>
</dl>
<p>Returns: (pd.DataFrame)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.convenience_loaders.load_dielectric_constant">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.convenience_loaders.</span></span><span class="sig-name descname"><span class="pre">load_dielectric_constant</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">include_metadata</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data_home</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">download_if_missing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.convenience_loaders.load_dielectric_constant" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for loading the dielectric_constant dataset.</p>
<dl>
<dt>Args:</dt><dd><dl class="simple">
<dt>include_metadata (bool): Whether or not to include the cif, meta,</dt><dd><p>and poscar dataset columns. False by default.</p>
</dd>
</dl>
<p>data_home (str, None): Where to look for and store the loaded dataset</p>
<dl class="simple">
<dt>download_if_missing (bool): Whether or not to download the dataset if</dt><dd><p>it isn’t on disk</p>
</dd>
</dl>
</dd>
</dl>
<p>Returns: (pd.DataFrame)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.convenience_loaders.load_double_perovskites_gap">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.convenience_loaders.</span></span><span class="sig-name descname"><span class="pre">load_double_perovskites_gap</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">return_lumo</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data_home</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">download_if_missing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.convenience_loaders.load_double_perovskites_gap" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for loading the double_perovskites_gap dataset.</p>
<dl>
<dt>Args:</dt><dd><dl class="simple">
<dt>return_lumo (bool) Whether or not to provide LUMO energy dataframe in</dt><dd><p>addition to gap dataframe. Defaults to False.</p>
</dd>
</dl>
<p>data_home (str, None): Where to look for and store the loaded dataset</p>
<dl class="simple">
<dt>download_if_missing (bool): Whether or not to download the dataset if</dt><dd><p>it isn’t on disk</p>
</dd>
</dl>
</dd>
</dl>
<p>Returns: (pd.DataFrame, tuple)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.convenience_loaders.load_double_perovskites_gap_lumo">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.convenience_loaders.</span></span><span class="sig-name descname"><span class="pre">load_double_perovskites_gap_lumo</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data_home</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">download_if_missing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.convenience_loaders.load_double_perovskites_gap_lumo" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for loading the double_perovskites_gap_lumo dataset.</p>
<dl>
<dt>Args:</dt><dd><p>data_home (str, None): Where to look for and store the loaded dataset</p>
<dl class="simple">
<dt>download_if_missing (bool): Whether or not to download the dataset if</dt><dd><p>it isn’t on disk</p>
</dd>
</dl>
</dd>
</dl>
<p>Returns: (pd.DataFrame)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.convenience_loaders.load_elastic_tensor">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.convenience_loaders.</span></span><span class="sig-name descname"><span class="pre">load_elastic_tensor</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">version</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'2015'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_metadata</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data_home</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">download_if_missing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.convenience_loaders.load_elastic_tensor" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for loading the elastic_tensor dataset.</p>
<dl>
<dt>Args:</dt><dd><dl class="simple">
<dt>version (str): Version of the elastic_tensor dataset to load</dt><dd><p>(defaults to 2015)</p>
</dd>
<dt>include_metadata (bool): Whether or not to include the cif, meta,</dt><dd><p>and poscar dataset columns. False by default.</p>
</dd>
</dl>
<p>data_home (str, None): Where to look for and store the loaded dataset</p>
<dl class="simple">
<dt>download_if_missing (bool): Whether or not to download the dataset if</dt><dd><p>it isn’t on disk</p>
</dd>
</dl>
</dd>
</dl>
<p>Returns: (pd.DataFrame)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.convenience_loaders.load_expt_formation_enthalpy">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.convenience_loaders.</span></span><span class="sig-name descname"><span class="pre">load_expt_formation_enthalpy</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data_home</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">download_if_missing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.convenience_loaders.load_expt_formation_enthalpy" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for loading the expt_formation_enthalpy dataset.</p>
<dl>
<dt>Args:</dt><dd><p>data_home (str, None): Where to look for and store the loaded dataset</p>
<dl class="simple">
<dt>download_if_missing (bool): Whether or not to download the dataset if</dt><dd><p>it isn’t on disk</p>
</dd>
</dl>
</dd>
</dl>
<p>Returns: (pd.DataFrame)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.convenience_loaders.load_expt_gap">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.convenience_loaders.</span></span><span class="sig-name descname"><span class="pre">load_expt_gap</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data_home</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">download_if_missing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.convenience_loaders.load_expt_gap" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for loading the expt_gap dataset.me</p>
<dl>
<dt>Args:</dt><dd><p>data_home (str, None): Where to look for and store the loaded dataset</p>
<dl class="simple">
<dt>download_if_missing (bool): Whether or not to download the dataset if</dt><dd><p>it isn’t on disk</p>
</dd>
</dl>
</dd>
</dl>
<p>Returns: (pd.DataFrame)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.convenience_loaders.load_flla">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.convenience_loaders.</span></span><span class="sig-name descname"><span class="pre">load_flla</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data_home</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">download_if_missing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.convenience_loaders.load_flla" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for loading the flla dataset.</p>
<dl>
<dt>Args:</dt><dd><p>data_home (str, None): Where to look for and store the loaded dataset</p>
<dl class="simple">
<dt>download_if_missing (bool): Whether or not to download the dataset if</dt><dd><p>it isn’t on disk</p>
</dd>
</dl>
</dd>
</dl>
<p>Returns: (pd.DataFrame)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.convenience_loaders.load_glass_binary">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.convenience_loaders.</span></span><span class="sig-name descname"><span class="pre">load_glass_binary</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">version</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'v2'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data_home</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">download_if_missing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.convenience_loaders.load_glass_binary" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for loading the glass_binary dataset.</p>
<dl>
<dt>Args:</dt><dd><dl class="simple">
<dt>version (str): Version identifier for dataset, see dataset description</dt><dd><p>for explanation of each. Defaults to v2</p>
</dd>
</dl>
<p>data_home (str, None): Where to look for and store the loaded dataset</p>
<dl class="simple">
<dt>download_if_missing (bool): Whether or not to download the dataset if</dt><dd><p>it isn’t on disk</p>
</dd>
</dl>
</dd>
</dl>
<p>Returns: (pd.DataFrame)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.convenience_loaders.load_glass_ternary_hipt">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.convenience_loaders.</span></span><span class="sig-name descname"><span class="pre">load_glass_ternary_hipt</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">system</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'all'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data_home</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">download_if_missing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.convenience_loaders.load_glass_ternary_hipt" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for loading the glass_ternary_hipt dataset.</p>
<dl>
<dt>Args:</dt><dd><dl class="simple">
<dt>system (str, list): return items only from the requested system(s)</dt><dd><p>options are: “CoFeZr”, “CoTiZr”, “CoVZr”, “FeTiNb”</p>
</dd>
</dl>
<p>data_home (str, None): Where to look for and store the loaded dataset</p>
<dl class="simple">
<dt>download_if_missing (bool): Whether or not to download the dataset if</dt><dd><p>it isn’t on disk</p>
</dd>
</dl>
</dd>
</dl>
<p>Returns: (pd.DataFrame)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.convenience_loaders.load_glass_ternary_landolt">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.convenience_loaders.</span></span><span class="sig-name descname"><span class="pre">load_glass_ternary_landolt</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">processing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'all'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">unique_composition</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data_home</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">download_if_missing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.convenience_loaders.load_glass_ternary_landolt" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for loading the glass_ternary_landolt dataset.</p>
<dl>
<dt>Args:</dt><dd><dl class="simple">
<dt>processing (str): return only items with a specified processing method</dt><dd><p>defaults to all, options are sputtering and meltspin</p>
</dd>
<dt>unique_composition (bool): Whether or not to combine compositions with</dt><dd><p>the same formula</p>
</dd>
</dl>
<p>data_home (str, None): Where to look for and store the loaded dataset</p>
<dl class="simple">
<dt>download_if_missing (bool): Whether or not to download the dataset if</dt><dd><p>it isn’t on disk</p>
</dd>
</dl>
</dd>
</dl>
<p>Returns: (pd.DataFrame)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.convenience_loaders.load_heusler_magnetic">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.convenience_loaders.</span></span><span class="sig-name descname"><span class="pre">load_heusler_magnetic</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data_home</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">download_if_missing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.convenience_loaders.load_heusler_magnetic" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for loading the heusler magnetic dataset.</p>
<dl>
<dt>Args:</dt><dd><p>data_home (str, None): Where to look for and store the loaded dataset</p>
<dl class="simple">
<dt>download_if_missing (bool): Whether or not to download the dataset if</dt><dd><p>it isn’t on disk</p>
</dd>
</dl>
</dd>
</dl>
<p>Returns: (pd.DataFrame)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.convenience_loaders.load_jarvis_dft_2d">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.convenience_loaders.</span></span><span class="sig-name descname"><span class="pre">load_jarvis_dft_2d</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">drop_nan_columns</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data_home</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">download_if_missing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.convenience_loaders.load_jarvis_dft_2d" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for loading the jarvis dft 2d dataset.</p>
<dl>
<dt>Args:</dt><dd><p>drop_nan_columns (list, str): Column or columns to drop rows
containing NaN values from</p>
<p>data_home (str, None): Where to look for and store the loaded dataset</p>
<dl class="simple">
<dt>download_if_missing (bool): Whether or not to download the dataset if</dt><dd><p>it isn’t on disk</p>
</dd>
</dl>
</dd>
</dl>
<p>Returns: (pd.DataFrame)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.convenience_loaders.load_jarvis_dft_3d">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.convenience_loaders.</span></span><span class="sig-name descname"><span class="pre">load_jarvis_dft_3d</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">drop_nan_columns</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data_home</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">download_if_missing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.convenience_loaders.load_jarvis_dft_3d" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for loading the jarvis dft 3d dataset.</p>
<dl>
<dt>Args:</dt><dd><p>drop_nan_columns (list, str): Column or columns to drop rows
containing NaN values from</p>
<p>data_home (str, None): Where to look for and store the loaded dataset</p>
<dl class="simple">
<dt>download_if_missing (bool): Whether or not to download the dataset if</dt><dd><p>it isn’t on disk</p>
</dd>
</dl>
</dd>
</dl>
<p>Returns: (pd.DataFrame)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.convenience_loaders.load_jarvis_ml_dft_training">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.convenience_loaders.</span></span><span class="sig-name descname"><span class="pre">load_jarvis_ml_dft_training</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">drop_nan_columns</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data_home</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">download_if_missing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.convenience_loaders.load_jarvis_ml_dft_training" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for loading the jarvis ml dft training dataset.</p>
<dl>
<dt>Args:</dt><dd><p>drop_nan_columns (list, str): Column or columns to drop rows
containing NaN values from</p>
<p>data_home (str, None): Where to look for and store the loaded dataset</p>
<dl class="simple">
<dt>download_if_missing (bool): Whether or not to download the dataset if</dt><dd><p>it isn’t on disk</p>
</dd>
</dl>
</dd>
</dl>
<p>Returns: (pd.DataFrame)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.convenience_loaders.load_m2ax">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.convenience_loaders.</span></span><span class="sig-name descname"><span class="pre">load_m2ax</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data_home</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">download_if_missing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.convenience_loaders.load_m2ax" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for loading the m2ax dataset.</p>
<dl>
<dt>Args:</dt><dd><p>data_home (str, None): Where to look for and store the loaded dataset</p>
<dl class="simple">
<dt>download_if_missing (bool): Whether or not to download the dataset if</dt><dd><p>it isn’t on disk</p>
</dd>
</dl>
</dd>
</dl>
<p>Returns: (pd.DataFrame)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.convenience_loaders.load_mp">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.convenience_loaders.</span></span><span class="sig-name descname"><span class="pre">load_mp</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">include_structures</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data_home</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">download_if_missing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.convenience_loaders.load_mp" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for loading the materials project dataset.</p>
<dl>
<dt>Args:</dt><dd><dl class="simple">
<dt>include_structures (bool) Whether or not to load the full mp</dt><dd><p>structure data. False by default.</p>
</dd>
</dl>
<p>data_home (str, None): Where to look for and store the loaded dataset</p>
<dl class="simple">
<dt>download_if_missing (bool): Whether or not to download the dataset if</dt><dd><p>it isn’t on disk</p>
</dd>
</dl>
</dd>
</dl>
<p>Returns: (pd.DataFrame)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.convenience_loaders.load_phonon_dielectric_mp">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.convenience_loaders.</span></span><span class="sig-name descname"><span class="pre">load_phonon_dielectric_mp</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data_home</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">download_if_missing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.convenience_loaders.load_phonon_dielectric_mp" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for loading the phonon_dielectric_mp dataset.</p>
<dl>
<dt>Args:</dt><dd><p>data_home (str, None): Where to look for and store the loaded dataset</p>
<dl class="simple">
<dt>download_if_missing (bool): Whether or not to download the dataset if</dt><dd><p>it isn’t on disk</p>
</dd>
</dl>
</dd>
</dl>
<p>Returns: (pd.DataFrame)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.convenience_loaders.load_piezoelectric_tensor">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.convenience_loaders.</span></span><span class="sig-name descname"><span class="pre">load_piezoelectric_tensor</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">include_metadata</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data_home</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">download_if_missing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.convenience_loaders.load_piezoelectric_tensor" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for loading the piezoelectric_tensor dataset.</p>
<dl>
<dt>Args:</dt><dd><dl class="simple">
<dt>include_metadata (bool): Whether or not to include the cif, meta,</dt><dd><p>and poscar dataset columns. False by default.</p>
</dd>
</dl>
<p>data_home (str, None): Where to look for and store the loaded dataset</p>
<dl class="simple">
<dt>download_if_missing (bool): Whether or not to download the dataset if</dt><dd><p>it isn’t on disk</p>
</dd>
</dl>
</dd>
</dl>
<p>Returns: (pd.DataFrame)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.convenience_loaders.load_steel_strength">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.convenience_loaders.</span></span><span class="sig-name descname"><span class="pre">load_steel_strength</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data_home</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">download_if_missing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.convenience_loaders.load_steel_strength" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for loading the steel strength dataset.</p>
<dl>
<dt>Args:</dt><dd><p>data_home (str, None): Where to look for and store the loaded dataset</p>
<dl class="simple">
<dt>download_if_missing (bool): Whether or not to download the dataset if</dt><dd><p>it isn’t on disk</p>
</dd>
</dl>
</dd>
</dl>
<p>Returns: (pd.DataFrame)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.convenience_loaders.load_wolverton_oxides">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.convenience_loaders.</span></span><span class="sig-name descname"><span class="pre">load_wolverton_oxides</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data_home</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">download_if_missing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.convenience_loaders.load_wolverton_oxides" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for loading the wolverton oxides dataset.</p>
<dl>
<dt>Args:</dt><dd><p>data_home (str, None): Where to look for and store the loaded dataset</p>
<dl class="simple">
<dt>download_if_missing (bool): Whether or not to download the dataset if</dt><dd><p>it isn’t on disk</p>
</dd>
</dl>
</dd>
</dl>
<p>Returns: (pd.DataFrame)</p>
</dd></dl>

</section>
<section id="module-matminer.datasets.dataset_retrieval">
<span id="matminer-datasets-dataset-retrieval-module"></span><h2>matminer.datasets.dataset_retrieval module<a class="headerlink" href="#module-matminer.datasets.dataset_retrieval" title="Permalink to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.dataset_retrieval.get_all_dataset_info">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.dataset_retrieval.</span></span><span class="sig-name descname"><span class="pre">get_all_dataset_info</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dataset_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.dataset_retrieval.get_all_dataset_info" title="Permalink to this definition">¶</a></dt>
<dd><dl class="simple">
<dt>Helper function to get all info for a particular dataset, including:</dt><dd><ul class="simple">
<li><p>Citation info</p></li>
<li><p>Bibtex-formatted references</p></li>
<li><p>Dataset columns and their descriptions</p></li>
<li><p>The dataset description</p></li>
<li><p>The number of entries in the dataset</p></li>
</ul>
</dd>
<dt>Args:</dt><dd><p>dataset_name (str): Name of the dataset querying info</p>
</dd>
<dt>Returns:</dt><dd><dl class="simple">
<dt>output_str (str): All metadata associated with the dataset, in a</dt><dd><p>formatted string.</p>
</dd>
</dl>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.dataset_retrieval.get_available_datasets">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.dataset_retrieval.</span></span><span class="sig-name descname"><span class="pre">get_available_datasets</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">print_format</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'medium'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sort_method</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'alphabetical'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.dataset_retrieval.get_available_datasets" title="Permalink to this definition">¶</a></dt>
<dd><p>Function for retrieving the datasets available within matminer.</p>
<dl>
<dt>Args:</dt><dd><dl>
<dt>print_format (None, str): None, “short”, “medium”, or “long”:</dt><dd><p>None: Don’t print anything
“short”: only the dataset names
“medium”: dataset names and their descriptions
“long”: All dataset info associated with the dataset</p>
</dd>
<dt>sort_method (str): By what metric to sort the datasets when retrieving</dt><dd><p>their information.</p>
<p>alphabetical: sorts by dataset name,
num_entries: sorts by number of dataset entries</p>
</dd>
</dl>
</dd>
</dl>
<p>Returns: (list)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.dataset_retrieval.get_dataset_attribute">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.dataset_retrieval.</span></span><span class="sig-name descname"><span class="pre">get_dataset_attribute</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dataset_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">attrib_key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.dataset_retrieval.get_dataset_attribute" title="Permalink to this definition">¶</a></dt>
<dd><p>Helper function for getting generic attributes of the dataset</p>
<dl>
<dt>Args:</dt><dd><p>dataset_name (str): Name of the dataset querying info from</p>
<p>attrib_key (str): Name of attribute to pull</p>
</dd>
</dl>
<p>Returns: Dataset attribute</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.dataset_retrieval.get_dataset_citations">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.dataset_retrieval.</span></span><span class="sig-name descname"><span class="pre">get_dataset_citations</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dataset_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.dataset_retrieval.get_dataset_citations" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for getting dataset citations</p>
<dl class="simple">
<dt>Args:</dt><dd><p>dataset_name (str): name of the dataset being queried</p>
</dd>
</dl>
<p>Returns: (list)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.dataset_retrieval.get_dataset_column_description">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.dataset_retrieval.</span></span><span class="sig-name descname"><span class="pre">get_dataset_column_description</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dataset_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dataset_column</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.dataset_retrieval.get_dataset_column_description" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for getting dataset column description</p>
<dl class="simple">
<dt>Args:</dt><dd><p>dataset_name (str): name of the dataset being queried
dataset_column (str): name of the column to get description from</p>
</dd>
</dl>
<p>Returns: (str)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.dataset_retrieval.get_dataset_columns">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.dataset_retrieval.</span></span><span class="sig-name descname"><span class="pre">get_dataset_columns</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dataset_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.dataset_retrieval.get_dataset_columns" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for getting dataset column list</p>
<dl class="simple">
<dt>Args:</dt><dd><p>dataset_name (str): name of the dataset being queried</p>
</dd>
</dl>
<p>Returns: (list)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.dataset_retrieval.get_dataset_description">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.dataset_retrieval.</span></span><span class="sig-name descname"><span class="pre">get_dataset_description</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dataset_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.dataset_retrieval.get_dataset_description" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for getting dataset description</p>
<dl class="simple">
<dt>Args:</dt><dd><p>dataset_name (str): name of the dataset being queried</p>
</dd>
</dl>
<p>Returns: (str)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.dataset_retrieval.get_dataset_num_entries">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.dataset_retrieval.</span></span><span class="sig-name descname"><span class="pre">get_dataset_num_entries</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dataset_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.dataset_retrieval.get_dataset_num_entries" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for getting dataset number of entries</p>
<dl class="simple">
<dt>Args:</dt><dd><p>dataset_name (str): name of the dataset being queried</p>
</dd>
</dl>
<p>Returns: (int)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.dataset_retrieval.get_dataset_reference">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.dataset_retrieval.</span></span><span class="sig-name descname"><span class="pre">get_dataset_reference</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dataset_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.dataset_retrieval.get_dataset_reference" title="Permalink to this definition">¶</a></dt>
<dd><p>Convenience function for getting dataset reference</p>
<dl class="simple">
<dt>Args:</dt><dd><p>dataset_name (str): name of the dataset being queried</p>
</dd>
</dl>
<p>Returns: (str)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="matminer.datasets.dataset_retrieval.load_dataset">
<span class="sig-prename descclassname"><span class="pre">matminer.datasets.dataset_retrieval.</span></span><span class="sig-name descname"><span class="pre">load_dataset</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data_home</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">download_if_missing</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pbar</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.dataset_retrieval.load_dataset" title="Permalink to this definition">¶</a></dt>
<dd><p>Loads a dataframe containing the dataset specified with the ‘name’ field.</p>
<p>Dataset file is stored/loaded from data_home if specified, otherwise at
the MATMINER_DATA environment variable if set or at matminer/datasets
by default.</p>
<dl>
<dt>Args:</dt><dd><dl class="simple">
<dt>name (str): keyword specifying what dataset to load, run</dt><dd><p>matminer.datasets.get_available_datasets() for options</p>
</dd>
</dl>
<p>data_home (str): path to folder to look for dataset file</p>
<dl class="simple">
<dt>download_if_missing (bool): whether to download the dataset if is not</dt><dd><p>found on disk</p>
</dd>
</dl>
<p>pbar (bool): If true, show progress bar for loading dataset.</p>
</dd>
<dt>Returns: (pd.DataFrame,</dt><dd><p>tuple -&gt; (pd.DataFrame, pd.DataFrame) if return_lumo = True)</p>
</dd>
</dl>
</dd></dl>

</section>
<section id="module-matminer.datasets.utils">
<span id="matminer-datasets-utils-module"></span><h2>matminer.datasets.utils module<a class="headerlink" href="#module-matminer.datasets.utils" title="Permalink to this heading">¶</a></h2>
</section>
<section id="module-matminer.datasets">
<span id="module-contents"></span><h2>Module contents<a class="headerlink" href="#module-matminer.datasets" title="Permalink to this heading">¶</a></h2>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">matminer.datasets package</a><ul>
<li><a class="reference internal" href="#subpackages">Subpackages</a></li>
<li><a class="reference internal" href="#submodules">Submodules</a></li>
<li><a class="reference internal" href="#module-matminer.datasets.convenience_loaders">matminer.datasets.convenience_loaders module</a><ul>
<li><a class="reference internal" href="#matminer.datasets.convenience_loaders.load_boltztrap_mp"><code class="docutils literal notranslate"><span class="pre">load_boltztrap_mp()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.convenience_loaders.load_brgoch_superhard_training"><code class="docutils literal notranslate"><span class="pre">load_brgoch_superhard_training()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.convenience_loaders.load_castelli_perovskites"><code class="docutils literal notranslate"><span class="pre">load_castelli_perovskites()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.convenience_loaders.load_citrine_thermal_conductivity"><code class="docutils literal notranslate"><span class="pre">load_citrine_thermal_conductivity()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.convenience_loaders.load_dielectric_constant"><code class="docutils literal notranslate"><span class="pre">load_dielectric_constant()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.convenience_loaders.load_double_perovskites_gap"><code class="docutils literal notranslate"><span class="pre">load_double_perovskites_gap()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.convenience_loaders.load_double_perovskites_gap_lumo"><code class="docutils literal notranslate"><span class="pre">load_double_perovskites_gap_lumo()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.convenience_loaders.load_elastic_tensor"><code class="docutils literal notranslate"><span class="pre">load_elastic_tensor()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.convenience_loaders.load_expt_formation_enthalpy"><code class="docutils literal notranslate"><span class="pre">load_expt_formation_enthalpy()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.convenience_loaders.load_expt_gap"><code class="docutils literal notranslate"><span class="pre">load_expt_gap()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.convenience_loaders.load_flla"><code class="docutils literal notranslate"><span class="pre">load_flla()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.convenience_loaders.load_glass_binary"><code class="docutils literal notranslate"><span class="pre">load_glass_binary()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.convenience_loaders.load_glass_ternary_hipt"><code class="docutils literal notranslate"><span class="pre">load_glass_ternary_hipt()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.convenience_loaders.load_glass_ternary_landolt"><code class="docutils literal notranslate"><span class="pre">load_glass_ternary_landolt()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.convenience_loaders.load_heusler_magnetic"><code class="docutils literal notranslate"><span class="pre">load_heusler_magnetic()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.convenience_loaders.load_jarvis_dft_2d"><code class="docutils literal notranslate"><span class="pre">load_jarvis_dft_2d()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.convenience_loaders.load_jarvis_dft_3d"><code class="docutils literal notranslate"><span class="pre">load_jarvis_dft_3d()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.convenience_loaders.load_jarvis_ml_dft_training"><code class="docutils literal notranslate"><span class="pre">load_jarvis_ml_dft_training()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.convenience_loaders.load_m2ax"><code class="docutils literal notranslate"><span class="pre">load_m2ax()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.convenience_loaders.load_mp"><code class="docutils literal notranslate"><span class="pre">load_mp()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.convenience_loaders.load_phonon_dielectric_mp"><code class="docutils literal notranslate"><span class="pre">load_phonon_dielectric_mp()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.convenience_loaders.load_piezoelectric_tensor"><code class="docutils literal notranslate"><span class="pre">load_piezoelectric_tensor()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.convenience_loaders.load_steel_strength"><code class="docutils literal notranslate"><span class="pre">load_steel_strength()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.convenience_loaders.load_wolverton_oxides"><code class="docutils literal notranslate"><span class="pre">load_wolverton_oxides()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.datasets.dataset_retrieval">matminer.datasets.dataset_retrieval module</a><ul>
<li><a class="reference internal" href="#matminer.datasets.dataset_retrieval.get_all_dataset_info"><code class="docutils literal notranslate"><span class="pre">get_all_dataset_info()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.dataset_retrieval.get_available_datasets"><code class="docutils literal notranslate"><span class="pre">get_available_datasets()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.dataset_retrieval.get_dataset_attribute"><code class="docutils literal notranslate"><span class="pre">get_dataset_attribute()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.dataset_retrieval.get_dataset_citations"><code class="docutils literal notranslate"><span class="pre">get_dataset_citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.dataset_retrieval.get_dataset_column_description"><code class="docutils literal notranslate"><span class="pre">get_dataset_column_description()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.dataset_retrieval.get_dataset_columns"><code class="docutils literal notranslate"><span class="pre">get_dataset_columns()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.dataset_retrieval.get_dataset_description"><code class="docutils literal notranslate"><span class="pre">get_dataset_description()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.dataset_retrieval.get_dataset_num_entries"><code class="docutils literal notranslate"><span class="pre">get_dataset_num_entries()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.dataset_retrieval.get_dataset_reference"><code class="docutils literal notranslate"><span class="pre">get_dataset_reference()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.dataset_retrieval.load_dataset"><code class="docutils literal notranslate"><span class="pre">load_dataset()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.datasets.utils">matminer.datasets.utils module</a></li>
<li><a class="reference internal" href="#module-matminer.datasets">Module contents</a></li>
</ul>
</li>
</ul>

  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/matminer.datasets.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer.datasets package</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2015, Anubhav Jain.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.3.0.
    </div>

  </body>
</html>