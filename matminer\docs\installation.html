
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

    <title>Installing matminer &#8212; matminer 0.9.0 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="_static/nature.css" />
    <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="_static/doctools.js"></script>
    <script src="_static/sphinx_highlight.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
 
<link href='https://fonts.googleapis.com/css?family=Lato:400,700' rel='stylesheet' type='text/css'>

  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Installing matminer</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="installing-matminer">
<span id="installation-tutorial"></span><h1>Installing matminer<a class="headerlink" href="#installing-matminer" title="Permalink to this heading">¶</a></h1>
<p>Matminer requires Python 3.6+.</p>
<p>There are a couple of quick and easy ways to install matminer (see also some <strong>tips</strong> below):</p>
<section id="install-and-update-via-pip">
<h2>Install and update via pip<a class="headerlink" href="#install-and-update-via-pip" title="Permalink to this heading">¶</a></h2>
<p>If you have installed pip, simply run the command in a bash terminal:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>$ pip install matminer
</pre></div>
</div>
<p>or, to install matminer in your user home folder, run the command:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>$ pip install matminer --user
</pre></div>
</div>
<p>To update matminer, simply type <code class="docutils literal notranslate"><span class="pre">pip</span> <span class="pre">install</span> <span class="pre">--upgrade</span> <span class="pre">matminer</span></code>.</p>
</section>
<section id="install-in-development-mode">
<h2>Install in development mode<a class="headerlink" href="#install-in-development-mode" title="Permalink to this heading">¶</a></h2>
<p>To install from the latest source of the matminer code in developmental mode, clone the Git source:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>$ git clone https://github.com/hackingmaterials/matminer.git
</pre></div>
</div>
<p>and then enter the cloned repository/folder to install in developer mode:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>$ <span class="nb">cd</span> matminer
$ python setup.py develop
</pre></div>
</div>
<p>To update matminer, enter your cloned folder and type <code class="docutils literal notranslate"><span class="pre">git</span> <span class="pre">pull</span></code> followed by <code class="docutils literal notranslate"><span class="pre">python</span> <span class="pre">setup.py</span> <span class="pre">develop</span></code>.</p>
</section>
<section id="tips">
<h2>Tips<a class="headerlink" href="#tips" title="Permalink to this heading">¶</a></h2>
<ul>
<li><p>Make sure you are using Python 3.6 or higher</p></li>
<li><p>If you have trouble with the installation of a component library (sympy, pymatgen, mdf-forge, etc.), you can try to run <code class="docutils literal notranslate"><span class="pre">pip</span> <span class="pre">install</span> <span class="pre">&lt;&lt;component&gt;&gt;</span></code> or (if you are using <a class="reference external" href="https://www.anaconda.com/distribution/">Anaconda</a>) <code class="docutils literal notranslate"><span class="pre">conda</span> <span class="pre">install</span> <span class="pre">&lt;&lt;component&gt;&gt;</span></code> first, and then re-try the installation.</p>
<blockquote>
<div><ul class="simple">
<li><p>For example, installing pymatgen on a Windows platform is easiest with Anaconda via <code class="docutils literal notranslate"><span class="pre">conda</span> <span class="pre">install</span> <span class="pre">-c</span> <span class="pre">conda-forge</span> <span class="pre">pymatgen</span></code>.</p></li>
</ul>
</div></blockquote>
</li>
<li><p>If you still have trouble, open up a ticket on our <a class="reference external" href="https://discuss.matsci.org/c/matminer">forum</a>  describing your problem in full (including your system specifications, Python version information, and input/output log). There is a good likelihood that someone else is running into the same issue, and by posting it on the forum we can help make the documentation clearer and smoother.</p></li>
</ul>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Installing matminer</a><ul>
<li><a class="reference internal" href="#install-and-update-via-pip">Install and update via pip</a></li>
<li><a class="reference internal" href="#install-in-development-mode">Install in development mode</a></li>
<li><a class="reference internal" href="#tips">Tips</a></li>
</ul>
</li>
</ul>

  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/installation.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Installing matminer</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2015, Anubhav Jain.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.3.0.
    </div>

  </body>
</html>