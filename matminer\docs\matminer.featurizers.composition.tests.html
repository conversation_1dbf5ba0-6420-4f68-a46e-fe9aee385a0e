
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

    <title>matminer.featurizers.composition.tests package &#8212; matminer 0.9.0 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="_static/nature.css" />
    <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="_static/doctools.js"></script>
    <script src="_static/sphinx_highlight.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
 
<link href='https://fonts.googleapis.com/css?family=Lato:400,700' rel='stylesheet' type='text/css'>

  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer.featurizers.composition.tests package</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="matminer-featurizers-composition-tests-package">
<h1>matminer.featurizers.composition.tests package<a class="headerlink" href="#matminer-featurizers-composition-tests-package" title="Permalink to this heading">¶</a></h1>
<section id="submodules">
<h2>Submodules<a class="headerlink" href="#submodules" title="Permalink to this heading">¶</a></h2>
</section>
<section id="module-matminer.featurizers.composition.tests.base">
<span id="matminer-featurizers-composition-tests-base-module"></span><h2>matminer.featurizers.composition.tests.base module<a class="headerlink" href="#module-matminer.featurizers.composition.tests.base" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.base.CompositionFeaturesTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.composition.tests.base.</span></span><span class="sig-name descname"><span class="pre">CompositionFeaturesTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.base.CompositionFeaturesTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">PymatgenTest</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.base.CompositionFeaturesTest.setUp">
<span class="sig-name descname"><span class="pre">setUp</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.base.CompositionFeaturesTest.setUp" title="Permalink to this definition">¶</a></dt>
<dd><p>Hook method for setting up the test fixture before exercising it.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.composition.tests.test_alloy">
<span id="matminer-featurizers-composition-tests-test-alloy-module"></span><h2>matminer.featurizers.composition.tests.test_alloy module<a class="headerlink" href="#module-matminer.featurizers.composition.tests.test_alloy" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_alloy.AlloyFeaturizersTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.composition.tests.test_alloy.</span></span><span class="sig-name descname"><span class="pre">AlloyFeaturizersTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_alloy.AlloyFeaturizersTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.composition.tests.base.CompositionFeaturesTest" title="matminer.featurizers.composition.tests.base.CompositionFeaturesTest"><code class="xref py py-class docutils literal notranslate"><span class="pre">CompositionFeaturesTest</span></code></a></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_alloy.AlloyFeaturizersTest.test_WenAlloys">
<span class="sig-name descname"><span class="pre">test_WenAlloys</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_alloy.AlloyFeaturizersTest.test_WenAlloys" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_alloy.AlloyFeaturizersTest.test_miedema_all">
<span class="sig-name descname"><span class="pre">test_miedema_all</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_alloy.AlloyFeaturizersTest.test_miedema_all" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_alloy.AlloyFeaturizersTest.test_miedema_ss">
<span class="sig-name descname"><span class="pre">test_miedema_ss</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_alloy.AlloyFeaturizersTest.test_miedema_ss" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_alloy.AlloyFeaturizersTest.test_yang">
<span class="sig-name descname"><span class="pre">test_yang</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_alloy.AlloyFeaturizersTest.test_yang" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.composition.tests.test_composite">
<span id="matminer-featurizers-composition-tests-test-composite-module"></span><h2>matminer.featurizers.composition.tests.test_composite module<a class="headerlink" href="#module-matminer.featurizers.composition.tests.test_composite" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.composition.tests.test_composite.</span></span><span class="sig-name descname"><span class="pre">CompositeFeaturesTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.composition.tests.base.CompositionFeaturesTest" title="matminer.featurizers.composition.tests.base.CompositionFeaturesTest"><code class="xref py py-class docutils literal notranslate"><span class="pre">CompositionFeaturesTest</span></code></a></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest.test_elem">
<span class="sig-name descname"><span class="pre">test_elem</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest.test_elem" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest.test_elem_deml">
<span class="sig-name descname"><span class="pre">test_elem_deml</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest.test_elem_deml" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest.test_elem_matminer">
<span class="sig-name descname"><span class="pre">test_elem_matminer</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest.test_elem_matminer" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest.test_elem_matscholar_el">
<span class="sig-name descname"><span class="pre">test_elem_matscholar_el</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest.test_elem_matscholar_el" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest.test_elem_megnet_el">
<span class="sig-name descname"><span class="pre">test_elem_megnet_el</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest.test_elem_megnet_el" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest.test_fere_corr">
<span class="sig-name descname"><span class="pre">test_fere_corr</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest.test_fere_corr" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest.test_meredig">
<span class="sig-name descname"><span class="pre">test_meredig</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest.test_meredig" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.composition.tests.test_element">
<span id="matminer-featurizers-composition-tests-test-element-module"></span><h2>matminer.featurizers.composition.tests.test_element module<a class="headerlink" href="#module-matminer.featurizers.composition.tests.test_element" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_element.ElementFeaturesTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.composition.tests.test_element.</span></span><span class="sig-name descname"><span class="pre">ElementFeaturesTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_element.ElementFeaturesTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.composition.tests.base.CompositionFeaturesTest" title="matminer.featurizers.composition.tests.base.CompositionFeaturesTest"><code class="xref py py-class docutils literal notranslate"><span class="pre">CompositionFeaturesTest</span></code></a></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_element.ElementFeaturesTest.test_band_center">
<span class="sig-name descname"><span class="pre">test_band_center</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_element.ElementFeaturesTest.test_band_center" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_element.ElementFeaturesTest.test_fraction">
<span class="sig-name descname"><span class="pre">test_fraction</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_element.ElementFeaturesTest.test_fraction" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_element.ElementFeaturesTest.test_stoich">
<span class="sig-name descname"><span class="pre">test_stoich</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_element.ElementFeaturesTest.test_stoich" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_element.ElementFeaturesTest.test_tm_fraction">
<span class="sig-name descname"><span class="pre">test_tm_fraction</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_element.ElementFeaturesTest.test_tm_fraction" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.composition.tests.test_ion">
<span id="matminer-featurizers-composition-tests-test-ion-module"></span><h2>matminer.featurizers.composition.tests.test_ion module<a class="headerlink" href="#module-matminer.featurizers.composition.tests.test_ion" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_ion.IonFeaturesTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.composition.tests.test_ion.</span></span><span class="sig-name descname"><span class="pre">IonFeaturesTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_ion.IonFeaturesTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.composition.tests.base.CompositionFeaturesTest" title="matminer.featurizers.composition.tests.base.CompositionFeaturesTest"><code class="xref py py-class docutils literal notranslate"><span class="pre">CompositionFeaturesTest</span></code></a></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_ion.IonFeaturesTest.test_cation_properties">
<span class="sig-name descname"><span class="pre">test_cation_properties</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_ion.IonFeaturesTest.test_cation_properties" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_ion.IonFeaturesTest.test_elec_affin">
<span class="sig-name descname"><span class="pre">test_elec_affin</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_ion.IonFeaturesTest.test_elec_affin" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_ion.IonFeaturesTest.test_en_diff">
<span class="sig-name descname"><span class="pre">test_en_diff</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_ion.IonFeaturesTest.test_en_diff" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_ion.IonFeaturesTest.test_ionic">
<span class="sig-name descname"><span class="pre">test_ionic</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_ion.IonFeaturesTest.test_ionic" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_ion.IonFeaturesTest.test_is_ionic">
<span class="sig-name descname"><span class="pre">test_is_ionic</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_ion.IonFeaturesTest.test_is_ionic" title="Permalink to this definition">¶</a></dt>
<dd><p>Test checking whether a compound is ionic</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_ion.IonFeaturesTest.test_oxidation_states">
<span class="sig-name descname"><span class="pre">test_oxidation_states</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_ion.IonFeaturesTest.test_oxidation_states" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.composition.tests.test_orbital">
<span id="matminer-featurizers-composition-tests-test-orbital-module"></span><h2>matminer.featurizers.composition.tests.test_orbital module<a class="headerlink" href="#module-matminer.featurizers.composition.tests.test_orbital" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_orbital.OrbitalFeaturesTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.composition.tests.test_orbital.</span></span><span class="sig-name descname"><span class="pre">OrbitalFeaturesTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_orbital.OrbitalFeaturesTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.composition.tests.base.CompositionFeaturesTest" title="matminer.featurizers.composition.tests.base.CompositionFeaturesTest"><code class="xref py py-class docutils literal notranslate"><span class="pre">CompositionFeaturesTest</span></code></a></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_orbital.OrbitalFeaturesTest.test_atomic_orbitals">
<span class="sig-name descname"><span class="pre">test_atomic_orbitals</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_orbital.OrbitalFeaturesTest.test_atomic_orbitals" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_orbital.OrbitalFeaturesTest.test_valence">
<span class="sig-name descname"><span class="pre">test_valence</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_orbital.OrbitalFeaturesTest.test_valence" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.composition.tests.test_packing">
<span id="matminer-featurizers-composition-tests-test-packing-module"></span><h2>matminer.featurizers.composition.tests.test_packing module<a class="headerlink" href="#module-matminer.featurizers.composition.tests.test_packing" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_packing.PackingFeaturesTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.composition.tests.test_packing.</span></span><span class="sig-name descname"><span class="pre">PackingFeaturesTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_packing.PackingFeaturesTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.composition.tests.base.CompositionFeaturesTest" title="matminer.featurizers.composition.tests.base.CompositionFeaturesTest"><code class="xref py py-class docutils literal notranslate"><span class="pre">CompositionFeaturesTest</span></code></a></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_packing.PackingFeaturesTest.test_ape">
<span class="sig-name descname"><span class="pre">test_ape</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_packing.PackingFeaturesTest.test_ape" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.composition.tests.test_thermo">
<span id="matminer-featurizers-composition-tests-test-thermo-module"></span><h2>matminer.featurizers.composition.tests.test_thermo module<a class="headerlink" href="#module-matminer.featurizers.composition.tests.test_thermo" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_thermo.ThermoFeaturesTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.composition.tests.test_thermo.</span></span><span class="sig-name descname"><span class="pre">ThermoFeaturesTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_thermo.ThermoFeaturesTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.featurizers.composition.tests.base.CompositionFeaturesTest" title="matminer.featurizers.composition.tests.base.CompositionFeaturesTest"><code class="xref py py-class docutils literal notranslate"><span class="pre">CompositionFeaturesTest</span></code></a></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_thermo.ThermoFeaturesTest.test_cohesive_energy">
<span class="sig-name descname"><span class="pre">test_cohesive_energy</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_thermo.ThermoFeaturesTest.test_cohesive_energy" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.composition.tests.test_thermo.ThermoFeaturesTest.test_cohesive_energy_mp">
<span class="sig-name descname"><span class="pre">test_cohesive_energy_mp</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.composition.tests.test_thermo.ThermoFeaturesTest.test_cohesive_energy_mp" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.composition.tests">
<span id="module-contents"></span><h2>Module contents<a class="headerlink" href="#module-matminer.featurizers.composition.tests" title="Permalink to this heading">¶</a></h2>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">matminer.featurizers.composition.tests package</a><ul>
<li><a class="reference internal" href="#submodules">Submodules</a></li>
<li><a class="reference internal" href="#module-matminer.featurizers.composition.tests.base">matminer.featurizers.composition.tests.base module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.base.CompositionFeaturesTest"><code class="docutils literal notranslate"><span class="pre">CompositionFeaturesTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.base.CompositionFeaturesTest.setUp"><code class="docutils literal notranslate"><span class="pre">CompositionFeaturesTest.setUp()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.composition.tests.test_alloy">matminer.featurizers.composition.tests.test_alloy module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_alloy.AlloyFeaturizersTest"><code class="docutils literal notranslate"><span class="pre">AlloyFeaturizersTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_alloy.AlloyFeaturizersTest.test_WenAlloys"><code class="docutils literal notranslate"><span class="pre">AlloyFeaturizersTest.test_WenAlloys()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_alloy.AlloyFeaturizersTest.test_miedema_all"><code class="docutils literal notranslate"><span class="pre">AlloyFeaturizersTest.test_miedema_all()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_alloy.AlloyFeaturizersTest.test_miedema_ss"><code class="docutils literal notranslate"><span class="pre">AlloyFeaturizersTest.test_miedema_ss()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_alloy.AlloyFeaturizersTest.test_yang"><code class="docutils literal notranslate"><span class="pre">AlloyFeaturizersTest.test_yang()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.composition.tests.test_composite">matminer.featurizers.composition.tests.test_composite module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest"><code class="docutils literal notranslate"><span class="pre">CompositeFeaturesTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest.test_elem"><code class="docutils literal notranslate"><span class="pre">CompositeFeaturesTest.test_elem()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest.test_elem_deml"><code class="docutils literal notranslate"><span class="pre">CompositeFeaturesTest.test_elem_deml()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest.test_elem_matminer"><code class="docutils literal notranslate"><span class="pre">CompositeFeaturesTest.test_elem_matminer()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest.test_elem_matscholar_el"><code class="docutils literal notranslate"><span class="pre">CompositeFeaturesTest.test_elem_matscholar_el()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest.test_elem_megnet_el"><code class="docutils literal notranslate"><span class="pre">CompositeFeaturesTest.test_elem_megnet_el()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest.test_fere_corr"><code class="docutils literal notranslate"><span class="pre">CompositeFeaturesTest.test_fere_corr()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest.test_meredig"><code class="docutils literal notranslate"><span class="pre">CompositeFeaturesTest.test_meredig()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.composition.tests.test_element">matminer.featurizers.composition.tests.test_element module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_element.ElementFeaturesTest"><code class="docutils literal notranslate"><span class="pre">ElementFeaturesTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_element.ElementFeaturesTest.test_band_center"><code class="docutils literal notranslate"><span class="pre">ElementFeaturesTest.test_band_center()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_element.ElementFeaturesTest.test_fraction"><code class="docutils literal notranslate"><span class="pre">ElementFeaturesTest.test_fraction()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_element.ElementFeaturesTest.test_stoich"><code class="docutils literal notranslate"><span class="pre">ElementFeaturesTest.test_stoich()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_element.ElementFeaturesTest.test_tm_fraction"><code class="docutils literal notranslate"><span class="pre">ElementFeaturesTest.test_tm_fraction()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.composition.tests.test_ion">matminer.featurizers.composition.tests.test_ion module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_ion.IonFeaturesTest"><code class="docutils literal notranslate"><span class="pre">IonFeaturesTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_ion.IonFeaturesTest.test_cation_properties"><code class="docutils literal notranslate"><span class="pre">IonFeaturesTest.test_cation_properties()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_ion.IonFeaturesTest.test_elec_affin"><code class="docutils literal notranslate"><span class="pre">IonFeaturesTest.test_elec_affin()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_ion.IonFeaturesTest.test_en_diff"><code class="docutils literal notranslate"><span class="pre">IonFeaturesTest.test_en_diff()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_ion.IonFeaturesTest.test_ionic"><code class="docutils literal notranslate"><span class="pre">IonFeaturesTest.test_ionic()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_ion.IonFeaturesTest.test_is_ionic"><code class="docutils literal notranslate"><span class="pre">IonFeaturesTest.test_is_ionic()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_ion.IonFeaturesTest.test_oxidation_states"><code class="docutils literal notranslate"><span class="pre">IonFeaturesTest.test_oxidation_states()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.composition.tests.test_orbital">matminer.featurizers.composition.tests.test_orbital module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_orbital.OrbitalFeaturesTest"><code class="docutils literal notranslate"><span class="pre">OrbitalFeaturesTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_orbital.OrbitalFeaturesTest.test_atomic_orbitals"><code class="docutils literal notranslate"><span class="pre">OrbitalFeaturesTest.test_atomic_orbitals()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_orbital.OrbitalFeaturesTest.test_valence"><code class="docutils literal notranslate"><span class="pre">OrbitalFeaturesTest.test_valence()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.composition.tests.test_packing">matminer.featurizers.composition.tests.test_packing module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_packing.PackingFeaturesTest"><code class="docutils literal notranslate"><span class="pre">PackingFeaturesTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_packing.PackingFeaturesTest.test_ape"><code class="docutils literal notranslate"><span class="pre">PackingFeaturesTest.test_ape()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.composition.tests.test_thermo">matminer.featurizers.composition.tests.test_thermo module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_thermo.ThermoFeaturesTest"><code class="docutils literal notranslate"><span class="pre">ThermoFeaturesTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_thermo.ThermoFeaturesTest.test_cohesive_energy"><code class="docutils literal notranslate"><span class="pre">ThermoFeaturesTest.test_cohesive_energy()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.composition.tests.test_thermo.ThermoFeaturesTest.test_cohesive_energy_mp"><code class="docutils literal notranslate"><span class="pre">ThermoFeaturesTest.test_cohesive_energy_mp()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.composition.tests">Module contents</a></li>
</ul>
</li>
</ul>

  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/matminer.featurizers.composition.tests.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer.featurizers.composition.tests package</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2015, Anubhav Jain.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.3.0.
    </div>

  </body>
</html>