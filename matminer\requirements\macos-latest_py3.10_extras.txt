#
# This file is autogenerated by pip-compile with Python 3.10
# by the following command:
#
#    pip-compile --all-extras --output-file=requirements/macos-latest_py3.10_extras.txt
#
aflow==0.0.11
    # via matminer (setup.py)
alabaster==0.7.13
    # via sphinx
ase==3.22.1
    # via
    #   aflow
    #   dscribe
    #   matminer (setup.py)
astroid==2.15.6
    # via pylint
attrs==23.1.0
    # via
    #   jsonschema
    #   referencing
babel==2.12.1
    # via sphinx
beautifulsoup4==4.12.2
    # via aflow
black==23.7.0
    # via matminer (setup.py)
certifi==2023.7.22
    # via requests
cffi==1.15.1
    # via cryptography
charset-normalizer==3.2.0
    # via requests
citrination-client==6.5.1
    # via matminer (setup.py)
click==8.1.7
    # via black
contourpy==1.1.0
    # via matplotlib
coverage[toml]==6.5.0
    # via
    #   coveralls
    #   matminer (setup.py)
    #   pytest-cov
coveralls==3.3.1
    # via matminer (setup.py)
cryptography==41.0.3
    # via
    #   globus-sdk
    #   pyjwt
cycler==0.11.0
    # via matplotlib
dill==0.3.7
    # via pylint
dnspython==2.4.2
    # via pymongo
docopt==0.6.2
    # via coveralls
docutils==0.20.1
    # via sphinx
dscribe==2.1.0
    # via matminer (setup.py)
emmet-core==0.67.5
    # via mp-api
exceptiongroup==1.1.3
    # via pytest
fair-research-login==0.3.1
    # via mdf-toolbox
flake8==6.1.0
    # via matminer (setup.py)
fonttools==4.42.1
    # via matplotlib
future==0.18.3
    # via
    #   matminer (setup.py)
    #   uncertainties
globus-nexus-client==0.4.1
    # via mdf-toolbox
globus-sdk==3.28.0
    # via
    #   fair-research-login
    #   globus-nexus-client
    #   mdf-forge
    #   mdf-toolbox
httplib2==0.22.0
    # via matminer (setup.py)
idna==3.4
    # via requests
imagesize==1.4.1
    # via sphinx
iniconfig==2.0.0
    # via pytest
isort==5.12.0
    # via pylint
jinja2==3.1.2
    # via
    #   aflow
    #   sphinx
jmespath==1.0.1
    # via matminer (setup.py)
joblib==1.3.2
    # via
    #   dscribe
    #   pymatgen
    #   scikit-learn
jsonschema==4.3.0
    # via
    #   matminer (setup.py)
    #   mdf-toolbox
kiwisolver==1.4.5
    # via matplotlib
latexcodec==2.0.1
    # via pybtex
lazy-object-proxy==1.9.0
    # via astroid
llvmlite==0.40.1
    # via numba
markupsafe==2.1.3
    # via jinja2
matplotlib==3.7.2
    # via
    #   ase
    #   pymatgen
mccabe==0.7.0
    # via
    #   flake8
    #   pylint
mdf-forge==0.8.0
    # via matminer (setup.py)
mdf-toolbox==0.6.0
    # via mdf-forge
monty==2023.9.5
    # via
    #   emmet-core
    #   matminer (setup.py)
    #   mp-api
    #   pymatgen
mp-api==0.35.1
    # via pymatgen
mpmath==1.3.0
    # via sympy
msgpack==1.0.5
    # via mp-api
mypy-extensions==1.0.0
    # via black
networkx==3.1
    # via pymatgen
numba==0.57.1
    # via sparse
numpy==1.24.4
    # via
    #   aflow
    #   ase
    #   contourpy
    #   dscribe
    #   matminer (setup.py)
    #   matplotlib
    #   numba
    #   pandas
    #   pymatgen
    #   pypif
    #   scikit-learn
    #   scipy
    #   sparse
    #   spglib
packaging==23.1
    # via
    #   black
    #   matplotlib
    #   plotly
    #   pytest
    #   sphinx
palettable==3.3.3
    # via pymatgen
pandas==2.1.0
    # via
    #   matminer (setup.py)
    #   pymatgen
pathspec==0.11.2
    # via black
pillow==10.0.0
    # via matplotlib
platformdirs==3.10.0
    # via
    #   black
    #   pylint
plotly==5.16.1
    # via pymatgen
pluggy==1.3.0
    # via pytest
pybind11==2.11.1
    # via dscribe
pybtex==0.24.0
    # via
    #   emmet-core
    #   pymatgen
pycodestyle==2.11.0
    # via flake8
pycparser==2.21
    # via cffi
pydantic==1.10.12
    # via
    #   emmet-core
    #   pymatgen
pyflakes==3.1.0
    # via flake8
pygments==2.16.1
    # via sphinx
pyjwt[crypto]==2.8.0
    # via globus-sdk
pylint==2.17.5
    # via matminer (setup.py)
pymatgen==2023.9.2
    # via
    #   emmet-core
    #   matminer (setup.py)
    #   mp-api
pymongo==4.5.0
    # via matminer (setup.py)
pyparsing==3.0.9
    # via
    #   httplib2
    #   matplotlib
pypif==2.1.2
    # via citrination-client
pyrsistent==0.19.3
    # via jsonschema
pytest==7.4.1
    # via
    #   matminer (setup.py)
    #   pytest-cov
    #   pytest-timeout
pytest-cov==4.1.0
    # via matminer (setup.py)
pytest-timeout==2.1.0
    # via matminer (setup.py)
python-dateutil==2.8.2
    # via
    #   matplotlib
    #   pandas
pytz==2023.3.post1
    # via pandas
pyyaml==6.0.1
    # via
    #   citrination-client
    #   pybtex
requests==2.31.0
    # via
    #   citrination-client
    #   coveralls
    #   globus-sdk
    #   matminer (setup.py)
    #   mdf-forge
    #   mdf-toolbox
    #   mp-api
    #   pymatgen
    #   sphinx
ruamel-yaml==0.17.32
    # via pymatgen
ruamel-yaml-clib==0.2.7
    # via ruamel-yaml
scikit-learn==1.3.0
    # via
    #   dscribe
    #   matminer (setup.py)
scipy==1.11.2
    # via
    #   ase
    #   dscribe
    #   pymatgen
    #   scikit-learn
    #   sparse
six==1.16.0
    # via
    #   aflow
    #   citrination-client
    #   latexcodec
    #   pybtex
    #   pypif
    #   python-dateutil
snowballstemmer==2.2.0
    # via sphinx
soupsieve==2.5
    # via beautifulsoup4
sparse==0.14.0
    # via dscribe
spglib==2.0.2
    # via
    #   emmet-core
    #   pymatgen
sphinx==7.2.5
    # via
    #   matminer (setup.py)
    #   sphinxcontrib-applehelp
    #   sphinxcontrib-devhelp
    #   sphinxcontrib-htmlhelp
    #   sphinxcontrib-qthelp
    #   sphinxcontrib-serializinghtml
sphinxcontrib-applehelp==1.0.7
    # via sphinx
sphinxcontrib-devhelp==1.0.5
    # via sphinx
sphinxcontrib-htmlhelp==2.0.4
    # via sphinx
sphinxcontrib-jsmath==1.0.1
    # via sphinx
sphinxcontrib-qthelp==1.0.6
    # via sphinx
sphinxcontrib-serializinghtml==1.1.9
    # via sphinx
sympy==1.12
    # via
    #   matminer (setup.py)
    #   pymatgen
tabulate==0.9.0
    # via pymatgen
tenacity==8.2.3
    # via plotly
termcolor==2.3.0
    # via aflow
threadpoolctl==3.2.0
    # via scikit-learn
tomli==2.0.1
    # via
    #   black
    #   coverage
    #   pylint
    #   pytest
tomlkit==0.12.1
    # via pylint
tqdm==4.66.1
    # via
    #   matminer (setup.py)
    #   mdf-forge
    #   pymatgen
typing-extensions==4.7.1
    # via
    #   astroid
    #   emmet-core
    #   mp-api
    #   pydantic
tzdata==2023.3
    # via pandas
ujson==5.8.0
    # via matminer (setup.py)
uncertainties==3.1.7
    # via pymatgen
urllib3==2.0.4
    # via requests
wrapt==1.15.0
    # via astroid

# The following packages are considered to be unsafe in a requirements file:
# setuptools
