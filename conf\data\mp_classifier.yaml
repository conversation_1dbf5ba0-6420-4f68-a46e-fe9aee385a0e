root_path: ${oc.env:PROJECT_ROOT}/data/mp_classifier
prop: ['formation_energy_per_atom', 'band_gap', 'composition'] # ['formation_energy', 'band_gap', 'composition']
num_targets: 1
# prop: scaled_lattice
# num_targets: 6
niggli: true
primitive: False
graph_method: crystalnn
lattice_scale_method: scale_length
preprocess_workers: 30
readout: mean
max_atoms: 20
otf_graph: false
eval_model_name: mp20
tolerance: 0.1
train_sample: 600000
val_sample: 60000
test_sample: 60000
use_space_group: true
use_pos_index: false
train_max_epochs: 50
early_stopping_patience: 100000
teacher_forcing_max_epoch: 50


datamodule:
  _target_: symmcd.pl_data.datamodule.CrystDataModuleClassifier

  datasets:
    train:
      _target_: symmcd.pl_data.dataset.CrystDataset_classifier
      name: train
      # path: ${data.root_path}/train.csv
      path: ${data.root_path}
      save_path: ${data.root_path}/train_sym.pt
      prop: ${data.prop}
      niggli: ${data.niggli}
      primitive: ${data.primitive}
      graph_method: ${data.graph_method}
      tolerance: ${data.tolerance}
      use_space_group: ${data.use_space_group}
      use_pos_index: ${data.use_pos_index}
      lattice_scale_method: ${data.lattice_scale_method}
      preprocess_workers: ${data.preprocess_workers}
      sample_nums: ${data.train_sample}
    val:
      - _target_: symmcd.pl_data.dataset.CrystDataset_classifier
        name: val
        # path: ${data.root_path}/val.csv
        path: ${data.root_path}
        save_path: ${data.root_path}/val_sym.pt
        prop: ${data.prop}
        niggli: ${data.niggli}
        primitive: ${data.primitive}
        graph_method: ${data.graph_method}
        tolerance: ${data.tolerance}
        use_space_group: ${data.use_space_group}
        use_pos_index: ${data.use_pos_index}
        lattice_scale_method: ${data.lattice_scale_method}
        preprocess_workers: ${data.preprocess_workers}
        sample_nums: ${data.val_sample}
    test:
      - _target_: symmcd.pl_data.dataset.CrystDataset_classifier
        name: test
        # path: ${data.root_path}/test.csv
        path: ${data.root_path}
        save_path: ${data.root_path}/test_sym.pt
        prop: ${data.prop}
        niggli: ${data.niggli}
        primitive: ${data.primitive}
        graph_method: ${data.graph_method}
        tolerance: ${data.tolerance}
        use_space_group: ${data.use_space_group}
        use_pos_index: ${data.use_pos_index}
        lattice_scale_method: ${data.lattice_scale_method}
        preprocess_workers: ${data.preprocess_workers}
        sample_nums: ${data.test_sample}

  num_workers:
    train: 0
    val: 0
    test: 0

  batch_size:
    train: 1024
    val: 1024
    test: 1024
