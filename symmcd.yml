name: symmcd
channels:
  - pyg
  - pytorch
  - nvidia
  - defaults
dependencies:
  - _libgcc_mutex=0.1
  - _openmp_mutex=5.1
  - aiohttp=3.9.3
  - aiosignal=1.2.0
  - async-timeout=4.0.3
  - attrs=23.1.0
  - blas=1.0
  - brotli-python=1.0.9
  - bzip2=1.0.8
  - ca-certificates=2024.3.11
  - certifi=2024.2.2
  - cuda-cudart=11.8.89
  - cuda-cupti=11.8.87
  - cuda-libraries=11.8.0
  - cuda-nvrtc=11.8.89
  - cuda-nvtx=11.8.86
  - cuda-runtime=11.8.0
  - ffmpeg=4.3
  - filelock=3.13.1
  - freetype=2.12.1
  - frozenlist=1.4.0
  - fsspec=2023.10.0
  - gmp=6.2.1
  - gmpy2=2.1.2
  - gnutls=3.6.15
  - intel-openmp=2023.1.0
  - jinja2=3.1.3
  - joblib=1.4.0
  - jpeg=9e
  - lame=3.100
  - lcms2=2.12
  - ld_impl_linux-64=2.38
  - lerc=3.0
  - libcublas=*********
  - libcufft=*********
  - libcufile=*******
  - libcurand=**********
  - libcusolver=*********
  - libcusparse=*********
  - libdeflate=1.17
  - libffi=3.4.4
  - libgcc-ng=11.2.0
  - libgfortran-ng=11.2.0
  - libgfortran5=11.2.0
  - libgomp=11.2.0
  - libiconv=1.16
  - libidn2=2.3.4
  - libjpeg-turbo=2.0.0
  - libnpp=*********
  - libnvjpeg=*********
  - libpng=1.6.39
  - libstdcxx-ng=11.2.0
  - libtasn1=4.19.0
  - libtiff=4.5.1
  - libunistring=0.9.10
  - libwebp-base=1.3.2
  - lightning-utilities=0.9.0
  - llvm-openmp=14.0.6
  - lz4-c=1.9.4
  - markupsafe=2.1.3
  - mkl=2023.1.0
  - mkl-service=2.4.0
  - mkl_fft=1.3.8
  - mkl_random=1.2.4
  - mpc=1.1.0
  - mpfr=4.0.2
  - mpmath=1.3.0
  - multidict=6.0.4
  - ncurses=6.4
  - nettle=3.7.3
  - numpy=1.26.4
  - numpy-base=1.26.4
  - openh264=2.1.1
  - openjpeg=2.4.0
  - openssl=3.0.13
  - pip=23.3.1
  - psutil=5.9.0
  - pyg=2.5.2
  - pysocks=1.7.1
  - python=3.9.19
  - pytorch=2.1.0
  - pytorch-cuda=11.8
  - pytorch-lightning=2.0.3
  - pytorch-mutex=1.0
  - pytorch-scatter=2.1.2
  - pyyaml=6.0.1
  - readline=8.2
  - requests=2.31.0
  - setuptools=68.2.2
  - sqlite=3.41.2
  - sympy=1.12
  - tbb=2021.8.0
  - tk=8.6.12
  - torchaudio=2.1.0
  - torchmetrics=1.1.2
  - torchtriton=2.1.0
  - torchvision=0.16.0
  - typing-extensions=4.9.0
  - typing_extensions=4.9.0
  - wheel=0.41.2
  - xz=5.4.6
  - yaml=0.2.5
  - yarl=1.9.3
  - zlib=1.2.13
  - zstd=1.5.5
  - pip:
    - antlr4-python3-runtime==4.9.3
    - appdirs==1.4.4
    - ase==3.22.1
    - chardet==5.2.0
    - charset-normalizer==3.3.2
    - chemparse==0.3.1
    - click==8.1.7
    - contourpy==1.2.1
    - cycler==0.12.1
    - dill==0.3.8
    - dnspython==2.6.1
    - docker-pycreds==0.4.0
    - einops==0.7.0
    - fonttools==4.51.0
    - future==1.0.0
    - gitdb==4.0.11
    - gitpython==3.1.43
    - hydra-core==1.3.2
    - idna==3.7
    - importlib-metadata==7.1.0
    - importlib-resources==6.4.0
    - kiwisolver==1.4.5
    - latexcodec==3.0.0
    - matminer==0.9.2
    - matplotlib==3.8.4
    - monty==2024.4.17
    - multiprocess==0.70.16
    - networkx==3.2.1
    - omegaconf==2.3.0
    - p-tqdm==1.4.0
    - packaging==24.0
    - palettable==3.3.3
    - pandas==2.2.2
    - pathos==0.3.2
    - pillow==10.3.0
    - plotly==5.21.0
    - pox==0.3.4
    - ppft==*******
    - protobuf==4.25.3
    - pybtex==0.24.0
    - pymatgen==2024.4.13
    - pymongo==4.7.0
    - pyparsing==3.1.2
    - python-dateutil==2.9.0.post0
    - python-dotenv==1.0.0
    - pytz==2024.1
    - pyxtal==0.6.7
    - ruamel-yaml==0.18.6
    - ruamel-yaml-clib==0.2.8
    - scikit-learn==1.4.2
    - scipy==1.13.0
    - sentry-sdk==2.0.1
    - setproctitle==1.3.3
    - six==1.16.0
    - smact==2.5.5
    - smmap==5.0.1
    - spglib==2.4.0
    - symd==1.1.0
    - tabulate==0.9.0
    - tenacity==8.2.3
    - threadpoolctl==3.4.0
    - tqdm==4.66.2
    - tzdata==2024.1
    - uncertainties==3.1.7
    - urllib3==2.2.1
    - wandb==0.16.6
    - zipp==3.18.1

