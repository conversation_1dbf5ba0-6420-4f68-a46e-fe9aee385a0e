
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

    <title>matminer &#8212; matminer 0.9.0 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="_static/nature.css" />
    <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="_static/doctools.js"></script>
    <script src="_static/sphinx_highlight.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
 
<link href='https://fonts.googleapis.com/css?family=Lato:400,700' rel='stylesheet' type='text/css'>

  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="matminer">
<h1>matminer<a class="headerlink" href="#matminer" title="Permalink to this heading">¶</a></h1>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="matminer.html">matminer package</a><ul>
<li class="toctree-l2"><a class="reference internal" href="matminer.html#subpackages">Subpackages</a><ul>
<li class="toctree-l3"><a class="reference internal" href="matminer.data_retrieval.html">matminer.data_retrieval package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#subpackages">Subpackages</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#module-matminer.data_retrieval.retrieve_AFLOW">matminer.data_retrieval.retrieve_AFLOW module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#module-matminer.data_retrieval.retrieve_Citrine">matminer.data_retrieval.retrieve_Citrine module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#module-matminer.data_retrieval.retrieve_MDF">matminer.data_retrieval.retrieve_MDF module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#module-matminer.data_retrieval.retrieve_MP">matminer.data_retrieval.retrieve_MP module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#module-matminer.data_retrieval.retrieve_MPDS">matminer.data_retrieval.retrieve_MPDS module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#module-matminer.data_retrieval.retrieve_MongoDB">matminer.data_retrieval.retrieve_MongoDB module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#module-matminer.data_retrieval.retrieve_base">matminer.data_retrieval.retrieve_base module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.data_retrieval.html#module-matminer.data_retrieval">Module contents</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.datasets.html">matminer.datasets package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.html#subpackages">Subpackages</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.html#module-matminer.datasets.convenience_loaders">matminer.datasets.convenience_loaders module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.html#module-matminer.datasets.dataset_retrieval">matminer.datasets.dataset_retrieval module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.html#module-matminer.datasets.utils">matminer.datasets.utils module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.datasets.html#module-matminer.datasets">Module contents</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.featurizers.html">matminer.featurizers package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#subpackages">Subpackages</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#module-matminer.featurizers.bandstructure">matminer.featurizers.bandstructure module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#module-matminer.featurizers.base">matminer.featurizers.base module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#module-matminer.featurizers.conversions">matminer.featurizers.conversions module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#module-matminer.featurizers.dos">matminer.featurizers.dos module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#module-matminer.featurizers.function">matminer.featurizers.function module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.featurizers.html#module-matminer.featurizers">Module contents</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="matminer.utils.html">matminer.utils package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#subpackages">Subpackages</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#module-matminer.utils.caching">matminer.utils.caching module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#module-matminer.utils.data">matminer.utils.data module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#module-matminer.utils.flatten_dict">matminer.utils.flatten_dict module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#module-matminer.utils.io">matminer.utils.io module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#module-matminer.utils.kernels">matminer.utils.kernels module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#module-matminer.utils.pipeline">matminer.utils.pipeline module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#module-matminer.utils.utils">matminer.utils.utils module</a></li>
<li class="toctree-l4"><a class="reference internal" href="matminer.utils.html#module-matminer.utils">Module contents</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="matminer.html#module-matminer">Module contents</a></li>
</ul>
</li>
</ul>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/modules.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2015, Anubhav Jain.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.3.0.
    </div>

  </body>
</html>