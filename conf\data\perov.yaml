root_path: ${oc.env:PROJECT_ROOT}/data/perov_5
prop: heat_ref
num_targets: 1
# prop: scaled_lattice
# num_targets: 6
niggli: true
primitive: false
graph_method: crystalnn
lattice_scale_method: scale_length
preprocess_workers: 30
readout: mean
max_atoms: 20
otf_graph: false
eval_model_name: perovskite
tolerance: 0.01

use_space_group: true
use_pos_index: false
number_representatives: 0
use_random_representatives: false

train_max_epochs: 3000
early_stopping_patience: 100000
teacher_forcing_max_epoch: 1500

eval_every_epoch: 500
eval_generate_samples: 100

datamodule:
  _target_: symmcd.pl_data.datamodule.CrystDataModule

  datasets:
    train:
      _target_: symmcd.pl_data.dataset.CrystDataset
      name: Formation energy train
      path: ${data.root_path}/train.csv
      save_path: ${data.root_path}/train_ori.pt
      prop: ${data.prop}
      niggli: ${data.niggli}
      primitive: ${data.primitive}
      graph_method: ${data.graph_method}
      tolerance: ${data.tolerance}
      use_space_group: ${data.use_space_group}
      use_pos_index: ${data.use_pos_index}
      number_representatives: ${data.number_representatives}
      use_random_representatives: ${data.use_random_representatives}
      lattice_scale_method: ${data.lattice_scale_method}
      preprocess_workers: ${data.preprocess_workers}
      gt_prop_eval_path:  ${data.root_path}/train_${data.eval_model_name}_${data.prop}.pt

    val:
      - _target_: symmcd.pl_data.dataset.CrystDataset
        name: Formation energy val
        path: ${data.root_path}/val.csv
        gt_crys_path: ${data.root_path}/val_gt_crys.pt
        save_path: ${data.root_path}/val_ori.pt
        prop: ${data.prop}
        niggli: ${data.niggli}
        primitive: ${data.primitive}
        graph_method: ${data.graph_method}
        tolerance: ${data.tolerance}
        use_space_group: ${data.use_space_group}
        use_pos_index: ${data.use_pos_index}
        number_representatives: ${data.number_representatives}
        use_random_representatives: ${data.use_random_representatives}
        lattice_scale_method: ${data.lattice_scale_method}
        preprocess_workers: ${data.preprocess_workers}
        gt_prop_eval_path:  ${data.root_path}/val_${data.eval_model_name}_${data.prop}.pt

    test:
      - _target_: symmcd.pl_data.dataset.CrystDataset
        name: Formation energy test
        path: ${data.root_path}/test.csv
        gt_crys_path: ${data.root_path}/test_gt_crys.pt
        save_path: ${data.root_path}/test_ori.pt
        prop: ${data.prop}
        niggli: ${data.niggli}
        primitive: ${data.primitive}
        graph_method: ${data.graph_method}
        tolerance: ${data.tolerance}
        use_space_group: ${data.use_space_group}
        use_pos_index: ${data.use_pos_index}
        number_representatives: ${data.number_representatives}
        use_random_representatives: ${data.use_random_representatives}
        lattice_scale_method: ${data.lattice_scale_method}
        preprocess_workers: ${data.preprocess_workers}
        gt_prop_eval_path:  ${data.root_path}/test_${data.eval_model_name}_${data.prop}.pt

  num_workers:
    train: 0
    val: 0
    test: 0

  batch_size:
    train: 1024
    val: 1024
    test: 256
