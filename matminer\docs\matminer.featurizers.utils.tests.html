
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

    <title>matminer.featurizers.utils.tests package &#8212; matminer 0.9.0 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="_static/nature.css" />
    <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="_static/doctools.js"></script>
    <script src="_static/sphinx_highlight.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
 
<link href='https://fonts.googleapis.com/css?family=Lato:400,700' rel='stylesheet' type='text/css'>

  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer.featurizers.utils.tests package</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="matminer-featurizers-utils-tests-package">
<h1>matminer.featurizers.utils.tests package<a class="headerlink" href="#matminer-featurizers-utils-tests-package" title="Permalink to this heading">¶</a></h1>
<section id="submodules">
<h2>Submodules<a class="headerlink" href="#submodules" title="Permalink to this heading">¶</a></h2>
</section>
<section id="module-matminer.featurizers.utils.tests.test_grdf">
<span id="matminer-featurizers-utils-tests-test-grdf-module"></span><h2>matminer.featurizers.utils.tests.test_grdf module<a class="headerlink" href="#module-matminer.featurizers.utils.tests.test_grdf" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.utils.tests.test_grdf.GRDFTests">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.utils.tests.test_grdf.</span></span><span class="sig-name descname"><span class="pre">GRDFTests</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.tests.test_grdf.GRDFTests" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">PymatgenTest</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_bessel">
<span class="sig-name descname"><span class="pre">test_bessel</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_bessel" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_cosine">
<span class="sig-name descname"><span class="pre">test_cosine</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_cosine" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_gaussian">
<span class="sig-name descname"><span class="pre">test_gaussian</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_gaussian" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_histogram">
<span class="sig-name descname"><span class="pre">test_histogram</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_histogram" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_load_class">
<span class="sig-name descname"><span class="pre">test_load_class</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_load_class" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_sin">
<span class="sig-name descname"><span class="pre">test_sin</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_sin" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.utils.tests.test_oxidation">
<span id="matminer-featurizers-utils-tests-test-oxidation-module"></span><h2>matminer.featurizers.utils.tests.test_oxidation module<a class="headerlink" href="#module-matminer.featurizers.utils.tests.test_oxidation" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.utils.tests.test_oxidation.OxidationTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.utils.tests.test_oxidation.</span></span><span class="sig-name descname"><span class="pre">OxidationTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.tests.test_oxidation.OxidationTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">PymatgenTest</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.tests.test_oxidation.OxidationTest.test_has_oxidation_states">
<span class="sig-name descname"><span class="pre">test_has_oxidation_states</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.tests.test_oxidation.OxidationTest.test_has_oxidation_states" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.utils.tests.test_stats">
<span id="matminer-featurizers-utils-tests-test-stats-module"></span><h2>matminer.featurizers.utils.tests.test_stats module<a class="headerlink" href="#module-matminer.featurizers.utils.tests.test_stats" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.featurizers.utils.tests.test_stats.TestPropertyStats">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.featurizers.utils.tests.test_stats.</span></span><span class="sig-name descname"><span class="pre">TestPropertyStats</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.tests.test_stats.TestPropertyStats" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.tests.test_stats.TestPropertyStats.setUp">
<span class="sig-name descname"><span class="pre">setUp</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.setUp" title="Permalink to this definition">¶</a></dt>
<dd><p>Hook method for setting up the test fixture before exercising it.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_avg_dev">
<span class="sig-name descname"><span class="pre">test_avg_dev</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_avg_dev" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_geom_std_dev">
<span class="sig-name descname"><span class="pre">test_geom_std_dev</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_geom_std_dev" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_holder_mean">
<span class="sig-name descname"><span class="pre">test_holder_mean</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_holder_mean" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_kurtosis">
<span class="sig-name descname"><span class="pre">test_kurtosis</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_kurtosis" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_maximum">
<span class="sig-name descname"><span class="pre">test_maximum</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_maximum" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_mean">
<span class="sig-name descname"><span class="pre">test_mean</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_mean" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_minimum">
<span class="sig-name descname"><span class="pre">test_minimum</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_minimum" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_mode">
<span class="sig-name descname"><span class="pre">test_mode</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_mode" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_quantile">
<span class="sig-name descname"><span class="pre">test_quantile</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_quantile" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_range">
<span class="sig-name descname"><span class="pre">test_range</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_range" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_skewness">
<span class="sig-name descname"><span class="pre">test_skewness</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_skewness" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_std_dev">
<span class="sig-name descname"><span class="pre">test_std_dev</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_std_dev" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.featurizers.utils.tests">
<span id="module-contents"></span><h2>Module contents<a class="headerlink" href="#module-matminer.featurizers.utils.tests" title="Permalink to this heading">¶</a></h2>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">matminer.featurizers.utils.tests package</a><ul>
<li><a class="reference internal" href="#submodules">Submodules</a></li>
<li><a class="reference internal" href="#module-matminer.featurizers.utils.tests.test_grdf">matminer.featurizers.utils.tests.test_grdf module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.utils.tests.test_grdf.GRDFTests"><code class="docutils literal notranslate"><span class="pre">GRDFTests</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_bessel"><code class="docutils literal notranslate"><span class="pre">GRDFTests.test_bessel()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_cosine"><code class="docutils literal notranslate"><span class="pre">GRDFTests.test_cosine()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_gaussian"><code class="docutils literal notranslate"><span class="pre">GRDFTests.test_gaussian()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_histogram"><code class="docutils literal notranslate"><span class="pre">GRDFTests.test_histogram()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_load_class"><code class="docutils literal notranslate"><span class="pre">GRDFTests.test_load_class()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_sin"><code class="docutils literal notranslate"><span class="pre">GRDFTests.test_sin()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.utils.tests.test_oxidation">matminer.featurizers.utils.tests.test_oxidation module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.utils.tests.test_oxidation.OxidationTest"><code class="docutils literal notranslate"><span class="pre">OxidationTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.utils.tests.test_oxidation.OxidationTest.test_has_oxidation_states"><code class="docutils literal notranslate"><span class="pre">OxidationTest.test_has_oxidation_states()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.utils.tests.test_stats">matminer.featurizers.utils.tests.test_stats module</a><ul>
<li><a class="reference internal" href="#matminer.featurizers.utils.tests.test_stats.TestPropertyStats"><code class="docutils literal notranslate"><span class="pre">TestPropertyStats</span></code></a><ul>
<li><a class="reference internal" href="#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.setUp"><code class="docutils literal notranslate"><span class="pre">TestPropertyStats.setUp()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_avg_dev"><code class="docutils literal notranslate"><span class="pre">TestPropertyStats.test_avg_dev()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_geom_std_dev"><code class="docutils literal notranslate"><span class="pre">TestPropertyStats.test_geom_std_dev()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_holder_mean"><code class="docutils literal notranslate"><span class="pre">TestPropertyStats.test_holder_mean()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_kurtosis"><code class="docutils literal notranslate"><span class="pre">TestPropertyStats.test_kurtosis()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_maximum"><code class="docutils literal notranslate"><span class="pre">TestPropertyStats.test_maximum()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_mean"><code class="docutils literal notranslate"><span class="pre">TestPropertyStats.test_mean()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_minimum"><code class="docutils literal notranslate"><span class="pre">TestPropertyStats.test_minimum()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_mode"><code class="docutils literal notranslate"><span class="pre">TestPropertyStats.test_mode()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_quantile"><code class="docutils literal notranslate"><span class="pre">TestPropertyStats.test_quantile()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_range"><code class="docutils literal notranslate"><span class="pre">TestPropertyStats.test_range()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_skewness"><code class="docutils literal notranslate"><span class="pre">TestPropertyStats.test_skewness()</span></code></a></li>
<li><a class="reference internal" href="#matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_std_dev"><code class="docutils literal notranslate"><span class="pre">TestPropertyStats.test_std_dev()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.featurizers.utils.tests">Module contents</a></li>
</ul>
</li>
</ul>

  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/matminer.featurizers.utils.tests.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer.featurizers.utils.tests package</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2015, Anubhav Jain.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.3.0.
    </div>

  </body>
</html>