
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

    <title>matminer.datasets.tests package &#8212; matminer 0.9.0 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="_static/nature.css" />
    <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="_static/doctools.js"></script>
    <script src="_static/sphinx_highlight.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
 
<link href='https://fonts.googleapis.com/css?family=Lato:400,700' rel='stylesheet' type='text/css'>

  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer.datasets.tests package</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="matminer-datasets-tests-package">
<h1>matminer.datasets.tests package<a class="headerlink" href="#matminer-datasets-tests-package" title="Permalink to this heading">¶</a></h1>
<section id="submodules">
<h2>Submodules<a class="headerlink" href="#submodules" title="Permalink to this heading">¶</a></h2>
</section>
<section id="module-matminer.datasets.tests.base">
<span id="matminer-datasets-tests-base-module"></span><h2>matminer.datasets.tests.base module<a class="headerlink" href="#module-matminer.datasets.tests.base" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.datasets.tests.base.DatasetTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.datasets.tests.base.</span></span><span class="sig-name descname"><span class="pre">DatasetTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.base.DatasetTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">TestCase</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.base.DatasetTest.setUp">
<span class="sig-name descname"><span class="pre">setUp</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.base.DatasetTest.setUp" title="Permalink to this definition">¶</a></dt>
<dd><p>Hook method for setting up the test fixture before exercising it.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-matminer.datasets.tests.test_convenience_loaders">
<span id="matminer-datasets-tests-test-convenience-loaders-module"></span><h2>matminer.datasets.tests.test_convenience_loaders module<a class="headerlink" href="#module-matminer.datasets.tests.test_convenience_loaders" title="Permalink to this heading">¶</a></h2>
</section>
<section id="module-matminer.datasets.tests.test_dataset_retrieval">
<span id="matminer-datasets-tests-test-dataset-retrieval-module"></span><h2>matminer.datasets.tests.test_dataset_retrieval module<a class="headerlink" href="#module-matminer.datasets.tests.test_dataset_retrieval" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.datasets.tests.test_dataset_retrieval.</span></span><span class="sig-name descname"><span class="pre">DataRetrievalTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.datasets.tests.base.DatasetTest" title="matminer.datasets.tests.base.DatasetTest"><code class="xref py py-class docutils literal notranslate"><span class="pre">DatasetTest</span></code></a></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_all_dataset_info">
<span class="sig-name descname"><span class="pre">test_get_all_dataset_info</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_all_dataset_info" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_attribute">
<span class="sig-name descname"><span class="pre">test_get_dataset_attribute</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_attribute" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_citations">
<span class="sig-name descname"><span class="pre">test_get_dataset_citations</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_citations" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_column_descriptions">
<span class="sig-name descname"><span class="pre">test_get_dataset_column_descriptions</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_column_descriptions" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_columns">
<span class="sig-name descname"><span class="pre">test_get_dataset_columns</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_columns" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_description">
<span class="sig-name descname"><span class="pre">test_get_dataset_description</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_description" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_num_entries">
<span class="sig-name descname"><span class="pre">test_get_dataset_num_entries</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_num_entries" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_reference">
<span class="sig-name descname"><span class="pre">test_get_dataset_reference</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_reference" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_load_dataset">
<span class="sig-name descname"><span class="pre">test_load_dataset</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_load_dataset" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_print_available_datasets">
<span class="sig-name descname"><span class="pre">test_print_available_datasets</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_print_available_datasets" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.datasets.tests.test_datasets">
<span id="matminer-datasets-tests-test-datasets-module"></span><h2>matminer.datasets.tests.test_datasets module<a class="headerlink" href="#module-matminer.datasets.tests.test_datasets" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.DataSetsTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.datasets.tests.test_datasets.</span></span><span class="sig-name descname"><span class="pre">DataSetsTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.DataSetsTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.datasets.tests.base.DatasetTest" title="matminer.datasets.tests.base.DatasetTest"><code class="xref py py-class docutils literal notranslate"><span class="pre">DatasetTest</span></code></a></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.DataSetsTest.universal_dataset_check">
<span class="sig-name descname"><span class="pre">universal_dataset_check</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dataset_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_headers</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">numeric_headers</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bool_headers</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">test_func</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.DataSetsTest.universal_dataset_check" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatbenchDatasetsTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.datasets.tests.test_datasets.</span></span><span class="sig-name descname"><span class="pre">MatbenchDatasetsTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatbenchDatasetsTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.datasets.tests.test_datasets.DataSetsTest" title="matminer.datasets.tests.test_datasets.DataSetsTest"><code class="xref py py-class docutils literal notranslate"><span class="pre">DataSetsTest</span></code></a></p>
<p>Matbench datasets are tested here.</p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatbenchDatasetsTest.test_matbench_v0_1">
<span class="sig-name descname"><span class="pre">test_matbench_v0_1</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatbenchDatasetsTest.test_matbench_v0_1" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.datasets.tests.test_datasets.</span></span><span class="sig-name descname"><span class="pre">MatminerDatasetsTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.datasets.tests.test_datasets.DataSetsTest" title="matminer.datasets.tests.test_datasets.DataSetsTest"><code class="xref py py-class docutils literal notranslate"><span class="pre">DataSetsTest</span></code></a></p>
<p>All datasets hosted with matminer are tested here, excluding matbench
datasets.</p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_boltztrap_mp">
<span class="sig-name descname"><span class="pre">test_boltztrap_mp</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_boltztrap_mp" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_brgoch_superhard_training">
<span class="sig-name descname"><span class="pre">test_brgoch_superhard_training</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_brgoch_superhard_training" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_castelli_perovskites">
<span class="sig-name descname"><span class="pre">test_castelli_perovskites</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_castelli_perovskites" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_citrine_thermal_conductivity">
<span class="sig-name descname"><span class="pre">test_citrine_thermal_conductivity</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_citrine_thermal_conductivity" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_dielectric_constant">
<span class="sig-name descname"><span class="pre">test_dielectric_constant</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_dielectric_constant" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_double_perovskites_gap">
<span class="sig-name descname"><span class="pre">test_double_perovskites_gap</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_double_perovskites_gap" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_double_perovskites_gap_lumo">
<span class="sig-name descname"><span class="pre">test_double_perovskites_gap_lumo</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_double_perovskites_gap_lumo" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_elastic_tensor_2015">
<span class="sig-name descname"><span class="pre">test_elastic_tensor_2015</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_elastic_tensor_2015" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_expt_formation_enthalpy">
<span class="sig-name descname"><span class="pre">test_expt_formation_enthalpy</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_expt_formation_enthalpy" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_expt_formation_enthalpy_kingsbury">
<span class="sig-name descname"><span class="pre">test_expt_formation_enthalpy_kingsbury</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_expt_formation_enthalpy_kingsbury" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_expt_gap">
<span class="sig-name descname"><span class="pre">test_expt_gap</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_expt_gap" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_expt_gap_kingsbury">
<span class="sig-name descname"><span class="pre">test_expt_gap_kingsbury</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_expt_gap_kingsbury" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_flla">
<span class="sig-name descname"><span class="pre">test_flla</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_flla" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_glass_binary">
<span class="sig-name descname"><span class="pre">test_glass_binary</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_glass_binary" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_glass_binary_v2">
<span class="sig-name descname"><span class="pre">test_glass_binary_v2</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_glass_binary_v2" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_glass_ternary_hipt">
<span class="sig-name descname"><span class="pre">test_glass_ternary_hipt</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_glass_ternary_hipt" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_glass_ternary_landolt">
<span class="sig-name descname"><span class="pre">test_glass_ternary_landolt</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_glass_ternary_landolt" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_heusler_magnetic">
<span class="sig-name descname"><span class="pre">test_heusler_magnetic</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_heusler_magnetic" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_jarvis_dft_2d">
<span class="sig-name descname"><span class="pre">test_jarvis_dft_2d</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_jarvis_dft_2d" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_jarvis_dft_3d">
<span class="sig-name descname"><span class="pre">test_jarvis_dft_3d</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_jarvis_dft_3d" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_jarvis_ml_dft_training">
<span class="sig-name descname"><span class="pre">test_jarvis_ml_dft_training</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_jarvis_ml_dft_training" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_m2ax">
<span class="sig-name descname"><span class="pre">test_m2ax</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_m2ax" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_mp_all_20181018">
<span class="sig-name descname"><span class="pre">test_mp_all_20181018</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_mp_all_20181018" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_mp_nostruct_20181018">
<span class="sig-name descname"><span class="pre">test_mp_nostruct_20181018</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_mp_nostruct_20181018" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_phonon_dielectric_mp">
<span class="sig-name descname"><span class="pre">test_phonon_dielectric_mp</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_phonon_dielectric_mp" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_piezoelectric_tensor">
<span class="sig-name descname"><span class="pre">test_piezoelectric_tensor</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_piezoelectric_tensor" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_ricci_boltztrap_mp_tabular">
<span class="sig-name descname"><span class="pre">test_ricci_boltztrap_mp_tabular</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_ricci_boltztrap_mp_tabular" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_steel_strength">
<span class="sig-name descname"><span class="pre">test_steel_strength</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_steel_strength" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_superconductivity2018">
<span class="sig-name descname"><span class="pre">test_superconductivity2018</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_superconductivity2018" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_tholander_nitrides_e_form">
<span class="sig-name descname"><span class="pre">test_tholander_nitrides_e_form</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_tholander_nitrides_e_form" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_ucsb_thermoelectrics">
<span class="sig-name descname"><span class="pre">test_ucsb_thermoelectrics</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_ucsb_thermoelectrics" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_wolverton_oxides">
<span class="sig-name descname"><span class="pre">test_wolverton_oxides</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_wolverton_oxides" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.datasets.tests.test_utils">
<span id="matminer-datasets-tests-test-utils-module"></span><h2>matminer.datasets.tests.test_utils module<a class="headerlink" href="#module-matminer.datasets.tests.test_utils" title="Permalink to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_utils.UtilsTest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">matminer.datasets.tests.test_utils.</span></span><span class="sig-name descname"><span class="pre">UtilsTest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">methodName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'runTest'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_utils.UtilsTest" title="Permalink to this definition">¶</a></dt>
<dd><p>Bases: <a class="reference internal" href="#matminer.datasets.tests.base.DatasetTest" title="matminer.datasets.tests.base.DatasetTest"><code class="xref py py-class docutils literal notranslate"><span class="pre">DatasetTest</span></code></a></p>
<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_utils.UtilsTest.test_fetch_external_dataset">
<span class="sig-name descname"><span class="pre">test_fetch_external_dataset</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_utils.UtilsTest.test_fetch_external_dataset" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_utils.UtilsTest.test_get_data_home">
<span class="sig-name descname"><span class="pre">test_get_data_home</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_utils.UtilsTest.test_get_data_home" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_utils.UtilsTest.test_get_file_sha256_hash">
<span class="sig-name descname"><span class="pre">test_get_file_sha256_hash</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_utils.UtilsTest.test_get_file_sha256_hash" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_utils.UtilsTest.test_load_dataset_dict">
<span class="sig-name descname"><span class="pre">test_load_dataset_dict</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_utils.UtilsTest.test_load_dataset_dict" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_utils.UtilsTest.test_read_dataframe_from_file">
<span class="sig-name descname"><span class="pre">test_read_dataframe_from_file</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_utils.UtilsTest.test_read_dataframe_from_file" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="matminer.datasets.tests.test_utils.UtilsTest.test_validate_dataset">
<span class="sig-name descname"><span class="pre">test_validate_dataset</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#matminer.datasets.tests.test_utils.UtilsTest.test_validate_dataset" title="Permalink to this definition">¶</a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-matminer.datasets.tests">
<span id="module-contents"></span><h2>Module contents<a class="headerlink" href="#module-matminer.datasets.tests" title="Permalink to this heading">¶</a></h2>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">matminer.datasets.tests package</a><ul>
<li><a class="reference internal" href="#submodules">Submodules</a></li>
<li><a class="reference internal" href="#module-matminer.datasets.tests.base">matminer.datasets.tests.base module</a><ul>
<li><a class="reference internal" href="#matminer.datasets.tests.base.DatasetTest"><code class="docutils literal notranslate"><span class="pre">DatasetTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.datasets.tests.base.DatasetTest.setUp"><code class="docutils literal notranslate"><span class="pre">DatasetTest.setUp()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.datasets.tests.test_convenience_loaders">matminer.datasets.tests.test_convenience_loaders module</a></li>
<li><a class="reference internal" href="#module-matminer.datasets.tests.test_dataset_retrieval">matminer.datasets.tests.test_dataset_retrieval module</a><ul>
<li><a class="reference internal" href="#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest"><code class="docutils literal notranslate"><span class="pre">DataRetrievalTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_all_dataset_info"><code class="docutils literal notranslate"><span class="pre">DataRetrievalTest.test_get_all_dataset_info()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_attribute"><code class="docutils literal notranslate"><span class="pre">DataRetrievalTest.test_get_dataset_attribute()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_citations"><code class="docutils literal notranslate"><span class="pre">DataRetrievalTest.test_get_dataset_citations()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_column_descriptions"><code class="docutils literal notranslate"><span class="pre">DataRetrievalTest.test_get_dataset_column_descriptions()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_columns"><code class="docutils literal notranslate"><span class="pre">DataRetrievalTest.test_get_dataset_columns()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_description"><code class="docutils literal notranslate"><span class="pre">DataRetrievalTest.test_get_dataset_description()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_num_entries"><code class="docutils literal notranslate"><span class="pre">DataRetrievalTest.test_get_dataset_num_entries()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_reference"><code class="docutils literal notranslate"><span class="pre">DataRetrievalTest.test_get_dataset_reference()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_load_dataset"><code class="docutils literal notranslate"><span class="pre">DataRetrievalTest.test_load_dataset()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_print_available_datasets"><code class="docutils literal notranslate"><span class="pre">DataRetrievalTest.test_print_available_datasets()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.datasets.tests.test_datasets">matminer.datasets.tests.test_datasets module</a><ul>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.DataSetsTest"><code class="docutils literal notranslate"><span class="pre">DataSetsTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.DataSetsTest.universal_dataset_check"><code class="docutils literal notranslate"><span class="pre">DataSetsTest.universal_dataset_check()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatbenchDatasetsTest"><code class="docutils literal notranslate"><span class="pre">MatbenchDatasetsTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatbenchDatasetsTest.test_matbench_v0_1"><code class="docutils literal notranslate"><span class="pre">MatbenchDatasetsTest.test_matbench_v0_1()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_boltztrap_mp"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_boltztrap_mp()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_brgoch_superhard_training"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_brgoch_superhard_training()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_castelli_perovskites"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_castelli_perovskites()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_citrine_thermal_conductivity"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_citrine_thermal_conductivity()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_dielectric_constant"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_dielectric_constant()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_double_perovskites_gap"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_double_perovskites_gap()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_double_perovskites_gap_lumo"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_double_perovskites_gap_lumo()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_elastic_tensor_2015"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_elastic_tensor_2015()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_expt_formation_enthalpy"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_expt_formation_enthalpy()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_expt_formation_enthalpy_kingsbury"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_expt_formation_enthalpy_kingsbury()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_expt_gap"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_expt_gap()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_expt_gap_kingsbury"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_expt_gap_kingsbury()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_flla"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_flla()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_glass_binary"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_glass_binary()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_glass_binary_v2"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_glass_binary_v2()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_glass_ternary_hipt"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_glass_ternary_hipt()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_glass_ternary_landolt"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_glass_ternary_landolt()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_heusler_magnetic"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_heusler_magnetic()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_jarvis_dft_2d"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_jarvis_dft_2d()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_jarvis_dft_3d"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_jarvis_dft_3d()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_jarvis_ml_dft_training"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_jarvis_ml_dft_training()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_m2ax"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_m2ax()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_mp_all_20181018"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_mp_all_20181018()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_mp_nostruct_20181018"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_mp_nostruct_20181018()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_phonon_dielectric_mp"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_phonon_dielectric_mp()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_piezoelectric_tensor"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_piezoelectric_tensor()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_ricci_boltztrap_mp_tabular"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_ricci_boltztrap_mp_tabular()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_steel_strength"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_steel_strength()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_superconductivity2018"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_superconductivity2018()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_tholander_nitrides_e_form"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_tholander_nitrides_e_form()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_ucsb_thermoelectrics"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_ucsb_thermoelectrics()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_wolverton_oxides"><code class="docutils literal notranslate"><span class="pre">MatminerDatasetsTest.test_wolverton_oxides()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.datasets.tests.test_utils">matminer.datasets.tests.test_utils module</a><ul>
<li><a class="reference internal" href="#matminer.datasets.tests.test_utils.UtilsTest"><code class="docutils literal notranslate"><span class="pre">UtilsTest</span></code></a><ul>
<li><a class="reference internal" href="#matminer.datasets.tests.test_utils.UtilsTest.test_fetch_external_dataset"><code class="docutils literal notranslate"><span class="pre">UtilsTest.test_fetch_external_dataset()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_utils.UtilsTest.test_get_data_home"><code class="docutils literal notranslate"><span class="pre">UtilsTest.test_get_data_home()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_utils.UtilsTest.test_get_file_sha256_hash"><code class="docutils literal notranslate"><span class="pre">UtilsTest.test_get_file_sha256_hash()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_utils.UtilsTest.test_load_dataset_dict"><code class="docutils literal notranslate"><span class="pre">UtilsTest.test_load_dataset_dict()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_utils.UtilsTest.test_read_dataframe_from_file"><code class="docutils literal notranslate"><span class="pre">UtilsTest.test_read_dataframe_from_file()</span></code></a></li>
<li><a class="reference internal" href="#matminer.datasets.tests.test_utils.UtilsTest.test_validate_dataset"><code class="docutils literal notranslate"><span class="pre">UtilsTest.test_validate_dataset()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-matminer.datasets.tests">Module contents</a></li>
</ul>
</li>
</ul>

  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/matminer.datasets.tests.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">matminer 0.9.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">matminer.datasets.tests package</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2015, Anubhav Jain.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.3.0.
    </div>

  </body>
</html>