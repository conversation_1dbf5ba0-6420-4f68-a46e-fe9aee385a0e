import time
import argparse
import torch
import csv
import os
from collections import Counter, defaultdict
from tqdm import tqdm
from torch.optim import Adam
from pathlib import Path
from types import SimpleNamespace
from torch_geometric.data import Data, Batch, DataLoader
from torch.utils.data import Dataset

from pymatgen.core.structure import Structure
from pymatgen.core.lattice import <PERSON><PERSON><PERSON>
from pymatgen.symmetry.analyzer import SpacegroupAnalyzer
from pymatgen.io.cif import CifWriter
from pyxtal.symmetry import Group
import chemparse
import numpy as np
from p_tqdm import p_map
import random
from sample_api import dataset_for_predictor, dataset_for_predictor_from_json, generate_sg_dist_from_dataset
import json

EPS = 1e-4*np.random.randn(3)
POINT = np.array([0.5, 0.5, 0.5]) + EPS
CON_RANGE = {'band_gap' : [0,9],
             'formation_energy_per_atom' : [-6,1],
            }


import sys
sys.path.append('.')
from scripts.eval_utils import load_model, lattices_to_params_shape, get_crystals_list


train_dist = {
    'perov' : [0, 0, 0, 0, 0, 1],
    'carbon' : [0.0,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
                0.3250697750779839,
                0.0,
                0.27795107535708424,
                0.0,
                0.15383352487276308,
                0.0,
                0.11246100804465604,
                0.0,
                0.04958134953209654,
                0.0,
                0.038745690362830404,
                0.0,
                0.019044491873255624,
                0.0,
                0.010178952552946971,
                0.0,
                0.007059596125430964,
                0.0,
                0.006074536200952225],
    'mp' : [0.0,
            0.0021742334905660377,
            0.021079009433962265,
            0.019826061320754717,
            0.15271226415094338,
            0.047132959905660375,
            0.08464770047169812,
            0.021079009433962265,
            0.07808814858490566,
            0.03434551886792453,
            0.0972877358490566,
            0.013303360849056603,
            0.09669811320754718,
            0.02155807783018868,
            0.06522700471698113,
            0.014372051886792452,
            0.06703272405660378,
            0.00972877358490566,
            0.053176591981132074,
            0.010576356132075472,
            0.08995430424528301]
}

def diffusion(loader, model, step_lr):

    frac_coords = []
    num_atoms = []
    atom_types = []
    lattices = []
    spacegroups = []
    site_symmetries = []
    for idx, batch in enumerate(loader):

        if torch.cuda.is_available():
            batch.cuda()
        outputs, traj = model.sample(batch, step_lr = step_lr)
        del traj
        frac_coords.append(outputs['frac_coords'].detach().cpu())
        num_atoms.append(outputs['num_atoms'].detach().cpu())
        atom_types.append(outputs['atom_types'].detach().cpu())
        lattices.append(outputs['lattices'].detach().cpu())
        spacegroups.append(outputs['spacegroup'].detach().cpu())
        site_symmetries.append(outputs['site_symm'].detach().cpu())

    frac_coords = torch.cat(frac_coords, dim=0)
    num_atoms = torch.cat(num_atoms, dim=0)
    atom_types = torch.cat(atom_types, dim=0)
    lattices = torch.cat(lattices, dim=0)
    spacegroups = torch.cat(spacegroups, dim=0)
    site_symmetries = torch.cat(site_symmetries, dim=0)
    lengths, angles = lattices_to_params_shape(lattices)

    return (
        frac_coords, atom_types, lattices, lengths, angles, num_atoms, spacegroups, site_symmetries
    )

class SampleDataset(Dataset):

    def __init__(self, dataset, total_num, train_ori_path=None, sg_info_path=None, restrict_spacegroups=None, cfg=None, gen_list=None):
        # pass the training data in the `train_ori_path` 
        # to get the distribution of space groups and atom numbers in the training dataset
        # restrict_spacegroups: np array of spacegroups to sample from (using renormalized full probabilities)
        
        super().__init__()
        self.total_num = total_num
        self.cfg = cfg
        #self.distribution = train_dist[dataset]
        #self.num_atoms = np.random.choice(len(self.distribution), total_num, p = self.distribution)
        self.is_carbon = dataset == 'carbon'

        self.sg_num_atoms, self.sg_dist, self.sg_number_binary_mapper = self.get_sg_statistics(train_ori_path, sg_info_path)

        if restrict_spacegroups is not None:
            print("Sampling ONLY from spacegroups " + str(restrict_spacegroups))
            new_sg_dist = np.zeros_like(self.sg_dist)
            new_sg_dist[restrict_spacegroups - 1] = self.sg_dist[restrict_spacegroups - 1]
            new_sg_dist = new_sg_dist / new_sg_dist.sum()
            self.sg_dist = new_sg_dist
        self.gen_list = gen_list

    def __len__(self) -> int:
        return self.total_num

    def __getitem__(self, index):
        p_sg_dist = self.sg_dist[index]
        spacegroup = np.random.choice(230, p = p_sg_dist) + 1
        num_atom = np.random.choice(list(self.sg_num_atoms[spacegroup].keys()), p = list(self.sg_num_atoms[spacegroup].values()))

        data = Data(
            num_atoms=torch.LongTensor([num_atom]),
            num_nodes=num_atom,
            spacegroup=spacegroup, # number
            sg_condition=self.sg_number_binary_mapper[spacegroup], # float tensor
        )

        if self.cfg.model.conditionmodel:
            condition_data = {}
            for condition_emb in self.cfg.model.conditionmodel.condition_embeddings:
                con_n = condition_emb.condition_name
                if args.json_file:
                    try:
                        condition_data[con_n] = self.gen_list[index][con_n]
                    except:
                        if con_n in CON_RANGE:
                            random_value = random.uniform(CON_RANGE[con_n][0],CON_RANGE[con_n][1])
                            condition_data[con_n] = random_value
                            print(f'leak {con_n} value, use random value: {random_value}')
                        else:
                            print(f'leak {con_n} value!')
                else:
                    try:
                        condition_data[con_n] = float(getattr(args, con_n))
                    except:
                        if con_n in CON_RANGE:
                            random_value = random.uniform(CON_RANGE[con_n][0],CON_RANGE[con_n][1])
                            condition_data[con_n] = random_value
                            print(f'leak {con_n} value, use random value: {random_value}')
                        else:
                            print(f'leak {con_n} value!')
            # print(condition_data)
            data.update(condition_data)

        if self.is_carbon:
            data.atom_types = torch.LongTensor([6] * num_atom)
        return data

    def get_sg_statistics(self, train_path=None, sg_info_path=None):
        '''
        Get per-spacegroup
        '''
        if sg_info_path and os.path.exists(sg_info_path):
            print(f'Loading spacegroup statistics from {sg_info_path}')
            sg_num_atoms = torch.load(os.path.join(sg_info_path, 'sg_num_atoms.pt'))
            sg_dist = torch.load(os.path.join(sg_info_path, 'sg_dist.pt'))
            sg_number_binary_mapper = torch.load(os.path.join(sg_info_path, 'sg_number_binary_mapper.pt'))
            return sg_num_atoms, sg_dist, sg_number_binary_mapper
        
        dataset = torch.load(train_path)
        dataset_len = len(dataset)
        
        sg_counter = defaultdict(lambda : 0)
        sg_num_atoms = defaultdict(lambda : defaultdict(lambda : 0))
        sg_number_binary_mapper = {}
        for i in range(dataset_len):
            data_dict = dataset[i]
            (frac_coords, atom_types, lengths, angles, ks, edge_indices,
            to_jimages, num_atoms) = data_dict['graph_arrays']
            spacegroup = data_dict['spacegroup']
            # masking on the basis of identifiers of orbits in a crystal
            identifiers = data_dict['identifier']
            
            mask = np.zeros_like(identifiers)

            # Process each unique identifier
            for identifier in np.unique(identifiers):
                # Find indices where this identifier occurs
                indices = np.where(identifiers == identifier)[0]
                # Get index closest to random point in center
                min_index = ((frac_coords - POINT)**2).sum(1)[indices].argmin().item()
                mask[indices[min_index]] = 1

            frac_coords = frac_coords[mask.astype(bool)]
            atom_types = atom_types[mask.astype(bool)]
            num_atoms = len(frac_coords)

            sg_counter[spacegroup] += 1
            sg_number_binary_mapper[spacegroup] = data_dict['sg_binary']
            sg_num_atoms[spacegroup][num_atoms] += 1

        # spacegroup distribution
        sg_dist = []
        for i in range(1, 231): sg_dist.append(sg_counter[i])
        sg_dist = np.array(sg_dist)
        sg_dist = sg_dist / dataset_len
        sg_number_binary_mapper = sg_number_binary_mapper
        
        # for each space group, atom number distribution
        for sg in sg_num_atoms:
            total = sum(sg_num_atoms[sg].values())
            for num_atoms in sg_num_atoms[sg]: sg_num_atoms[sg][num_atoms] /= total
        if sg_info_path:
            sg_num_atoms_hashable = {k: {kk: vv for kk, vv in v.items()} for k, v in sg_num_atoms.items()}
            torch.save((sg_num_atoms_hashable, sg_dist, sg_number_binary_mapper), sg_info_path)
        return  sg_num_atoms, sg_dist, sg_number_binary_mapper

def save_cif(model_path, crys_array_list, label):
    if label == '':
        cif_path = model_path / 'crystal.csv'
    else:
        cif_path = model_path / f'crystal_{label}.csv'
    with open(cif_path, 'a', newline='') as csvfile:
        csvwriter = csv.writer(csvfile, delimiter=' ')
        for i in tqdm(range(len(crys_array_list))):
            crys_dict = crys_array_list[i]
            if len(crys_dict['atom_types'].shape) == 2:
                atom_types = crys_dict['atom_types'].argmax(-1) + 1
            else:
                atom_types = crys_dict['atom_types']
            crystal = Structure(
                lattice=Lattice.from_parameters(
                    *(crys_dict['lengths'].tolist() + crys_dict['angles'].tolist())),
                species=atom_types,
                coords=crys_dict['frac_coords'],
                coords_are_cartesian=False)
            csvwriter.writerow([crystal.to(fmt='cif')])

def save_json(model_path, crys_array_list, label, json_file):
    if label == '':
        json_path = model_path / 'crystal.json'
    else:
        json_path = model_path / f'crystal_{label}.json'
    samples = []
    con_list = []

    with open(json_file,'r',encoding='UTF-8') as f:
        json_list = json.load(f)

    for json_data in json_list:
        nums = json_data["sample_nums"]
        json_data.pop("sample_nums", None)
        for num in range(nums):
            con_list.append(json_data)

    for i in tqdm(range(len(crys_array_list))):
        sample = {}
        crys_dict = crys_array_list[i]

        if len(crys_dict['atom_types'].shape) == 2:
            atom_types = crys_dict['atom_types'].argmax(-1) + 1
        else:
            atom_types = crys_dict['atom_types']
        crystal = Structure(
            lattice=Lattice.from_parameters(
                *(crys_dict['lengths'].tolist() + crys_dict['angles'].tolist())),
            species=atom_types,
            coords=crys_dict['frac_coords'],
            coords_are_cartesian=False)
        sample['cif'] = str(CifWriter(crystal))
        sample['id'] = i
        sample.update(con_list[i])

        samples.append(sample)

    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(samples, f, indent = 8, ensure_ascii=False)      

def main(args):

    gen_list = None
    total_num = args.batch_size * args.num_batches_to_samples
    if args.json_file != '':
        dataset, gen_list = dataset_for_predictor_from_json(args.json_file)
        total_num = len(gen_list)
    else:
        dataset = dataset_for_predictor(args.composition, args.band_gap, args.formation_energy_per_atom, total_num)

    start_time = time.time()
    outs = generate_sg_dist_from_dataset(args.predictor_path, dataset, args.batch_size)
    sg_dist = []

    for out in outs: 
        sg_dist_tensor = out/(out.sum())
        sg_dist_array = np.array(sg_dist_tensor.cpu())
        sg_dist.append(sg_dist_array)

    torch.save(sg_dist, args.sg_info_path + '/sg_dist.pt')
    print('Save Probability Distribution of Space Group.')

    model_path = Path(args.model_path)
    model, _, cfg = load_model(
        model_path, load_data=False)

    if torch.cuda.is_available():
        model.to('cuda')

    print('Evaluate the diffusion model.')
    restrict_spacegroups = np.array(args.restrict_spacegroups) if args.restrict_spacegroups is not None else None

    test_set = SampleDataset(args.dataset, 
                            total_num, 
                            train_ori_path=cfg.data.datamodule.datasets.train.save_path,
                            sg_info_path=args.sg_info_path,
                            restrict_spacegroups=restrict_spacegroups,
                            cfg=cfg,
                            gen_list=gen_list,
                            )

    test_loader = DataLoader(test_set, batch_size = args.batch_size)

    start_time = time.time()
    (frac_coords, atom_types, lattices, lengths, angles, num_atoms, spacegroups, site_symmetries) = diffusion(test_loader, model, args.step_lr)

    if args.label == '':
        gen_out_name = 'eval_gen.pt'
    else:
        gen_out_name = f'eval_gen_{args.label}.pt'

    torch.save({
        'eval_setting': args,
        'frac_coords': frac_coords,
        'num_atoms': num_atoms,
        'atom_types': atom_types,
        'lengths': lengths,
        'angles': angles,
        'spacegroups': spacegroups,
        'site_symmetries': site_symmetries,
    }, model_path / gen_out_name)

    if args.save_cif is not None:
        crys_array_list = get_crystals_list(frac_coords, atom_types, lengths, angles, num_atoms, spacegroups=spacegroups, site_symmetries=site_symmetries)
        save_cif(model_path, crys_array_list, args.label)

    if args.save_json is not None and args.json_file:
        crys_array_list = get_crystals_list(frac_coords, atom_types, lengths, angles, num_atoms, spacegroups=spacegroups, site_symmetries=site_symmetries)
        save_json(model_path, crys_array_list, args.label, args.json_file)



if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--predictor_path', required=True)
    parser.add_argument('--composition', default=None, type=str)
    parser.add_argument('--band_gap', type=float)
    parser.add_argument('--formation_energy_per_atom', type=float)
    parser.add_argument('--model_path', required=True)
    parser.add_argument('--dataset', required=True)
    parser.add_argument('--step_lr', default=1e-5, type=float, help='step size for Langevin dynamics')
    parser.add_argument('--num_batches_to_samples', default=1, type=int)
    parser.add_argument('--batch_size', default=100, type=int)
    parser.add_argument('--json_file', type=str, default='')
    parser.add_argument('--label', default='')
    parser.add_argument('--restrict_spacegroups', nargs='+', type=int, help='list of spacegroups to sample from')
    parser.add_argument('--save_cif', help='option to save cif files', default=None)
    parser.add_argument('--save_json', help='option to save json files', default=None)
    parser.add_argument('--sg_info_path', default='./sg_info', type=str, help='space group information to build dataset')
    args = parser.parse_args()

    main(args)
