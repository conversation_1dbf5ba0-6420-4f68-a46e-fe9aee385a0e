# CrystalGen

## Setup

#### Recommended installation method

It is recommended to install each necessary package (with appropriate versions mentioned in the `symmcd.yaml`) file following this order: `pytorch`, `pytorch-lightning`, `pyg`, `pyxtal`, `pymatgen`, `matminer`, `e<PERSON>ps`, `hydra-core`, `symd`, `dotenv`, `wandb`, `p_tqdm`, `torch_scatter`, `torch_sparse`, `smact`, `chemparse`.

#### Other installation method

1. Rename the `.env.template` file into `.env` and specify the following variables.

```
PROJECT_ROOT: the absolute path of this repo
HYDRA_JOBS: the absolute path to save hydra outputs
WABDB_DIR: the absolute path to save wandb outputs
WABDB_DIR: the absolute path to save wandb cache outputs
```

2. Create the anaconda environment with the `symmcd.yml` file (`conda env create -f symmcd.yml`: creates environment with name `symmcd`)

3. Install `matminer` with `cd matminer && pip install -e .`

4. Clone [cdvae repo](https://github.com/txie-93/cdvae) in the same directory level as `conf` and `data` and install it

5. You can now execute the training command [below](#Training) without errors. If it throws some package error, please install those (and create a PR).

```
CrystalGen (should now look like this, and the environment should contain cdvae and matminer packages)
├── cdvae
├── conf
├── data 
├── symmcd
├── matminer
├── scripts
├── .env
├── .........
```

## Training

Before training, you should change the following in `cdvae/cdvae/pl_modules/gnn.py` (`swish` can no longer be imported from `torch_geometric.nn.acts`):

* Comment out the import: `from torch_geometric.nn.acts import swish`
* Add the following lines to the script before the `class InteractionPPBlock`:

```
def swish(x):
    return x * x.sigmoid()
```

#### For the Ab Initio Generation task

```
python symmcd/run.py data=<dataset> model=discrete_diffusion_w_site_symm expname=<expname>
```

#### For the Property-conditioned Generation task

Modify the `yaml` file in `/conf/model/conditionmodel`  to ensure that the dataset contains the material properties that need to be embedded, then run:

```
python symmcd/run_finetune_mg.py data=<dataset> model=discrete_diffusion_w_site_symm_con_mg expname=<expname>
```

## Evaluation

#### Ab initio generation

```
python scripts/generation.py --model_path <model_path> --dataset <dataset>
python scripts/compute_metrics --root_path <model_path> --tasks gen --gt_file data/<dataset>/test.csv

```

#### Property-conditioned Generation

```
python scripts/generation_nan.py --model_path <model_path> --dataset <dataset> --save_cif True --save_json True
python scripts/compute_metrics.py --root_path <model_path> --tasks gen --gt_file data/<dataset>/test.csv
```





