_target_: symmcd.pl_modules.ConditionModel.ConditioningModule_like_MatterGen
n_features: 1024
n_layers: 2
n_cond: 1 #条件数量 记得更改！！！

condition_embeddings:
  # - _target_: symmcd.pl_modules.ConditionModel.ScalarConditionEmbedding_like_MatterGen
  #   condition_name: formation_energy_per_atom
  #   condition_min: -6.0
  #   condition_max: 1.0
  #   grid_spacing: 0.5
  #   n_features: 1024
  #   n_layers: 1
    
  - _target_: symmcd.pl_modules.ConditionModel.ScalarConditionEmbedding_like_MatterGen
    condition_name: band_gap # dft_band_gap band_gap
    condition_min: -1.0
    condition_max: 20.0
    grid_spacing: 0.5
    n_features: 1024
    n_layers: 1

  # - _target_: symmcd.pl_modules.ConditionModel.ScalarConditionEmbedding_like_MatterGen
  #   condition_name: energy_above_hull # energy_above_hull e_above_hull
  #   condition_min: 0.00
  #   condition_max: 0.10
  #   grid_spacing: 0.01
  #   n_features: 1024
  #   n_layers: 1

  # - _target_: symmcd.pl_modules.ConditionModel.ScalarConditionEmbedding_like_MatterGen
  #   condition_name: ml_bulk_modulus # ml_bulk_modulus
  #   condition_min: 0.
  #   condition_max: 400.
  #   grid_spacing: 10
  #   n_features: 1024
  #   n_layers: 1

  # - _target_: symmcd.pl_modules.ConditionModel.ChemicalSystemMultiHotEmbedding
  #   condition_name: elements # elements chemical_system
  #   n_features: 1024