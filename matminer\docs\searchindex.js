Search.setIndex({"docnames": ["changelog", "contributors", "dataset_addition_guide", "dataset_summary", "example_bulkmod", "featurizer_summary", "index", "installation", "matminer", "matminer.data_retrieval", "matminer.data_retrieval.tests", "matminer.datasets", "matminer.datasets.tests", "matminer.featurizers", "matminer.featurizers.composition", "matminer.featurizers.composition.tests", "matminer.featurizers.site", "matminer.featurizers.site.tests", "matminer.featurizers.structure", "matminer.featurizers.structure.tests", "matminer.featurizers.tests", "matminer.featurizers.utils", "matminer.featurizers.utils.tests", "matminer.figrecipes", "matminer.figrecipes.tests", "matminer.utils", "matminer.utils.data_files", "matminer.utils.tests", "modules"], "filenames": ["changelog.rst", "contributors.rst", "dataset_addition_guide.rst", "dataset_summary.rst", "example_bulkmod.rst", "featurizer_summary.rst", "index.rst", "installation.rst", "matminer.rst", "matminer.data_retrieval.rst", "matminer.data_retrieval.tests.rst", "matminer.datasets.rst", "matminer.datasets.tests.rst", "matminer.featurizers.rst", "matminer.featurizers.composition.rst", "matminer.featurizers.composition.tests.rst", "matminer.featurizers.site.rst", "matminer.featurizers.site.tests.rst", "matminer.featurizers.structure.rst", "matminer.featurizers.structure.tests.rst", "matminer.featurizers.tests.rst", "matminer.featurizers.utils.rst", "matminer.featurizers.utils.tests.rst", "matminer.figrecipes.rst", "matminer.figrecipes.tests.rst", "matminer.utils.rst", "matminer.utils.data_files.rst", "matminer.utils.tests.rst", "modules.rst"], "titles": ["MatMiner Changelog", "MatMiner Contributors", "Guide to adding datasets to matminer", "Table of Datasets", "Predicting bulk moduli with matminer", "bandstructure", "matminer (Materials Data Mining)", "Installing matminer", "matminer package", "matminer.data_retrieval package", "matminer.data_retrieval.tests package", "matminer.datasets package", "matminer.datasets.tests package", "matminer.featurizers package", "matminer.featurizers.composition package", "matminer.featurizers.composition.tests package", "matminer.featurizers.site package", "matminer.featurizers.site.tests package", "matminer.featurizers.structure package", "matminer.featurizers.structure.tests package", "matminer.featurizers.tests package", "matminer.featurizers.utils package", "matminer.featurizers.utils.tests package", "matminer.figrecipes package", "matminer.figrecipes.tests package", "matminer.utils package", "matminer.utils.data_files package", "matminer.utils.tests package", "matminer"], "terms": {"start": [0, 1, 13, 18, 21], "v0": [0, 1, 3, 9], "6": [0, 1, 3, 6, 7, 14, 16, 18], "onward": [0, 1], "i": [0, 1, 2, 3, 4, 6, 7, 9, 11, 13, 14, 15, 16, 18, 20, 21, 25], "longer": [0, 1], "maintain": [0, 1, 2, 18], "pleas": [0, 1, 6, 9, 14, 16, 18, 25], "check": [0, 1, 2, 6, 13, 14, 15, 18, 21], "github": [0, 1, 4, 6, 7, 9, 13, 16, 18, 25], "commit": [0, 2], "log": [0, 7, 18], "record": [0, 3, 9, 25], "chang": [0, 3, 6, 9, 13, 14, 16, 20, 25], "5": [0, 3, 9, 13, 14, 16, 18, 21], "updat": [0, 13, 18, 20], "bv": [0, 18], "paramet": [0, 3, 5, 9, 13, 14, 16, 18, 20, 25], "2020": [0, 3, 14], "version": [0, 2, 3, 6, 7, 11, 16], "exist": [0, 2, 3, 5, 13, 14, 18, 25], "2016": [0, 3, 9, 16, 18, 25], "includ": [0, 2, 3, 5, 6, 7, 9, 11, 13, 14, 16, 18, 20, 25], "error": [0, 3, 6, 9, 13, 16, 18, 25], "rh4": 0, "fix": [0, 2, 13, 16], "matbench": [0, 3, 6, 12], "dataset": [0, 4, 8, 13, 14, 25, 28], "url": [0, 2, 3, 9], "4": [0, 3, 6, 9, 16, 18], "make": [0, 3, 6, 7, 9, 13, 16, 18, 21, 25], "basefeatur": [0, 5, 8, 13, 14, 16, 18, 20], "an": [0, 2, 3, 4, 5, 6, 9, 13, 14, 16, 17, 18, 20, 25], "abc": [0, 13], "abstractmethod": 0, "j": [0, 3, 6, 9, 14, 16, 18, 25], "riebesel": [0, 1], "default": [0, 2, 3, 9, 11, 13, 14, 16, 18, 20, 25], "ewald": [0, 3, 16, 18], "summat": [0, 16, 18], "per": [0, 3, 4, 5, 9, 14, 18], "atom": [0, 3, 4, 5, 6, 9, 13, 14, 16, 18, 25], "featur": [0, 3, 8, 11, 25, 28], "name": [0, 2, 3, 5, 9, 11, 13, 14, 16, 18, 20, 21, 25], "reflect": [0, 3, 14, 16], "thi": [0, 2, 3, 4, 5, 6, 9, 13, 14, 16, 18, 20, 21, 25], "A": [0, 2, 3, 5, 6, 9, 13, 14, 16, 18, 20, 25], "jain": [0, 1, 3, 6, 9, 13, 14, 16, 18, 20, 25], "add": [0, 2, 3, 5, 6, 13, 20], "descript": [0, 2, 3, 5, 9, 11, 13], "magpi": [0, 14, 25], "dunn": [0, 1, 3, 6], "correct": [0, 3, 16, 18, 25], "yield": [0, 3, 18], "strength": [0, 3, 11, 13, 14, 16], "being": [0, 2, 3, 11, 13, 18, 21], "accident": 0, "gpa": [0, 3, 4, 6], "instead": [0, 1, 13, 16, 18], "mpa": [0, 3], "minor": [0, 3], "b": [0, 3, 9, 13, 14, 16, 18, 25], "krull": [0, 1], "ganos": [0, 1, 3], "3": [0, 3, 6, 7, 9, 14, 16, 18, 21, 25], "intersticedistribut": [0, 5, 13, 16], "q": [0, 6, 16, 21], "wang": [0, 1, 3, 6, 16], "dimension": [0, 3, 5, 13, 16, 18], "more": [0, 2, 3, 4, 5, 6, 9, 13, 14, 16, 18, 20, 25], "accur": [0, 3, 13, 14, 18, 20], "cohesiveenergymp": [0, 5, 13, 14], "get": [0, 5, 9, 11, 13, 14, 16, 18, 21, 25], "mp": [0, 3, 9, 11], "cohes": [0, 5, 14, 25], "energi": [0, 3, 5, 11, 13, 14, 16, 18, 25], "from": [0, 2, 3, 6, 7, 9, 11, 13, 14, 16, 18, 19, 21, 25], "rester": 0, "dataretriev": [0, 9], "decod": [0, 5, 9, 13, 25], "entiti": 0, "depend": [0, 2, 3, 9, 13, 14, 16, 18, 20, 25], "test": [0, 3, 4, 6, 8, 9, 11, 13, 14, 16, 18, 21, 23, 25], "misc": [0, 3, 8, 13], "document": [0, 6, 7, 9, 13, 14, 16, 20, 25], "l": [0, 3, 6, 16, 18, 25], "ward": [0, 1, 3, 6, 16, 18, 19, 25], "": [0, 2, 3, 4, 6, 9, 11, 13, 14, 16, 18, 20, 25], "p": [0, 3, 5, 9, 13, 14, 16, 18, 25], "ong": [0, 1, 3, 9, 25], "2": [0, 3, 6, 9, 13, 14, 16, 18, 21, 25], "forum": [0, 6, 7], "discours": [0, 6], "link": [0, 2, 3, 9, 13, 16], "structuralcomplex": [0, 5, 13, 18], "k": [0, 3, 6, 9, 13, 14, 16, 18], "muraoka": [0, 1], "resolv": [0, 16], "option": [0, 2, 3, 4, 9, 11, 13, 16, 18, 21, 25], "requir": [0, 2, 3, 5, 7, 9, 13, 14, 16, 18, 20], "problem": [0, 3, 6, 7], "sklearn": [0, 4, 25], "refer": [0, 3, 5, 9, 11, 13, 14, 16, 18, 20], "dscribe": [0, 5, 16], "1": [0, 3, 5, 6, 9, 13, 14, 16, 18, 21, 25], "wa": [0, 2, 3, 16, 18], "skip": 0, "due": [0, 3], "upload": 0, "issu": [0, 3, 7], "0": [0, 2, 3, 4, 6, 9, 13, 14, 16, 18, 20, 21, 25], "ensur": [0, 2, 3, 13, 18], "yang": [0, 5, 14], "omega": [0, 14], "never": 0, "nan": [0, 4, 6, 9, 11, 13, 18, 25], "yangsolidsolut": [0, 5, 13, 14], "complet": [0, 3, 4, 13], "sum": [0, 3, 13, 14, 16, 18], "tabl": [0, 5, 6, 14, 18, 25], "some": [0, 2, 3, 6, 7, 9, 13, 14, 16, 21, 25], "code": [0, 3, 6, 7, 9, 13, 16, 25], "cleanup": 0, "n": [0, 2, 3, 6, 13, 14, 16, 18, 21, 25], "wagner": [0, 1], "9": [0, 3, 25], "meredig": [0, 3, 5, 13, 14], "composit": [0, 3, 4, 6, 8, 11, 13, 16, 20, 21, 25], "trewartha": [0, 1], "miedema": [0, 5, 13, 14, 25], "model": [0, 2, 3, 5, 6, 9, 13, 14, 16, 25], "latest": [0, 3, 7], "pymatgen": [0, 3, 4, 5, 6, 7, 9, 13, 14, 16, 18, 25], "8": [0, 3, 16, 25], "optim": [0, 3, 14, 18], "global": [0, 5, 18], "instabl": [0, 5, 18], "index": [0, 3, 5, 6, 9, 13, 14, 16, 18, 20, 25], "7": [0, 3, 4, 14, 18], "remov": [0, 3, 4, 9, 25], "soap": [0, 5, 13, 16], "normal": [0, 13, 16, 18, 25], "flag": [0, 18], "murakoa": 0, "precheck": [0, 8, 13, 14, 18, 20], "improv": [0, 3, 6], "structur": [0, 2, 3, 6, 8, 9, 11, 13, 14, 16, 17, 20, 25], "type": [0, 2, 3, 6, 7, 13, 14, 16, 18, 20], "doc": [0, 3, 9, 13], "cgcnn": 0, "citrin": [0, 1, 3, 6, 9, 11], "d": [0, 3, 5, 9, 13, 14, 16, 18, 25], "nishikawa": [0, 1], "bond": [0, 6, 8, 13, 25], "valenc": [0, 3, 5, 13, 14, 18, 25], "data": [0, 2, 3, 5, 8, 9, 11, 13, 14, 16, 18, 20, 21, 28], "mpdataretriev": [0, 4, 6, 8, 9], "citat": [0, 2, 3, 8, 9, 11, 13, 14, 16, 18, 20, 25], "implementor": [0, 8, 13, 14, 16, 18, 20], "list": [0, 1, 2, 3, 5, 6, 9, 11, 13, 14, 16, 18, 20, 21, 25], "bug": 0, "c": [0, 3, 4, 7, 9, 13, 14, 18, 25], "legaspi": [0, 1], "precheck_datafram": [0, 8, 13, 14, 18, 20], "function": [0, 2, 3, 6, 8, 9, 11, 14, 16, 18, 20, 21, 25, 28], "can": [0, 2, 3, 4, 6, 7, 9, 13, 14, 16, 18, 20, 21, 25], "us": [0, 2, 3, 5, 6, 7, 9, 13, 14, 16, 18, 20, 21, 25], "quickli": [0, 2, 6, 14], "see": [0, 2, 3, 4, 6, 7, 9, 11, 13, 14, 16, 18, 20, 21, 25], "like": [0, 2, 3, 5, 6, 9, 13, 14, 16, 18, 20, 21], "give": [0, 13, 16, 18], "valu": [0, 3, 4, 6, 9, 11, 13, 14, 16, 18, 21, 25], "megnet": [0, 25], "1neuron": 0, "element": [0, 3, 6, 8, 9, 13, 16, 18, 20, 21, 25], "embed": [0, 25], "inplac": [0, 13], "set": [0, 2, 3, 4, 6, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 22, 25, 27], "cherfaoui": [0, 1], "convers": [0, 8, 9, 28], "most": [0, 3, 5, 13, 14, 18, 20], "stabl": [0, 3, 13, 14, 16, 25], "elementproperti": [0, 4, 5, 13, 14], "sourc": [0, 2, 3, 6, 7, 9, 14, 16, 25], "label": [0, 6, 13, 14, 16, 18, 20, 21, 25], "api": [0, 4, 6, 9, 13, 14], "kei": [0, 3, 4, 9, 13, 14, 16, 18, 20, 25], "detect": [0, 9, 16], "logic": 0, "matscimalcolm": 0, "typo": 0, "got": [0, 6, 25], "introduc": [0, 3], "pypi": [0, 9], "releas": 0, "better": [0, 13], "flatten": [0, 5, 9, 13, 16, 18, 21, 25], "coloumbmatrix": 0, "them": [0, 3, 9, 13, 16, 18, 21], "usabl": [0, 2], "packag": [0, 2, 3, 6, 28], "dosasymmetri": [0, 5, 8, 13], "m": [0, 3, 6, 9, 14, 16, 18, 25], "dylla": [0, 1, 6], "aflow": [0, 9], "retriev": [0, 2, 3, 4, 9, 11, 13, 14, 16, 25], "sitedo": [0, 5, 8, 13], "variou": [0, 3, 6, 9, 16, 18], "py3": 0, "pytorch": 0, "pip": [0, 9], "setup": [0, 2, 7, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 25, 27], "jarvi": [0, 3, 5, 11, 18], "file": [0, 3, 9, 11, 16, 25], "configur": [0, 14], "t": [0, 2, 3, 5, 11, 13, 14, 16, 18, 25], "xie": [0, 1], "text": [0, 4], "mine": [0, 6, 8], "brgoch": [0, 3], "dopp": [0, 1, 3], "quartil": 0, "propertystat": [0, 13, 14, 21], "cfid": [0, 3, 5, 18], "descriptor": [0, 2, 3, 5, 11, 14, 16, 18], "choudhari": [0, 1, 3, 25], "allow": [0, 6, 9, 13, 16, 18], "oxi": 0, "return": [0, 2, 5, 9, 11, 13, 14, 16, 18, 20, 21, 25], "origin": [0, 2, 3, 6, 9, 13, 14, 16, 18, 25], "object": [0, 2, 3, 5, 6, 9, 13, 14, 16, 18, 21, 25], "contribut": [0, 1, 3, 5, 13, 14, 16, 18], "miss": [0, 2, 9, 14, 25], "loader": [0, 2, 6], "mdf": [0, 7, 9], "unit": [0, 2, 3, 9, 14, 16, 18], "mai": [0, 3, 9, 13, 16, 18, 25], "work": [0, 2, 3, 6, 9, 13, 14, 18, 20, 21], "properli": [0, 2, 20], "further": [0, 1, 3, 5, 9, 13, 14, 16, 18], "revamp": 0, "manag": [0, 3], "chunksiz": [0, 8, 13], "multiprocess": [0, 6, 13], "should": [0, 2, 3, 9, 13, 14, 16, 18, 20, 21, 25], "perform": [0, 2, 3, 5, 13], "oxid": [0, 3, 5, 6, 8, 11, 13, 14, 18, 25], "state": [0, 3, 6, 9, 13, 14, 18, 21, 25], "exampl": [0, 2, 4, 7, 9, 13, 14, 16, 18, 21, 25], "class": [0, 2, 3, 6, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 25, 27], "support": [0, 2, 9, 13, 14, 16, 25], "datafram": [0, 2, 5, 9, 11, 13, 14, 16, 18, 20, 25], "bandcent": [0, 5, 13, 14], "larg": [0, 3, 13, 14, 16, 18], "coeffici": [0, 3, 9, 16], "faghaninia": [0, 1, 6], "multifeatur": [0, 20], "custom": [0, 6], "progress": [0, 11, 13, 25], "bar": [0, 11, 13, 14, 25], "run": [0, 2, 3, 5, 7, 10, 11, 13, 16, 18], "notebook": [0, 4, 6], "multi": [0, 13], "featurizers": 0, "refactor": 0, "util": [0, 2, 6, 8, 13, 14, 16, 28], "consist": [0, 2, 3, 6, 16, 18], "parallel": [0, 6, 13], "averag": [0, 3, 5, 6, 13, 14, 16, 18, 21], "length": [0, 5, 6, 13, 16, 18], "angl": [0, 3, 5, 14, 16, 18], "implement": [0, 5, 9, 13, 14, 16, 18, 20, 25], "rui": [0, 1], "abil": [0, 3, 5, 13, 18, 20], "serial": 0, "json": [0, 2, 3, 5, 6, 9, 13, 25], "montyencod": [0, 2], "ad": [0, 3, 6, 9, 13, 14, 16, 18, 21, 25], "fraction": [0, 5, 13, 14, 16, 18, 21], "atomicorbit": [0, 5, 13, 14], "ofm": [0, 18], "functionfeatur": [0, 5, 8, 13], "montoya": [0, 1, 6], "bugfix": 0, "properti": [0, 2, 3, 4, 5, 6, 8, 9, 13, 14, 16, 18, 19, 21, 25], "seko": [0, 16], "represent": [0, 3, 5, 6, 9, 13, 18, 25], "multiplefeatur": [0, 5, 8, 13], "compat": [0, 6, 9, 13, 25], "intuit": 0, "input": [0, 3, 5, 7, 9, 13, 14, 16, 18, 20], "argument": [0, 2, 6, 9, 13, 18, 21, 25], "featurize_mani": [0, 8, 13], "boop": [0, 16], "thompson": [0, 1], "progressbar": 0, "lookuip": 0, "magpiedata": [0, 8, 13, 14, 16, 25], "site": [0, 3, 6, 8, 13, 14, 20, 25], "covari": [0, 18], "skew": [0, 13, 21], "kurtosi": [0, 13, 21], "new": [0, 2, 3, 9, 13, 16, 21], "scheme": [0, 2, 16], "grdf": [0, 5, 8, 13, 16], "af": [0, 5, 16], "bin": [0, 16, 18, 21], "bandedg": 0, "renam": [0, 2, 3], "hybrid": [0, 5, 8, 13], "smoother": [0, 7], "hoverinfo": 0, "mani": [0, 3, 6, 13, 14, 16, 18, 20], "plot": [0, 6], "unsupport": 0, "abort": 0, "faster": [0, 16], "gaussiansymmfunc": [0, 5, 13, 16], "resili": 0, "atomicpackingeffici": [0, 5, 13, 14], "prdf": [0, 5, 16, 18], "tsv": 0, "package_data": 0, "instal": [0, 9], "gdrf": 0, "william": [0, 1, 3], "messag": [0, 4, 18], "tool": [0, 2, 4, 6, 9, 14, 16], "pipelin": [0, 3, 6, 8, 13, 28], "integr": [0, 5, 16, 18], "brenneck": [0, 1], "chemic": [0, 3, 6, 8, 9, 13, 14, 18], "cnfingerprint": 0, "zimmermann": [0, 1, 6], "hat": [0, 16], "tip": 0, "dwaraknath": [0, 3], "phase": [0, 3, 9, 14], "diagram": [0, 3, 9], "triangl": 0, "harmon": [0, 3, 5, 16], "mean": [0, 3, 4, 5, 9, 13, 14, 16, 18, 21, 25], "holder_mean": [0, 13, 21], "xrdpowderpattern": [0, 5, 13, 18], "xy": [0, 6], "debug": 0, "deprec": [0, 9], "crystalsitefingerprint": 0, "few": [0, 6], "old": 0, "unus": [0, 16, 18, 25], "op": [0, 16, 18], "method": [0, 3, 6, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 25, 27], "etc": [0, 3, 6, 7, 9, 13, 14, 18, 20, 25], "stackedfeatur": [0, 5, 8, 13], "branchpointenergi": [0, 5, 8, 13], "major": [0, 13], "overhaul": 0, "redesign": 0, "solid": [0, 3, 14, 16, 18, 25], "solut": [0, 3, 14], "cluster": [0, 5, 14, 16], "pack": [0, 8, 13, 16, 18], "effici": [0, 3, 5, 9, 14, 18, 25], "pycc": 0, "bajaj": [0, 1, 6], "branch": [0, 5, 13], "point": [0, 3, 5, 6, 13, 16], "take": [0, 2, 3, 6, 9, 13, 16, 21, 25], "account": [0, 2, 3, 16, 18], "symmetri": [0, 8, 13, 16], "cach": [0, 8, 13, 16, 20, 28], "crystalnnfingerprint": [0, 5, 13, 16], "x": [0, 3, 4, 5, 6, 9, 13, 14, 16, 18, 20, 21, 25], "y": [0, 3, 4, 13, 16, 18, 20, 25], "speedup": 0, "chemenv": [0, 16], "waroqui": [0, 1], "heterogen": [0, 16], "maximum": [0, 3, 5, 9, 13, 14, 16, 18, 21], "order": [0, 3, 6, 8, 13, 14, 16], "bagofbond": [0, 5, 13, 18], "base": [0, 2, 3, 6, 8, 9, 11, 14, 16, 18, 20, 21, 22, 25, 27, 28], "paper": [0, 6, 11, 13, 16, 18], "now": [0, 4], "bondfract": [0, 5, 13, 18], "dopingfermi": [0, 5, 8, 13], "shortcut": 0, "static": [0, 2, 3, 9, 13, 14, 16, 18, 20, 21], "mode": [0, 3, 4, 9, 13, 16, 18, 21, 25], "output": [0, 4, 5, 6, 7, 9, 13, 16, 18, 25], "plotlyfig": [0, 4, 6], "figrecip": [0, 6], "fit_featur": 0, "dep": 0, "combin": [0, 3, 11, 13, 14, 16, 18, 25], "mini": 0, "n_job": [0, 4, 8, 13], "cpu_count": 0, "move": 0, "jupyt": [0, 6], "matminer_exampl": [0, 6], "repo": [0, 6], "separ": [0, 2, 3, 9, 13, 18, 21], "preset": [0, 13, 14, 16, 18], "circleci": [0, 2], "modifi": [0, 2, 13], "chemicalrso": 0, "fit": [0, 6, 8, 9, 13, 16, 18, 20, 25], "bostrom": 0, "yaml": 0, "rework": 0, "subclass": [0, 9, 13, 14, 16, 18, 20], "baseestim": [0, 13, 25], "transformermixin": [0, 13, 25], "need": [0, 2, 9, 13, 14, 16, 18], "column": [0, 2, 3, 4, 6, 9, 11, 13, 14, 18, 25], "clean": [0, 3, 25], "up": [0, 3, 7, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 22, 27], "getter": [0, 4], "signatur": [0, 16, 18], "re": [0, 7], "adapt": [0, 3, 4, 6, 18], "voronoifingerprint": [0, 5, 13, 16], "chemicalsro": [0, 5, 13, 16], "slighli": 0, "py2": 0, "offici": [0, 9], "drop": [0, 2, 11, 25], "upgrad": [0, 7], "python": [0, 2, 5, 6, 7, 13, 16, 25], "panda": [0, 2, 3, 6, 9, 13, 16, 25], "coordinationnumb": [0, 5, 13, 16, 18], "nearneighbor": [0, 14, 16, 18, 25], "algo": 0, "fingerprint": [0, 8, 13], "opstructurefingerprint": 0, "sitestatsfingerprint": [0, 5, 13, 18], "ani": [0, 2, 3, 5, 6, 13, 14, 16, 18, 20, 21, 25], "format": [0, 2, 3, 4, 5, 6, 9, 11, 13, 14, 16, 18, 20, 25], "feature_label": [0, 8, 13, 14, 16, 18, 20], "alwai": [0, 3, 9, 25], "bystrom": [0, 1, 6], "multipl": [0, 2, 3, 5, 13, 14, 16, 18, 20, 21], "outdat": 0, "intern": [0, 3, 9, 25], "advanc": [0, 3, 6, 9, 16], "bcc": [0, 14, 16], "renorm": 0, "sampl": [0, 3, 9, 13], "sever": [0, 2, 3, 4, 13, 18, 25], "turn": [0, 2, 6, 16, 25], "string": [0, 3, 5, 6, 9, 11, 13, 14, 16, 18, 20, 21, 25], "dict": [0, 5, 9, 13, 14, 16, 18], "extend": [0, 3, 18], "ternari": [0, 3, 14], "higher": [0, 7, 9], "do": [0, 2, 6, 8, 9, 14, 16, 18, 20, 28], "lot": 0, "review": [0, 3], "trim": 0, "bsfeatur": 0, "dosfeatur": [0, 5, 8, 13], "cnsitefingerprint": 0, "goe": 0, "cn": [0, 16], "16": [0, 3, 18], "two": [0, 2, 3, 9, 13, 14, 16, 18, 21, 25], "stat": [0, 4, 8, 13, 14, 16, 18], "doubl": [0, 3], "colon": [0, 21], "underscor": [0, 13], "param": [0, 16, 18], "voronoi": [0, 5, 16, 18], "citrinedataretriev": [0, 6, 8, 9], "bandstructurefeatur": 0, "featurize_datafram": [0, 4, 6, 8, 13], "ignor": [0, 4, 21], "patch": 0, "cation": [0, 3, 5, 14, 18, 25], "anion": [0, 5, 14, 18, 25], "onli": [0, 2, 3, 9, 11, 13, 14, 16, 18, 25], "degeneraci": [0, 13], "cbm": [0, 3, 13], "vbm": [0, 3, 13], "bandfeatur": [0, 5, 8, 13], "agnifingerprint": [0, 5, 13, 16], "band": [0, 3, 5, 6, 9, 13, 14], "coulombmatrix": [0, 5, 13, 18], "sinecoulombmatrix": [0, 5, 13, 18], "orbitalfieldmatrix": [0, 5, 13, 18], "interfac": [0, 2, 5, 6, 9, 16, 25], "git": [0, 7], "lf": 0, "csv": [0, 14, 25], "determin": [0, 2, 3, 5, 13, 14, 16, 18, 25], "complex": [0, 3, 13, 18], "mongo": [0, 9], "queri": [0, 6, 9, 11], "project": [0, 2, 3, 5, 6, 9, 11, 13, 14, 25], "enforc": 0, "lower": [0, 3, 9], "case": [0, 3, 9, 13, 14, 16, 18], "sort": [0, 9, 11, 13, 16, 18, 21], "number": [0, 3, 4, 5, 6, 9, 11, 13, 14, 16, 18, 19], "electroneg": [0, 5, 6, 14, 16, 25], "avoid": [0, 13, 16], "pernici": 0, "behavior": [0, 3, 9, 13, 14], "oop": 0, "style": [0, 6, 9, 13], "codebas": 0, "chen": [0, 1, 3, 6, 25], "mathew": [0, 1], "aggarw": [0, 1], "frost": [0, 1], "mpd": [0, 6, 9], "e": [0, 3, 5, 6, 9, 13, 14, 16, 18, 20, 21, 25], "blokhin": [0, 1, 9], "partial": [0, 5, 13, 16, 18], "rdf": [0, 8, 13], "local": [0, 3, 5, 6, 14, 16, 19], "environ": [0, 3, 4, 5, 6, 9, 11, 16, 18], "motif": [0, 3, 16], "For": [0, 2, 3, 6, 7, 9, 13, 14, 16, 18, 21], "befor": [0, 3, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 22, 27], "consult": [0, 25], "histori": 0, "led": 1, "anubhav": [1, 3, 13, 14, 16, 18, 20], "hackingmateri": [1, 7], "research": [1, 2, 3, 6, 9, 25], "lawrenc": 1, "berkelei": 1, "nation": [1, 3], "lab": 1, "saurabh": 1, "through": [1, 3, 6, 13, 25], "informat": [1, 3, 6], "alireza": 1, "nil": 1, "qi": [1, 3], "sayan": 1, "rowchowdhuri": 1, "alex": [1, 3], "jason": [1, 3], "julien": 1, "daniel": [1, 3], "sami": 1, "logan": [1, 3], "jime": 1, "ashwin": 1, "aik": 1, "kiran": 1, "matt": 1, "horton": [1, 3], "donni": 1, "winston": [1, 3], "joseph": 1, "koki": 1, "christian": 1, "kyle": 1, "asta": [1, 3, 6], "uc": 1, "shyue": [1, 3, 25], "ping": [1, 3, 25], "san": [1, 25], "diego": [1, 25], "evgeni": 1, "tild": 1, "max": [1, 3, 9, 14, 16, 18, 25], "snyder": [1, 3, 6], "northwestern": 1, "david": [1, 3, 25], "louvain": 1, "belgium": 1, "u": [1, 3, 6, 25], "michigan": 1, "ann": 1, "arbor": 1, "aidan": 1, "sandia": 1, "kamal": [1, 3], "nist": [1, 3, 14, 18], "tian": 1, "mit": [1, 3], "daiki": 1, "nichola": 1, "amali": 1, "janosh": 1, "brandon": 1, "all": [2, 3, 4, 5, 6, 9, 11, 12, 13, 14, 16, 18, 20, 21, 25], "inform": [2, 3, 5, 6, 7, 9, 11, 13, 14, 16, 18, 20], "current": [2, 3, 13], "10": [2, 3, 4, 9, 13, 14, 16, 18, 25], "24": [2, 3], "2018": [2, 3, 6, 9], "In": [2, 3, 13, 14, 16, 18, 25], "addit": [2, 3, 6, 11, 13, 16], "provid": [2, 3, 6, 9, 11, 13, 14, 16, 20, 21, 25], "standard": [2, 3, 9, 16, 18, 21], "materi": [2, 3, 8, 9, 11, 13, 14, 16, 18, 25], "scienc": [2, 3, 6, 9, 16, 25], "databas": [2, 3, 4, 6, 9, 14], "also": [2, 3, 6, 7, 9, 13, 14, 16, 18, 20, 25], "suit": [2, 3, 13], "pre": [2, 6, 13, 16], "store": [2, 11, 13, 16, 25], "compress": [2, 3, 14, 25], "These": [2, 3, 9, 13, 14, 16, 18, 25], "ar": [2, 3, 6, 7, 9, 11, 12, 13, 14, 16, 18, 20, 21, 25], "figshar": [2, 3], "academ": 2, "storag": 2, "platform": [2, 7, 9], "each": [2, 3, 5, 9, 11, 13, 14, 16, 18, 20, 21, 25], "discover": 2, "clearli": 2, "contributor": [2, 6, 9, 13], "gener": [2, 3, 9, 11, 13, 14, 16, 18, 20, 21, 25], "said": 2, "serv": 2, "purpos": [2, 3, 5, 9, 13, 14, 25], "first": [2, 3, 5, 7, 9, 13, 14, 16, 18, 21], "wai": [2, 3, 6, 7, 9, 13, 21, 25], "other": [2, 3, 6, 9, 13, 14, 16, 18, 20, 25], "easili": 2, "access": [2, 3, 9, 25], "hack": 2, "group": [2, 3, 4, 5, 16, 18], "second": [2, 9, 13, 14, 18, 21, 25], "commun": [2, 3, 6, 14, 16], "benchmark": [2, 3, 6], "As": [2, 13, 18], "applic": [2, 3, 6, 9, 18, 25], "machin": [2, 3, 5, 6, 9, 13, 14, 16, 18, 25], "learn": [2, 3, 4, 5, 6, 9, 13, 14, 16, 18, 25], "matur": 2, "grow": [2, 6], "train": [2, 3, 4, 6, 11, 13, 16, 18, 20, 25], "compar": [2, 3, 6, 13, 16], "against": [2, 3, 9, 14, 16], "collect": [2, 3, 6, 9, 16, 18], "six": 2, "primari": [2, 13, 21, 25], "step": [2, 9, 16, 18, 25], "The": [2, 3, 5, 6, 9, 11, 13, 14, 16, 18, 21, 25], "defin": [2, 3, 4, 5, 9, 13, 14, 16, 18, 25], "how": [2, 3, 5, 6, 13, 16, 18], "handl": [2, 6, 9, 20], "avail": [2, 3, 4, 6, 9, 11, 13, 14, 16, 18, 20, 25], "edit": [2, 3, 25], "place": [2, 13, 14, 16, 18], "either": [2, 3, 6, 9, 13, 14, 16, 18, 20], "within": [2, 3, 6, 11, 13, 14, 16, 18, 25], "dev_script": 2, "dataset_manag": 2, "folder": [2, 7, 11], "assum": [2, 3, 13, 14, 21], "encod": [2, 25], "monti": 2, "prep_dataset_for_figshar": 2, "py": [2, 4, 7, 18, 25], "written": [2, 13], "expedit": 2, "process": [2, 3, 6, 11, 13, 14, 18], "If": [2, 3, 4, 6, 7, 9, 11, 13, 14, 16, 18, 20, 21, 25], "modif": [2, 3], "content": [2, 3, 28], "one": [2, 3, 5, 9, 13, 14, 16, 18, 20, 25], "simpli": [2, 7, 25], "convert": [2, 5, 6, 9, 13, 18, 25], "desir": [2, 14, 16, 21], "so": [2, 9, 13, 16, 18], "fp": 2, "path": [2, 3, 11, 25], "ct": 2, "compression_typ": 2, "gz": [2, 25], "bz2": [2, 25], "directori": 2, "given": [2, 3, 5, 13, 14, 16, 18], "crawl": 2, "try": [2, 6, 7, 9, 13, 25], "prep": 2, "dataset_to_json": 2, "along": [2, 3, 13], "txt": [2, 25], "contain": [2, 3, 5, 6, 9, 11, 13, 14, 16, 18, 21], "which": [2, 3, 6, 9, 13, 14, 16, 18, 20, 21, 25], "later": [2, 3], "doe": [2, 3, 6, 9, 13, 14, 16, 18, 25], "made": [2, 3, 25], "you": [2, 4, 5, 6, 7, 9, 13, 14, 16, 18, 21], "would": [2, 6, 13, 21], "user": [2, 3, 6, 7, 9, 13, 14, 16, 25], "small": [2, 13, 14, 16], "prior": [2, 3, 18], "select": [2, 5, 6, 13, 25], "write": [2, 13, 25], "preprocessor": 2, "necessari": [2, 13, 16], "tupl": [2, 9, 11, 13, 16, 18, 20, 25], "form": [2, 3, 4, 9, 13, 14, 16, 18], "string_of_dataset_nam": 2, "produc": [2, 3, 5, 13, 18], "than": [2, 3, 5, 9, 13, 14, 16, 18, 20, 25], "dataset_name_1": 2, "dataset_name_2": 2, "df_1": 2, "df_2": 2, "def": 2, "_preprocess_heusler_magnet": 2, "file_path": 2, "df": [2, 6, 9, 13, 16, 25], "_read_dataframe_from_fil": 2, "dropcol": 2, "gap": [2, 3, 6, 9, 11, 13], "width": [2, 3, 4, 16, 18, 21], "stabil": [2, 3], "axi": [2, 3, 18], "heusler_magnet": 2, "here": [2, 3, 4, 6, 12, 13, 16, 18, 25], "simpl": [2, 3, 6, 9, 16], "what": [2, 9, 11, 13, 16], "pass": [2, 13, 14, 16, 18, 20, 21, 25], "keyword": [2, 3, 9, 11, 13, 18, 21], "underli": [2, 9, 18], "_preprocess_double_perovskites_gap": 2, "pd": [2, 4, 6, 11, 13], "read_excel": 2, "sheet_nam": 2, "bandgap": [2, 3, 13, 14], "a1_atom": 2, "a_1": [2, 3], "b1_atom": 2, "b_1": [2, 3], "a2_atom": 2, "a_2": [2, 3], "b2_atom": 2, "b_2": [2, 3], "lumo": [2, 3, 5, 11, 14], "double_perovskites_gap": [2, 11], "double_perovskites_gap_lumo": [2, 11], "dictionari": [2, 3, 6, 9, 13, 14, 16, 18, 20, 25], "map": [2, 3, 4, 9, 13, 16], "identifi": [2, 3, 9, 11, 16, 18], "call": [2, 9, 13, 14, 16, 18, 21], "_datasets_to_preprocessing_routin": 2, "elastic_tensor_2015": [2, 6], "_preprocess_elastic_tensor_2015": 2, "piezoelectric_tensor": [2, 11], "_preprocess_piezoelectric_tensor": 2, "wolverton_oxid": 2, "_preprocess_wolverton_oxid": 2, "m2ax_elast": 2, "_preprocess_m2ax": 2, "your_dataset_file_nam": 2, "your_preprocessor": 2, "onc": 2, "done": [2, 3, 9, 13, 18], "readi": [2, 13, 18], "open": [2, 3, 6, 7, 25], "servic": [2, 9], "follow": [2, 6, 7, 9, 13, 14, 16, 18], "procedur": [2, 3], "when": [2, 3, 9, 11, 13, 14, 16, 18, 20, 21, 25], "similar": [2, 3, 5, 6, 9, 14, 18], "protocol": 2, "well": [2, 3, 6, 13, 14, 16, 18], "entri": [2, 3, 4, 9, 11, 13, 14, 18, 20, 21], "fill": [2, 25], "out": [2, 3, 4, 6, 13, 18], "carefulli": 2, "expect": [2, 9, 13, 14, 16], "qualiti": [2, 20], "outsid": 2, "thoroughli": 2, "cite": [2, 3, 9, 13], "dataset_metadata": 2, "automat": [2, 6, 13, 14, 18, 25], "proper": [2, 13], "regularli": 2, "thei": [2, 3, 9, 13, 25], "match": [2, 3, 9, 13, 18], "while": [2, 3, 9], "appropri": 2, "manual": [2, 3], "prefer": [2, 9, 16], "helper": [2, 9, 11, 25], "modify_dataset_metadata": 2, "bulk": [2, 3, 6, 14, 25], "prevent": [2, 16, 18], "mistak": 2, "guidelin": 2, "attribut": [2, 4, 5, 6, 11, 13, 14, 16, 18, 19, 20, 25], "download": [2, 3, 9, 11], "individu": [2, 9, 13], "item": [2, 11, 13, 18], "specif": [2, 3, 5, 6, 7, 9, 14, 16, 18, 21, 25], "replac": [2, 9, 18], "newli": 2, "look": [2, 9, 11, 14, 16, 21], "over": [2, 3, 9, 13, 14, 16, 18], "conveni": [2, 6, 9, 11, 25], "explicitli": [2, 13, 16, 18], "singl": [2, 3, 13, 14, 18, 20, 25], "oppos": [2, 13], "post": [2, 7], "filter": [2, 4, 6, 9, 13], "after": [2, 3, 9, 13, 17, 21, 27], "ha": [2, 3, 6, 13, 14, 16, 18, 20, 21], "been": [2, 3, 6, 13, 14, 18], "alongsid": [2, 3], "save": [2, 13], "datasettest": [2, 11, 12], "unittest": 2, "testcas": [2, 10, 12, 20, 22, 27], "self": [2, 13, 16, 18, 20], "dataset_nam": [2, 11, 12], "flla": [2, 11], "test_dataset": [2, 8, 11], "its": [2, 3, 5, 6, 13, 14, 16, 18, 20], "describ": [2, 3, 4, 5, 7, 13, 14, 16, 18, 25], "info": [2, 5, 9, 11, 13, 16], "typic": 2, "univers": [2, 3, 25], "specifi": [2, 3, 4, 6, 9, 11, 13, 14, 18, 21], "test_dielectric_const": [2, 11, 12], "object_head": [2, 12], "material_id": [2, 3, 6, 9], "formula": [2, 3, 9, 11, 13, 14, 18], "e_electron": [2, 3], "e_tot": [2, 3], "cif": [2, 3, 11, 25], "meta": [2, 3, 11], "poscar": [2, 3, 11], "numeric_head": [2, 12], "nsite": [2, 3, 4, 18], "space_group": [2, 3], "volum": [2, 3, 4, 5, 13, 16, 18, 21], "band_gap": [2, 3, 4, 6, 9, 13], "poly_electron": [2, 3], "poly_tot": [2, 3], "bool_head": [2, 12], "pot_ferroelectr": [2, 3], "uniqu": [2, 3, 9, 13, 16, 18], "_unique_test": 2, "assertequ": 2, "universal_dataset_check": [2, 11, 12], "dielectric_const": [2, 11], "test_func": [2, 12], "convenience_load": [2, 6, 8, 28], "just": [2, 4, 13, 18], "result": [2, 3, 6, 9, 13, 16, 18, 20, 21, 25], "load_dataset": [2, 6, 8, 11], "extra": [2, 13, 18], "subset": [2, 11], "certain": [2, 3, 5, 9, 14, 16, 18, 25], "load_elastic_tensor": [2, 8, 11], "2015": [2, 3, 5, 9, 11, 14, 18], "include_metadata": [2, 11], "fals": [2, 6, 9, 11, 13, 14, 16, 18, 20, 21, 25], "data_hom": [2, 11], "none": [2, 4, 9, 10, 11, 12, 13, 14, 16, 18, 20, 21, 25], "download_if_miss": [2, 11], "true": [2, 3, 4, 6, 9, 11, 13, 14, 16, 18, 20, 25], "elastic_tensor": [2, 3, 11], "arg": [2, 9, 11, 13, 14, 16, 18, 20, 21, 25], "str": [2, 4, 9, 11, 13, 14, 16, 18, 20, 21, 25], "bool": [2, 9, 11, 13, 14, 16, 18, 20, 25], "whether": [2, 3, 9, 11, 13, 14, 15, 16, 18, 20, 21, 25], "where": [2, 3, 6, 9, 11, 13, 14, 16, 18], "loom": 2, "isn": [2, 11], "disk": [2, 11, 25], "_": [2, 3, 13, 14, 16, 18, 25], "kpoint_dens": [2, 3], "find": [3, 5, 6, 9, 13, 14, 16, 18, 25], "45": 3, "matmin": [3, 5], "effect": [3, 16, 19, 25], "mass": [3, 16, 18], "thermoelectr": 3, "8924": 3, "compound": [3, 5, 6, 9, 13, 14, 15, 25], "calcul": [3, 6, 13, 14, 16, 18, 21], "boltztrap": 3, "softwar": 3, "gga": 3, "pbe": 3, "densiti": [3, 4, 6, 9, 13, 16, 18, 25], "theori": [3, 5, 9, 14, 25], "2574": 3, "regressor": 3, "predict": [3, 6, 14, 16, 18, 25], "shear": [3, 6, 14], "modulu": [3, 4, 6, 14], "18": [3, 16], "928": 3, "perovskit": [3, 18], "abx": 3, "combinator": 3, "gllbsc": 3, "report": [3, 5, 13, 14, 25], "absolut": [3, 5, 13, 14, 16, 18, 21], "edg": [3, 5, 13, 16], "posit": [3, 5, 13, 14, 16, 18], "heat": [3, 9, 25], "18928": 3, "thermal": [3, 11], "conduct": [3, 11, 13], "872": 3, "measur": [3, 6, 13, 14, 16, 18], "experiment": [3, 6, 9, 13], "056": 3, "dielectr": 3, "dfpt": 3, "1056": 3, "1306": 3, "o6": 3, "gritsenko": 3, "van": 3, "leeuwen": 3, "lenth": 3, "baerend": 3, "potenti": [3, 16, 18], "gpaw": 3, "supplementari": 3, "55": [3, 6], "181": 3, "elast": [3, 4, 11, 14], "dft": [3, 4, 11], "1181": 3, "enthalpi": [3, 5, 14, 25], "inorgan": [3, 25], "year": 3, "calorimetr": 3, "experi": [3, 6], "1276": 3, "2135": 3, "6354": 3, "semiconductor": [3, 13], "ident": [3, 13, 18], "except": [3, 9, 13, 18], "id": [3, 9, 13], "have": [3, 6, 7, 9, 13, 14, 16, 18], "associ": [3, 5, 11, 13, 18], "same": [3, 5, 7, 9, 11, 13, 14, 16, 18, 21, 25], "4604": 3, "3938": 3, "comput": [3, 4, 5, 6, 9, 13, 14, 16, 18, 20, 21, 25], "crystal": [3, 6, 9, 13, 16, 18, 25], "metal": [3, 5, 13, 14, 16, 18, 25], "glass": [3, 14, 16, 25], "binari": [3, 14], "alloi": [3, 8, 9, 13, 16, 25], "techniqu": [3, 25], "melt": [3, 14], "spin": [3, 13], "mechan": 3, "5959": 3, "duplic": 3, "merg": 3, "5483": 3, "high": [3, 13, 14, 16], "throughput": 3, "sputter": [3, 11], "possibl": [3, 4, 5, 9, 11, 13, 14, 16, 18, 25], "5170": 3, "nonequilibrium": 3, "amorph": [3, 5, 14, 16], "landolt": 3, "b\u00f6rnstein": 3, "7191": 3, "1153": 3, "heusler": [3, 11], "magnet": [3, 5, 11, 14], "electron": [3, 6, 13, 14, 18], "636": 3, "2d": [3, 11, 16], "optb88vdw": 3, "tbmbj": 3, "taken": [3, 13, 16, 25], "25": 3, "923": 3, "25923": 3, "759": 3, "24759": 3, "223": 3, "comprehens": [3, 6, 9], "survei": 3, "cover": 3, "et": [3, 5, 14, 16, 18, 25], "al": [3, 5, 6, 9, 14, 16, 18, 25], "refract": 3, "4764": 3, "alon": [3, 5, 13, 14, 16], "classifi": [3, 13, 18], "4921": 3, "full": [3, 6, 7, 9, 11, 16], "5680": 3, "exfoli": 3, "log10": [3, 6], "vrh": 3, "10987": 3, "132752": 3, "106113": 3, "vibrat": 3, "1265": 3, "steel": [3, 11], "312": 3, "copi": [3, 21], "83989": 3, "phonon": [3, 9], "lattic": [3, 9, 16, 18], "1296": 3, "via": [3, 5, 6, 9, 13, 14, 16, 20], "abinit": 3, "approxim": [3, 14, 16, 18], "perturb": [3, 16], "941": 3, "piezoelectr": 3, "ab": [3, 4, 14, 16, 25], "initio": 3, "transport": 3, "47737": 3, "ultim": [3, 13], "tensil": 3, "extract": [3, 4, 6, 13, 25], "de": [3, 16], "000": [3, 14, 25], "superconduct": 3, "critic": 3, "temperatur": [3, 11, 13, 14], "stanev": 3, "japanes": 3, "institut": [3, 13, 14, 16, 18, 20, 25], "16414": 3, "challeng": 3, "quantum": [3, 18], "divers": 3, "12": [3, 16], "8k": 3, "polymorph": 3, "zn": 3, "ti": [3, 13], "zr": 3, "hf": 3, "system": [3, 5, 7, 9, 11, 14, 16, 18], "12815": 3, "100": [3, 9, 13, 18], "ucsb": 3, "aggreg": [3, 5, 6, 9, 18], "108": [3, 18], "public": [3, 6, 16, 18, 25], "person": 3, "1093": 3, "914": 3, "constant": [3, 14, 16], "vacanc": 3, "4914": 3, "300": [3, 6, 13], "kelvin": [3, 13], "carrier": [3, 13], "concentr": [3, 13, 14], "1e18": [3, 13], "cm3": [3, 13], "m_n": 3, "m_e": 3, "unitless": 3, "ratio": [3, 14], "m_p": 3, "mpid": 3, "pf_n": 3, "power": [3, 6, 16, 21], "factor": [3, 13, 16, 18, 21], "uw": 3, "cm2": 3, "microwatt": 3, "relax": [3, 9], "time": [3, 4, 5, 9, 13, 14, 16, 18], "1e": [3, 13], "14": [3, 16], "pf_p": 3, "s_n": 3, "seebeck": [3, 9], "micro": 3, "volt": 3, "s_p": 3, "ricci": 3, "f": [3, 5, 9, 13, 14, 16], "sci": [3, 6, 25], "170085": 3, "doi": [3, 9, 14, 16, 18, 25], "1038": [3, 14, 25], "sdata": 3, "2017": [3, 9, 16, 18, 19], "85": 3, "w": [3, 5, 9, 13, 16, 25], "aydemir": 3, "rignanes": 3, "g": [3, 5, 6, 9, 13, 14, 16, 18, 20, 21, 25], "hautier": [3, 25], "dryad": 3, "digit": 3, "repositori": [3, 6, 7, 9], "http": [3, 4, 7, 9, 13, 14, 16, 18, 25], "org": [3, 9, 16, 18, 25], "5061": 3, "gn001": 3, "bibtex": [3, 6, 9, 11, 13, 14, 16, 18, 20], "articl": [3, 6], "ricci2017": 3, "author": [3, 6, 13, 14, 16, 18, 20, 25], "francesco": 3, "wei": 3, "umut": 3, "jeffrei": 3, "gian": 3, "marco": 3, "geoffroi": 3, "titl": [3, 9], "journal": [3, 18], "scientif": [3, 6, 25], "month": 3, "jul": 3, "dai": 3, "04": [3, 9], "publish": [3, 9, 16], "page": [3, 6, 9], "note": [3, 9, 13, 14, 16, 18, 25], "dx": [3, 18], "dryad_gn001": 3, "brgoch_feat": 3, "studi": [3, 25], "bulk_modulu": 3, "shear_modulu": [3, 14], "suspect_valu": 3, "did": 3, "close": 3, "1gpa": 3, "cross": [3, 4, 16], "could": [3, 11, 13, 14], "found": [3, 6, 11, 13, 16, 18, 25], "direct": [3, 13, 16], "search": [3, 6, 9, 18], "ultraincompress": 3, "superhard": 3, "aria": 3, "mansouri": 3, "tehrani": 3, "anton": 3, "o": [3, 6, 9, 14, 16, 18, 25], "oliynyk": 3, "marcu": 3, "parri": 3, "zeshan": 3, "rizvi": 3, "samantha": 3, "couper": 3, "feng": 3, "lin": 3, "lowel": 3, "miyagi": 3, "taylor": 3, "spark": 3, "jakoah": 3, "american": 3, "societi": 3, "140": 3, "31": [3, 25], "9844": 3, "9853": 3, "1021": [3, 25], "jac": 3, "8b02717": 3, "pmid": 3, "30010335": 3, "eprint": 3, "e_form": 3, "ev": [3, 13, 14], "oxygen": 3, "water": 3, "vapor": 3, "molecul": [3, 16, 18, 25], "were": [3, 13, 25], "fermi": [3, 5, 13], "level": [3, 5, 9, 13, 25], "thermodynam": [3, 5, 14], "bodi": [3, 18], "bandwidth": 3, "boolean": [3, 13, 14, 16, 18, 21, 25], "indic": [3, 4, 13, 14, 16, 18, 25], "mu_b": 3, "moment": [3, 14], "term": [3, 5, 14], "bohr": 3, "magneton": 3, "repres": [3, 4, 5, 6, 9, 13, 14, 16, 18], "ivano": 3, "castelli": 3, "landi": 3, "kristian": 3, "thygesen": 3, "s\u00f8ren": 3, "dahl": 3, "ib": 3, "chorkendorff": 3, "thoma": 3, "jaramillo": 3, "karsten": 3, "jacobsen": 3, "2012": [3, 5, 14, 18], "cubic": [3, 9, 18], "photon": 3, "split": [3, 25], "9034": 3, "9043": 3, "1039": [3, 16], "c2ee22341d": 3, "royal": 3, "chemistri": [3, 16, 18, 25], "abstract": [3, 5, 9, 13, 18, 21, 25], "photoelectrochem": 3, "cell": [3, 9, 18], "pec": 3, "climat": 3, "our": [3, 6, 7], "Such": 3, "devic": 3, "develop": [3, 5, 6, 9, 14, 18, 25], "semiconduct": 3, "tailor": 3, "respect": [3, 6, 13, 14, 16, 18], "light": 3, "absorpt": 3, "we": [3, 4, 7, 9, 13, 14, 16, 18], "screen": 3, "around": [3, 5, 13, 16], "19": 3, "oxynitrid": 3, "oxysulfid": 3, "oxyfluorid": 3, "oxyfluoronitrid": 3, "mind": 3, "address": 3, "three": [3, 13], "main": [3, 13, 14, 16, 18, 20, 25], "absorb": 3, "transpar": 3, "shield": 3, "protect": [3, 18], "corros": 3, "end": [3, 13, 18, 25], "20": [3, 4, 13, 16, 18], "15": [3, 4, 9], "differ": [3, 5, 6, 9, 13, 14, 16, 18, 19, 20, 21, 25], "invit": 3, "investig": 3, "295": 3, "room": [3, 11], "k_condit": [3, 11], "condit": [3, 16], "k_condition_unit": 3, "k_expt": 3, "si": [3, 6, 9, 13], "www": [3, 14, 25], "com": [3, 4, 7, 14, 16, 18, 25], "howpublish": 3, "tensor": 3, "total": [3, 13, 14, 16, 18, 25], "incorpor": [3, 9], "both": [3, 5, 13, 14, 16, 18, 25], "ionic": [3, 5, 6, 14, 15, 18], "metadata": [3, 6, 11], "datapoint": 3, "eigenvalu": [3, 13, 18, 21], "ferroelectr": 3, "integ": [3, 18, 25], "crystallograph": [3, 9], "seri": [3, 5, 13, 16, 18, 20], "angstrom": [3, 16, 18], "supercel": [3, 18], "quantiti": 3, "petousi": 3, "mrdjenovich": 3, "ballouz": 3, "liu": 3, "graf": 3, "schladt": 3, "persson": [3, 6, 9], "prinz": 3, "discoveri": 3, "novel": 3, "optic": 3, "160134": 3, "petousis2017": 3, "ioanni": 3, "eric": 3, "miao": 3, "donald": 3, "tanja": 3, "kristin": 3, "fritz": 3, "jan": 3, "134": [3, 16], "speci": [3, 5, 13, 14, 16, 18, 25], "occupi": [3, 14], "a1": 3, "a2": 3, "b1": 3, "b2": 3, "discuss": [3, 16], "pilania": 3, "rep": 3, "19375": 3, "srep19375": 3, "cmr": 3, "fysik": 3, "dtu": 3, "dk": 3, "pilania2016": 3, "mannodi": 3, "kanakkithodi": 3, "uberuaga": 3, "ramprasad": 3, "r": [3, 4, 5, 6, 9, 13, 14, 16, 18, 25], "gubernati": 3, "lookman": 3, "who": [3, 13], "lowest": [3, 14], "unoccupi": [3, 14, 16], "molecular": [3, 14, 18], "obit": 3, "g_reuss": 3, "bound": [3, 9], "polycrystallin": 3, "g_vrh": [3, 6], "g_voigt": 3, "upper": [3, 9], "k_reuss": 3, "k_vrh": [3, 4, 6], "k_voigt": 3, "compliance_tensor": 3, "elastic_anisotropi": 3, "metric": [3, 4, 11, 16], "correspond": [3, 9, 13, 14, 16, 18, 25], "ieee": 3, "orient": [3, 16, 25], "symmetr": [3, 18, 21], "elastic_tensor_origin": 3, "unsymmetr": 3, "convent": [3, 21], "poisson_ratio": [3, 6], "respons": [3, 25], "load": [3, 6, 11, 16, 25], "jong": 3, "angsten": 3, "notestin": 3, "gamst": 3, "sluiter": 3, "Ande": 3, "zwaag": 3, "der": 3, "plata": 3, "toher": [3, 9], "curtarolo": [3, 9], "ceder": [3, 9], "chart": 3, "crystallin": [3, 14], "150009": 3, "dejong2015": 3, "maarten": 3, "randi": 3, "anthoni": 3, "marcel": 3, "krishna": 3, "chaitanya": 3, "sybrand": 3, "jose": 3, "cormac": 3, "stefano": 3, "gerbrand": 3, "mark": 3, "mar": 3, "17": [3, 14], "There": [3, 7, 9, 13, 16], "276": 3, "mostli": 3, "oqmdid": 3, "expt": 3, "oqmd": [3, 9], "pearson": 3, "symbol": [3, 4, 6, 13, 14], "space": [3, 13, 16, 18, 25], "natur": [3, 13, 14, 25], "sdata2017162": 3, "kim2017": 3, "kim": 3, "georg": 3, "meschel": 3, "v": [3, 9, 14, 16, 18, 25], "nash": 3, "philip": 3, "intermetal": [3, 5, 14], "oct": 3, "170162": 3, "162": 3, "kim_meschel_nash_chen_2017": 3, "experimental_formation_enthalpies_for_intermetallic_phases_and_other_inorganic_compound": 3, "3822835": 3, "6084": 3, "m9": 3, "v1": 3, "abstractnot": 3, "reaction": 3, "compon": [3, 7, 16], "fundament": 3, "coupl": [3, 7], "calorimetri": 3, "howev": [3, 9], "often": 3, "intens": 3, "present": [3, 6, 13, 16, 18, 25], "transit": [3, 5, 14], "rare": 3, "earth": 3, "borid": 3, "carbid": 3, "silicid": 3, "50": [3, 4, 18], "quaternari": [3, 14], "becom": 3, "principl": [3, 9], "comparison": 3, "susan": 3, "compil": [3, 14], "primarili": [3, 25], "kubaschewski": 3, "janaf": 3, "liquid": [3, 14], "gase": 3, "exclud": [3, 9, 12, 16, 25], "dedupl": 3, "descipt": 3, "assign": [3, 16], "among": [3, 6, 18], "2021": 3, "03": 3, "22": 3, "theoret": 3, "had": 3, "least": [3, 16, 18, 21], "icsd": 3, "200": [3, 4], "mev": 3, "hull": [3, 16], "e_above_hul": [3, 4], "candid": 3, "chose": 3, "spacegroup": [3, 4, 5, 18], "expt_form_": 3, "298": 3, "uncertainti": 3, "phaseinfo": 3, "likely_mpid": 3, "kingsburi": 3, "mcdermott": 3, "framework": [3, 25], "quantifi": [3, 5, 13, 18], "chemrxiv": 3, "preprint": 3, "26434": 3, "14593476": 3, "springer": [3, 9], "busi": 3, "media": 3, "llc": [3, 14], "springernatur": 3, "book": [3, 13], "kubaschewski1993": 3, "alcock": 3, "spencer": 3, "6th": 3, "isbn": [3, 25], "0080418880": 3, "pergamon": 3, "press": 3, "thermochemistri": [3, 5, 14], "1993": 3, "18434": 3, "t42s31": 3, "kinet": 3, "gov": [3, 13, 14, 16, 18, 20], "malcolm": 3, "chase": 3, "thermochem": 3, "technologi": 3, "1998": 3, "rzyman2000309": 3, "alf": 3, "versu": 3, "calphad": 3, "309": 3, "318": 3, "2000": 3, "issn": 3, "0364": 3, "5916": 3, "1016": [3, 9, 16, 18], "s0364": 3, "01": [3, 14, 16], "00007": 3, "sciencedirect": 3, "pii": [3, 14], "s0364591601000074": 3, "rzyman": 3, "z": [3, 16], "moser": 3, "miodownik": 3, "kaufman": 3, "watson": 3, "weinert": 3, "crc2007": 3, "asin": 3, "0849304881": 3, "crc": 3, "handbook": [3, 9, 25], "dewei": 3, "530": 3, "ean": 3, "9780849304880": 3, "88": 3, "interhash": 3, "da6394e1a9c5f450ed705c32ec82bb08": 3, "intrahash": 3, "5ff8f541915536461697300e8727f265": 3, "crc_handbook": 3, "physic": [3, 6, 14, 16, 18, 25], "88th": 3, "2007": 3, "grindy2013": 3, "grindi": 3, "scott": 3, "bryce": 3, "kirklin": 3, "saal": 3, "jame": 3, "wolverton": [3, 11, 25], "1103": [3, 16, 18], "physrevb": [3, 16, 18], "87": [3, 16], "075150": 3, "10980121": 3, "condens": 3, "matter": [3, 25], "approach": [3, 13, 16, 18], "accuraci": [3, 16, 18], "diatom": 3, "2013": [3, 16, 25], "pub": 3, "ac": [3, 25], "suppl": 3, "jpclett": 3, "8b00124": 3, "zhuo": 3, "ya": 3, "letter": 3, "1668": 3, "1673": 3, "29532658": 3, "bartel": 3, "gupta": 3, "munro": 3, "scan": 3, "metagga": 3, "autom": 3, "workflow": [3, 6], "prepar": 3, "dunn2020": 3, "alexand": 3, "automatmin": [3, 6], "algorithm": [3, 6, 13], "npj": [3, 25], "sep": 3, "138": 3, "evalu": [3, 13, 14, 16, 18, 20], "supervis": 3, "ml": [3, 5, 6, 11, 13, 18], "13": 3, "thinspac": 3, "task": [3, 13], "rang": [3, 5, 9, 13, 14, 16, 18, 21, 25], "size": [3, 4, 5, 13, 14, 16, 18, 21, 25], "132k": 3, "deriv": [3, 13, 14, 16, 18, 20], "highli": 3, "extens": [3, 14], "fulli": [3, 9], "primit": [3, 5, 9, 13, 16], "without": [3, 6, 13, 25], "intervent": 3, "hyperparamet": 3, "tune": 3, "art": 3, "graph": [3, 18, 25], "neural": [3, 16, 25], "network": [3, 16, 25], "tradit": 3, "random": [3, 5, 18], "forest": 3, "achiev": [3, 13], "best": [3, 13], "show": [3, 6, 9, 11, 13, 25], "capabl": [3, 6, 13], "expos": 3, "advantag": [3, 6, 13], "appear": 3, "outperform": 3, "textasciitild": 3, "104": 3, "greater": [3, 13, 14], "encourag": [3, 6], "2057": 3, "3960": 3, "s41524": 3, "020": 3, "00406": 3, "decomposit": 3, "formation_energi": 3, "0k": 3, "0atm": 3, "zero": [3, 14, 16, 18], "pure": 3, "formation_energy_per_atom": [3, 4, 9, 14], "faber": [3, 18], "lindmaa": 3, "von": 3, "lilienfeld": 3, "armiento": 3, "int": [3, 9, 11, 13, 14, 16, 18, 21, 25], "chem": [3, 16, 18], "115": [3, 18], "1094": 3, "1101": 3, "1002": 3, "qua": 3, "24917": 3, "raw": [3, 5, 9, 13, 18, 25], "richard": [3, 25], "dacek": 3, "cholia": [3, 9, 25], "gunter": [3, 9], "skinner": 3, "commentari": 3, "genom": [3, 9, 25], "acceler": [3, 14], "innov": 3, "apl": 3, "mater": [3, 6, 25], "11002": 3, "felix": 3, "anatol": 3, "rickard": 3, "period": [3, 5, 14, 16, 18, 25], "onlinelibrari": 3, "wilei": 3, "pdf": 3, "vector": [3, 5, 14, 16, 18, 25], "organ": 3, "success": 3, "coulomb": [3, 5, 16, 18], "matrix": [3, 8, 13, 16, 21], "consid": [3, 9, 13, 14, 16], "relat": [3, 5, 14, 16, 18, 25], "electrostat": [3, 16, 18], "interact": [3, 5, 6, 16, 18, 21], "between": [3, 5, 13, 14, 16, 18, 21, 25], "repeat": [3, 16], "ii": 3, "neighbor": [3, 5, 13, 14, 16, 18, 20, 25], "iii": 3, "ansatz": 3, "mimic": 3, "basic": [3, 6, 11, 14], "sine": [3, 13, 18, 21], "coordin": [3, 6, 13, 16, 18, 19], "laplacian": [3, 25], "kernel": [3, 8, 28], "manhattan": 3, "norm": [3, 5, 14], "reproduc": [3, 6, 13, 25], "obtain": [3, 6, 9, 13, 18, 25], "3000": 3, "49": 3, "64": 3, "inc": 3, "1063": [3, 16], "4812323": 3, "davidson": 3, "stephen": 3, "shreya": 3, "dan": 3, "011002": 3, "interv": 3, "59": 3, "target": [3, 4, 5, 9, 11, 13, 14, 16, 25], "gfa": 3, "monolith": 3, "non": [3, 9, 13, 16, 18, 20, 25], "correl": 3, "design": [3, 14, 16, 21], "am": 3, "cr": 3, "7b01046": 3, "sun": 3, "bai": 3, "h": [3, 6, 25], "li": [3, 9, 18], "understand": [3, 6, 13], "3434": 3, "3439": 3, "28697303": 3, "disagr": 3, "hipt": 3, "co": [3, 6, 16, 21], "fe": [3, 9, 14], "nb": [3, 9], "corespond": 3, "cofezr": [3, 11], "cotizr": [3, 11], "covzr": [3, 11], "fetinb": [3, 11], "iter": [3, 13, 16, 18], "By": [3, 11, 13, 16, 18], "fang": 3, "ren": 3, "travi": 3, "kevin": 3, "law": [3, 14], "christoph": 3, "hattrick": 3, "simper": 3, "apurva": 3, "mehta": 3, "apr": 3, "eaaq1566": 3, "reneaaq1566": 3, "1126": 3, "sciadv": 3, "aaq1566": 3, "With": [3, 18], "hundr": 3, "technolog": 3, "societ": 3, "face": [3, 16, 18], "todai": 3, "guidanc": 3, "vast": 3, "combinatori": 3, "frustratingli": 3, "slow": [3, 9, 25], "expens": [3, 13, 14, 16, 25], "especi": [3, 13, 16], "strongli": 3, "influenc": 3, "previous": 3, "observ": [3, 13, 16], "physiochem": 3, "synthesi": 3, "textendash": 3, "guid": [3, 6], "hitp": 3, "good": [3, 7, 13, 14, 18, 20], "agreement": 3, "quantit": 3, "discrep": 3, "precis": [3, 18], "retrain": 3, "refin": 3, "significantli": [3, 14], "across": [3, 5, 13, 16, 18], "valid": [3, 4, 9, 25], "unreport": 3, "although": [3, 25], "rapid": 3, "sensit": [3, 18], "predictor": 3, "thu": [3, 16], "promis": 3, "greatli": 3, "believ": 3, "paradigm": 3, "wider": 3, "prove": 3, "equal": [3, 13, 14, 21], "sciencemag": 3, "varieti": [3, 6], "thousand": [3, 13, 14, 20], "reduc": [3, 13], "6203": 3, "6118": 3, "6780": 3, "5800": 3, "5736": 3, "qc": 3, "quasi": [3, 9], "meltspin": [3, 11], "kawazo": 3, "masumoto": 3, "tsai": 3, "yu": 3, "aihara": 3, "jr": 3, "1997": 3, "ed": [3, 9], "springermateri": 3, "introduct": [3, 25], "37a": 3, "gp": 3, "9783540605072": 3, "verlag": 3, "berlin": 3, "heidelberg": 3, "09": 3, "2019": [3, 14, 16, 25], "landoltbornstein1997": 3, "sm_lbs_978": 3, "540": 3, "47679": 3, "5_2": 3, "editor": 3, "textperiodcent": 3, "datasheet": 3, "rnstein": 3, "1007": [3, 9], "10510374": 3, "copyright": [3, 14, 25], "part": [3, 13], "23": 3, "10510374_2": 3, "lb": 3, "ward2016": 3, "agraw": [3, 25], "ankit": 3, "alok": 3, "aug": 3, "26": [3, 9], "16028": [3, 25], "npjcompumat": 3, "28": 3, "576": 3, "449": 3, "half": 3, "128": 3, "invers": [3, 18, 21], "latt": [3, 14], "const": 3, "satur": 3, "emu": 3, "cc": 3, "num_electron": 3, "pol": 3, "polar": 3, "struct": [3, 9, 14, 16, 18], "tetragon": [3, 18], "150561": 3, "alabama": 3, "epsilon_x": 3, "opt": 3, "calculu": 3, "epsilon_i": 3, "epsilon_z": 3, "exfoliation_en": 3, "monolay": 3, "jid": 3, "initi": [3, 13, 14, 16, 18, 21], "identif": 3, "character": [3, 13, 18, 25], "irina": 3, "kalish": 3, "ryan": 3, "beam": [3, 18], "francesca": 3, "tavazza": 3, "5179": 3, "orcid": 3, "0000": 3, "0001": 3, "9737": 3, "8074": 3, "jdft_2d": 3, "choudhary2017": 3, "criterion": [3, 9, 14], "mainli": [3, 13], "rel": [3, 5, 13, 16, 18, 25], "1356": 3, "satisfi": [3, 9, 14], "creat": [3, 4, 6, 13, 16, 18, 21], "layer": [3, 18, 25], "energet": 3, "1012": 3, "430": 3, "371": 3, "common": [3, 6, 9, 16, 18, 25], "rest": [3, 9], "underwai": 3, "To": [3, 6, 7, 13, 14, 16, 18], "suggest": [3, 5, 9, 16], "accept": [3, 13, 25], "molybdenum": 3, "tellurid": 3, "rai": [3, 18], "diffract": [3, 5, 18], "raman": 3, "scatter": 3, "limit": [3, 6, 9, 13, 14, 18, 25], "publicli": 3, "websit": 3, "ctcm": 3, "extasciitild": 3, "knc6": 3, "jvasp": 3, "html": [3, 4, 9, 13, 25], "2045": [3, 9], "2322": 3, "s41598": 3, "017": 3, "05402": 3, "choudhary__2018": 3, "2018_json": 3, "6815705": 3, "3d": [3, 11, 18], "low": 3, "waal": 3, "gowoon": 3, "cheon": 3, "evan": 3, "reed": 3, "phy": [3, 16, 18, 25], "rev": [3, 16, 18, 25], "98": [3, 25], "014107": [3, 16], "jdft_3d": 3, "numpag": 3, "ap": [3, 14, 16, 18], "6815699": 3, "v2": [3, 11], "section": 3, "vasp": 3, "nanowir": 3, "1d": [3, 5, 18, 21], "0d": 3, "carri": [3, 16], "pattern": [3, 13, 18], "radial": [3, 5, 13, 16, 18, 21], "distribut": [3, 5, 13, 16, 18, 21, 25], "gamma": [3, 13, 14, 16], "mass_x": 3, "mass_i": 3, "mass_z": 3, "e_exfol": 3, "hole": [3, 13], "forc": [3, 5, 18, 25], "field": [3, 5, 9, 11, 13, 18], "inspir": [3, 5, 18], "fast": [3, 13, 14, 20, 25], "landscap": 3, "brian": 3, "decost": 3, "083801": [3, 18], "physrevmateri": [3, 18], "choudhary_2018": 3, "descriptors_and_material_properti": 3, "6870101": 3, "classic": [3, 5, 18], "25000": 3, "paw": 3, "pw91": 3, "c11": 3, "hexagon": [3, 18], "c12": 3, "c13": 3, "c33": 3, "c44": 3, "d_ma": 3, "distanc": [3, 5, 13, 14, 16, 18, 21], "d_mx": 3, "iopscienc": 3, "iop": 3, "1088": 3, "0953": 3, "8984": 3, "21": 3, "30": [3, 4, 13], "305403": 3, "warschkow": 3, "bilek": 3, "mckenzi": 3, "ax": [3, 16], "stack": 3, "2009": 3, "famili": 3, "nanolamin": 3, "compos": 3, "slab": 3, "nitrid": 3, "manifest": 3, "benefici": 3, "ceram": 3, "attract": 3, "scale": [3, 13, 16, 18], "240": 3, "reveal": 3, "govern": 3, "role": 3, "shearabl": 3, "strongest": 3, "seen": 3, "plane": 3, "11": 3, "extrem": [3, 14], "snc": 3, "far": [3, 18], "44": [3, 16], "abov": [3, 6, 13], "convex": [3, 16], "150mev": 3, "those": [3, 9, 16, 18, 21, 25], "less": [3, 13, 14, 25], "nobl": 3, "april": 3, "nest": [3, 9, 25], "must": [3, 9, 13, 14, 16, 18, 25], "detail": [3, 6, 9, 13, 14, 16, 18, 21, 25], "variabl": [3, 4, 9, 11, 13, 14], "jain2013": 3, "2166532x": 3, "aip": [3, 16], "ampad": 3, "i1": 3, "p011002": 3, "s1": 3, "agg": 3, "accord": [3, 9, 13, 14, 16, 18], "span": [3, 6], "1ev": 3, "remain": [3, 16, 25], "closest": [3, 5, 18], "masouri": 3, "lett": [3, 18], "conflict": 3, "enter": [3, 7], "nonmet": [3, 25], "is_met": 3, "agre": 3, "classif": [3, 13, 25], "neg": [3, 4, 13, 14, 16], "fail": [3, 13, 14, 18, 20], "logarithm": [3, 13], "voigt": 3, "reuss": 3, "hill": 3, "moduli": [3, 14], "5ev": 3, "entir": [3, 13, 14, 16, 18], "rpbe": 3, "petretto": 3, "last": [3, 13, 16, 18], "phdo": 3, "peak": 3, "frequenc": [3, 21], "highest": [3, 14], "cm": 3, "estim": [3, 5, 13, 14, 16, 18, 20], "domin": 3, "longitudin": 3, "180065": 3, "65": 3, "petretto2018": 3, "guido": 3, "shyam": 3, "miranda": 3, "henriqu": 3, "giantomassi": 3, "matteo": 3, "setten": 3, "michiel": 3, "gonz": 3, "xavier": 3, "petretto_dwaraknath_miranda_winston_giantomassi_rignanese_van": 3, "setten_gonze_persson_hautier_2018": 3, "throughput_dens": 3, "functional_perturbation_theory_phonons_for_inorganic_materi": 3, "3938023": 3, "knowledg": [3, 13, 25], "import": [3, 6, 13], "phenomena": 3, "spectra": 3, "hinder": 3, "analysi": [3, 6, 16, 18, 25], "dispers": [3, 18], "1521": [3, 18], "153092": 3, "mp_all": 3, "mp_nostruct": 3, "voight": 3, "e_hul": 3, "anisotropi": [3, 16], "eps_electron": 3, "eps_tot": 3, "vacuum": 3, "strucutr": 3, "eij_max": 3, "point_group": 3, "v_max": 3, "geerl": 3, "enabl": [3, 13, 16, 18], "150053": 3, "henri": 3, "aslaug": 3, "29": [3, 18], "53": 3, "multivari": 3, "simul": [3, 6, 14], "down": [3, 6, 18], "tabular": 3, "moder": 3, "m\u2091\u1d9c\u1d52\u207f\u1d48": 3, "\u03c3": 3, "\u03ba\u2091": 3, "pf": [3, 4, 6], "dope": [3, 13], "10\u00b9\u2078": 3, "\u00b3": 3, "minimum": [3, 9, 13, 16, 18, 21], "chosen": [3, 14, 18], "1300": 3, "10\u00b9\u2076": 3, "10\u00b2\u00b9": 3, "divid": [3, 14, 16, 18], "\u00b9\u2074": 3, "task_id": 3, "gradient": 3, "\u03b4e": 3, "\u00e5\u00b3": 3, "\u00b5v": 3, "300k": [3, 13], "microvolt": 3, "s\u1d49": 3, "1300k": 3, "21cm": 3, "\u03c9": 3, "\u00b5w": 3, "k\u00b2": 3, "\u03c3\u1d49": 3, "pf\u1d49": 3, "electr": 3, "\u03ba\u2091\u1d49": 3, "m\u2091\u1d9c": 3, "\u03b5": 3, "m\u2091": 3, "\u03b5\u2081": 3, "1st": [3, 14], "\u03b5\u2082": 3, "2nd": [3, 21], "\u03b5\u2083": 3, "3rd": 3, "pretty_formula": [3, 4], "weight": [3, 13, 14, 16, 18, 21], "percent": 3, "elong": 3, "mn": 3, "mo": 3, "ni": 3, "tc": 3, "No": 3, "core": [3, 5, 13, 16, 18], "asid": 3, "un": 3, "under": [3, 6, 13, 16], "creativ": 3, "licens": [3, 6], "creativecommon": 3, "018": 3, "0085": 3, "stanev2018": 3, "jun": 3, "valentin": 3, "corei": 3, "os": [3, 9], "gilad": 3, "kusn": 3, "efrain": 3, "rodriguez": [3, 18], "johnpierr": 3, "paglion": 3, "ichiro": 3, "takeuchi": [3, 25], "nimssupercon": 3, "supercon": 3, "nim": 3, "go": 3, "jp": 3, "index_en": 3, "station": 3, "815": 3, "share": [3, 16], "toolkit": [3, 6], "zenodo": 3, "5530535": 3, "yjj3zhdmjlq": 3, "licenc": 3, "collat": 3, "ht_id": 3, "collabor": 3, "rhy": 3, "goodal": 3, "human": [3, 13], "readabl": 3, "track": [3, 6], "httk": 3, "initial_structur": [3, 9], "final_structur": [3, 9], "e_vasp_per_atom": 3, "final": [3, 18], "chemical_system": 3, "actual": [3, 14, 16], "tholander2016strong": 3, "strong": 3, "tiznn2": 3, "zrznn2": 3, "hfznn2": 3, "tholand": 3, "andersson": 3, "cba": 3, "tasnadi": 3, "ferenc": 3, "bj": 3, "rn": 3, "appli": [3, 5, 6, 13, 25], "120": 3, "225102": 3, "webpag": 3, "mrl": 3, "edu": 3, "8080": 3, "datamin": 3, "jsp": 3, "src": 3, "nanoparticl": 3, "brief": [3, 9], "rho": 3, "ohm": 3, "resist": 3, "muv": 3, "mk": 3, "zt": 3, "figur": 3, "merit": 3, "kappa": 3, "watt": 3, "meter": 3, "sigma": [3, 13, 16, 25], "siemen": 3, "bibtext_ref": 3, "150557": 3, "gaultois2013": 3, "cm400893e": 3, "2911": 3, "2920": 3, "michael": 3, "gaultoi": 3, "borg": 3, "ram": 3, "seshadri": 3, "bonificio": 3, "clark": 3, "driven": 3, "resourc": [3, 9, 25], "consider": 3, "abo3": 3, "emeri": 3, "alpha": [3, 18], "degre": [3, 16, 18, 21], "pervoskit": 3, "beta": 3, "wrt": 3, "db": [3, 9], "distort": [3, 16], "vpa": [3, 4, 18], "170153": 3, "153": 3, "5334142": 3, "emery2017": 3, "antoin": 3, "chri": 3, "emery_2017": 3, "throughput_dft_calculations_of_formation_energy_stability_and_oxygen_vacancy_formation_energy_of_abo3_perovskit": 3, "fuel": 3, "piezo": 3, "ferro": 3, "remark": 3, "substitut": [3, 13, 18], "await": 3, "exhaust": 3, "329": 3, "ubiquit": 3, "395": 3, "yet": 3, "therefor": 3, "avenu": 3, "futur": [3, 13, 14, 20], "activ": 3, "area": [3, 6, 16, 18], "minut": 4, "retrieve_mp": [4, 6, 8, 28], "popul": 4, "scikit": [4, 6, 13, 25], "librari": [4, 5, 6, 7, 9, 13, 16, 25], "displai": [4, 13], "warn": [4, 9, 13, 14, 18, 20], "filterwarn": 4, "numpi": [4, 13, 16, 18, 21], "np": [4, 13, 16, 18, 21], "view": 4, "set_opt": 4, "1000": [4, 9], "max_column": 4, "max_row": 4, "1a": 4, "data_retriev": [4, 6, 8, 28], "api_kei": [4, 9], "your": [4, 6, 7, 9, 13, 14, 16], "mapi_kei": [4, 13, 14], "mpr": 4, "criteria": [4, 6, 9], "ne": [4, 16], "want": [4, 6, 13, 18], "materialsproject": 4, "mapidoc": 4, "df_mp": [4, 6], "get_datafram": [4, 6, 8, 9], "print": [4, 9, 11], "len": [4, 9, 18], "6023": 4, "1b": 4, "explor": [4, 6], "head": 4, "1c": 4, "unstabl": 4, "2a": 4, "2b": 4, "pymatgendata": [4, 8, 14, 25], "lambda": [4, 13, 14, 16], "row": [4, 6, 9, 11, 13, 14, 18], "atomic_mass": 4, "atomic_radiu": 4, "boiling_point": 4, "melting_point": [4, 14], "std_dev": [4, 13, 16, 18, 21], "ep": 4, "data_sourc": [4, 14, 16], "dropna": 4, "3a": 4, "relev": [4, 6, 9, 13], "x_col": 4, "as_matrix": 4, "3b": 4, "linear_model": 4, "linearregress": 4, "mean_squared_error": 4, "lr": 4, "statist": [4, 5, 14, 16, 18, 21], "round": 4, "score": [4, 13], "3f": 4, "sqrt": [4, 13, 14], "y_true": 4, "y_pred": 4, "804": 4, "32": [4, 18], "558": 4, "3c": 4, "model_select": 4, "kfold": 4, "cross_val_scor": 4, "fold": [4, 16], "90": [4, 18], "crossvalid": 4, "n_split": 4, "shuffl": 4, "random_st": 4, "cv": 4, "rmse_scor": 4, "33": 4, "plotli": [4, 6], "make_plot": 4, "x_titl": [4, 6], "y_titl": [4, 6], "plot_titl": 4, "plot_mod": 4, "offlin": 4, "margin_left": 4, "150": 4, "textsiz": 4, "35": 4, "ticksiz": 4, "filenam": [4, 6, 25], "lr_regress": 4, "line": [4, 9, 13, 16], "perfect": 4, "xy_param": 4, "400": 4, "y_col": 4, "color": [4, 6], "black": 4, "legend": 4, "xy_plot": 4, "marker_outline_width": 4, "add_xy_plot": 4, "great": 4, "let": [4, 6], "examin": 4, "5a": 4, "ensembl": 4, "randomforestregressor": 4, "rf": 4, "n_estim": 4, "988": 4, "947": 4, "5b": 4, "087": 4, "6a": 4, "pf_rf": 4, "rf_regress": 4, "xy_lin": 4, "450": 4, "6b": 4, "feature_importances_": 4, "asarrai": 4, "argsort": 4, "margin_bottom": 4, "rf_import": 4, "bar_chart": 4, "below": [5, 6, 7, 9, 13, 14, 25], "modul": [5, 6, 28], "special": [5, 14], "mix": [5, 14, 25], "mismatch": [5, 14], "zhang": [5, 14], "wenalloi": [5, 13, 14], "categori": [5, 9, 14], "stoichiometri": [5, 13, 14], "elementfract": [5, 13, 14], "tmetalfract": [5, 13, 14], "stoichiometr": [5, 14], "center": [5, 13, 14, 16, 18, 21], "oxidationst": [5, 13, 14], "about": [5, 6, 9, 13, 14, 20], "ionproperti": [5, 13, 14], "electronaffin": [5, 13, 14], "affin": [5, 14], "formal": [5, 14, 18, 25], "charg": [5, 14, 16, 18, 25], "electronegativitydiff": [5, 13, 14], "homo": [5, 14], "valenceorbit": [5, 13, 14], "shell": [5, 14, 16, 18], "characterist": [5, 13, 14], "geometr": [5, 14, 21], "cohesiveenergi": [5, 13, 14], "lookup": [5, 14, 18, 25], "conversionfeatur": [5, 6, 8, 13], "strtocomposit": [5, 8, 13], "structuretocomposit": [5, 8, 13], "structuretoistructur": [5, 8, 13], "immut": [5, 13], "istructur": [5, 13, 18, 25], "dicttoobject": [5, 8, 13], "mson": [5, 13], "jsontoobject": [5, 8, 13], "structuretooxidstructur": [5, 8, 13], "compositiontooxidcomposit": [5, 8, 13], "compositiontostructurefrommp": [5, 8, 13], "pymatgenfunctionappl": [5, 6, 8, 13], "aseatomstostructur": [5, 6, 8, 13], "ase": [5, 6, 9, 13], "particular": [5, 9, 11, 13, 18, 25], "completedo": [5, 13], "signific": [5, 13, 14], "charact": [5, 13, 14, 18], "asymmetri": [5, 13], "bondorientationalparamet": [5, 13, 16], "spheric": [5, 16], "averagebondlength": [5, 13, 16], "averagebondangl": [5, 13, 16], "rather": [5, 9, 13, 14, 16, 18, 20], "geometri": [5, 16], "short": [5, 6, 11, 16], "deviat": [5, 14, 16, 18, 21], "nomin": [5, 16], "ewaldsiteenergi": [5, 13, 16], "localpropertydiffer": [5, 13, 16], "siteelementalproperti": [5, 13, 16], "smooth": [5, 16], "overlap": [5, 16], "product": [5, 13, 16], "gaussian": [5, 13, 16, 21, 25], "window": [5, 7, 16, 21], "botu": [5, 16], "opsitefingerprint": [5, 13, 16], "env": [5, 16], "tessel": [5, 16, 18], "chemenvsitefingerprint": [5, 13, 16], "resembl": [5, 16], "ideal": [5, 13, 14, 16, 18, 20, 25], "miscellan": [5, 16, 18], "interstic": [5, 16], "nearest": [5, 13, 14, 16, 18, 20, 25], "behler": [5, 16], "generalizedradialdistributionfunct": [5, 13, 16], "angularfourierseri": [5, 13, 16], "angular": [5, 16, 18], "fourier": [5, 16], "nearestneighbor": [5, 16, 18], "bag": [5, 13, 18], "hansen": [5, 18], "globalinstabilityindex": [5, 13, 18], "structuralheterogen": [5, 13, 18], "varianc": [5, 18], "minimumrelativedist": [5, 13, 18], "kind": [5, 13, 14, 18], "jarviscfid": [5, 13, 18], "matric": [5, 18, 20], "friendli": [5, 18], "nuclear": [5, 18], "variant": [5, 18], "ewaldenergi": [5, 13, 18], "structurecomposit": [5, 13, 18], "arrai": [5, 6, 9, 13, 16, 18, 21, 25], "powder": [5, 18], "densityfeatur": [5, 13, 18], "chemicalord": [5, 13, 18], "much": [5, 13, 18], "maximumpackingeffici": [5, 13, 18], "shannon": [5, 18], "entropi": [5, 13, 14, 18], "radialdistributionfunct": [5, 13, 18], "partialradialdistributionfunct": [5, 13, 18], "xtal": [5, 18], "electronicradialdistributionfunct": [5, 13, 18], "inher": [5, 18], "redf": [5, 18], "globalsymmetryfeatur": [5, 13, 18], "linear": [5, 13, 14, 18, 25], "chain": [5, 18], "OR": [5, 18], "routin": [6, 13], "40": 6, "domain": 6, "own": [6, 25], "transform": [6, 8, 9, 13, 25], "numer": [6, 16, 18, 20, 25], "70": 6, "bandstructur": [6, 8, 9, 28], "expans": 6, "practic": [6, 13, 18], "deep": 6, "infer": [6, 18], "come": [6, 13, 16, 25], "soon": 6, "itself": [6, 13], "downstream": 6, "bsd": 6, "tour": 6, "scroll": [6, 14], "imagenet": 6, "dynam": [6, 9, 13], "leaderboard": 6, "autogener": 6, "tutori": 6, "fe3o4": [6, 14], "thing": 6, "radii": [6, 14, 16], "substitu": 6, "sophist": 6, "easi": [6, 7, 13, 17], "biggest": 6, "hous": 6, "extern": [6, 8, 13], "execut": 6, "pars": [6, 13], "emploi": [6, 13, 21], "retrieve_citrin": [6, 8, 28], "df_citrin": 6, "mongodataretriev": [6, 8, 9], "anoth": [6, 13, 16, 18], "mongodb": [6, 9], "flexibl": [6, 9], "schema": [6, 9], "rich": 6, "syntax": [6, 9], "ever": 6, "leav": 6, "interpret": 6, "unifi": [6, 18], "jarvis_dft_3d": 6, "Or": 6, "oper": [6, 9, 13, 21, 25], "load_jarvis_dft_3d": [6, 8, 11], "drop_nan_column": [6, 11], "visual": 6, "prototyp": [6, 9], "bulk_shear_moduli": 6, "colorscal": 6, "picnic": 6, "browser": 6, "summari": [6, 13], "toler": [6, 13, 14], "robustli": 6, "10k": 6, "ASE": [6, 9, 13], "aa2": 6, "ignore_error": [6, 13], "ca": [6, 18, 25], "37728887": 6, "57871271": 6, "73949": 6, "707570": 6, "pbc": 6, "inf": 6, "068845188153371": 6, "406272406310": 6, "06884519": 6, "40627241": 6, "45891585": 6, "908485": 6, "*********": 6, "06428082": 6, "117271": 6, "mg": 6, "0963526175": 6, "0689416025": 6, "536": 6, "09635262": 6, "0689416": 6, "53602403": 6, "593": 6, "690196": 6, "10982": 6, "n3": 6, "10983": 6, "****************": 6, "4173924989": 6, "51157821": 6, "4173925": 6, "21553922": 6, "724276": 6, "10984": 6, "***************": 6, "5112839336581": 6, "37546772": 6, "51128393": 6, "81784473": 6, "4573": 6, "342423": 6, "10985": 6, "55195829": 6, "770852": 6, "10986": 6, "44565668": 6, "05259079": 6, "ind": 6, "445": 6, "954243": 6, "arbitrari": [6, 13], "chard": [6, 9], "foster": [6, 9, 16], "152": 6, "60": [6, 25], "69": 6, "help": [6, 7, 13], "clariti": 6, "credit": 6, "everi": [6, 14, 16, 18, 25], "keep": [6, 13], "someth": [6, 9], "tell": 6, "stuck": 6, "everyon": 6, "know": [6, 13], "difficult": [6, 25], "fork": 6, "pull": [6, 7, 11], "request": [6, 9, 11], "submit": [6, 9], "question": [6, 13], "contact": [6, 13], "quick": [7, 9, 13], "command": 7, "bash": 7, "termin": 7, "home": 7, "development": 7, "clone": 7, "cd": 7, "sure": 7, "troubl": 7, "sympi": [7, 13], "forg": [7, 9], "anaconda": 7, "conda": 7, "easiest": 7, "still": [7, 16, 18], "ticket": 7, "likelihood": 7, "someon": 7, "els": [7, 13, 16, 18], "clearer": 7, "submodul": [8, 28], "test_retrieve_aflow": [8, 9], "test_retrieve_citrin": [8, 9], "test_retrieve_mdf": [8, 9], "test_retrieve_mp": [8, 9], "test_retrieve_mpd": [8, 9], "test_retrieve_mongodb": [8, 9], "retrieve_aflow": [8, 28], "aflowdataretriev": [8, 9], "api_link": [8, 9], "get_relaxed_structur": [8, 9], "retrievalqueri": [8, 9], "from_pymongo": [8, 9], "__init__": [8, 9, 13, 14, 16, 18, 20, 21, 25], "get_data": [8, 9], "get_valu": [8, 9], "parse_scalar": [8, 9], "retrieve_mdf": [8, 28], "mdfdataretriev": [8, 9], "make_datafram": [8, 9], "try_get_prop_by_material_id": [8, 9], "retrieve_mpd": [8, 28], "apierror": [8, 9], "mpdsdataretriev": [8, 9], "chillouttim": [8, 9], "compile_cryst": [8, 9], "default_properti": [8, 9], "endpoint": [8, 9], "maxnpag": [8, 9], "pages": [8, 9], "retrieve_mongodb": [8, 28], "clean_project": [8, 9], "is_int": [8, 9], "remove_int": [8, 9], "retrieve_bas": [8, 28], "basedataretriev": [8, 9], "test_convenience_load": [8, 11], "test_dataset_retriev": [8, 11], "test_util": [8, 11], "load_boltztrap_mp": [8, 11], "load_brgoch_superhard_train": [8, 11], "load_castelli_perovskit": [8, 11], "load_citrine_thermal_conduct": [8, 11], "load_dielectric_const": [8, 11], "load_double_perovskites_gap": [8, 11], "load_double_perovskites_gap_lumo": [8, 11], "load_expt_formation_enthalpi": [8, 11], "load_expt_gap": [8, 11], "load_flla": [8, 11], "load_glass_binari": [8, 11], "load_glass_ternary_hipt": [8, 11], "load_glass_ternary_landolt": [8, 11], "load_heusler_magnet": [8, 11], "load_jarvis_dft_2d": [8, 11], "load_jarvis_ml_dft_train": [8, 11], "load_m2ax": [8, 11], "load_mp": [8, 11], "load_phonon_dielectric_mp": [8, 11], "load_piezoelectric_tensor": [8, 11], "load_steel_strength": [8, 11], "load_wolverton_oxid": [8, 11], "dataset_retriev": [8, 28], "get_all_dataset_info": [8, 11], "get_available_dataset": [8, 11], "get_dataset_attribut": [8, 11], "get_dataset_cit": [8, 11], "get_dataset_column_descript": [8, 11], "get_dataset_column": [8, 11], "get_dataset_descript": [8, 11], "get_dataset_num_entri": [8, 11], "get_dataset_refer": [8, 11], "ion": [8, 13, 18], "orbit": [8, 13, 16, 18], "thermo": [8, 13], "test_bandstructur": [8, 13], "test_bas": [8, 13], "test_convers": [8, 13], "test_do": [8, 13], "test_funct": [8, 13], "get_bindex_bspin": [8, 13], "featurize_wrapp": [8, 13], "fit_featurize_datafram": [8, 13, 18], "set_chunks": [8, 13], "set_n_job": [8, 13], "get_cbm_vbm_scor": [8, 13], "get_site_dos_scor": [8, 13], "illegal_charact": [8, 13], "exp_dict": [8, 13], "generate_string_express": [8, 13], "generate_expressions_combin": [8, 13], "data_fil": [8, 14, 25], "deml_elementdata": [8, 25], "test_cach": [8, 13, 20, 25], "test_data": [8, 25], "test_flatten_dict": [8, 25], "test_io": [8, 25], "get_all_nearest_neighbor": [8, 25], "get_nearest_neighbor": [8, 25], "abstractdata": [8, 14, 16, 25], "get_elemental_properti": [8, 25], "cohesiveenergydata": [8, 25], "demldata": [8, 14, 25, 27], "get_charge_dependent_properti": [8, 25], "get_oxidation_st": [8, 14, 25], "iucrbondvalencedata": [8, 25], "get_bv_param": [8, 13, 18, 25], "interpolate_soft_anion": [8, 25], "megnetelementdata": [8, 25], "matscholarelementdata": [8, 14, 25], "mixingenthalpi": [8, 25], "get_mixing_enthalpi": [8, 25], "oxidationstatedependentdata": [8, 25], "get_charge_dependent_property_from_speci": [8, 25], "oxidationstatesmixin": [8, 25], "flatten_dict": [8, 28], "io": [8, 9, 13, 28], "load_dataframe_from_json": [8, 25], "store_dataframe_as_json": [8, 25], "gaussian_kernel": [8, 25], "laplacian_kernel": [8, 25], "dropexclud": [8, 25], "itemselector": [8, 25], "homogenize_multiindex": [8, 25], "aflowdataretrievaltest": [9, 10], "test_get_data": [9, 10, 25, 27], "citrinedataretrievaltest": [9, 10], "test_multiple_items_in_list": [9, 10], "mdfdataretrievaltest": [9, 10], "setupclass": [9, 10], "test_get_datafram": [9, 10], "test_get_dataframe_by_queri": [9, 10], "test_make_datafram": [9, 10], "mpdataretrievaltest": [9, 10], "mpdsdataretrievaltest": [9, 10], "test_valid_answ": [9, 10], "mongodataretrievaltest": [9, 10], "test_cleaned_project": [9, 10], "test_remove_int": [9, 10], "aflux": 9, "consortium": 9, "server": 9, "robust": [9, 13, 16, 25], "addition": [9, 18], "rose": 9, "gossett": 9, "nardelli": 9, "fornari": 9, "lux": 9, "137": 9, "362": 9, "370": 9, "commatsci": 9, "036": 9, "bibtext": 9, "request_s": 9, "10000": 9, "request_limit": 9, "index_auid": 9, "build": 9, "pymongo": 9, "Then": [9, 13, 18], "auid": 9, "aurl": 9, "singleton": 9, "gt": 9, "lt": 9, "a17a2da2f3d3953a": 9, "invalid": 9, "read": [9, 13, 25], "prototype_structur": 9, "input_structur": 9, "band_structur": 9, "todo": [9, 16, 18], "catalog": 9, "batch_siz": 9, "instanc": [9, 13, 16, 18], "constructor": 9, "classmethod": [9, 10, 14, 16, 18], "client": 9, "ve": 9, "citrine_kei": 9, "prop": [9, 14], "data_typ": 9, "min_measur": 9, "max_measur": 9, "from_record": 9, "data_set_id": 9, "max_result": 9, "machine_learn": 9, "num": 9, "pif": 9, "common_field": 9, "secondary_field": 9, "print_properties_opt": 9, "unsur": 9, "exact": [9, 25], "word": [9, 25], "capit": 9, "chemicalformula": 9, "recommend": [9, 13, 16, 18, 25], "dict_item": 9, "scalar": [9, 25], "anonym": 9, "kwarg": [9, 13, 16, 18], "facil": 9, "invoc": 9, "authent": 9, "mdf_dr": 9, "ag": 9, "Be": 9, "source_nam": 9, "match_rang": 9, "blaiszik": 9, "pruyn": 9, "ananthakrishnan": 9, "tueck": 9, "jom": 9, "68": [9, 25], "2052": 9, "s11837": 9, "016": 9, "2001": 9, "login": 9, "globu": 9, "local_ep": 9, "squeri": 9, "unwind_arrai": [9, 25], "explicit": [9, 13], "unwind": 9, "coarsen": 9, "semisolid": 9, "cu": 9, "tag": [9, 18], "outcar": 9, "resource_typ": 9, "match_field": 9, "converg": 9, "exclude_field": 9, "oqdm": 9, "exclude_rang": 9, "brafman": 9, "program": 9, "transfer": [9, 25], "97": 9, "209": 9, "215": 9, "2014": 9, "037": 9, "config": 9, "mp_decod": 9, "index_mpid": 9, "mprester": 9, "1234": 9, "fe2o3": [9, 13], "2o3": 9, "materials_id": 9, "plu": 9, "bandstructure_uniform": 9, "phonon_bandstructur": 9, "phonon_ddb": 9, "phonon_do": 9, "long": [9, 11, 13, 14, 20], "material_id_list": 9, "get_prop_by_material_id": 9, "readili": 9, "get_bandstructure_by_material_id": 9, "get_": 9, "_by_material_id": 9, "line_mod": 9, "favor": [9, 16], "mpds_client": 9, "msg": 9, "usag": 9, "export": 9, "mpds_kei": 9, "srtio3": 9, "jsonobj": 9, "sg": 9, "99": 9, "cell_abc": 9, "sg_n": 9, "basis_noneq": 9, "els_noneq": 9, "villar": 9, "paul": [9, 16], "big": 9, "toward": 9, "andreoni": 9, "yip": 9, "cham": 9, "pp": 9, "978": [9, 25], "319": [9, 25], "42913": 9, "7_62": 9, "consum": 9, "envvar": 9, "gatewai": 9, "datarow": 9, "flavor": 9, "pmg": [9, 13], "attent": 9, "wrap": 9, "occup": [9, 18], "construct": [9, 16, 18], "facet": [9, 16], "categ_a": 9, "val_a": 9, "categ_b": 9, "val_b": 9, "iodid": 9, "capac": 9, "distinct": [9, 18], "concept": [9, 13], "interest": [9, 25], "nax": 9, "ariti": 9, "shape": [9, 16], "schemata": 9, "coll": 9, "idx_field": 9, "strict": [9, 13], "dot": [9, 25], "notat": [9, 25], "auto": [9, 13], "disallow": 9, "inclus": [9, 18], "adher": 9, "sensibl": [9, 16], "mydataretriev": 9, "NOT": 9, "bg": 9, "suffici": 9, "sparingli": 9, "augment": 9, "nativ": 9, "overrid": [9, 13], "It": [9, 13, 16, 18], "web": 9, "googl": [9, 13], "styleguid": [9, 13], "pyguid": [9, 13], "methodnam": [10, 12, 15, 17, 19, 20, 22, 27], "runtest": [10, 12, 15, 17, 19, 20, 22, 27], "hook": [10, 12, 15, 17, 19, 20, 22, 27], "fixtur": [10, 12, 15, 17, 19, 20, 22, 27], "exercis": [10, 12, 15, 17, 19, 20, 22, 27], "pymatgentest": [10, 15, 17, 19, 20, 22, 27], "dataretrievaltest": [11, 12], "test_get_all_dataset_info": [11, 12], "test_get_dataset_attribut": [11, 12], "test_get_dataset_cit": [11, 12], "test_get_dataset_column_descript": [11, 12], "test_get_dataset_column": [11, 12], "test_get_dataset_descript": [11, 12], "test_get_dataset_num_entri": [11, 12], "test_get_dataset_refer": [11, 12], "test_load_dataset": [11, 12], "test_print_available_dataset": [11, 12], "datasetstest": [11, 12], "matbenchdatasetstest": [11, 12], "test_matbench_v0_1": [11, 12], "matminerdatasetstest": [11, 12], "test_boltztrap_mp": [11, 12], "test_brgoch_superhard_train": [11, 12], "test_castelli_perovskit": [11, 12], "test_citrine_thermal_conduct": [11, 12], "test_double_perovskites_gap": [11, 12], "test_double_perovskites_gap_lumo": [11, 12], "test_elastic_tensor_2015": [11, 12], "test_expt_formation_enthalpi": [11, 12], "test_expt_formation_enthalpy_kingsburi": [11, 12], "test_expt_gap": [11, 12], "test_expt_gap_kingsburi": [11, 12], "test_flla": [11, 12], "test_glass_binari": [11, 12], "test_glass_binary_v2": [11, 12], "test_glass_ternary_hipt": [11, 12], "test_glass_ternary_landolt": [11, 12], "test_heusler_magnet": [11, 12], "test_jarvis_dft_2d": [11, 12], "test_jarvis_dft_3d": [11, 12], "test_jarvis_ml_dft_train": [11, 12], "test_m2ax": [11, 12], "test_mp_all_20181018": [11, 12], "test_mp_nostruct_20181018": [11, 12], "test_phonon_dielectric_mp": [11, 12], "test_piezoelectric_tensor": [11, 12], "test_ricci_boltztrap_mp_tabular": [11, 12], "test_steel_strength": [11, 12], "test_superconductivity2018": [11, 12], "test_tholander_nitrides_e_form": [11, 12], "test_ucsb_thermoelectr": [11, 12], "test_wolverton_oxid": [11, 12], "utilstest": [11, 12], "test_fetch_external_dataset": [11, 12], "test_get_data_hom": [11, 12], "test_get_file_sha256_hash": [11, 12], "test_load_dataset_dict": [11, 12], "test_read_dataframe_from_fil": [11, 12], "test_validate_dataset": [11, 12], "boltztrap_mp": 11, "drop_suspect": 11, "expt_formation_enthalpi": 11, "engin": 11, "brgoch_featur": 11, "basic_descriptor": 11, "possibli": 11, "incorrect": 11, "verifi": 11, "castelli_perovskit": 11, "room_temperatur": 11, "return_lumo": 11, "expt_gap": 11, "me": 11, "glass_binari": 11, "explan": 11, "glass_ternary_hipt": 11, "unique_composit": 11, "glass_ternary_landolt": 11, "m2ax": 11, "include_structur": 11, "phonon_dielectric_mp": 11, "output_str": 11, "print_format": 11, "medium": 11, "sort_method": 11, "alphabet": [11, 18], "don": [11, 14, 16, 25], "anyth": 11, "num_entri": 11, "attrib_kei": 11, "dataset_column": 11, "pbar": [11, 13, 25], "otherwis": [11, 13, 14, 16, 18], "matminer_data": 11, "host": [12, 18], "test_alloi": [13, 14], "test_composit": [13, 14, 18], "test_el": [13, 14], "test_ion": [13, 14], "test_orbit": [13, 14], "test_pack": [13, 14], "test_thermo": [13, 14], "deltah_chem": [13, 14], "deltah_elast": [13, 14], "deltah_struct": [13, 14], "deltah_topo": [13, 14], "compute_atomic_fract": [13, 14], "compute_configuration_entropi": [13, 14], "compute_delta": [13, 14], "compute_enthalpi": [13, 14], "compute_gamma_radii": [13, 14], "compute_lambda": [13, 14], "compute_local_mismatch": [13, 14], "compute_magpie_summari": [13, 14], "compute_strength_local_mismatch_shear": [13, 14], "compute_weight_fract": [13, 14], "compute_omega": [13, 14], "from_preset": [13, 14, 16, 18], "deml_data": [13, 14], "magpie_data": [13, 14], "cationproperti": [13, 14], "is_ion": [13, 14], "compute_nearest_cluster_dist": [13, 14], "compute_simultaneous_packing_effici": [13, 14], "create_cluster_lookup_tool": [13, 14], "find_ideal_cluster_s": [13, 14], "get_ideal_radius_ratio": [13, 14], "test_bond": [13, 16, 18], "test_chem": [13, 16], "test_extern": [13, 16], "test_fingerprint": [13, 16], "test_misc": [13, 16, 18], "test_rdf": [13, 16, 18], "get_wigner_coeff": [13, 16], "load_cn_motif_op_param": [13, 16], "load_cn_target_motif_op": [13, 16], "analyze_area_interstic": [13, 16], "analyze_dist_interstic": [13, 16], "analyze_vol_interstic": [13, 16], "cosine_cutoff": [13, 16], "g2": [13, 16], "g4": [13, 16], "test_matrix": [13, 18, 20], "test_ord": [13, 18], "test_sit": [13, 18], "test_symmetri": [13, 18], "enumerate_all_bond": [13, 18], "enumerate_bond": [13, 18], "calc_bv_sum": [13, 18], "calc_gii_iucr": [13, 18], "calc_gii_pymatgen": [13, 18], "compute_bv": [13, 18], "get_equiv_sit": [13, 18], "get_chem": [13, 18], "get_chg": [13, 18], "get_distribut": [13, 18], "get_atom_ofm": [13, 18], "get_mean_ofm": [13, 18], "get_ohv": [13, 18], "get_single_ofm": [13, 18], "get_structure_ofm": [13, 18], "compute_prdf": [13, 18], "get_rdf_bin_label": [13, 18], "partialssitestatsfingerprint": [13, 18], "compute_pssf": [13, 18], "all_featur": [13, 18], "crystal_idx": [13, 18], "bandstructurefeaturestest": [13, 20], "test_bandfeatur": [13, 20], "test_branchpointenergi": [13, 20], "fittablefeatur": [13, 20], "matrixfeatur": [13, 20], "multiargs2": [13, 20], "multitypefeatur": [13, 20], "multiplefeaturefeatur": [13, 20], "singlefeatur": [13, 20], "singlefeaturizermultiarg": [13, 20], "singlefeaturizermultiargswithprecheck": [13, 20], "singlefeaturizerwithprecheck": [13, 20], "testbaseclass": [13, 20], "make_test_data": [13, 20], "test_datafram": [13, 16, 17, 20], "test_featurize_mani": [13, 20], "test_fitt": [13, 20], "test_ignore_error": [13, 20], "test_indic": [13, 20], "test_inplac": [13, 20], "test_multifeature_no_zero_index": [13, 20], "test_multifeatures_multiarg": [13, 20], "test_multiindex_in_multifeatur": [13, 20], "test_multiindex_inplac": [13, 20], "test_multiindex_return": [13, 20], "test_multipl": [13, 20], "test_multiprocessing_df": [13, 20], "test_multitype_multifeat": [13, 20], "test_precheck": [13, 20], "test_stacked_featur": [13, 20], "testconvers": [13, 20], "test_ase_convers": [13, 20], "test_composition_to_oxidcomposit": [13, 20], "test_composition_to_structurefrommp": [13, 20], "test_conversion_multiindex": [13, 20], "test_conversion_multiindex_dynam": [13, 20], "test_conversion_overwrit": [13, 20], "test_dict_to_object": [13, 20], "test_json_to_object": [13, 20], "test_pymatgen_general_convert": [13, 20], "test_str_to_composit": [13, 20], "test_structure_to_composit": [13, 20], "test_structure_to_oxidstructur": [13, 20], "test_to_istructur": [13, 20], "dosfeaturestest": [13, 20], "test_dosfeatur": [13, 20], "test_dopingfermi": [13, 20], "test_dosasymmetri": [13, 20], "test_hybrid": [13, 20], "test_sitedo": [13, 20], "testfunctionfeatur": [13, 20], "test_featur": [13, 20], "test_featurize_label": [13, 20], "test_helper_funct": [13, 20], "test_multi_featur": [13, 20], "test_grdf": [13, 16, 17, 21], "test_oxid": [13, 21], "test_stat": [13, 21], "abstractpairwis": [13, 16, 21], "bessel": [13, 16, 21], "cosin": [13, 16, 21], "histogram": [13, 16, 21], "initialize_pairwise_funct": [13, 21], "has_oxidation_st": [13, 21], "avg_dev": [13, 18, 21], "calc_stat": [13, 21], "geom_std_dev": [13, 21], "inverse_mean": [13, 21], "quantil": [13, 21], "kpoint": 13, "find_method": 13, "nband": 13, "1x3": 13, "interpol": 13, "noth": 13, "scipi": [13, 18], "griddata": 13, "bandstructuresymmlin": 13, "float": [13, 14, 16, 18, 21, 25], "is_gap_direct": 13, "direct_gap": 13, "p_ex1_norm": 13, "n_ex1_norm": 13, "p_ex1_degen": 13, "n_ex1_degen": 13, "n_0": 13, "0_en": 13, "p_0": [13, 16], "extremum": 13, "is_cbm": 13, "get_cbm": 13, "email": [13, 14, 16, 18, 20], "ajain": [13, 14, 16, 18, 20], "lbl": [13, 14, 16, 18, 20], "lbnl": [13, 14, 16, 18, 20], "n_vb": 13, "n_cb": 13, "calculate_band_edg": 13, "atol": 13, "05": [13, 16, 18], "bpe": 13, "calc": 13, "equival": [13, 14, 16, 18], "irreduc": 13, "brillouin": 13, "zone": 13, "ibz": 13, "subtract": 13, "branch_point_energi": 13, "target_gap": 13, "uniform": [13, 18, 25], "symm": [13, 18, 21], "scissor": 13, "scope": 13, "scikitlearn": 13, "wrapper": [13, 18], "beyond": 13, "peopl": 13, "wrote": 13, "meaning": 13, "twice": 13, "affect": 13, "get_param": 13, "set_param": 13, "interoper": 13, "worthwhil": 13, "hard": [13, 14], "particularli": [13, 14, 16, 18], "literatur": [13, 25], "worth": 13, "parallelis": 13, "lightweight": 13, "overhead": 13, "pool": 13, "increas": [13, 18], "_chunksiz": 13, "magnitud": 13, "chunk": 13, "computation": 13, "thread": 13, "contrast": [13, 16], "trial": 13, "optimum": 13, "rule": [13, 18], "thumb": 13, "declar": 13, "overview": 13, "mention": 13, "reader": 13, "hyperlink": 13, "readthedoc": 13, "block": 13, "enough": 13, "explain": 13, "math": [13, 14], "behind": 13, "abl": 13, "idea": [13, 14], "col_id": 13, "return_error": 13, "multiindex": [13, 25], "thrown": 13, "encount": 13, "xfeatur": 13, "memori": 13, "overwrit": 13, "suppli": [13, 18], "traceback": 13, "fit_kwarg": [13, 16, 18, 20], "fit_arg": 13, "fit_transform": [13, 18], "guarante": [13, 14, 20, 25], "correctli": [13, 14, 18, 20], "ground": [13, 14, 20], "truth": [13, 14, 20], "unlik": [13, 14, 20], "obsolet": [13, 14, 20], "overridden": [13, 14, 16, 18, 20], "opportun": [13, 14, 20], "throw": [13, 14, 18, 20], "runtim": [13, 14, 20], "return_frac": 13, "union": [13, 25], "unless": [13, 18], "reason": 13, "unreli": 13, "concurr": 13, "job": 13, "spawn": 13, "dure": [13, 18, 25], "seem": 13, "caus": 13, "oom": 13, "hpc": 13, "node": 13, "known": [13, 14, 25], "workaround": 13, "iterate_over_entri": 13, "class_nam": 13, "regress": [13, 25], "probabl": [13, 18], "standalon": 13, "target_col_id": 13, "overwrite_data": 13, "even": [13, 18], "ase_atom": 13, "composition_oxid": 13, "coerce_mix": 13, "return_original_on_error": 13, "guess": 13, "alreadi": [13, 14, 16, 18], "begin": [13, 18, 21], "target_column": 13, "strip": 13, "cannot": [13, 18], "control": 13, "add_oxidation_state_by_guess": 13, "comp": [13, 14, 21], "decor": 13, "map_kei": 13, "docstr": 13, "accordingli": 13, "super": 13, "_object": 13, "dict_data": 13, "msonabl": 13, "as_dict": 13, "json_data": 13, "to_json": 13, "func": 13, "func_arg": 13, "func_kwarg": 13, "anonymized_formula": 13, "And": 13, "obj": 13, "string_composit": 13, "structure_oxid": 13, "decay_length": 13, "sampling_resolut": 13, "gaussian_smear": 13, "underlin": 13, "top": [13, 14], "cb": 13, "vb": 13, "exponenti": 13, "decai": 13, "cutoff": [13, 16, 18, 21], "five": [13, 18], "smear": [13, 18], "featurize_label": 13, "xbm_score_i": 13, "ith": 13, "xbm_location_i": 13, "xbm_character_i": 13, "xbm_specie_i": 13, "ex": 13, "xbm_hybrid": 13, "amount": [13, 16], "ln": [13, 14], "larger": [13, 16], "pdo": 13, "eref": 13, "midgap": 13, "return_eref": 13, "featurizar": 13, "fermido": 13, "treat": [13, 18], "align": 13, "equilibrium": 13, "dos_fermi": 13, "band_cent": 13, "fermi_c": 13, "20t300": 13, "1e20": 13, "fermi_c1": 13, "18t600": 13, "600k": 13, "quotient": 13, "directli": [13, 21], "meant": 13, "semi": [13, 14], "cbm_": 13, "energy_cutoff": 13, "vbm_sp": 13, "sp": [13, 18], "vbm_": 13, "vbm_p": 13, "cbm_si_p": 13, "extrema": 13, "pair": [13, 14, 16, 18, 25], "becaus": [13, 16, 18], "cbm_score_i": 13, "cbm_score_tot": 13, "vbm_score_i": 13, "vbm_score_tot": 13, "idx": [13, 16], "cbm_score": 13, "vbm_score": 13, "orbital_scor": 13, "locat": 13, "gather": [13, 14, 18], "accumul": 13, "express": 13, "multi_feature_depth": 13, "postprocess": 13, "combo_funct": 13, "latexify_label": 13, "elimin": 13, "redund": 13, "illeg": 13, "exp": [13, 16], "magpiedata_avg_dev_nfval": 13, "magpiedata_range_numb": 13, "nfvalenc": 13, "parseabl": 13, "pairwis": [13, 16, 18, 21], "cast": 13, "complex128": 13, "prod": 13, "cumul": 13, "combo": 13, "cleanli": 13, "render": 13, "latex": 13, "essenti": 13, "function_list": 13, "Not": [13, 18], "intend": 13, "invok": 13, "input_variable_nam": 13, "combo_depth": 13, "ones": [13, 16], "independ": 13, "trivial": 13, "compositionfeaturestest": [14, 15], "alloyfeaturizerstest": [14, 15], "test_wenalloi": [14, 15], "test_miedema_al": [14, 15], "test_miedema_ss": [14, 15], "test_yang": [14, 15], "compositefeaturestest": [14, 15], "test_elem": [14, 15], "test_elem_deml": [14, 15], "test_elem_matmin": [14, 15], "test_elem_matscholar_el": [14, 15], "test_elem_megnet_el": [14, 15], "test_fere_corr": [14, 15], "test_meredig": [14, 15], "elementfeaturestest": [14, 15], "test_band_cent": [14, 15], "test_fract": [14, 15], "test_stoich": [14, 15], "test_tm_fract": [14, 15], "ionfeaturestest": [14, 15], "test_cation_properti": [14, 15], "test_elec_affin": [14, 15], "test_en_diff": [14, 15], "test_is_ion": [14, 15], "test_oxidation_st": [14, 15], "orbitalfeaturestest": [14, 15], "test_atomic_orbit": [14, 15], "test_val": [14, 15], "packingfeaturestest": [14, 15], "test_ap": [14, 15], "thermofeaturestest": [14, 15], "test_cohesive_energi": [14, 15], "test_cohesive_energy_mp": [14, 15], "struct_typ": 14, "ss_type": 14, "min": [14, 18], "empir": [14, 25], "multicompon": 14, "formul": 14, "1980": 14, "sub": [14, 16, 18], "care": 14, "inter": [14, 18], "ss": 14, "amor": 14, "fcc": [14, 16], "hcp": [14, 16], "no_latt": 14, "73": 14, "molar_volum": 14, "electron_dens": 14, "valence_electron": 14, "a_const": 14, "r_const": 14, "h_tran": 14, "structural_st": 14, "miedema_deltah_int": 14, "miedema_deltah_ss": 14, "miedema_deltah_amor": 14, "frac": [14, 16, 18], "topolog": 14, "lattice_typ": 14, "qnd": [14, 18], "assist": 14, "wen": 14, "acta": [14, 18, 25], "materiala": 14, "170": 14, "109": 14, "117": 14, "battel": 14, "allianc": 14, "right": 14, "reserv": 14, "delta": [14, 18, 25], "vec": 14, "n_": [14, 16], "c_i": 14, "ga": 14, "left": 14, "v_i": [14, 16], "ass": 14, "h_mix": 14, "miracle_radius_stat": 14, "smallest": 14, "rac": 14, "r_": [14, 16], "miracleradiu": [14, 16], "yang_delta": 14, "eq": 14, "c_j": 14, "v_j": 14, "c_": 14, "v_": 14, "attribute_nam": 14, "mean_shear_modulu": 14, "g_i": 14, "ight": 14, "g_": [14, 16], "strengthen": 14, "linkinghub": 14, "elsevi": 14, "s0254058411009357": 14, "balanc": 14, "r_i": [14, 18], "radiu": [14, 16, 18], "miracl": 14, "tandfonlin": 14, "ref": 14, "1179": 14, "095066010x12646898728200": 14, "assess": [14, 21, 25], "t_m": 14, "s_": 14, "h_": 14, "nearli": 14, "unari": 14, "deml": [14, 25], "all_attribut": 14, "preset_nam": 14, "matscholar_el": 14, "megnet_el": 14, "103": 14, "constitu": [14, 25], "rough": 14, "p_list": 14, "num_atom": 14, "p_norm": 14, "lp": 14, "frac_magn_atom": 14, "magn_moment": 14, "avg_anion_affin": 14, "cacoso": 14, "manner": 14, "en_diff_stat": 14, "oxidationstatemixin": 14, "dramat": 14, "heteroval": 14, "cpd_possibl": 14, "neutral": 14, "max_ionic_char": 14, "avg_ionic_char": 14, "estiat": 14, "pml": 14, "dilut": 14, "fec0": 14, "00001": 14, "truncat": [14, 16, 18], "situat": 14, "awar": 14, "homo_charact": 14, "homo_el": 14, "homo_energi": 14, "lumo_charact": 14, "lumo_el": 14, "lumo_energi": 14, "gap_ao": 14, "energei": 14, "avg": [14, 18], "valence_attribut": 14, "threshold": 14, "n_nearest": 14, "max_typ": 14, "sphere": [14, 16, 18], "doifind": 14, "ncomms9123": 14, "simultan": 14, "central": [14, 16], "too": 14, "dist": [14, 16, 18], "thr": 14, "closer": 14, "ferri": 14, "nat": [14, 16], "8123": 14, "veri": [14, 25], "l_2": 14, "unmatch": 14, "nearbi": 14, "radius_ratio": 14, "minim": [14, 18], "rp": 14, "n_neighbor": 14, "lord": 14, "ranganathan": 14, "nn": [14, 16, 18], "yourself": 14, "eg": 14, "nacl": 14, "sitefeaturizertest": [16, 17], "teardown": [16, 17, 25, 27], "bondingtest": [16, 17], "test_averagebondangl": [16, 17], "test_averagebondlength": [16, 17], "test_bop": [16, 17], "chemicalsitetest": [16, 17], "test_chemicalsro": [16, 17], "test_ewald_sit": [16, 17], "test_local_prop_diff": [16, 17], "test_site_elem_prop": [16, 17], "externalsitetest": [16, 17], "test_soap": [16, 17], "fingerprinttest": [16, 17], "test_chemenv_site_fingerprint": [16, 17], "test_crystal_nn_fingerprint": [16, 17], "test_off_center_cscl": [16, 17], "test_op_site_fingerprint": [16, 17], "test_simple_cub": [16, 17], "test_voronoifingerprint": [16, 17], "miscsitetest": [16, 17], "test_cn": [16, 17], "test_interstice_distribution_of_cryst": [16, 17], "test_interstice_distribution_of_glass": [16, 17], "rdftest": [16, 17], "test_af": [16, 17], "test_gaussiansymmfunc": [16, 17], "adjac": 16, "strc": [16, 18], "proxim": 16, "max_l": 16, "compute_w": 16, "compute_w_hat": 16, "bop": 16, "invari": 16, "rotat": 16, "steinhardt": 16, "weigh": [16, 18], "mickel": 16, "scitat": 16, "4774084": 16, "vari": 16, "smoothli": 16, "propos": [16, 18], "_prb_": [16, 18], "1983": 16, "wigner": 16, "3j": 16, "triplet": 16, "sro": 16, "f_el": 16, "n_el": 16, "c_el": 16, "rais": 16, "csro__": 16, "csro": 16, "mendeleev": [16, 18], "intersect": [16, 18], "el_list_": 16, "besid": 16, "el_amt_dict_": 16, "choic": [16, 18], "voronoinn": [16, 18], "jmolnn": [16, 18], "miniumdistancenn": [16, 18], "minimumokeeffenn": [16, 18], "minimumvirenn": [16, 18], "loop": 16, "ewald_site_energi": 16, "decim": [16, 18], "sign": 16, "a_n": 16, "sum_n": [16, 18], "p_n": 16, "aspect": 16, "schmidt": 16, "_chem": 16, "mater_": 16, "rcut": 16, "nmax": 16, "lmax": 16, "rbf": 16, "gto": 16, "crossov": 16, "spectrum": 16, "real": 16, "tesser": 16, "basi": [16, 18], "orthonorm": 16, "altern": 16, "polynomi": 16, "On": 16, "albert": 16, "bart\u00f3k": 16, "risi": 16, "kondor": 16, "g\u00e1bor": 16, "cs\u00e1nyi": 16, "184115": 16, "alchem": 16, "sandip": 16, "michel": 16, "ceriotti": 16, "13754": 16, "c6cp00415f": 16, "singroup": 16, "himanen": 16, "ger": 16, "morooka": 16, "federici": 16, "canova": 16, "ranawat": 16, "gao": 16, "rink": 16, "106949": 16, "cpc": 16, "region": 16, "bigger": 16, "expand": 16, "nl": 16, "sum_": [16, 18], "mathrm": 16, "beta_": 16, "alpha_": 16, "cut": [16, 18], "disabl": 16, "definit": [16, 18], "sitecollect": 16, "choos": [16, 18], "eta": 16, "empirici": 16, "a_i": 16, "limits_": 16, "ij": 16, "pi": 16, "r_c": 16, "th": [16, 18], "bold": 16, "_i": 16, "_j": 16, "differenti": [16, 18], "mayb": 16, "cetyp": 16, "strategi": 16, "geom_find": 16, "max_csm": 16, "max_dist_fac": 16, "41": 16, "percentag": 16, "ce": 16, "chemenvstrategi": 16, "localgeometryfind": 16, "finder": 16, "continu": 16, "csm": 16, "constrain": 16, "multi_weight": 16, "op_typ": 16, "chem_info": 16, "wt": 16, "multipli": [16, 18], "crystalnn": [16, 18], "liter": 16, "octahedr": 16, "tetrahedr": 16, "target_motif": 16, "dr": [16, 18], "ddr": 16, "ndr": 16, "dop": 16, "001": 16, "dist_exp": 16, "zero_op": 16, "compli": 16, "next": 16, "largest": [16, 18], "variat": [16, 18], "wherea": [16, 18], "expon": 16, "penal": 16, "switch": 16, "off": [16, 18], "tetrahedron": 16, "opval": 16, "use_symm_weight": 16, "symm_weight": 16, "solid_angl": 16, "stats_vol": 16, "stats_area": 16, "stats_dist": 16, "n_i": 16, "denot": [16, 18], "icosahedra": 16, "stronger": 16, "use_weight": 16, "polyhedron": 16, "sub_polyhedra": 16, "face_dist": 16, "fpor": 16, "discontinu": 16, "96": 16, "cn_": 16, "w_n": [16, 18], "coordint": 16, "interstice_typ": 16, "radius_typ": 16, "categor": 16, "empti": 16, "triangul": 16, "surfac": 16, "portion": 16, "5537": 16, "anisotrop": 16, "inequ": 16, "grid": 16, "despit": 16, "systemat": 16, "vol": 16, "nn_coord": 16, "nn_r": 16, "convex_hull_simplic": 16, "analyz": [16, 18], "simplici": 16, "area_interstice_list": 16, "center_r": 16, "nn_dist": 16, "dist_interstice_list": 16, "center_coord": 16, "tetrahedra": 16, "volume_interstice_list": 16, "interstice_fp": 16, "g_n": 16, "squar": [16, 18], "trig": 16, "d_n": 16, "rectangular": [16, 21], "gn": 16, "g1": 16, "slowli": 16, "std": 16, "dev": 16, "etas_g2": 16, "etas_g4": 16, "zetas_g4": 16, "gammas_g4": 16, "074106": 16, "2011": 16, "fortran": 16, "amp": 16, "atomist": 16, "bitbucket": 16, "andrewpeterson": 16, "80": 16, "005": 16, "zeta": 16, "ndarrai": [16, 18, 21], "neigh_dist": 16, "neigh_coord": 16, "n_bin": 16, "omit": [16, 18], "n_site": 16, "translat": 16, "mutual": 16, "exclus": 16, "pairwise_grdf": 16, "site2": 16, "deconstruct": [17, 27], "structurefeaturestest": [18, 19], "bondingstructuretest": [18, 19], "test_globalinstabilityindex": [18, 19], "test_bob": [18, 19], "test_bondfract": [18, 19], "test_min_relative_dist": [18, 19], "test_ward_prb_2017_strhet": [18, 19], "compositestructurefeaturestest": [18, 19], "test_jarviscfid": [18, 19], "matrixstructurefeaturestest": [18, 19], "test_coulomb_matrix": [18, 19], "test_orbital_field_matrix": [18, 19], "test_sine_coulomb_matrix": [18, 19], "miscstructurefeaturestest": [18, 19], "test_composition_featur": [18, 19], "test_ewald": [18, 19], "test_xrd_powderpattern": [18, 19], "orderstructurefeaturestest": [18, 19], "test_density_featur": [18, 19], "test_ordering_param": [18, 19], "test_packing_effici": [18, 19], "test_structural_complex": [18, 19], "structurerdftest": [18, 19], "test_get_rdf_bin_label": [18, 19], "test_prdf": [18, 19], "test_rdf_and_peak": [18, 19], "test_redf": [18, 19], "partialstructuresitesfeaturestest": [18, 19], "test_partialsitestatsfingerprint": [18, 19], "test_ward_prb_2017_efftcn": [18, 19], "test_ward_prb_2017_lpd": [18, 19], "structuresitesfeaturestest": [18, 19], "test_sitestatsfingerprint": [18, 19], "structuresymmetryfeaturestest": [18, 19], "test_dimension": [18, 19], "test_global_symmetri": [18, 19], "coulomb_matrix": 18, "token": 18, "coloumb": 18, "occur": [18, 21], "nonloc": 18, "pad": 18, "cl": 18, "return_baglen": 18, "concaten": 18, "val": 18, "local_env": 18, "bbv": 18, "no_oxi": 18, "approx_bond": 18, "allowed_bond": 18, "ie": 18, "bad": 18, "balip": 18, "batio3": 18, "agnost": 18, "ca2": 18, "o2": 18, "ca3": 18, "forbidden": 18, "euclidean": 18, "listlik": 18, "training_data": 18, "r_cut": 18, "disordered_pymatgen": 18, "iucr": [18, 25], "disord": 18, "prone": 18, "gii": 18, "r2bacuo5": 18, "lu": 18, "yb": 18, "tm": 18, "er": 18, "ho": 18, "dy": 18, "gd": 18, "eu": 18, "sm": 18, "neutron": 18, "salina": 18, "sanchez": 18, "garcia": 18, "mu\u00f1oz": 18, "carvaj": 18, "saez": 18, "puch": 18, "martinez": 18, "201": 18, "211": 18, "1992": 18, "0022": 18, "4596": 18, "92": 18, "90094": 18, "fall": 18, "back": [18, 25], "root": 18, "site_v": 18, "site_el": 18, "neighbor_list": 18, "tabul": [18, 25], "scale_factor": 18, "965": 18, "tunabl": 18, "ro": 18, "cat_val": [18, 25], "an_val": [18, 25], "iupac": [18, 25], "bond_val_list": [18, 25], "include_dist": 18, "include_speci": 18, "f_ij": 18, "r_ij": 18, "atom_i": 18, "atom_j": 18, "fact": 18, "regardless": 18, "flat": 18, "fewer": 18, "tent": 18, "dists_relative_min": 18, "mrd": 18, "use_cel": 18, "use_chem": 18, "use_chg": 18, "use_rdf": 18, "use_adf": 18, "use_ddf": 18, "use_nn": 18, "chemo": 18, "dihedr": 18, "557": 18, "usnistgov": 18, "prmateri": 18, "438": 18, "378": 18, "179": 18, "arr": 18, "c_size": 18, "max_cut": 18, "adfa": 18, "adfb": 18, "ddf": 18, "bondo": 18, "diag_elem": 18, "put": 18, "forward": 18, "rupp": 18, "058301": 18, "diagon": 18, "m_ij": 18, "z_i": 18, "z_j": 18, "r_j": 18, "period_tag": 18, "39": 18, "subshel": 18, "32x32": 18, "39x39": 18, "polyhedra": 18, "lanthanid": 18, "actinid": 18, "1024": 18, "pham": 18, "_sci": 18, "tech": 18, "adv": 18, "mat_": 18, "1080": 18, "14686996": 18, "1378060": 18, "themselv": 18, "mean_ofm": 18, "count": 18, "hot": 18, "whose": 18, "ohv": 18, "my_ohv": 18, "site_dict": 18, "atom_ofm": 18, "sin": [18, 21], "dimens": 18, "per_atom": 18, "_charg": 18, "structure_": 18, "ewald_energi": 18, "two_theta_rang": 18, "127": 18, "bw_method": 18, "pattern_length": 18, "gaussian_kd": 18, "two_theta": 18, "wavelength": 18, "xrd": 18, "xrdcalcul": 18, "radiat": 18, "warren": 18, "cowlei": 18, "t_n": 18, "x_t": 18, "t_p": 18, "randomli": 18, "surround": 18, "sup": 18, "desired_featur": 18, "touch": 18, "symprec": 18, "equat": 18, "p_i": 18, "log_2": 18, "m_i": 18, "inequival": 18, "bit": 18, "willighagen": 18, "cryst": [18, 25], "2005": [18, 25], "b61": 18, "36": 18, "valenceionicradiusevalu": 18, "bin_siz": 18, "include_elem": 18, "exclude_elem": 18, "broken": 18, "schutt": 18, "prb": 18, "89": 18, "205118": 18, "discret": 18, "00": 18, "dist_bin": 18, "pari": 18, "nx1": 18, "bin_dist": 18, "inner": 18, "site_featur": 18, "min_oxi": 18, "max_oxi": 18, "break": 18, "nth": 18, "_mode": 18, "2nd_mode": 18, "pssf": 18, "ssf": 18, "sitestatsfeatur": 18, "fittabl": 18, "nn_method": 18, "isol": 18, "connect": 18, "centrosymmetri": 18, "spacegroup_num": 18, "crystal_system": 18, "crystal_system_int": 18, "is_centrosymmetr": 18, "n_symmetry_op": 18, "monoclin": 18, "orthorhomb": 18, "triclin": 18, "trigon": 18, "refitt": 20, "dtype": [20, 25], "lack": 20, "grdftest": [21, 22], "test_bessel": [21, 22], "test_cosin": [21, 22], "test_gaussian": [21, 22], "test_histogram": [21, 22], "test_load_class": [21, 22], "test_sin": [21, 22], "oxidationtest": [21, 22], "test_has_oxidation_st": [21, 22], "testpropertystat": [21, 22], "test_avg_dev": [21, 22], "test_geom_std_dev": [21, 22], "test_holder_mean": [21, 22], "test_kurtosi": [21, 22], "test_maximum": [21, 22], "test_mean": [21, 22], "test_minimum": [21, 22], "test_mod": [21, 22], "test_quantil": [21, 22], "test_rang": [21, 22], "test_skew": [21, 22], "test_std_dev": [21, 22], "instanti": [21, 25], "commonli": 21, "holder": 21, "0th": 21, "cours": 21, "data_lst": 21, "arithmet": 21, "frequent": 21, "shew": 21, "test_plot": 23, "testcach": [25, 27], "testdemldata": [25, 27], "test_get_oxid": [25, 27], "test_get_properti": [25, 27], "testiucrbondvalencedata": [25, 27], "testmegnetdata": [25, 27], "testmagpiedata": [25, 27], "testmatscholardata": [25, 27], "testmixingenthalpi": [25, 27], "testpymatgendata": [25, 27], "flattendicttest": [25, 27], "test_flatten_nested_dict": [25, 27], "iotest": [25, 27], "test_load_dataframe_from_json": [25, 27], "test_store_dataframe_as_json": [25, 27], "generate_json_fil": [25, 27], "get_all_nn_info": 25, "site_idx": 25, "get_nn_info": 25, "elem": 25, "property_nam": 25, "knowledgedoor": 25, "onlin": 25, "elements_handbook": 25, "cohesive_energi": 25, "8th": 25, "charl": 25, "kittel": 25, "471": 25, "41526": 25, "hayr": 25, "stevanov": 25, "conden": 25, "93": 25, "interpolate_soft": 25, "crystallographi": 25, "notic": 25, "disclaim": 25, "fee": 25, "profit": 25, "anyon": 25, "owner": 25, "suitabl": 25, "_audit_upd": 25, "regard": 25, "brown": 25, "brockhous": 25, "mcmaster": 25, "hamilton": 25, "ontario": 25, "canada": 25, "idbrown": 25, "warrant": 25, "nor": 25, "advis": 25, "br": 25, "se": 25, "assumpt": 25, "brese": 25, "keeff": 25, "1991": 25, "b47": 25, "194": 25, "usual": 25, "soft": 25, "materialsvirtuallab": 25, "chi": 25, "weik": 25, "ye": 25, "yunx": 25, "zuo": 25, "zheng": 25, "3564": 25, "3572": 25, "chemmat": 25, "9b01294": 25, "smaller": 25, "though": 25, "quit": 25, "magpie_elementdata_feature_descript": 25, "nlp": 25, "million": 25, "tshitoyan": 25, "dagdelen": 25, "weston": 25, "unsupervis": 25, "captur": 25, "latent": 25, "571": 25, "95": 25, "s41586": 25, "019": 25, "1335": 25, "inou": 25, "Its": 25, "tran": 25, "46": 25, "2817": 25, "2829": 25, "valid_element_list": 25, "elema": 25, "elemb": 25, "use_common_oxi_st": 25, "kocher": 25, "314": 25, "nested_dict": 25, "lead_kei": 25, "recurs": 25, "walk": 25, "append": 25, "front": 25, "ascii": 25, "inconveni": 25, "suffix": 25, "to_dict": 25, "preserv": 25, "rate": 25, "arr0": 25, "arr1": 25, "krr": 25, "trick": 25, "unwant": 25, "featureunion": 25, "auto_exampl": 25, "hetero_feature_union": 25, "default_kei": 25, "coerc": 25, "homogen": 25, "parent": 25, "subpackag": 28}, "objects": {"": [[8, 0, 0, "-", "matminer"]], "matminer": [[9, 0, 0, "-", "data_retrieval"], [11, 0, 0, "-", "datasets"], [13, 0, 0, "-", "featurizers"], [25, 0, 0, "-", "utils"]], "matminer.data_retrieval": [[9, 0, 0, "-", "retrieve_AFLOW"], [9, 0, 0, "-", "retrieve_Citrine"], [9, 0, 0, "-", "retrieve_MDF"], [9, 0, 0, "-", "retrieve_MP"], [9, 0, 0, "-", "retrieve_MPDS"], [9, 0, 0, "-", "retrieve_MongoDB"], [9, 0, 0, "-", "retrieve_base"], [10, 0, 0, "-", "tests"]], "matminer.data_retrieval.retrieve_AFLOW": [[9, 1, 1, "", "AFLOWDataRetrieval"], [9, 1, 1, "", "RetrievalQuery"]], "matminer.data_retrieval.retrieve_AFLOW.AFLOWDataRetrieval": [[9, 2, 1, "", "api_link"], [9, 2, 1, "", "citations"], [9, 2, 1, "", "get_dataframe"], [9, 2, 1, "", "get_relaxed_structure"]], "matminer.data_retrieval.retrieve_AFLOW.RetrievalQuery": [[9, 2, 1, "", "from_pymongo"]], "matminer.data_retrieval.retrieve_Citrine": [[9, 1, 1, "", "CitrineDataRetrieval"], [9, 3, 1, "", "get_value"], [9, 3, 1, "", "parse_scalars"]], "matminer.data_retrieval.retrieve_Citrine.CitrineDataRetrieval": [[9, 2, 1, "", "__init__"], [9, 2, 1, "", "api_link"], [9, 2, 1, "", "citations"], [9, 2, 1, "", "get_data"], [9, 2, 1, "", "get_dataframe"]], "matminer.data_retrieval.retrieve_MDF": [[9, 1, 1, "", "MDFDataRetrieval"], [9, 3, 1, "", "make_dataframe"]], "matminer.data_retrieval.retrieve_MDF.MDFDataRetrieval": [[9, 2, 1, "", "__init__"], [9, 2, 1, "", "api_link"], [9, 2, 1, "", "citations"], [9, 2, 1, "", "get_data"], [9, 2, 1, "", "get_dataframe"]], "matminer.data_retrieval.retrieve_MP": [[9, 1, 1, "", "MPDataRetrieval"]], "matminer.data_retrieval.retrieve_MP.MPDataRetrieval": [[9, 2, 1, "", "__init__"], [9, 2, 1, "", "api_link"], [9, 2, 1, "", "citations"], [9, 2, 1, "", "get_data"], [9, 2, 1, "", "get_dataframe"], [9, 2, 1, "", "try_get_prop_by_material_id"]], "matminer.data_retrieval.retrieve_MPDS": [[9, 4, 1, "", "APIError"], [9, 1, 1, "", "MPDSDataRetrieval"]], "matminer.data_retrieval.retrieve_MPDS.APIError": [[9, 2, 1, "", "__init__"]], "matminer.data_retrieval.retrieve_MPDS.MPDSDataRetrieval": [[9, 2, 1, "", "__init__"], [9, 2, 1, "", "api_link"], [9, 5, 1, "", "chillouttime"], [9, 2, 1, "", "citations"], [9, 2, 1, "", "compile_crystal"], [9, 5, 1, "", "default_properties"], [9, 5, 1, "", "endpoint"], [9, 2, 1, "", "get_data"], [9, 2, 1, "", "get_dataframe"], [9, 5, 1, "", "maxnpages"], [9, 5, 1, "", "pagesize"]], "matminer.data_retrieval.retrieve_MongoDB": [[9, 1, 1, "", "MongoDataRetrieval"], [9, 3, 1, "", "clean_projection"], [9, 3, 1, "", "is_int"], [9, 3, 1, "", "remove_ints"]], "matminer.data_retrieval.retrieve_MongoDB.MongoDataRetrieval": [[9, 2, 1, "", "__init__"], [9, 2, 1, "", "api_link"], [9, 2, 1, "", "get_dataframe"]], "matminer.data_retrieval.retrieve_base": [[9, 1, 1, "", "BaseDataRetrieval"]], "matminer.data_retrieval.retrieve_base.BaseDataRetrieval": [[9, 2, 1, "", "api_link"], [9, 2, 1, "", "citations"], [9, 2, 1, "", "get_dataframe"]], "matminer.data_retrieval.tests": [[10, 0, 0, "-", "base"], [10, 0, 0, "-", "test_retrieve_AFLOW"], [10, 0, 0, "-", "test_retrieve_Citrine"], [10, 0, 0, "-", "test_retrieve_MDF"], [10, 0, 0, "-", "test_retrieve_MP"], [10, 0, 0, "-", "test_retrieve_MPDS"], [10, 0, 0, "-", "test_retrieve_MongoDB"]], "matminer.data_retrieval.tests.test_retrieve_AFLOW": [[10, 1, 1, "", "AFLOWDataRetrievalTest"]], "matminer.data_retrieval.tests.test_retrieve_AFLOW.AFLOWDataRetrievalTest": [[10, 2, 1, "", "setUp"], [10, 2, 1, "", "test_get_data"]], "matminer.data_retrieval.tests.test_retrieve_Citrine": [[10, 1, 1, "", "CitrineDataRetrievalTest"]], "matminer.data_retrieval.tests.test_retrieve_Citrine.CitrineDataRetrievalTest": [[10, 2, 1, "", "setUp"], [10, 2, 1, "", "test_get_data"], [10, 2, 1, "", "test_multiple_items_in_list"]], "matminer.data_retrieval.tests.test_retrieve_MDF": [[10, 1, 1, "", "MDFDataRetrievalTest"]], "matminer.data_retrieval.tests.test_retrieve_MDF.MDFDataRetrievalTest": [[10, 2, 1, "", "setUpClass"], [10, 2, 1, "", "test_get_dataframe"], [10, 2, 1, "", "test_get_dataframe_by_query"], [10, 2, 1, "", "test_make_dataframe"]], "matminer.data_retrieval.tests.test_retrieve_MP": [[10, 1, 1, "", "MPDataRetrievalTest"]], "matminer.data_retrieval.tests.test_retrieve_MP.MPDataRetrievalTest": [[10, 2, 1, "", "setUp"], [10, 2, 1, "", "test_get_data"]], "matminer.data_retrieval.tests.test_retrieve_MPDS": [[10, 1, 1, "", "MPDSDataRetrievalTest"]], "matminer.data_retrieval.tests.test_retrieve_MPDS.MPDSDataRetrievalTest": [[10, 2, 1, "", "setUp"], [10, 2, 1, "", "test_valid_answer"]], "matminer.data_retrieval.tests.test_retrieve_MongoDB": [[10, 1, 1, "", "MongoDataRetrievalTest"]], "matminer.data_retrieval.tests.test_retrieve_MongoDB.MongoDataRetrievalTest": [[10, 2, 1, "", "test_cleaned_projection"], [10, 2, 1, "", "test_get_dataframe"], [10, 2, 1, "", "test_remove_ints"]], "matminer.datasets": [[11, 0, 0, "-", "convenience_loaders"], [11, 0, 0, "-", "dataset_retrieval"], [12, 0, 0, "-", "tests"], [11, 0, 0, "-", "utils"]], "matminer.datasets.convenience_loaders": [[11, 3, 1, "", "load_boltztrap_mp"], [11, 3, 1, "", "load_brgoch_superhard_training"], [11, 3, 1, "", "load_castelli_perovskites"], [11, 3, 1, "", "load_citrine_thermal_conductivity"], [11, 3, 1, "", "load_dielectric_constant"], [11, 3, 1, "", "load_double_perovskites_gap"], [11, 3, 1, "", "load_double_perovskites_gap_lumo"], [11, 3, 1, "", "load_elastic_tensor"], [11, 3, 1, "", "load_expt_formation_enthalpy"], [11, 3, 1, "", "load_expt_gap"], [11, 3, 1, "", "load_flla"], [11, 3, 1, "", "load_glass_binary"], [11, 3, 1, "", "load_glass_ternary_hipt"], [11, 3, 1, "", "load_glass_ternary_landolt"], [11, 3, 1, "", "load_heusler_magnetic"], [11, 3, 1, "", "load_jarvis_dft_2d"], [11, 3, 1, "", "load_jarvis_dft_3d"], [11, 3, 1, "", "load_jarvis_ml_dft_training"], [11, 3, 1, "", "load_m2ax"], [11, 3, 1, "", "load_mp"], [11, 3, 1, "", "load_phonon_dielectric_mp"], [11, 3, 1, "", "load_piezoelectric_tensor"], [11, 3, 1, "", "load_steel_strength"], [11, 3, 1, "", "load_wolverton_oxides"]], "matminer.datasets.dataset_retrieval": [[11, 3, 1, "", "get_all_dataset_info"], [11, 3, 1, "", "get_available_datasets"], [11, 3, 1, "", "get_dataset_attribute"], [11, 3, 1, "", "get_dataset_citations"], [11, 3, 1, "", "get_dataset_column_description"], [11, 3, 1, "", "get_dataset_columns"], [11, 3, 1, "", "get_dataset_description"], [11, 3, 1, "", "get_dataset_num_entries"], [11, 3, 1, "", "get_dataset_reference"], [11, 3, 1, "", "load_dataset"]], "matminer.datasets.tests": [[12, 0, 0, "-", "base"], [12, 0, 0, "-", "test_convenience_loaders"], [12, 0, 0, "-", "test_dataset_retrieval"], [12, 0, 0, "-", "test_datasets"], [12, 0, 0, "-", "test_utils"]], "matminer.datasets.tests.base": [[12, 1, 1, "", "DatasetTest"]], "matminer.datasets.tests.base.DatasetTest": [[12, 2, 1, "", "setUp"]], "matminer.datasets.tests.test_dataset_retrieval": [[12, 1, 1, "", "DataRetrievalTest"]], "matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest": [[12, 2, 1, "", "test_get_all_dataset_info"], [12, 2, 1, "", "test_get_dataset_attribute"], [12, 2, 1, "", "test_get_dataset_citations"], [12, 2, 1, "", "test_get_dataset_column_descriptions"], [12, 2, 1, "", "test_get_dataset_columns"], [12, 2, 1, "", "test_get_dataset_description"], [12, 2, 1, "", "test_get_dataset_num_entries"], [12, 2, 1, "", "test_get_dataset_reference"], [12, 2, 1, "", "test_load_dataset"], [12, 2, 1, "", "test_print_available_datasets"]], "matminer.datasets.tests.test_datasets": [[12, 1, 1, "", "DataSetsTest"], [12, 1, 1, "", "MatbenchDatasetsTest"], [12, 1, 1, "", "MatminerDatasetsTest"]], "matminer.datasets.tests.test_datasets.DataSetsTest": [[12, 2, 1, "", "universal_dataset_check"]], "matminer.datasets.tests.test_datasets.MatbenchDatasetsTest": [[12, 2, 1, "", "test_matbench_v0_1"]], "matminer.datasets.tests.test_datasets.MatminerDatasetsTest": [[12, 2, 1, "", "test_boltztrap_mp"], [12, 2, 1, "", "test_brgoch_superhard_training"], [12, 2, 1, "", "test_castelli_perovskites"], [12, 2, 1, "", "test_citrine_thermal_conductivity"], [12, 2, 1, "", "test_dielectric_constant"], [12, 2, 1, "", "test_double_perovskites_gap"], [12, 2, 1, "", "test_double_perovskites_gap_lumo"], [12, 2, 1, "", "test_elastic_tensor_2015"], [12, 2, 1, "", "test_expt_formation_enthalpy"], [12, 2, 1, "", "test_expt_formation_enthalpy_kingsbury"], [12, 2, 1, "", "test_expt_gap"], [12, 2, 1, "", "test_expt_gap_kingsbury"], [12, 2, 1, "", "test_flla"], [12, 2, 1, "", "test_glass_binary"], [12, 2, 1, "", "test_glass_binary_v2"], [12, 2, 1, "", "test_glass_ternary_hipt"], [12, 2, 1, "", "test_glass_ternary_landolt"], [12, 2, 1, "", "test_heusler_magnetic"], [12, 2, 1, "", "test_jarvis_dft_2d"], [12, 2, 1, "", "test_jarvis_dft_3d"], [12, 2, 1, "", "test_jarvis_ml_dft_training"], [12, 2, 1, "", "test_m2ax"], [12, 2, 1, "", "test_mp_all_20181018"], [12, 2, 1, "", "test_mp_nostruct_20181018"], [12, 2, 1, "", "test_phonon_dielectric_mp"], [12, 2, 1, "", "test_piezoelectric_tensor"], [12, 2, 1, "", "test_ricci_boltztrap_mp_tabular"], [12, 2, 1, "", "test_steel_strength"], [12, 2, 1, "", "test_superconductivity2018"], [12, 2, 1, "", "test_tholander_nitrides_e_form"], [12, 2, 1, "", "test_ucsb_thermoelectrics"], [12, 2, 1, "", "test_wolverton_oxides"]], "matminer.datasets.tests.test_utils": [[12, 1, 1, "", "UtilsTest"]], "matminer.datasets.tests.test_utils.UtilsTest": [[12, 2, 1, "", "test_fetch_external_dataset"], [12, 2, 1, "", "test_get_data_home"], [12, 2, 1, "", "test_get_file_sha256_hash"], [12, 2, 1, "", "test_load_dataset_dict"], [12, 2, 1, "", "test_read_dataframe_from_file"], [12, 2, 1, "", "test_validate_dataset"]], "matminer.featurizers": [[13, 0, 0, "-", "bandstructure"], [13, 0, 0, "-", "base"], [14, 0, 0, "-", "composition"], [13, 0, 0, "-", "conversions"], [13, 0, 0, "-", "dos"], [13, 0, 0, "-", "function"], [16, 0, 0, "-", "site"], [18, 0, 0, "-", "structure"], [20, 0, 0, "-", "tests"], [21, 0, 0, "-", "utils"]], "matminer.featurizers.bandstructure": [[13, 1, 1, "", "BandFeaturizer"], [13, 1, 1, "", "BranchPointEnergy"]], "matminer.featurizers.bandstructure.BandFeaturizer": [[13, 2, 1, "", "__init__"], [13, 2, 1, "", "citations"], [13, 2, 1, "", "feature_labels"], [13, 2, 1, "", "featurize"], [13, 2, 1, "", "get_bindex_bspin"], [13, 2, 1, "", "implementors"]], "matminer.featurizers.bandstructure.BranchPointEnergy": [[13, 2, 1, "", "__init__"], [13, 2, 1, "", "citations"], [13, 2, 1, "", "feature_labels"], [13, 2, 1, "", "featurize"], [13, 2, 1, "", "implementors"]], "matminer.featurizers.base": [[13, 1, 1, "", "BaseFeaturizer"], [13, 1, 1, "", "MultipleFeaturizer"], [13, 1, 1, "", "StackedFeaturizer"]], "matminer.featurizers.base.BaseFeaturizer": [[13, 6, 1, "", "chunksize"], [13, 2, 1, "", "citations"], [13, 2, 1, "", "feature_labels"], [13, 2, 1, "", "featurize"], [13, 2, 1, "", "featurize_dataframe"], [13, 2, 1, "", "featurize_many"], [13, 2, 1, "", "featurize_wrapper"], [13, 2, 1, "", "fit"], [13, 2, 1, "", "fit_featurize_dataframe"], [13, 2, 1, "", "implementors"], [13, 6, 1, "", "n_jobs"], [13, 2, 1, "", "precheck"], [13, 2, 1, "", "precheck_dataframe"], [13, 2, 1, "", "set_chunksize"], [13, 2, 1, "", "set_n_jobs"], [13, 2, 1, "", "transform"]], "matminer.featurizers.base.MultipleFeaturizer": [[13, 2, 1, "", "__init__"], [13, 2, 1, "", "citations"], [13, 2, 1, "", "feature_labels"], [13, 2, 1, "", "featurize"], [13, 2, 1, "", "featurize_many"], [13, 2, 1, "", "featurize_wrapper"], [13, 2, 1, "", "fit"], [13, 2, 1, "", "implementors"], [13, 2, 1, "", "set_n_jobs"]], "matminer.featurizers.base.StackedFeaturizer": [[13, 2, 1, "", "__init__"], [13, 2, 1, "", "citations"], [13, 2, 1, "", "feature_labels"], [13, 2, 1, "", "featurize"], [13, 2, 1, "", "implementors"]], "matminer.featurizers.composition": [[14, 0, 0, "-", "alloy"], [14, 0, 0, "-", "composite"], [14, 0, 0, "-", "element"], [14, 0, 0, "-", "ion"], [14, 0, 0, "-", "orbital"], [14, 0, 0, "-", "packing"], [15, 0, 0, "-", "tests"], [14, 0, 0, "-", "thermo"]], "matminer.featurizers.composition.alloy": [[14, 1, 1, "", "Miedema"], [14, 1, 1, "", "WenAlloys"], [14, 1, 1, "", "YangSolidSolution"]], "matminer.featurizers.composition.alloy.Miedema": [[14, 2, 1, "", "__init__"], [14, 2, 1, "", "citations"], [14, 2, 1, "", "deltaH_chem"], [14, 2, 1, "", "deltaH_elast"], [14, 2, 1, "", "deltaH_struct"], [14, 2, 1, "", "deltaH_topo"], [14, 2, 1, "", "feature_labels"], [14, 2, 1, "", "featurize"], [14, 2, 1, "", "implementors"], [14, 2, 1, "", "precheck"]], "matminer.featurizers.composition.alloy.WenAlloys": [[14, 2, 1, "", "__init__"], [14, 2, 1, "", "citations"], [14, 2, 1, "", "compute_atomic_fraction"], [14, 2, 1, "", "compute_configuration_entropy"], [14, 2, 1, "", "compute_delta"], [14, 2, 1, "", "compute_enthalpy"], [14, 2, 1, "", "compute_gamma_radii"], [14, 2, 1, "", "compute_lambda"], [14, 2, 1, "", "compute_local_mismatch"], [14, 2, 1, "", "compute_magpie_summary"], [14, 2, 1, "", "compute_strength_local_mismatch_shear"], [14, 2, 1, "", "compute_weight_fraction"], [14, 2, 1, "", "feature_labels"], [14, 2, 1, "", "featurize"], [14, 2, 1, "", "implementors"], [14, 2, 1, "", "precheck"]], "matminer.featurizers.composition.alloy.YangSolidSolution": [[14, 2, 1, "", "__init__"], [14, 2, 1, "", "citations"], [14, 2, 1, "", "compute_delta"], [14, 2, 1, "", "compute_omega"], [14, 2, 1, "", "feature_labels"], [14, 2, 1, "", "featurize"], [14, 2, 1, "", "implementors"], [14, 2, 1, "", "precheck"]], "matminer.featurizers.composition.composite": [[14, 1, 1, "", "ElementProperty"], [14, 1, 1, "", "Meredig"]], "matminer.featurizers.composition.composite.ElementProperty": [[14, 2, 1, "", "__init__"], [14, 2, 1, "", "citations"], [14, 2, 1, "", "feature_labels"], [14, 2, 1, "", "featurize"], [14, 2, 1, "", "from_preset"], [14, 2, 1, "", "implementors"]], "matminer.featurizers.composition.composite.Meredig": [[14, 2, 1, "", "__init__"], [14, 2, 1, "", "citations"], [14, 2, 1, "", "feature_labels"], [14, 2, 1, "", "featurize"], [14, 2, 1, "", "implementors"]], "matminer.featurizers.composition.element": [[14, 1, 1, "", "BandCenter"], [14, 1, 1, "", "ElementFraction"], [14, 1, 1, "", "Stoichiometry"], [14, 1, 1, "", "TMetalFraction"]], "matminer.featurizers.composition.element.BandCenter": [[14, 2, 1, "", "citations"], [14, 5, 1, "", "deml_data"], [14, 2, 1, "", "feature_labels"], [14, 2, 1, "", "featurize"], [14, 2, 1, "", "implementors"], [14, 5, 1, "", "magpie_data"]], "matminer.featurizers.composition.element.ElementFraction": [[14, 2, 1, "", "__init__"], [14, 2, 1, "", "citations"], [14, 2, 1, "", "feature_labels"], [14, 2, 1, "", "featurize"], [14, 2, 1, "", "implementors"]], "matminer.featurizers.composition.element.Stoichiometry": [[14, 2, 1, "", "__init__"], [14, 2, 1, "", "citations"], [14, 2, 1, "", "feature_labels"], [14, 2, 1, "", "featurize"], [14, 2, 1, "", "implementors"]], "matminer.featurizers.composition.element.TMetalFraction": [[14, 2, 1, "", "__init__"], [14, 2, 1, "", "citations"], [14, 2, 1, "", "feature_labels"], [14, 2, 1, "", "featurize"], [14, 2, 1, "", "implementors"]], "matminer.featurizers.composition.ion": [[14, 1, 1, "", "CationProperty"], [14, 1, 1, "", "ElectronAffinity"], [14, 1, 1, "", "ElectronegativityDiff"], [14, 1, 1, "", "IonProperty"], [14, 1, 1, "", "OxidationStates"], [14, 3, 1, "", "is_ionic"]], "matminer.featurizers.composition.ion.CationProperty": [[14, 2, 1, "", "citations"], [14, 2, 1, "", "feature_labels"], [14, 2, 1, "", "featurize"], [14, 2, 1, "", "from_preset"]], "matminer.featurizers.composition.ion.ElectronAffinity": [[14, 2, 1, "", "__init__"], [14, 2, 1, "", "citations"], [14, 2, 1, "", "feature_labels"], [14, 2, 1, "", "featurize"], [14, 2, 1, "", "implementors"]], "matminer.featurizers.composition.ion.ElectronegativityDiff": [[14, 2, 1, "", "__init__"], [14, 2, 1, "", "citations"], [14, 2, 1, "", "feature_labels"], [14, 2, 1, "", "featurize"], [14, 2, 1, "", "implementors"]], "matminer.featurizers.composition.ion.IonProperty": [[14, 2, 1, "", "__init__"], [14, 2, 1, "", "citations"], [14, 2, 1, "", "feature_labels"], [14, 2, 1, "", "featurize"], [14, 2, 1, "", "implementors"]], "matminer.featurizers.composition.ion.OxidationStates": [[14, 2, 1, "", "__init__"], [14, 2, 1, "", "citations"], [14, 2, 1, "", "feature_labels"], [14, 2, 1, "", "featurize"], [14, 2, 1, "", "from_preset"], [14, 2, 1, "", "implementors"]], "matminer.featurizers.composition.orbital": [[14, 1, 1, "", "AtomicOrbitals"], [14, 1, 1, "", "ValenceOrbital"]], "matminer.featurizers.composition.orbital.AtomicOrbitals": [[14, 2, 1, "", "citations"], [14, 2, 1, "", "feature_labels"], [14, 2, 1, "", "featurize"], [14, 2, 1, "", "implementors"]], "matminer.featurizers.composition.orbital.ValenceOrbital": [[14, 2, 1, "", "__init__"], [14, 2, 1, "", "citations"], [14, 2, 1, "", "feature_labels"], [14, 2, 1, "", "featurize"], [14, 2, 1, "", "implementors"]], "matminer.featurizers.composition.packing": [[14, 1, 1, "", "AtomicPackingEfficiency"]], "matminer.featurizers.composition.packing.AtomicPackingEfficiency": [[14, 2, 1, "", "__init__"], [14, 2, 1, "", "citations"], [14, 2, 1, "", "compute_nearest_cluster_distance"], [14, 2, 1, "", "compute_simultaneous_packing_efficiency"], [14, 2, 1, "", "create_cluster_lookup_tool"], [14, 2, 1, "", "feature_labels"], [14, 2, 1, "", "featurize"], [14, 2, 1, "", "find_ideal_cluster_size"], [14, 2, 1, "", "get_ideal_radius_ratio"], [14, 2, 1, "", "implementors"]], "matminer.featurizers.composition.tests": [[15, 0, 0, "-", "base"], [15, 0, 0, "-", "test_alloy"], [15, 0, 0, "-", "test_composite"], [15, 0, 0, "-", "test_element"], [15, 0, 0, "-", "test_ion"], [15, 0, 0, "-", "test_orbital"], [15, 0, 0, "-", "test_packing"], [15, 0, 0, "-", "test_thermo"]], "matminer.featurizers.composition.tests.base": [[15, 1, 1, "", "CompositionFeaturesTest"]], "matminer.featurizers.composition.tests.base.CompositionFeaturesTest": [[15, 2, 1, "", "setUp"]], "matminer.featurizers.composition.tests.test_alloy": [[15, 1, 1, "", "AlloyFeaturizersTest"]], "matminer.featurizers.composition.tests.test_alloy.AlloyFeaturizersTest": [[15, 2, 1, "", "test_WenAlloys"], [15, 2, 1, "", "test_miedema_all"], [15, 2, 1, "", "test_miedema_ss"], [15, 2, 1, "", "test_yang"]], "matminer.featurizers.composition.tests.test_composite": [[15, 1, 1, "", "CompositeFeaturesTest"]], "matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest": [[15, 2, 1, "", "test_elem"], [15, 2, 1, "", "test_elem_deml"], [15, 2, 1, "", "test_elem_matminer"], [15, 2, 1, "", "test_elem_matscholar_el"], [15, 2, 1, "", "test_elem_megnet_el"], [15, 2, 1, "", "test_fere_corr"], [15, 2, 1, "", "test_meredig"]], "matminer.featurizers.composition.tests.test_element": [[15, 1, 1, "", "ElementFeaturesTest"]], "matminer.featurizers.composition.tests.test_element.ElementFeaturesTest": [[15, 2, 1, "", "test_band_center"], [15, 2, 1, "", "test_fraction"], [15, 2, 1, "", "test_stoich"], [15, 2, 1, "", "test_tm_fraction"]], "matminer.featurizers.composition.tests.test_ion": [[15, 1, 1, "", "IonFeaturesTest"]], "matminer.featurizers.composition.tests.test_ion.IonFeaturesTest": [[15, 2, 1, "", "test_cation_properties"], [15, 2, 1, "", "test_elec_affin"], [15, 2, 1, "", "test_en_diff"], [15, 2, 1, "", "test_ionic"], [15, 2, 1, "", "test_is_ionic"], [15, 2, 1, "", "test_oxidation_states"]], "matminer.featurizers.composition.tests.test_orbital": [[15, 1, 1, "", "OrbitalFeaturesTest"]], "matminer.featurizers.composition.tests.test_orbital.OrbitalFeaturesTest": [[15, 2, 1, "", "test_atomic_orbitals"], [15, 2, 1, "", "test_valence"]], "matminer.featurizers.composition.tests.test_packing": [[15, 1, 1, "", "PackingFeaturesTest"]], "matminer.featurizers.composition.tests.test_packing.PackingFeaturesTest": [[15, 2, 1, "", "test_ape"]], "matminer.featurizers.composition.tests.test_thermo": [[15, 1, 1, "", "ThermoFeaturesTest"]], "matminer.featurizers.composition.tests.test_thermo.ThermoFeaturesTest": [[15, 2, 1, "", "test_cohesive_energy"], [15, 2, 1, "", "test_cohesive_energy_mp"]], "matminer.featurizers.composition.thermo": [[14, 1, 1, "", "CohesiveEnergy"], [14, 1, 1, "", "CohesiveEnergyMP"]], "matminer.featurizers.composition.thermo.CohesiveEnergy": [[14, 2, 1, "", "__init__"], [14, 2, 1, "", "citations"], [14, 2, 1, "", "feature_labels"], [14, 2, 1, "", "featurize"], [14, 2, 1, "", "implementors"]], "matminer.featurizers.composition.thermo.CohesiveEnergyMP": [[14, 2, 1, "", "__init__"], [14, 2, 1, "", "citations"], [14, 2, 1, "", "feature_labels"], [14, 2, 1, "", "featurize"], [14, 2, 1, "", "implementors"]], "matminer.featurizers.conversions": [[13, 1, 1, "", "ASEAtomstoStructure"], [13, 1, 1, "", "CompositionToOxidComposition"], [13, 1, 1, "", "CompositionToStructureFromMP"], [13, 1, 1, "", "ConversionFeaturizer"], [13, 1, 1, "", "DictToObject"], [13, 1, 1, "", "JsonToObject"], [13, 1, 1, "", "PymatgenFunctionApplicator"], [13, 1, 1, "", "StrToComposition"], [13, 1, 1, "", "StructureToComposition"], [13, 1, 1, "", "StructureToIStructure"], [13, 1, 1, "", "StructureToOxidStructure"]], "matminer.featurizers.conversions.ASEAtomstoStructure": [[13, 2, 1, "", "__init__"], [13, 2, 1, "", "featurize"], [13, 2, 1, "", "implementors"]], "matminer.featurizers.conversions.CompositionToOxidComposition": [[13, 2, 1, "", "__init__"], [13, 2, 1, "", "citations"], [13, 2, 1, "", "featurize"], [13, 2, 1, "", "implementors"]], "matminer.featurizers.conversions.CompositionToStructureFromMP": [[13, 2, 1, "", "__init__"], [13, 2, 1, "", "citations"], [13, 2, 1, "", "featurize"], [13, 2, 1, "", "implementors"]], "matminer.featurizers.conversions.ConversionFeaturizer": [[13, 2, 1, "", "__init__"], [13, 2, 1, "", "citations"], [13, 2, 1, "", "feature_labels"], [13, 2, 1, "", "featurize"], [13, 2, 1, "", "featurize_dataframe"], [13, 2, 1, "", "implementors"]], "matminer.featurizers.conversions.DictToObject": [[13, 2, 1, "", "__init__"], [13, 2, 1, "", "citations"], [13, 2, 1, "", "featurize"], [13, 2, 1, "", "implementors"]], "matminer.featurizers.conversions.JsonToObject": [[13, 2, 1, "", "__init__"], [13, 2, 1, "", "citations"], [13, 2, 1, "", "featurize"], [13, 2, 1, "", "implementors"]], "matminer.featurizers.conversions.PymatgenFunctionApplicator": [[13, 2, 1, "", "__init__"], [13, 2, 1, "", "featurize"], [13, 2, 1, "", "implementors"]], "matminer.featurizers.conversions.StrToComposition": [[13, 2, 1, "", "__init__"], [13, 2, 1, "", "citations"], [13, 2, 1, "", "featurize"], [13, 2, 1, "", "implementors"]], "matminer.featurizers.conversions.StructureToComposition": [[13, 2, 1, "", "__init__"], [13, 2, 1, "", "citations"], [13, 2, 1, "", "featurize"], [13, 2, 1, "", "implementors"]], "matminer.featurizers.conversions.StructureToIStructure": [[13, 2, 1, "", "__init__"], [13, 2, 1, "", "citations"], [13, 2, 1, "", "featurize"], [13, 2, 1, "", "implementors"]], "matminer.featurizers.conversions.StructureToOxidStructure": [[13, 2, 1, "", "__init__"], [13, 2, 1, "", "citations"], [13, 2, 1, "", "featurize"], [13, 2, 1, "", "implementors"]], "matminer.featurizers.dos": [[13, 1, 1, "", "DOSFeaturizer"], [13, 1, 1, "", "DopingFermi"], [13, 1, 1, "", "DosAsymmetry"], [13, 1, 1, "", "Hybridization"], [13, 1, 1, "", "SiteDOS"], [13, 3, 1, "", "get_cbm_vbm_scores"], [13, 3, 1, "", "get_site_dos_scores"]], "matminer.featurizers.dos.DOSFeaturizer": [[13, 2, 1, "", "__init__"], [13, 2, 1, "", "citations"], [13, 2, 1, "", "feature_labels"], [13, 2, 1, "", "featurize"], [13, 2, 1, "", "implementors"]], "matminer.featurizers.dos.DopingFermi": [[13, 2, 1, "", "__init__"], [13, 2, 1, "", "citations"], [13, 2, 1, "", "feature_labels"], [13, 2, 1, "", "featurize"], [13, 2, 1, "", "implementors"]], "matminer.featurizers.dos.DosAsymmetry": [[13, 2, 1, "", "__init__"], [13, 2, 1, "", "citations"], [13, 2, 1, "", "feature_labels"], [13, 2, 1, "", "featurize"], [13, 2, 1, "", "implementors"]], "matminer.featurizers.dos.Hybridization": [[13, 2, 1, "", "__init__"], [13, 2, 1, "", "citations"], [13, 2, 1, "", "feature_labels"], [13, 2, 1, "", "featurize"], [13, 2, 1, "", "implementors"]], "matminer.featurizers.dos.SiteDOS": [[13, 2, 1, "", "__init__"], [13, 2, 1, "", "citations"], [13, 2, 1, "", "feature_labels"], [13, 2, 1, "", "featurize"], [13, 2, 1, "", "implementors"]], "matminer.featurizers.function": [[13, 1, 1, "", "FunctionFeaturizer"], [13, 3, 1, "", "generate_expressions_combinations"]], "matminer.featurizers.function.FunctionFeaturizer": [[13, 5, 1, "", "ILLEGAL_CHARACTERS"], [13, 2, 1, "", "__init__"], [13, 2, 1, "", "citations"], [13, 6, 1, "", "exp_dict"], [13, 2, 1, "", "feature_labels"], [13, 2, 1, "", "featurize"], [13, 2, 1, "", "fit"], [13, 2, 1, "", "generate_string_expressions"], [13, 2, 1, "", "implementors"]], "matminer.featurizers.site": [[16, 0, 0, "-", "bonding"], [16, 0, 0, "-", "chemical"], [16, 0, 0, "-", "external"], [16, 0, 0, "-", "fingerprint"], [16, 0, 0, "-", "misc"], [16, 0, 0, "-", "rdf"], [17, 0, 0, "-", "tests"]], "matminer.featurizers.site.bonding": [[16, 1, 1, "", "AverageBondAngle"], [16, 1, 1, "", "AverageBondLength"], [16, 1, 1, "", "BondOrientationalParameter"], [16, 3, 1, "", "get_wigner_coeffs"]], "matminer.featurizers.site.bonding.AverageBondAngle": [[16, 2, 1, "", "__init__"], [16, 2, 1, "", "citations"], [16, 2, 1, "", "feature_labels"], [16, 2, 1, "", "featurize"], [16, 2, 1, "", "implementors"]], "matminer.featurizers.site.bonding.AverageBondLength": [[16, 2, 1, "", "__init__"], [16, 2, 1, "", "citations"], [16, 2, 1, "", "feature_labels"], [16, 2, 1, "", "featurize"], [16, 2, 1, "", "implementors"]], "matminer.featurizers.site.bonding.BondOrientationalParameter": [[16, 2, 1, "", "__init__"], [16, 2, 1, "", "citations"], [16, 2, 1, "", "feature_labels"], [16, 2, 1, "", "featurize"], [16, 2, 1, "", "implementors"]], "matminer.featurizers.site.chemical": [[16, 1, 1, "", "ChemicalSRO"], [16, 1, 1, "", "EwaldSiteEnergy"], [16, 1, 1, "", "LocalPropertyDifference"], [16, 1, 1, "", "SiteElementalProperty"]], "matminer.featurizers.site.chemical.ChemicalSRO": [[16, 2, 1, "", "__init__"], [16, 2, 1, "", "citations"], [16, 2, 1, "", "feature_labels"], [16, 2, 1, "", "featurize"], [16, 2, 1, "", "fit"], [16, 2, 1, "", "from_preset"], [16, 2, 1, "", "implementors"]], "matminer.featurizers.site.chemical.EwaldSiteEnergy": [[16, 2, 1, "", "__init__"], [16, 2, 1, "", "citations"], [16, 2, 1, "", "feature_labels"], [16, 2, 1, "", "featurize"], [16, 2, 1, "", "implementors"]], "matminer.featurizers.site.chemical.LocalPropertyDifference": [[16, 2, 1, "", "__init__"], [16, 2, 1, "", "citations"], [16, 2, 1, "", "feature_labels"], [16, 2, 1, "", "featurize"], [16, 2, 1, "", "from_preset"], [16, 2, 1, "", "implementors"]], "matminer.featurizers.site.chemical.SiteElementalProperty": [[16, 2, 1, "", "__init__"], [16, 2, 1, "", "citations"], [16, 2, 1, "", "feature_labels"], [16, 2, 1, "", "featurize"], [16, 2, 1, "", "from_preset"], [16, 2, 1, "", "implementors"]], "matminer.featurizers.site.external": [[16, 1, 1, "", "SOAP"]], "matminer.featurizers.site.external.SOAP": [[16, 2, 1, "", "__init__"], [16, 2, 1, "", "citations"], [16, 2, 1, "", "feature_labels"], [16, 2, 1, "", "featurize"], [16, 2, 1, "", "fit"], [16, 2, 1, "", "from_preset"], [16, 2, 1, "", "implementors"]], "matminer.featurizers.site.fingerprint": [[16, 1, 1, "", "AGNIFingerprints"], [16, 1, 1, "", "ChemEnvSiteFingerprint"], [16, 1, 1, "", "CrystalNNFingerprint"], [16, 1, 1, "", "OPSiteFingerprint"], [16, 1, 1, "", "VoronoiFingerprint"], [16, 3, 1, "", "load_cn_motif_op_params"], [16, 3, 1, "", "load_cn_target_motif_op"]], "matminer.featurizers.site.fingerprint.AGNIFingerprints": [[16, 2, 1, "", "__init__"], [16, 2, 1, "", "citations"], [16, 2, 1, "", "feature_labels"], [16, 2, 1, "", "featurize"], [16, 2, 1, "", "implementors"]], "matminer.featurizers.site.fingerprint.ChemEnvSiteFingerprint": [[16, 2, 1, "", "__init__"], [16, 2, 1, "", "citations"], [16, 2, 1, "", "feature_labels"], [16, 2, 1, "", "featurize"], [16, 2, 1, "", "from_preset"], [16, 2, 1, "", "implementors"]], "matminer.featurizers.site.fingerprint.CrystalNNFingerprint": [[16, 2, 1, "", "__init__"], [16, 2, 1, "", "citations"], [16, 2, 1, "", "feature_labels"], [16, 2, 1, "", "featurize"], [16, 2, 1, "", "from_preset"], [16, 2, 1, "", "implementors"]], "matminer.featurizers.site.fingerprint.OPSiteFingerprint": [[16, 2, 1, "", "__init__"], [16, 2, 1, "", "citations"], [16, 2, 1, "", "feature_labels"], [16, 2, 1, "", "featurize"], [16, 2, 1, "", "implementors"]], "matminer.featurizers.site.fingerprint.VoronoiFingerprint": [[16, 2, 1, "", "__init__"], [16, 2, 1, "", "citations"], [16, 2, 1, "", "feature_labels"], [16, 2, 1, "", "featurize"], [16, 2, 1, "", "implementors"]], "matminer.featurizers.site.misc": [[16, 1, 1, "", "CoordinationNumber"], [16, 1, 1, "", "IntersticeDistribution"]], "matminer.featurizers.site.misc.CoordinationNumber": [[16, 2, 1, "", "__init__"], [16, 2, 1, "", "citations"], [16, 2, 1, "", "feature_labels"], [16, 2, 1, "", "featurize"], [16, 2, 1, "", "from_preset"], [16, 2, 1, "", "implementors"]], "matminer.featurizers.site.misc.IntersticeDistribution": [[16, 2, 1, "", "__init__"], [16, 2, 1, "", "analyze_area_interstice"], [16, 2, 1, "", "analyze_dist_interstices"], [16, 2, 1, "", "analyze_vol_interstice"], [16, 2, 1, "", "citations"], [16, 2, 1, "", "feature_labels"], [16, 2, 1, "", "featurize"], [16, 2, 1, "", "implementors"]], "matminer.featurizers.site.rdf": [[16, 1, 1, "", "AngularFourierSeries"], [16, 1, 1, "", "GaussianSymmFunc"], [16, 1, 1, "", "GeneralizedRadialDistributionFunction"]], "matminer.featurizers.site.rdf.AngularFourierSeries": [[16, 2, 1, "", "__init__"], [16, 2, 1, "", "citations"], [16, 2, 1, "", "feature_labels"], [16, 2, 1, "", "featurize"], [16, 2, 1, "", "from_preset"], [16, 2, 1, "", "implementors"]], "matminer.featurizers.site.rdf.GaussianSymmFunc": [[16, 2, 1, "", "__init__"], [16, 2, 1, "", "citations"], [16, 2, 1, "", "cosine_cutoff"], [16, 2, 1, "", "feature_labels"], [16, 2, 1, "", "featurize"], [16, 2, 1, "", "g2"], [16, 2, 1, "", "g4"], [16, 2, 1, "", "implementors"]], "matminer.featurizers.site.rdf.GeneralizedRadialDistributionFunction": [[16, 2, 1, "", "__init__"], [16, 2, 1, "", "citations"], [16, 2, 1, "", "feature_labels"], [16, 2, 1, "", "featurize"], [16, 2, 1, "", "fit"], [16, 2, 1, "", "from_preset"], [16, 2, 1, "", "implementors"]], "matminer.featurizers.site.tests": [[17, 0, 0, "-", "base"], [17, 0, 0, "-", "test_bonding"], [17, 0, 0, "-", "test_chemical"], [17, 0, 0, "-", "test_external"], [17, 0, 0, "-", "test_fingerprint"], [17, 0, 0, "-", "test_misc"], [17, 0, 0, "-", "test_rdf"]], "matminer.featurizers.site.tests.base": [[17, 1, 1, "", "SiteFeaturizerTest"]], "matminer.featurizers.site.tests.base.SiteFeaturizerTest": [[17, 2, 1, "", "setUp"], [17, 2, 1, "", "tearDown"]], "matminer.featurizers.site.tests.test_bonding": [[17, 1, 1, "", "BondingTest"]], "matminer.featurizers.site.tests.test_bonding.BondingTest": [[17, 2, 1, "", "test_AverageBondAngle"], [17, 2, 1, "", "test_AverageBondLength"], [17, 2, 1, "", "test_bop"]], "matminer.featurizers.site.tests.test_chemical": [[17, 1, 1, "", "ChemicalSiteTests"]], "matminer.featurizers.site.tests.test_chemical.ChemicalSiteTests": [[17, 2, 1, "", "test_chemicalSRO"], [17, 2, 1, "", "test_ewald_site"], [17, 2, 1, "", "test_local_prop_diff"], [17, 2, 1, "", "test_site_elem_prop"]], "matminer.featurizers.site.tests.test_external": [[17, 1, 1, "", "ExternalSiteTests"]], "matminer.featurizers.site.tests.test_external.ExternalSiteTests": [[17, 2, 1, "", "test_SOAP"]], "matminer.featurizers.site.tests.test_fingerprint": [[17, 1, 1, "", "FingerprintTests"]], "matminer.featurizers.site.tests.test_fingerprint.FingerprintTests": [[17, 2, 1, "", "test_chemenv_site_fingerprint"], [17, 2, 1, "", "test_crystal_nn_fingerprint"], [17, 2, 1, "", "test_dataframe"], [17, 2, 1, "", "test_off_center_cscl"], [17, 2, 1, "", "test_op_site_fingerprint"], [17, 2, 1, "", "test_simple_cubic"], [17, 2, 1, "", "test_voronoifingerprint"]], "matminer.featurizers.site.tests.test_misc": [[17, 1, 1, "", "MiscSiteTests"]], "matminer.featurizers.site.tests.test_misc.MiscSiteTests": [[17, 2, 1, "", "test_cns"], [17, 2, 1, "", "test_interstice_distribution_of_crystal"], [17, 2, 1, "", "test_interstice_distribution_of_glass"]], "matminer.featurizers.site.tests.test_rdf": [[17, 1, 1, "", "RDFTests"]], "matminer.featurizers.site.tests.test_rdf.RDFTests": [[17, 2, 1, "", "test_afs"], [17, 2, 1, "", "test_gaussiansymmfunc"], [17, 2, 1, "", "test_grdf"]], "matminer.featurizers.structure": [[18, 0, 0, "-", "bonding"], [18, 0, 0, "-", "composite"], [18, 0, 0, "-", "matrix"], [18, 0, 0, "-", "misc"], [18, 0, 0, "-", "order"], [18, 0, 0, "-", "rdf"], [18, 0, 0, "-", "sites"], [18, 0, 0, "-", "symmetry"], [19, 0, 0, "-", "tests"]], "matminer.featurizers.structure.bonding": [[18, 1, 1, "", "BagofBonds"], [18, 1, 1, "", "BondFractions"], [18, 1, 1, "", "GlobalInstabilityIndex"], [18, 1, 1, "", "MinimumRelativeDistances"], [18, 1, 1, "", "StructuralHeterogeneity"]], "matminer.featurizers.structure.bonding.BagofBonds": [[18, 2, 1, "", "__init__"], [18, 2, 1, "", "bag"], [18, 2, 1, "", "citations"], [18, 2, 1, "", "feature_labels"], [18, 2, 1, "", "featurize"], [18, 2, 1, "", "fit"], [18, 2, 1, "", "implementors"]], "matminer.featurizers.structure.bonding.BondFractions": [[18, 2, 1, "", "__init__"], [18, 2, 1, "", "citations"], [18, 2, 1, "", "enumerate_all_bonds"], [18, 2, 1, "", "enumerate_bonds"], [18, 2, 1, "", "feature_labels"], [18, 2, 1, "", "featurize"], [18, 2, 1, "", "fit"], [18, 2, 1, "", "from_preset"], [18, 2, 1, "", "implementors"]], "matminer.featurizers.structure.bonding.GlobalInstabilityIndex": [[18, 2, 1, "", "__init__"], [18, 2, 1, "", "calc_bv_sum"], [18, 2, 1, "", "calc_gii_iucr"], [18, 2, 1, "", "calc_gii_pymatgen"], [18, 2, 1, "", "citations"], [18, 2, 1, "", "compute_bv"], [18, 2, 1, "", "feature_labels"], [18, 2, 1, "", "featurize"], [18, 2, 1, "", "get_bv_params"], [18, 2, 1, "", "get_equiv_sites"], [18, 2, 1, "", "implementors"], [18, 2, 1, "", "precheck"]], "matminer.featurizers.structure.bonding.MinimumRelativeDistances": [[18, 2, 1, "", "__init__"], [18, 2, 1, "", "citations"], [18, 2, 1, "", "feature_labels"], [18, 2, 1, "", "featurize"], [18, 2, 1, "", "fit"], [18, 2, 1, "", "implementors"]], "matminer.featurizers.structure.bonding.StructuralHeterogeneity": [[18, 2, 1, "", "__init__"], [18, 2, 1, "", "citations"], [18, 2, 1, "", "feature_labels"], [18, 2, 1, "", "featurize"], [18, 2, 1, "", "implementors"]], "matminer.featurizers.structure.composite": [[18, 1, 1, "", "JarvisCFID"]], "matminer.featurizers.structure.composite.JarvisCFID": [[18, 2, 1, "", "__init__"], [18, 2, 1, "", "citations"], [18, 2, 1, "", "feature_labels"], [18, 2, 1, "", "featurize"], [18, 2, 1, "", "get_chem"], [18, 2, 1, "", "get_chg"], [18, 2, 1, "", "get_distributions"], [18, 2, 1, "", "implementors"]], "matminer.featurizers.structure.matrix": [[18, 1, 1, "", "CoulombMatrix"], [18, 1, 1, "", "OrbitalFieldMatrix"], [18, 1, 1, "", "SineCoulombMatrix"]], "matminer.featurizers.structure.matrix.CoulombMatrix": [[18, 2, 1, "", "__init__"], [18, 2, 1, "", "citations"], [18, 2, 1, "", "feature_labels"], [18, 2, 1, "", "featurize"], [18, 2, 1, "", "fit"], [18, 2, 1, "", "implementors"]], "matminer.featurizers.structure.matrix.OrbitalFieldMatrix": [[18, 2, 1, "", "__init__"], [18, 2, 1, "", "citations"], [18, 2, 1, "", "feature_labels"], [18, 2, 1, "", "featurize"], [18, 2, 1, "", "get_atom_ofms"], [18, 2, 1, "", "get_mean_ofm"], [18, 2, 1, "", "get_ohv"], [18, 2, 1, "", "get_single_ofm"], [18, 2, 1, "", "get_structure_ofm"], [18, 2, 1, "", "implementors"]], "matminer.featurizers.structure.matrix.SineCoulombMatrix": [[18, 2, 1, "", "__init__"], [18, 2, 1, "", "citations"], [18, 2, 1, "", "feature_labels"], [18, 2, 1, "", "featurize"], [18, 2, 1, "", "fit"], [18, 2, 1, "", "implementors"]], "matminer.featurizers.structure.misc": [[18, 1, 1, "", "EwaldEnergy"], [18, 1, 1, "", "StructureComposition"], [18, 1, 1, "", "XRDPowderPattern"]], "matminer.featurizers.structure.misc.EwaldEnergy": [[18, 2, 1, "", "__init__"], [18, 2, 1, "", "citations"], [18, 2, 1, "", "feature_labels"], [18, 2, 1, "", "featurize"], [18, 2, 1, "", "implementors"]], "matminer.featurizers.structure.misc.StructureComposition": [[18, 2, 1, "", "__init__"], [18, 2, 1, "", "citations"], [18, 2, 1, "", "feature_labels"], [18, 2, 1, "", "featurize"], [18, 2, 1, "", "fit"], [18, 2, 1, "", "implementors"]], "matminer.featurizers.structure.misc.XRDPowderPattern": [[18, 2, 1, "", "__init__"], [18, 2, 1, "", "citations"], [18, 2, 1, "", "feature_labels"], [18, 2, 1, "", "featurize"], [18, 2, 1, "", "implementors"]], "matminer.featurizers.structure.order": [[18, 1, 1, "", "ChemicalOrdering"], [18, 1, 1, "", "DensityFeatures"], [18, 1, 1, "", "MaximumPackingEfficiency"], [18, 1, 1, "", "StructuralComplexity"]], "matminer.featurizers.structure.order.ChemicalOrdering": [[18, 2, 1, "", "__init__"], [18, 2, 1, "", "citations"], [18, 2, 1, "", "feature_labels"], [18, 2, 1, "", "featurize"], [18, 2, 1, "", "implementors"]], "matminer.featurizers.structure.order.DensityFeatures": [[18, 2, 1, "", "__init__"], [18, 2, 1, "", "citations"], [18, 2, 1, "", "feature_labels"], [18, 2, 1, "", "featurize"], [18, 2, 1, "", "implementors"], [18, 2, 1, "", "precheck"]], "matminer.featurizers.structure.order.MaximumPackingEfficiency": [[18, 2, 1, "", "citations"], [18, 2, 1, "", "feature_labels"], [18, 2, 1, "", "featurize"], [18, 2, 1, "", "implementors"]], "matminer.featurizers.structure.order.StructuralComplexity": [[18, 2, 1, "", "__init__"], [18, 2, 1, "", "citations"], [18, 2, 1, "", "feature_labels"], [18, 2, 1, "", "featurize"], [18, 2, 1, "", "implementors"]], "matminer.featurizers.structure.rdf": [[18, 1, 1, "", "ElectronicRadialDistributionFunction"], [18, 1, 1, "", "PartialRadialDistributionFunction"], [18, 1, 1, "", "RadialDistributionFunction"], [18, 3, 1, "", "get_rdf_bin_labels"]], "matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction": [[18, 2, 1, "", "__init__"], [18, 2, 1, "", "citations"], [18, 2, 1, "", "feature_labels"], [18, 2, 1, "", "featurize"], [18, 2, 1, "", "implementors"], [18, 2, 1, "", "precheck"]], "matminer.featurizers.structure.rdf.PartialRadialDistributionFunction": [[18, 2, 1, "", "__init__"], [18, 2, 1, "", "citations"], [18, 2, 1, "", "compute_prdf"], [18, 2, 1, "", "feature_labels"], [18, 2, 1, "", "featurize"], [18, 2, 1, "", "fit"], [18, 2, 1, "", "implementors"], [18, 2, 1, "", "precheck"]], "matminer.featurizers.structure.rdf.RadialDistributionFunction": [[18, 2, 1, "", "__init__"], [18, 2, 1, "", "citations"], [18, 2, 1, "", "feature_labels"], [18, 2, 1, "", "featurize"], [18, 2, 1, "", "implementors"], [18, 2, 1, "", "precheck"]], "matminer.featurizers.structure.sites": [[18, 1, 1, "", "PartialsSiteStatsFingerprint"], [18, 1, 1, "", "SiteStatsFingerprint"]], "matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint": [[18, 2, 1, "", "__init__"], [18, 2, 1, "", "compute_pssf"], [18, 2, 1, "", "feature_labels"], [18, 2, 1, "", "featurize"], [18, 2, 1, "", "fit"], [18, 2, 1, "", "implementors"]], "matminer.featurizers.structure.sites.SiteStatsFingerprint": [[18, 2, 1, "", "__init__"], [18, 2, 1, "", "citations"], [18, 2, 1, "", "feature_labels"], [18, 2, 1, "", "featurize"], [18, 2, 1, "", "fit"], [18, 2, 1, "", "from_preset"], [18, 2, 1, "", "implementors"]], "matminer.featurizers.structure.symmetry": [[18, 1, 1, "", "Dimensionality"], [18, 1, 1, "", "GlobalSymmetryFeatures"]], "matminer.featurizers.structure.symmetry.Dimensionality": [[18, 2, 1, "", "__init__"], [18, 2, 1, "", "citations"], [18, 2, 1, "", "feature_labels"], [18, 2, 1, "", "featurize"], [18, 2, 1, "", "implementors"]], "matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures": [[18, 2, 1, "", "__init__"], [18, 5, 1, "", "all_features"], [18, 2, 1, "", "citations"], [18, 5, 1, "", "crystal_idx"], [18, 2, 1, "", "feature_labels"], [18, 2, 1, "", "featurize"], [18, 2, 1, "", "implementors"]], "matminer.featurizers.structure.tests": [[19, 0, 0, "-", "base"], [19, 0, 0, "-", "test_bonding"], [19, 0, 0, "-", "test_composite"], [19, 0, 0, "-", "test_matrix"], [19, 0, 0, "-", "test_misc"], [19, 0, 0, "-", "test_order"], [19, 0, 0, "-", "test_rdf"], [19, 0, 0, "-", "test_sites"], [19, 0, 0, "-", "test_symmetry"]], "matminer.featurizers.structure.tests.base": [[19, 1, 1, "", "StructureFeaturesTest"]], "matminer.featurizers.structure.tests.base.StructureFeaturesTest": [[19, 2, 1, "", "setUp"]], "matminer.featurizers.structure.tests.test_bonding": [[19, 1, 1, "", "BondingStructureTest"]], "matminer.featurizers.structure.tests.test_bonding.BondingStructureTest": [[19, 2, 1, "", "test_GlobalInstabilityIndex"], [19, 2, 1, "", "test_bob"], [19, 2, 1, "", "test_bondfractions"], [19, 2, 1, "", "test_min_relative_distances"], [19, 2, 1, "", "test_ward_prb_2017_strhet"]], "matminer.featurizers.structure.tests.test_composite": [[19, 1, 1, "", "CompositeStructureFeaturesTest"]], "matminer.featurizers.structure.tests.test_composite.CompositeStructureFeaturesTest": [[19, 2, 1, "", "test_jarvisCFID"]], "matminer.featurizers.structure.tests.test_matrix": [[19, 1, 1, "", "MatrixStructureFeaturesTest"]], "matminer.featurizers.structure.tests.test_matrix.MatrixStructureFeaturesTest": [[19, 2, 1, "", "test_coulomb_matrix"], [19, 2, 1, "", "test_orbital_field_matrix"], [19, 2, 1, "", "test_sine_coulomb_matrix"]], "matminer.featurizers.structure.tests.test_misc": [[19, 1, 1, "", "MiscStructureFeaturesTest"]], "matminer.featurizers.structure.tests.test_misc.MiscStructureFeaturesTest": [[19, 2, 1, "", "test_composition_features"], [19, 2, 1, "", "test_ewald"], [19, 2, 1, "", "test_xrd_powderPattern"]], "matminer.featurizers.structure.tests.test_order": [[19, 1, 1, "", "OrderStructureFeaturesTest"]], "matminer.featurizers.structure.tests.test_order.OrderStructureFeaturesTest": [[19, 2, 1, "", "test_density_features"], [19, 2, 1, "", "test_ordering_param"], [19, 2, 1, "", "test_packing_efficiency"], [19, 2, 1, "", "test_structural_complexity"]], "matminer.featurizers.structure.tests.test_rdf": [[19, 1, 1, "", "StructureRDFTest"]], "matminer.featurizers.structure.tests.test_rdf.StructureRDFTest": [[19, 2, 1, "", "test_get_rdf_bin_labels"], [19, 2, 1, "", "test_prdf"], [19, 2, 1, "", "test_rdf_and_peaks"], [19, 2, 1, "", "test_redf"]], "matminer.featurizers.structure.tests.test_sites": [[19, 1, 1, "", "PartialStructureSitesFeaturesTest"], [19, 1, 1, "", "StructureSitesFeaturesTest"]], "matminer.featurizers.structure.tests.test_sites.PartialStructureSitesFeaturesTest": [[19, 2, 1, "", "test_partialsitestatsfingerprint"], [19, 2, 1, "", "test_ward_prb_2017_efftcn"], [19, 2, 1, "", "test_ward_prb_2017_lpd"]], "matminer.featurizers.structure.tests.test_sites.StructureSitesFeaturesTest": [[19, 2, 1, "", "test_sitestatsfingerprint"], [19, 2, 1, "", "test_ward_prb_2017_efftcn"], [19, 2, 1, "", "test_ward_prb_2017_lpd"]], "matminer.featurizers.structure.tests.test_symmetry": [[19, 1, 1, "", "StructureSymmetryFeaturesTest"]], "matminer.featurizers.structure.tests.test_symmetry.StructureSymmetryFeaturesTest": [[19, 2, 1, "", "test_dimensionality"], [19, 2, 1, "", "test_global_symmetry"]], "matminer.featurizers.tests": [[20, 0, 0, "-", "test_bandstructure"], [20, 0, 0, "-", "test_base"], [20, 0, 0, "-", "test_conversions"], [20, 0, 0, "-", "test_dos"], [20, 0, 0, "-", "test_function"]], "matminer.featurizers.tests.test_bandstructure": [[20, 1, 1, "", "BandstructureFeaturesTest"]], "matminer.featurizers.tests.test_bandstructure.BandstructureFeaturesTest": [[20, 2, 1, "", "setUp"], [20, 2, 1, "", "test_BandFeaturizer"], [20, 2, 1, "", "test_BranchPointEnergy"]], "matminer.featurizers.tests.test_base": [[20, 1, 1, "", "FittableFeaturizer"], [20, 1, 1, "", "MatrixFeaturizer"], [20, 1, 1, "", "MultiArgs2"], [20, 1, 1, "", "MultiTypeFeaturizer"], [20, 1, 1, "", "MultipleFeatureFeaturizer"], [20, 1, 1, "", "SingleFeaturizer"], [20, 1, 1, "", "SingleFeaturizerMultiArgs"], [20, 1, 1, "", "SingleFeaturizerMultiArgsWithPrecheck"], [20, 1, 1, "", "SingleFeaturizerWithPrecheck"], [20, 1, 1, "", "TestBaseClass"]], "matminer.featurizers.tests.test_base.FittableFeaturizer": [[20, 2, 1, "", "citations"], [20, 2, 1, "", "feature_labels"], [20, 2, 1, "", "featurize"], [20, 2, 1, "", "fit"], [20, 2, 1, "", "implementors"]], "matminer.featurizers.tests.test_base.MatrixFeaturizer": [[20, 2, 1, "", "citations"], [20, 2, 1, "", "feature_labels"], [20, 2, 1, "", "featurize"], [20, 2, 1, "", "implementors"]], "matminer.featurizers.tests.test_base.MultiArgs2": [[20, 2, 1, "", "__init__"], [20, 2, 1, "", "citations"], [20, 2, 1, "", "feature_labels"], [20, 2, 1, "", "featurize"], [20, 2, 1, "", "implementors"]], "matminer.featurizers.tests.test_base.MultiTypeFeaturizer": [[20, 2, 1, "", "citations"], [20, 2, 1, "", "feature_labels"], [20, 2, 1, "", "featurize"], [20, 2, 1, "", "implementors"]], "matminer.featurizers.tests.test_base.MultipleFeatureFeaturizer": [[20, 2, 1, "", "citations"], [20, 2, 1, "", "feature_labels"], [20, 2, 1, "", "featurize"], [20, 2, 1, "", "implementors"]], "matminer.featurizers.tests.test_base.SingleFeaturizer": [[20, 2, 1, "", "citations"], [20, 2, 1, "", "feature_labels"], [20, 2, 1, "", "featurize"], [20, 2, 1, "", "implementors"]], "matminer.featurizers.tests.test_base.SingleFeaturizerMultiArgs": [[20, 2, 1, "", "featurize"]], "matminer.featurizers.tests.test_base.SingleFeaturizerMultiArgsWithPrecheck": [[20, 2, 1, "", "precheck"]], "matminer.featurizers.tests.test_base.SingleFeaturizerWithPrecheck": [[20, 2, 1, "", "precheck"]], "matminer.featurizers.tests.test_base.TestBaseClass": [[20, 2, 1, "", "make_test_data"], [20, 2, 1, "", "setUp"], [20, 2, 1, "", "test_caching"], [20, 2, 1, "", "test_dataframe"], [20, 2, 1, "", "test_featurize_many"], [20, 2, 1, "", "test_fittable"], [20, 2, 1, "", "test_ignore_errors"], [20, 2, 1, "", "test_indices"], [20, 2, 1, "", "test_inplace"], [20, 2, 1, "", "test_matrix"], [20, 2, 1, "", "test_multifeature_no_zero_index"], [20, 2, 1, "", "test_multifeatures_multiargs"], [20, 2, 1, "", "test_multiindex_in_multifeaturizer"], [20, 2, 1, "", "test_multiindex_inplace"], [20, 2, 1, "", "test_multiindex_return"], [20, 2, 1, "", "test_multiple"], [20, 2, 1, "", "test_multiprocessing_df"], [20, 2, 1, "", "test_multitype_multifeat"], [20, 2, 1, "", "test_precheck"], [20, 2, 1, "", "test_stacked_featurizer"]], "matminer.featurizers.tests.test_conversions": [[20, 1, 1, "", "TestConversions"]], "matminer.featurizers.tests.test_conversions.TestConversions": [[20, 2, 1, "", "test_ase_conversion"], [20, 2, 1, "", "test_composition_to_oxidcomposition"], [20, 2, 1, "", "test_composition_to_structurefromMP"], [20, 2, 1, "", "test_conversion_multiindex"], [20, 2, 1, "", "test_conversion_multiindex_dynamic"], [20, 2, 1, "", "test_conversion_overwrite"], [20, 2, 1, "", "test_dict_to_object"], [20, 2, 1, "", "test_json_to_object"], [20, 2, 1, "", "test_pymatgen_general_converter"], [20, 2, 1, "", "test_str_to_composition"], [20, 2, 1, "", "test_structure_to_composition"], [20, 2, 1, "", "test_structure_to_oxidstructure"], [20, 2, 1, "", "test_to_istructure"]], "matminer.featurizers.tests.test_dos": [[20, 1, 1, "", "DOSFeaturesTest"]], "matminer.featurizers.tests.test_dos.DOSFeaturesTest": [[20, 2, 1, "", "setUp"], [20, 2, 1, "", "test_DOSFeaturizer"], [20, 2, 1, "", "test_DopingFermi"], [20, 2, 1, "", "test_DosAsymmetry"], [20, 2, 1, "", "test_Hybridization"], [20, 2, 1, "", "test_SiteDOS"]], "matminer.featurizers.tests.test_function": [[20, 1, 1, "", "TestFunctionFeaturizer"]], "matminer.featurizers.tests.test_function.TestFunctionFeaturizer": [[20, 2, 1, "", "setUp"], [20, 2, 1, "", "test_featurize"], [20, 2, 1, "", "test_featurize_labels"], [20, 2, 1, "", "test_helper_functions"], [20, 2, 1, "", "test_multi_featurizer"]], "matminer.featurizers.utils": [[21, 0, 0, "-", "grdf"], [21, 0, 0, "-", "oxidation"], [21, 0, 0, "-", "stats"], [22, 0, 0, "-", "tests"]], "matminer.featurizers.utils.grdf": [[21, 1, 1, "", "AbstractPairwise"], [21, 1, 1, "", "Bessel"], [21, 1, 1, "", "Cosine"], [21, 1, 1, "", "Gaussian"], [21, 1, 1, "", "Histogram"], [21, 1, 1, "", "Sine"], [21, 3, 1, "", "initialize_pairwise_function"]], "matminer.featurizers.utils.grdf.AbstractPairwise": [[21, 2, 1, "", "name"], [21, 2, 1, "", "volume"]], "matminer.featurizers.utils.grdf.Bessel": [[21, 2, 1, "", "__init__"]], "matminer.featurizers.utils.grdf.Cosine": [[21, 2, 1, "", "__init__"], [21, 2, 1, "", "volume"]], "matminer.featurizers.utils.grdf.Gaussian": [[21, 2, 1, "", "__init__"], [21, 2, 1, "", "volume"]], "matminer.featurizers.utils.grdf.Histogram": [[21, 2, 1, "", "__init__"], [21, 2, 1, "", "volume"]], "matminer.featurizers.utils.grdf.Sine": [[21, 2, 1, "", "__init__"], [21, 2, 1, "", "volume"]], "matminer.featurizers.utils.oxidation": [[21, 3, 1, "", "has_oxidation_states"]], "matminer.featurizers.utils.stats": [[21, 1, 1, "", "PropertyStats"]], "matminer.featurizers.utils.stats.PropertyStats": [[21, 2, 1, "", "avg_dev"], [21, 2, 1, "", "calc_stat"], [21, 2, 1, "", "eigenvalues"], [21, 2, 1, "", "flatten"], [21, 2, 1, "", "geom_std_dev"], [21, 2, 1, "", "holder_mean"], [21, 2, 1, "", "inverse_mean"], [21, 2, 1, "", "kurtosis"], [21, 2, 1, "", "maximum"], [21, 2, 1, "", "mean"], [21, 2, 1, "", "minimum"], [21, 2, 1, "", "mode"], [21, 2, 1, "", "quantile"], [21, 2, 1, "", "range"], [21, 2, 1, "", "skewness"], [21, 2, 1, "", "sorted"], [21, 2, 1, "", "std_dev"]], "matminer.featurizers.utils.tests": [[22, 0, 0, "-", "test_grdf"], [22, 0, 0, "-", "test_oxidation"], [22, 0, 0, "-", "test_stats"]], "matminer.featurizers.utils.tests.test_grdf": [[22, 1, 1, "", "GRDFTests"]], "matminer.featurizers.utils.tests.test_grdf.GRDFTests": [[22, 2, 1, "", "test_bessel"], [22, 2, 1, "", "test_cosine"], [22, 2, 1, "", "test_gaussian"], [22, 2, 1, "", "test_histogram"], [22, 2, 1, "", "test_load_class"], [22, 2, 1, "", "test_sin"]], "matminer.featurizers.utils.tests.test_oxidation": [[22, 1, 1, "", "OxidationTest"]], "matminer.featurizers.utils.tests.test_oxidation.OxidationTest": [[22, 2, 1, "", "test_has_oxidation_states"]], "matminer.featurizers.utils.tests.test_stats": [[22, 1, 1, "", "TestPropertyStats"]], "matminer.featurizers.utils.tests.test_stats.TestPropertyStats": [[22, 2, 1, "", "setUp"], [22, 2, 1, "", "test_avg_dev"], [22, 2, 1, "", "test_geom_std_dev"], [22, 2, 1, "", "test_holder_mean"], [22, 2, 1, "", "test_kurtosis"], [22, 2, 1, "", "test_maximum"], [22, 2, 1, "", "test_mean"], [22, 2, 1, "", "test_minimum"], [22, 2, 1, "", "test_mode"], [22, 2, 1, "", "test_quantile"], [22, 2, 1, "", "test_range"], [22, 2, 1, "", "test_skewness"], [22, 2, 1, "", "test_std_dev"]], "matminer.utils": [[25, 0, 0, "-", "caching"], [25, 0, 0, "-", "data"], [26, 0, 0, "-", "data_files"], [25, 0, 0, "-", "flatten_dict"], [25, 0, 0, "-", "io"], [25, 0, 0, "-", "kernels"], [25, 0, 0, "-", "pipeline"], [27, 0, 0, "-", "tests"], [25, 0, 0, "-", "utils"]], "matminer.utils.caching": [[25, 3, 1, "", "get_all_nearest_neighbors"], [25, 3, 1, "", "get_nearest_neighbors"]], "matminer.utils.data": [[25, 1, 1, "", "AbstractData"], [25, 1, 1, "", "CohesiveEnergyData"], [25, 1, 1, "", "DemlData"], [25, 1, 1, "", "IUCrBondValenceData"], [25, 1, 1, "", "MEGNetElementData"], [25, 1, 1, "", "MagpieData"], [25, 1, 1, "", "MatscholarElementData"], [25, 1, 1, "", "MixingEnthalpy"], [25, 1, 1, "", "OxidationStateDependentData"], [25, 1, 1, "", "OxidationStatesMixin"], [25, 1, 1, "", "PymatgenData"]], "matminer.utils.data.AbstractData": [[25, 2, 1, "", "get_elemental_properties"], [25, 2, 1, "", "get_elemental_property"]], "matminer.utils.data.CohesiveEnergyData": [[25, 2, 1, "", "__init__"], [25, 2, 1, "", "get_elemental_property"]], "matminer.utils.data.DemlData": [[25, 2, 1, "", "__init__"], [25, 2, 1, "", "get_charge_dependent_property"], [25, 2, 1, "", "get_elemental_property"], [25, 2, 1, "", "get_oxidation_states"]], "matminer.utils.data.IUCrBondValenceData": [[25, 2, 1, "", "__init__"], [25, 2, 1, "", "get_bv_params"], [25, 2, 1, "", "interpolate_soft_anions"]], "matminer.utils.data.MEGNetElementData": [[25, 2, 1, "", "__init__"], [25, 2, 1, "", "get_elemental_property"]], "matminer.utils.data.MagpieData": [[25, 2, 1, "", "__init__"], [25, 2, 1, "", "get_elemental_property"], [25, 2, 1, "", "get_oxidation_states"]], "matminer.utils.data.MatscholarElementData": [[25, 2, 1, "", "__init__"], [25, 2, 1, "", "get_elemental_property"]], "matminer.utils.data.MixingEnthalpy": [[25, 2, 1, "", "__init__"], [25, 2, 1, "", "get_mixing_enthalpy"]], "matminer.utils.data.OxidationStateDependentData": [[25, 2, 1, "", "get_charge_dependent_property"], [25, 2, 1, "", "get_charge_dependent_property_from_specie"]], "matminer.utils.data.OxidationStatesMixin": [[25, 2, 1, "", "get_oxidation_states"]], "matminer.utils.data.PymatgenData": [[25, 2, 1, "", "__init__"], [25, 2, 1, "", "get_charge_dependent_property"], [25, 2, 1, "", "get_elemental_property"], [25, 2, 1, "", "get_oxidation_states"]], "matminer.utils.data_files": [[26, 0, 0, "-", "deml_elementdata"]], "matminer.utils.flatten_dict": [[25, 3, 1, "", "flatten_dict"]], "matminer.utils.io": [[25, 3, 1, "", "load_dataframe_from_json"], [25, 3, 1, "", "store_dataframe_as_json"]], "matminer.utils.kernels": [[25, 3, 1, "", "gaussian_kernel"], [25, 3, 1, "", "laplacian_kernel"]], "matminer.utils.pipeline": [[25, 1, 1, "", "DropExcluded"], [25, 1, 1, "", "ItemSelector"]], "matminer.utils.pipeline.DropExcluded": [[25, 2, 1, "", "__init__"], [25, 2, 1, "", "fit"], [25, 2, 1, "", "transform"]], "matminer.utils.pipeline.ItemSelector": [[25, 2, 1, "", "__init__"], [25, 2, 1, "", "fit"], [25, 2, 1, "", "transform"]], "matminer.utils.tests": [[27, 0, 0, "-", "test_caching"], [27, 0, 0, "-", "test_data"], [27, 0, 0, "-", "test_flatten_dict"], [27, 0, 0, "-", "test_io"]], "matminer.utils.tests.test_caching": [[27, 1, 1, "", "TestCaching"]], "matminer.utils.tests.test_caching.TestCaching": [[27, 2, 1, "", "test_cache"]], "matminer.utils.tests.test_data": [[27, 1, 1, "", "TestDemlData"], [27, 1, 1, "", "TestIUCrBondValenceData"], [27, 1, 1, "", "TestMEGNetData"], [27, 1, 1, "", "TestMagpieData"], [27, 1, 1, "", "TestMatScholarData"], [27, 1, 1, "", "TestMixingEnthalpy"], [27, 1, 1, "", "TestPymatgenData"]], "matminer.utils.tests.test_data.TestDemlData": [[27, 2, 1, "", "setUp"], [27, 2, 1, "", "test_get_oxidation"], [27, 2, 1, "", "test_get_property"]], "matminer.utils.tests.test_data.TestIUCrBondValenceData": [[27, 2, 1, "", "setUp"], [27, 2, 1, "", "test_get_data"]], "matminer.utils.tests.test_data.TestMEGNetData": [[27, 2, 1, "", "setUp"], [27, 2, 1, "", "test_get_property"]], "matminer.utils.tests.test_data.TestMagpieData": [[27, 2, 1, "", "setUp"], [27, 2, 1, "", "test_get_oxidation"], [27, 2, 1, "", "test_get_property"]], "matminer.utils.tests.test_data.TestMatScholarData": [[27, 2, 1, "", "setUp"], [27, 2, 1, "", "test_get_property"]], "matminer.utils.tests.test_data.TestMixingEnthalpy": [[27, 2, 1, "", "setUp"], [27, 2, 1, "", "test_get_data"]], "matminer.utils.tests.test_data.TestPymatgenData": [[27, 2, 1, "", "setUp"], [27, 2, 1, "", "test_get_oxidation"], [27, 2, 1, "", "test_get_property"]], "matminer.utils.tests.test_flatten_dict": [[27, 1, 1, "", "FlattenDictTest"]], "matminer.utils.tests.test_flatten_dict.FlattenDictTest": [[27, 2, 1, "", "test_flatten_nested_dict"]], "matminer.utils.tests.test_io": [[27, 1, 1, "", "IOTest"], [27, 3, 1, "", "generate_json_files"]], "matminer.utils.tests.test_io.IOTest": [[27, 2, 1, "", "setUp"], [27, 2, 1, "", "tearDown"], [27, 2, 1, "", "test_load_dataframe_from_json"], [27, 2, 1, "", "test_store_dataframe_as_json"]], "matminer.utils.utils": [[25, 3, 1, "", "homogenize_multiindex"]]}, "objtypes": {"0": "py:module", "1": "py:class", "2": "py:method", "3": "py:function", "4": "py:exception", "5": "py:attribute", "6": "py:property"}, "objnames": {"0": ["py", "module", "Python module"], "1": ["py", "class", "Python class"], "2": ["py", "method", "Python method"], "3": ["py", "function", "Python function"], "4": ["py", "exception", "Python exception"], "5": ["py", "attribute", "Python attribute"], "6": ["py", "property", "Python property"]}, "titleterms": {"matmin": [0, 1, 2, 4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28], "changelog": [0, 6], "contributor": 1, "lbnl": 1, "hack": 1, "materi": [1, 4, 5, 6], "univers": 1, "chicago": 1, "persson": 1, "group": 1, "other": [1, 5], "guid": 2, "ad": 2, "dataset": [2, 3, 6, 11, 12], "1": [2, 4], "fork": 2, "repositori": 2, "github": 2, "2": [2, 4], "prepar": 2, "long": 2, "term": 2, "host": 2, "To": 2, "updat": [2, 7], "script": 2, "preprocess": 2, "your": 2, "3": [2, 4], "upload": 2, "4": [2, 4], "metadata": 2, "file": 2, "5": [2, 4], "test": [2, 10, 12, 15, 17, 19, 20, 22, 24, 27], "load": 2, "code": 2, "6": [2, 4], "make": 2, "pull": 2, "request": 2, "tabl": 3, "info": 3, "boltztrap_mp": 3, "brgoch_superhard_train": 3, "castelli_perovskit": 3, "citrine_thermal_conduct": 3, "dielectric_const": 3, "double_perovskites_gap": 3, "double_perovskites_gap_lumo": 3, "elastic_tensor_2015": 3, "expt_formation_enthalpi": 3, "expt_formation_enthalpy_kingsburi": 3, "expt_gap": 3, "expt_gap_kingsburi": 3, "flla": 3, "glass_binari": 3, "glass_binary_v2": 3, "glass_ternary_hipt": 3, "glass_ternary_landolt": 3, "heusler_magnet": 3, "jarvis_dft_2d": 3, "jarvis_dft_3d": 3, "jarvis_ml_dft_train": 3, "m2ax": 3, "matbench_dielectr": 3, "matbench_expt_gap": 3, "matbench_expt_is_met": 3, "matbench_glass": 3, "matbench_jdft2d": 3, "matbench_log_gvrh": 3, "matbench_log_kvrh": 3, "matbench_mp_e_form": 3, "matbench_mp_gap": 3, "matbench_mp_is_met": 3, "matbench_perovskit": 3, "matbench_phonon": 3, "matbench_steel": 3, "mp_all_20181018": 3, "mp_nostruct_20181018": 3, "phonon_dielectric_mp": 3, "piezoelectric_tensor": 3, "ricci_boltztrap_mp_tabular": 3, "steel_strength": 3, "superconductivity2018": 3, "tholander_nitrid": 3, "ucsb_thermoelectr": 3, "wolverton_oxid": 3, "predict": 4, "bulk": 4, "moduli": 4, "fit": 4, "data": [4, 6, 25], "mine": 4, "model": 4, "6000": 4, "calcul": [4, 5], "from": [4, 5], "project": 4, "preambl": 4, "step": 4, "us": 4, "obtain": 4, "mp": 4, "automat": 4, "panda": 4, "datafram": [4, 6], "add": 4, "descriptor": [4, 6], "featur": [4, 5, 6, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22], "linear": 4, "regress": 4, "get": 4, "r2": 4, "rmse": 4, "plot": [4, 23], "result": 4, "figrecip": [4, 23, 24], "follow": 4, "similar": 4, "random": 4, "forest": 4, "our": 4, "determin": 4, "what": 4, "ar": 4, "most": 4, "import": 4, "bandstructur": [5, 13], "deriv": 5, "": 5, "electron": 5, "base": [5, 10, 12, 13, 15, 17, 19], "parent": 5, "class": 5, "meta": 5, "composit": [5, 14, 15, 18], "alloi": [5, 14], "element": [5, 14], "ion": [5, 14], "orbit": [5, 14], "pack": [5, 14], "thermo": [5, 14], "convers": [5, 6, 13], "util": [5, 11, 21, 22, 25, 26, 27], "do": [5, 13], "densiti": 5, "state": 5, "function": [5, 13], "expand": 5, "set": 5, "site": [5, 16, 17, 18], "individu": 5, "crystal": 5, "structur": [5, 18, 19], "bond": [5, 16, 18], "chemic": [5, 16], "extern": [5, 16], "fingerprint": [5, 16], "misc": [5, 16, 18], "rdf": [5, 16, 18], "gener": [5, 6], "matrix": [5, 18], "order": [5, 18], "symmetri": [5, 18], "relat": 6, "softwar": 6, "quick": 6, "link": 6, "instal": [6, 7], "overview": 6, "retriev": 6, "easili": 6, "put": 6, "complex": 6, "onlin": 6, "access": 6, "readi": 6, "made": 6, "one": 6, "line": 6, "mung": 6, "exampl": 6, "citat": 6, "cite": 6, "contribut": 6, "support": 6, "via": 7, "pip": 7, "develop": 7, "mode": 7, "tip": 7, "packag": [8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], "subpackag": [8, 9, 11, 13, 14, 16, 18, 21, 23, 25], "modul": [8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], "content": [8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], "data_retriev": [9, 10], "submodul": [9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], "retrieve_aflow": 9, "retrieve_citrin": 9, "retrieve_mdf": 9, "retrieve_mp": 9, "retrieve_mpd": 9, "retrieve_mongodb": 9, "retrieve_bas": 9, "test_retrieve_aflow": 10, "test_retrieve_citrin": 10, "test_retrieve_mdf": 10, "test_retrieve_mp": 10, "test_retrieve_mpd": 10, "test_retrieve_mongodb": 10, "convenience_load": 11, "dataset_retriev": 11, "test_convenience_load": 12, "test_dataset_retriev": 12, "test_dataset": 12, "test_util": 12, "test_alloi": 15, "test_composit": [15, 19], "test_el": 15, "test_ion": 15, "test_orbit": 15, "test_pack": 15, "test_thermo": 15, "test_bond": [17, 19], "test_chem": 17, "test_extern": 17, "test_fingerprint": 17, "test_misc": [17, 19], "test_rdf": [17, 19], "test_matrix": 19, "test_ord": 19, "test_sit": 19, "test_symmetri": 19, "test_bandstructur": 20, "test_bas": 20, "test_convers": 20, "test_do": 20, "test_funct": 20, "grdf": 21, "oxid": 21, "stat": 21, "test_grdf": 22, "test_oxid": 22, "test_stat": 22, "test_plot": 24, "cach": 25, "flatten_dict": 25, "io": 25, "kernel": 25, "pipelin": 25, "data_fil": 26, "deml_elementdata": 26, "test_cach": 27, "test_data": 27, "test_flatten_dict": 27, "test_io": 27}, "envversion": {"sphinx.domains.c": 2, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 8, "sphinx.domains.index": 1, "sphinx.domains.javascript": 2, "sphinx.domains.math": 2, "sphinx.domains.python": 3, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx.ext.intersphinx": 1, "sphinx.ext.todo": 2, "sphinx": 57}, "alltitles": {"matminer Changelog": [[0, "matminer-changelog"]], "matminer Contributors": [[1, "matminer-contributors"]], "LBNL - Hacking Materials": [[1, "id1"]], "University of Chicago": [[1, "university-of-chicago"]], "LBNL - Persson Group": [[1, "id2"]], "Other": [[1, "other"]], "Guide to adding datasets to matminer": [[2, "guide-to-adding-datasets-to-matminer"]], "1. Fork the matminer repository on GitHub": [[2, "fork-the-matminer-repository-on-github"]], "2. Prepare the dataset for long term hosting": [[2, "prepare-the-dataset-for-long-term-hosting"]], "To update the script to preprocess your dataset:": [[2, "to-update-the-script-to-preprocess-your-dataset"]], "3. Upload the dataset to long term hosting": [[2, "upload-the-dataset-to-long-term-hosting"]], "4. Update the matminer dataset metadata file": [[2, "update-the-matminer-dataset-metadata-file"]], "5. Update the dataset tests and loading code": [[2, "update-the-dataset-tests-and-loading-code"]], "6. Make a Pull Request to the matminer GitHub repository": [[2, "make-a-pull-request-to-the-matminer-github-repository"]], "Table of Datasets": [[3, "table-of-datasets"]], "Dataset info": [[3, "dataset-info"]], "boltztrap_mp": [[3, "boltztrap-mp"]], "brgoch_superhard_training": [[3, "brgoch-superhard-training"]], "castelli_perovskites": [[3, "castelli-perovskites"]], "citrine_thermal_conductivity": [[3, "citrine-thermal-conductivity"]], "dielectric_constant": [[3, "dielectric-constant"]], "double_perovskites_gap": [[3, "double-perovskites-gap"]], "double_perovskites_gap_lumo": [[3, "double-perovskites-gap-lumo"]], "elastic_tensor_2015": [[3, "elastic-tensor-2015"]], "expt_formation_enthalpy": [[3, "expt-formation-enthalpy"]], "expt_formation_enthalpy_kingsbury": [[3, "expt-formation-enthalpy-kingsbury"]], "expt_gap": [[3, "expt-gap"]], "expt_gap_kingsbury": [[3, "expt-gap-kingsbury"]], "flla": [[3, "flla"]], "glass_binary": [[3, "glass-binary"]], "glass_binary_v2": [[3, "glass-binary-v2"]], "glass_ternary_hipt": [[3, "glass-ternary-hipt"]], "glass_ternary_landolt": [[3, "glass-ternary-landolt"]], "heusler_magnetic": [[3, "heusler-magnetic"]], "jarvis_dft_2d": [[3, "jarvis-dft-2d"]], "jarvis_dft_3d": [[3, "jarvis-dft-3d"]], "jarvis_ml_dft_training": [[3, "jarvis-ml-dft-training"]], "m2ax": [[3, "m2ax"]], "matbench_dielectric": [[3, "matbench-dielectric"]], "matbench_expt_gap": [[3, "matbench-expt-gap"]], "matbench_expt_is_metal": [[3, "matbench-expt-is-metal"]], "matbench_glass": [[3, "matbench-glass"]], "matbench_jdft2d": [[3, "matbench-jdft2d"]], "matbench_log_gvrh": [[3, "matbench-log-gvrh"]], "matbench_log_kvrh": [[3, "matbench-log-kvrh"]], "matbench_mp_e_form": [[3, "matbench-mp-e-form"]], "matbench_mp_gap": [[3, "matbench-mp-gap"]], "matbench_mp_is_metal": [[3, "matbench-mp-is-metal"]], "matbench_perovskites": [[3, "matbench-perovskites"]], "matbench_phonons": [[3, "matbench-phonons"]], "matbench_steels": [[3, "matbench-steels"]], "mp_all_20181018": [[3, "mp-all-20181018"]], "mp_nostruct_20181018": [[3, "mp-nostruct-20181018"]], "phonon_dielectric_mp": [[3, "phonon-dielectric-mp"]], "piezoelectric_tensor": [[3, "piezoelectric-tensor"]], "ricci_boltztrap_mp_tabular": [[3, "ricci-boltztrap-mp-tabular"]], "steel_strength": [[3, "steel-strength"]], "superconductivity2018": [[3, "superconductivity2018"]], "tholander_nitrides": [[3, "tholander-nitrides"]], "ucsb_thermoelectrics": [[3, "ucsb-thermoelectrics"]], "wolverton_oxides": [[3, "wolverton-oxides"]], "Predicting bulk moduli with matminer": [[4, "predicting-bulk-moduli-with-matminer"]], "Fit data mining models to ~6000 calculated bulk moduli from Materials Project": [[4, "fit-data-mining-models-to-6000-calculated-bulk-moduli-from-materials-project"]], "Preamble": [[4, "preamble"]], "Step 1: Use matminer to obtain data from MP (automatically) in a \u201cpandas\u201d dataframe": [[4, "step-1-use-matminer-to-obtain-data-from-mp-automatically-in-a-pandas-dataframe"]], "Step 2: Add descriptors/features": [[4, "step-2-add-descriptors-features"]], "Step 3: Fit a Linear Regression model, get R2 and RMSE": [[4, "step-3-fit-a-linear-regression-model-get-r2-and-rmse"]], "Step 4: Plot the results with FigRecipes": [[4, "step-4-plot-the-results-with-figrecipes"]], "Step 5: Follow similar steps for a Random Forest model": [[4, "step-5-follow-similar-steps-for-a-random-forest-model"]], "Step 6: Plot our results and determine what features are the most important": [[4, "step-6-plot-our-results-and-determine-what-features-are-the-most-important"]], "bandstructure": [[5, "bandstructure"]], "Features derived from a material\u2019s electronic bandstructure.": [[5, "features-derived-from-a-material-s-electronic-bandstructure"]], "base": [[5, "base"]], "Parent classes and meta-featurizers.": [[5, "parent-classes-and-meta-featurizers"]], "composition": [[5, "composition"]], "Features based on a material\u2019s composition.": [[5, "features-based-on-a-material-s-composition"]], "alloy": [[5, "alloy"]], "composite": [[5, "composite"], [5, "id2"]], "element": [[5, "element"]], "ion": [[5, "ion"]], "orbital": [[5, "orbital"]], "packing": [[5, "packing"]], "thermo": [[5, "thermo"]], "conversions": [[5, "conversions"]], "Conversion utilities.": [[5, "conversion-utilities"]], "dos": [[5, "dos"]], "Features based on a material\u2019s electronic density of states.": [[5, "features-based-on-a-material-s-electronic-density-of-states"]], "function": [[5, "function"]], "Classes for expanding sets of features calculated with other featurizers.": [[5, "classes-for-expanding-sets-of-features-calculated-with-other-featurizers"]], "site": [[5, "site"]], "Features from individual sites in a material\u2019s crystal structure.": [[5, "features-from-individual-sites-in-a-material-s-crystal-structure"]], "bonding": [[5, "bonding"], [5, "id1"]], "chemical": [[5, "chemical"]], "external": [[5, "external"]], "fingerprint": [[5, "fingerprint"]], "misc": [[5, "misc"], [5, "id3"]], "rdf": [[5, "rdf"], [5, "id4"]], "structure": [[5, "structure"]], "Generating features based on a material\u2019s crystal structure.": [[5, "generating-features-based-on-a-material-s-crystal-structure"]], "matrix": [[5, "matrix"]], "order": [[5, "order"]], "sites": [[5, "sites"]], "symmetry": [[5, "symmetry"]], "matminer": [[6, "matminer"], [28, "matminer"]], "Related software": [[6, "related-software"]], "Quick Links": [[6, "quick-links"]], "Installation": [[6, "installation"]], "Overview": [[6, "overview"]], "Featurizers generate descriptors for materials": [[6, "featurizers-generate-descriptors-for-materials"]], "Data retrieval easily puts complex online data into dataframes": [[6, "data-retrieval-easily-puts-complex-online-data-into-dataframes"]], "Access ready-made datasets in one line": [[6, "access-ready-made-datasets-in-one-line"]], "Data munging with Conversion Featurizers": [[6, "data-munging-with-conversion-featurizers"]], "Examples": [[6, "examples"]], "Citations and Changelog": [[6, "citations-and-changelog"]], "Citing matminer": [[6, "citing-matminer"]], "Changelog": [[6, "changelog"]], "Contributions and Support": [[6, "contributions-and-support"]], "Installing matminer": [[7, "installing-matminer"]], "Install and update via pip": [[7, "install-and-update-via-pip"]], "Install in development mode": [[7, "install-in-development-mode"]], "Tips": [[7, "tips"]], "matminer package": [[8, "matminer-package"]], "Subpackages": [[8, "subpackages"], [9, "subpackages"], [11, "subpackages"], [13, "subpackages"], [14, "subpackages"], [16, "subpackages"], [18, "subpackages"], [21, "subpackages"], [23, "subpackages"], [25, "subpackages"]], "Module contents": [[8, "module-matminer"], [9, "module-matminer.data_retrieval"], [10, "module-matminer.data_retrieval.tests"], [11, "module-matminer.datasets"], [12, "module-matminer.datasets.tests"], [13, "module-matminer.featurizers"], [14, "module-matminer.featurizers.composition"], [15, "module-matminer.featurizers.composition.tests"], [16, "module-matminer.featurizers.site"], [17, "module-matminer.featurizers.site.tests"], [18, "module-matminer.featurizers.structure"], [19, "module-matminer.featurizers.structure.tests"], [20, "module-matminer.featurizers.tests"], [21, "module-matminer.featurizers.utils"], [22, "module-matminer.featurizers.utils.tests"], [23, "module-contents"], [24, "module-contents"], [25, "module-matminer.utils"], [26, "module-matminer.utils.data_files"], [27, "module-matminer.utils.tests"]], "matminer.data_retrieval package": [[9, "matminer-data-retrieval-package"]], "Submodules": [[9, "submodules"], [10, "submodules"], [11, "submodules"], [12, "submodules"], [13, "submodules"], [14, "submodules"], [15, "submodules"], [16, "submodules"], [17, "submodules"], [18, "submodules"], [19, "submodules"], [20, "submodules"], [21, "submodules"], [22, "submodules"], [23, "submodules"], [24, "submodules"], [25, "submodules"], [26, "submodules"], [27, "submodules"]], "matminer.data_retrieval.retrieve_AFLOW module": [[9, "module-matminer.data_retrieval.retrieve_AFLOW"]], "matminer.data_retrieval.retrieve_Citrine module": [[9, "module-matminer.data_retrieval.retrieve_Citrine"]], "matminer.data_retrieval.retrieve_MDF module": [[9, "module-matminer.data_retrieval.retrieve_MDF"]], "matminer.data_retrieval.retrieve_MP module": [[9, "module-matminer.data_retrieval.retrieve_MP"]], "matminer.data_retrieval.retrieve_MPDS module": [[9, "module-matminer.data_retrieval.retrieve_MPDS"]], "matminer.data_retrieval.retrieve_MongoDB module": [[9, "module-matminer.data_retrieval.retrieve_MongoDB"]], "matminer.data_retrieval.retrieve_base module": [[9, "module-matminer.data_retrieval.retrieve_base"]], "matminer.data_retrieval.tests package": [[10, "matminer-data-retrieval-tests-package"]], "matminer.data_retrieval.tests.base module": [[10, "module-matminer.data_retrieval.tests.base"]], "matminer.data_retrieval.tests.test_retrieve_AFLOW module": [[10, "module-matminer.data_retrieval.tests.test_retrieve_AFLOW"]], "matminer.data_retrieval.tests.test_retrieve_Citrine module": [[10, "module-matminer.data_retrieval.tests.test_retrieve_Citrine"]], "matminer.data_retrieval.tests.test_retrieve_MDF module": [[10, "module-matminer.data_retrieval.tests.test_retrieve_MDF"]], "matminer.data_retrieval.tests.test_retrieve_MP module": [[10, "module-matminer.data_retrieval.tests.test_retrieve_MP"]], "matminer.data_retrieval.tests.test_retrieve_MPDS module": [[10, "module-matminer.data_retrieval.tests.test_retrieve_MPDS"]], "matminer.data_retrieval.tests.test_retrieve_MongoDB module": [[10, "module-matminer.data_retrieval.tests.test_retrieve_MongoDB"]], "matminer.datasets package": [[11, "matminer-datasets-package"]], "matminer.datasets.convenience_loaders module": [[11, "module-matminer.datasets.convenience_loaders"]], "matminer.datasets.dataset_retrieval module": [[11, "module-matminer.datasets.dataset_retrieval"]], "matminer.datasets.utils module": [[11, "module-matminer.datasets.utils"]], "matminer.datasets.tests package": [[12, "matminer-datasets-tests-package"]], "matminer.datasets.tests.base module": [[12, "module-matminer.datasets.tests.base"]], "matminer.datasets.tests.test_convenience_loaders module": [[12, "module-matminer.datasets.tests.test_convenience_loaders"]], "matminer.datasets.tests.test_dataset_retrieval module": [[12, "module-matminer.datasets.tests.test_dataset_retrieval"]], "matminer.datasets.tests.test_datasets module": [[12, "module-matminer.datasets.tests.test_datasets"]], "matminer.datasets.tests.test_utils module": [[12, "module-matminer.datasets.tests.test_utils"]], "matminer.featurizers package": [[13, "matminer-featurizers-package"]], "matminer.featurizers.bandstructure module": [[13, "module-matminer.featurizers.bandstructure"]], "matminer.featurizers.base module": [[13, "module-matminer.featurizers.base"]], "matminer.featurizers.conversions module": [[13, "module-matminer.featurizers.conversions"]], "matminer.featurizers.dos module": [[13, "module-matminer.featurizers.dos"]], "matminer.featurizers.function module": [[13, "module-matminer.featurizers.function"]], "matminer.featurizers.composition package": [[14, "matminer-featurizers-composition-package"]], "matminer.featurizers.composition.alloy module": [[14, "module-matminer.featurizers.composition.alloy"]], "matminer.featurizers.composition.composite module": [[14, "module-matminer.featurizers.composition.composite"]], "matminer.featurizers.composition.element module": [[14, "module-matminer.featurizers.composition.element"]], "matminer.featurizers.composition.ion module": [[14, "module-matminer.featurizers.composition.ion"]], "matminer.featurizers.composition.orbital module": [[14, "module-matminer.featurizers.composition.orbital"]], "matminer.featurizers.composition.packing module": [[14, "module-matminer.featurizers.composition.packing"]], "matminer.featurizers.composition.thermo module": [[14, "module-matminer.featurizers.composition.thermo"]], "matminer.featurizers.composition.tests package": [[15, "matminer-featurizers-composition-tests-package"]], "matminer.featurizers.composition.tests.base module": [[15, "module-matminer.featurizers.composition.tests.base"]], "matminer.featurizers.composition.tests.test_alloy module": [[15, "module-matminer.featurizers.composition.tests.test_alloy"]], "matminer.featurizers.composition.tests.test_composite module": [[15, "module-matminer.featurizers.composition.tests.test_composite"]], "matminer.featurizers.composition.tests.test_element module": [[15, "module-matminer.featurizers.composition.tests.test_element"]], "matminer.featurizers.composition.tests.test_ion module": [[15, "module-matminer.featurizers.composition.tests.test_ion"]], "matminer.featurizers.composition.tests.test_orbital module": [[15, "module-matminer.featurizers.composition.tests.test_orbital"]], "matminer.featurizers.composition.tests.test_packing module": [[15, "module-matminer.featurizers.composition.tests.test_packing"]], "matminer.featurizers.composition.tests.test_thermo module": [[15, "module-matminer.featurizers.composition.tests.test_thermo"]], "matminer.featurizers.site package": [[16, "matminer-featurizers-site-package"]], "matminer.featurizers.site.bonding module": [[16, "module-matminer.featurizers.site.bonding"]], "matminer.featurizers.site.chemical module": [[16, "module-matminer.featurizers.site.chemical"]], "matminer.featurizers.site.external module": [[16, "module-matminer.featurizers.site.external"]], "matminer.featurizers.site.fingerprint module": [[16, "module-matminer.featurizers.site.fingerprint"]], "matminer.featurizers.site.misc module": [[16, "module-matminer.featurizers.site.misc"]], "matminer.featurizers.site.rdf module": [[16, "module-matminer.featurizers.site.rdf"]], "matminer.featurizers.site.tests package": [[17, "matminer-featurizers-site-tests-package"]], "matminer.featurizers.site.tests.base module": [[17, "module-matminer.featurizers.site.tests.base"]], "matminer.featurizers.site.tests.test_bonding module": [[17, "module-matminer.featurizers.site.tests.test_bonding"]], "matminer.featurizers.site.tests.test_chemical module": [[17, "module-matminer.featurizers.site.tests.test_chemical"]], "matminer.featurizers.site.tests.test_external module": [[17, "module-matminer.featurizers.site.tests.test_external"]], "matminer.featurizers.site.tests.test_fingerprint module": [[17, "module-matminer.featurizers.site.tests.test_fingerprint"]], "matminer.featurizers.site.tests.test_misc module": [[17, "module-matminer.featurizers.site.tests.test_misc"]], "matminer.featurizers.site.tests.test_rdf module": [[17, "module-matminer.featurizers.site.tests.test_rdf"]], "matminer.featurizers.structure package": [[18, "matminer-featurizers-structure-package"]], "matminer.featurizers.structure.bonding module": [[18, "module-matminer.featurizers.structure.bonding"]], "matminer.featurizers.structure.composite module": [[18, "module-matminer.featurizers.structure.composite"]], "matminer.featurizers.structure.matrix module": [[18, "module-matminer.featurizers.structure.matrix"]], "matminer.featurizers.structure.misc module": [[18, "module-matminer.featurizers.structure.misc"]], "matminer.featurizers.structure.order module": [[18, "module-matminer.featurizers.structure.order"]], "matminer.featurizers.structure.rdf module": [[18, "module-matminer.featurizers.structure.rdf"]], "matminer.featurizers.structure.sites module": [[18, "module-matminer.featurizers.structure.sites"]], "matminer.featurizers.structure.symmetry module": [[18, "module-matminer.featurizers.structure.symmetry"]], "matminer.featurizers.structure.tests package": [[19, "matminer-featurizers-structure-tests-package"]], "matminer.featurizers.structure.tests.base module": [[19, "module-matminer.featurizers.structure.tests.base"]], "matminer.featurizers.structure.tests.test_bonding module": [[19, "module-matminer.featurizers.structure.tests.test_bonding"]], "matminer.featurizers.structure.tests.test_composite module": [[19, "module-matminer.featurizers.structure.tests.test_composite"]], "matminer.featurizers.structure.tests.test_matrix module": [[19, "module-matminer.featurizers.structure.tests.test_matrix"]], "matminer.featurizers.structure.tests.test_misc module": [[19, "module-matminer.featurizers.structure.tests.test_misc"]], "matminer.featurizers.structure.tests.test_order module": [[19, "module-matminer.featurizers.structure.tests.test_order"]], "matminer.featurizers.structure.tests.test_rdf module": [[19, "module-matminer.featurizers.structure.tests.test_rdf"]], "matminer.featurizers.structure.tests.test_sites module": [[19, "module-matminer.featurizers.structure.tests.test_sites"]], "matminer.featurizers.structure.tests.test_symmetry module": [[19, "module-matminer.featurizers.structure.tests.test_symmetry"]], "matminer.featurizers.tests package": [[20, "matminer-featurizers-tests-package"]], "matminer.featurizers.tests.test_bandstructure module": [[20, "module-matminer.featurizers.tests.test_bandstructure"]], "matminer.featurizers.tests.test_base module": [[20, "module-matminer.featurizers.tests.test_base"]], "matminer.featurizers.tests.test_conversions module": [[20, "module-matminer.featurizers.tests.test_conversions"]], "matminer.featurizers.tests.test_dos module": [[20, "module-matminer.featurizers.tests.test_dos"]], "matminer.featurizers.tests.test_function module": [[20, "module-matminer.featurizers.tests.test_function"]], "matminer.featurizers.utils package": [[21, "matminer-featurizers-utils-package"]], "matminer.featurizers.utils.grdf module": [[21, "module-matminer.featurizers.utils.grdf"]], "matminer.featurizers.utils.oxidation module": [[21, "module-matminer.featurizers.utils.oxidation"]], "matminer.featurizers.utils.stats module": [[21, "module-matminer.featurizers.utils.stats"]], "matminer.featurizers.utils.tests package": [[22, "matminer-featurizers-utils-tests-package"]], "matminer.featurizers.utils.tests.test_grdf module": [[22, "module-matminer.featurizers.utils.tests.test_grdf"]], "matminer.featurizers.utils.tests.test_oxidation module": [[22, "module-matminer.featurizers.utils.tests.test_oxidation"]], "matminer.featurizers.utils.tests.test_stats module": [[22, "module-matminer.featurizers.utils.tests.test_stats"]], "matminer.figrecipes package": [[23, "matminer-figrecipes-package"]], "matminer.figrecipes.plot module": [[23, "matminer-figrecipes-plot-module"]], "matminer.figrecipes.tests package": [[24, "matminer-figrecipes-tests-package"]], "matminer.figrecipes.tests.test_plots module": [[24, "matminer-figrecipes-tests-test-plots-module"]], "matminer.utils package": [[25, "matminer-utils-package"]], "matminer.utils.caching module": [[25, "module-matminer.utils.caching"]], "matminer.utils.data module": [[25, "module-matminer.utils.data"]], "matminer.utils.flatten_dict module": [[25, "module-matminer.utils.flatten_dict"]], "matminer.utils.io module": [[25, "module-matminer.utils.io"]], "matminer.utils.kernels module": [[25, "module-matminer.utils.kernels"]], "matminer.utils.pipeline module": [[25, "module-matminer.utils.pipeline"]], "matminer.utils.utils module": [[25, "module-matminer.utils.utils"]], "matminer.utils.data_files package": [[26, "matminer-utils-data-files-package"]], "matminer.utils.data_files.deml_elementdata module": [[26, "module-matminer.utils.data_files.deml_elementdata"]], "matminer.utils.tests package": [[27, "matminer-utils-tests-package"]], "matminer.utils.tests.test_caching module": [[27, "module-matminer.utils.tests.test_caching"]], "matminer.utils.tests.test_data module": [[27, "module-matminer.utils.tests.test_data"]], "matminer.utils.tests.test_flatten_dict module": [[27, "module-matminer.utils.tests.test_flatten_dict"]], "matminer.utils.tests.test_io module": [[27, "module-matminer.utils.tests.test_io"]]}, "indexentries": {"matminer": [[8, "module-matminer"]], "module": [[8, "module-matminer"], [9, "module-matminer.data_retrieval"], [9, "module-matminer.data_retrieval.retrieve_AFLOW"], [9, "module-matminer.data_retrieval.retrieve_Citrine"], [9, "module-matminer.data_retrieval.retrieve_MDF"], [9, "module-matminer.data_retrieval.retrieve_MP"], [9, "module-matminer.data_retrieval.retrieve_MPDS"], [9, "module-matminer.data_retrieval.retrieve_MongoDB"], [9, "module-matminer.data_retrieval.retrieve_base"], [10, "module-matminer.data_retrieval.tests"], [10, "module-matminer.data_retrieval.tests.base"], [10, "module-matminer.data_retrieval.tests.test_retrieve_AFLOW"], [10, "module-matminer.data_retrieval.tests.test_retrieve_Citrine"], [10, "module-matminer.data_retrieval.tests.test_retrieve_MDF"], [10, "module-matminer.data_retrieval.tests.test_retrieve_MP"], [10, "module-matminer.data_retrieval.tests.test_retrieve_MPDS"], [10, "module-matminer.data_retrieval.tests.test_retrieve_MongoDB"], [11, "module-matminer.datasets"], [11, "module-matminer.datasets.convenience_loaders"], [11, "module-matminer.datasets.dataset_retrieval"], [11, "module-matminer.datasets.utils"], [12, "module-matminer.datasets.tests"], [12, "module-matminer.datasets.tests.base"], [12, "module-matminer.datasets.tests.test_convenience_loaders"], [12, "module-matminer.datasets.tests.test_dataset_retrieval"], [12, "module-matminer.datasets.tests.test_datasets"], [12, "module-matminer.datasets.tests.test_utils"], [13, "module-matminer.featurizers"], [13, "module-matminer.featurizers.bandstructure"], [13, "module-matminer.featurizers.base"], [13, "module-matminer.featurizers.conversions"], [13, "module-matminer.featurizers.dos"], [13, "module-matminer.featurizers.function"], [14, "module-matminer.featurizers.composition"], [14, "module-matminer.featurizers.composition.alloy"], [14, "module-matminer.featurizers.composition.composite"], [14, "module-matminer.featurizers.composition.element"], [14, "module-matminer.featurizers.composition.ion"], [14, "module-matminer.featurizers.composition.orbital"], [14, "module-matminer.featurizers.composition.packing"], [14, "module-matminer.featurizers.composition.thermo"], [15, "module-matminer.featurizers.composition.tests"], [15, "module-matminer.featurizers.composition.tests.base"], [15, "module-matminer.featurizers.composition.tests.test_alloy"], [15, "module-matminer.featurizers.composition.tests.test_composite"], [15, "module-matminer.featurizers.composition.tests.test_element"], [15, "module-matminer.featurizers.composition.tests.test_ion"], [15, "module-matminer.featurizers.composition.tests.test_orbital"], [15, "module-matminer.featurizers.composition.tests.test_packing"], [15, "module-matminer.featurizers.composition.tests.test_thermo"], [16, "module-matminer.featurizers.site"], [16, "module-matminer.featurizers.site.bonding"], [16, "module-matminer.featurizers.site.chemical"], [16, "module-matminer.featurizers.site.external"], [16, "module-matminer.featurizers.site.fingerprint"], [16, "module-matminer.featurizers.site.misc"], [16, "module-matminer.featurizers.site.rdf"], [17, "module-matminer.featurizers.site.tests"], [17, "module-matminer.featurizers.site.tests.base"], [17, "module-matminer.featurizers.site.tests.test_bonding"], [17, "module-matminer.featurizers.site.tests.test_chemical"], [17, "module-matminer.featurizers.site.tests.test_external"], [17, "module-matminer.featurizers.site.tests.test_fingerprint"], [17, "module-matminer.featurizers.site.tests.test_misc"], [17, "module-matminer.featurizers.site.tests.test_rdf"], [18, "module-matminer.featurizers.structure"], [18, "module-matminer.featurizers.structure.bonding"], [18, "module-matminer.featurizers.structure.composite"], [18, "module-matminer.featurizers.structure.matrix"], [18, "module-matminer.featurizers.structure.misc"], [18, "module-matminer.featurizers.structure.order"], [18, "module-matminer.featurizers.structure.rdf"], [18, "module-matminer.featurizers.structure.sites"], [18, "module-matminer.featurizers.structure.symmetry"], [19, "module-matminer.featurizers.structure.tests"], [19, "module-matminer.featurizers.structure.tests.base"], [19, "module-matminer.featurizers.structure.tests.test_bonding"], [19, "module-matminer.featurizers.structure.tests.test_composite"], [19, "module-matminer.featurizers.structure.tests.test_matrix"], [19, "module-matminer.featurizers.structure.tests.test_misc"], [19, "module-matminer.featurizers.structure.tests.test_order"], [19, "module-matminer.featurizers.structure.tests.test_rdf"], [19, "module-matminer.featurizers.structure.tests.test_sites"], [19, "module-matminer.featurizers.structure.tests.test_symmetry"], [20, "module-matminer.featurizers.tests"], [20, "module-matminer.featurizers.tests.test_bandstructure"], [20, "module-matminer.featurizers.tests.test_base"], [20, "module-matminer.featurizers.tests.test_conversions"], [20, "module-matminer.featurizers.tests.test_dos"], [20, "module-matminer.featurizers.tests.test_function"], [21, "module-matminer.featurizers.utils"], [21, "module-matminer.featurizers.utils.grdf"], [21, "module-matminer.featurizers.utils.oxidation"], [21, "module-matminer.featurizers.utils.stats"], [22, "module-matminer.featurizers.utils.tests"], [22, "module-matminer.featurizers.utils.tests.test_grdf"], [22, "module-matminer.featurizers.utils.tests.test_oxidation"], [22, "module-matminer.featurizers.utils.tests.test_stats"], [25, "module-matminer.utils"], [25, "module-matminer.utils.caching"], [25, "module-matminer.utils.data"], [25, "module-matminer.utils.flatten_dict"], [25, "module-matminer.utils.io"], [25, "module-matminer.utils.kernels"], [25, "module-matminer.utils.pipeline"], [25, "module-matminer.utils.utils"], [26, "module-matminer.utils.data_files"], [26, "module-matminer.utils.data_files.deml_elementdata"], [27, "module-matminer.utils.tests"], [27, "module-matminer.utils.tests.test_caching"], [27, "module-matminer.utils.tests.test_data"], [27, "module-matminer.utils.tests.test_flatten_dict"], [27, "module-matminer.utils.tests.test_io"]], "aflowdataretrieval (class in matminer.data_retrieval.retrieve_aflow)": [[9, "matminer.data_retrieval.retrieve_AFLOW.AFLOWDataRetrieval"]], "apierror": [[9, "matminer.data_retrieval.retrieve_MPDS.APIError"]], "basedataretrieval (class in matminer.data_retrieval.retrieve_base)": [[9, "matminer.data_retrieval.retrieve_base.BaseDataRetrieval"]], "citrinedataretrieval (class in matminer.data_retrieval.retrieve_citrine)": [[9, "matminer.data_retrieval.retrieve_Citrine.CitrineDataRetrieval"]], "mdfdataretrieval (class in matminer.data_retrieval.retrieve_mdf)": [[9, "matminer.data_retrieval.retrieve_MDF.MDFDataRetrieval"]], "mpdsdataretrieval (class in matminer.data_retrieval.retrieve_mpds)": [[9, "matminer.data_retrieval.retrieve_MPDS.MPDSDataRetrieval"]], "mpdataretrieval (class in matminer.data_retrieval.retrieve_mp)": [[9, "matminer.data_retrieval.retrieve_MP.MPDataRetrieval"]], "mongodataretrieval (class in matminer.data_retrieval.retrieve_mongodb)": [[9, "matminer.data_retrieval.retrieve_MongoDB.MongoDataRetrieval"]], "retrievalquery (class in matminer.data_retrieval.retrieve_aflow)": [[9, "matminer.data_retrieval.retrieve_AFLOW.RetrievalQuery"]], "__init__() (matminer.data_retrieval.retrieve_citrine.citrinedataretrieval method)": [[9, "matminer.data_retrieval.retrieve_Citrine.CitrineDataRetrieval.__init__"]], "__init__() (matminer.data_retrieval.retrieve_mdf.mdfdataretrieval method)": [[9, "matminer.data_retrieval.retrieve_MDF.MDFDataRetrieval.__init__"]], "__init__() (matminer.data_retrieval.retrieve_mp.mpdataretrieval method)": [[9, "matminer.data_retrieval.retrieve_MP.MPDataRetrieval.__init__"]], "__init__() (matminer.data_retrieval.retrieve_mpds.apierror method)": [[9, "matminer.data_retrieval.retrieve_MPDS.APIError.__init__"]], "__init__() (matminer.data_retrieval.retrieve_mpds.mpdsdataretrieval method)": [[9, "matminer.data_retrieval.retrieve_MPDS.MPDSDataRetrieval.__init__"]], "__init__() (matminer.data_retrieval.retrieve_mongodb.mongodataretrieval method)": [[9, "matminer.data_retrieval.retrieve_MongoDB.MongoDataRetrieval.__init__"]], "api_link() (matminer.data_retrieval.retrieve_aflow.aflowdataretrieval method)": [[9, "matminer.data_retrieval.retrieve_AFLOW.AFLOWDataRetrieval.api_link"]], "api_link() (matminer.data_retrieval.retrieve_citrine.citrinedataretrieval method)": [[9, "matminer.data_retrieval.retrieve_Citrine.CitrineDataRetrieval.api_link"]], "api_link() (matminer.data_retrieval.retrieve_mdf.mdfdataretrieval method)": [[9, "matminer.data_retrieval.retrieve_MDF.MDFDataRetrieval.api_link"]], "api_link() (matminer.data_retrieval.retrieve_mp.mpdataretrieval method)": [[9, "matminer.data_retrieval.retrieve_MP.MPDataRetrieval.api_link"]], "api_link() (matminer.data_retrieval.retrieve_mpds.mpdsdataretrieval method)": [[9, "matminer.data_retrieval.retrieve_MPDS.MPDSDataRetrieval.api_link"]], "api_link() (matminer.data_retrieval.retrieve_mongodb.mongodataretrieval method)": [[9, "matminer.data_retrieval.retrieve_MongoDB.MongoDataRetrieval.api_link"]], "api_link() (matminer.data_retrieval.retrieve_base.basedataretrieval method)": [[9, "matminer.data_retrieval.retrieve_base.BaseDataRetrieval.api_link"]], "chillouttime (matminer.data_retrieval.retrieve_mpds.mpdsdataretrieval attribute)": [[9, "matminer.data_retrieval.retrieve_MPDS.MPDSDataRetrieval.chillouttime"]], "citations() (matminer.data_retrieval.retrieve_aflow.aflowdataretrieval method)": [[9, "matminer.data_retrieval.retrieve_AFLOW.AFLOWDataRetrieval.citations"]], "citations() (matminer.data_retrieval.retrieve_citrine.citrinedataretrieval method)": [[9, "matminer.data_retrieval.retrieve_Citrine.CitrineDataRetrieval.citations"]], "citations() (matminer.data_retrieval.retrieve_mdf.mdfdataretrieval method)": [[9, "matminer.data_retrieval.retrieve_MDF.MDFDataRetrieval.citations"]], "citations() (matminer.data_retrieval.retrieve_mp.mpdataretrieval method)": [[9, "matminer.data_retrieval.retrieve_MP.MPDataRetrieval.citations"]], "citations() (matminer.data_retrieval.retrieve_mpds.mpdsdataretrieval method)": [[9, "matminer.data_retrieval.retrieve_MPDS.MPDSDataRetrieval.citations"]], "citations() (matminer.data_retrieval.retrieve_base.basedataretrieval method)": [[9, "matminer.data_retrieval.retrieve_base.BaseDataRetrieval.citations"]], "clean_projection() (in module matminer.data_retrieval.retrieve_mongodb)": [[9, "matminer.data_retrieval.retrieve_MongoDB.clean_projection"]], "compile_crystal() (matminer.data_retrieval.retrieve_mpds.mpdsdataretrieval static method)": [[9, "matminer.data_retrieval.retrieve_MPDS.MPDSDataRetrieval.compile_crystal"]], "default_properties (matminer.data_retrieval.retrieve_mpds.mpdsdataretrieval attribute)": [[9, "matminer.data_retrieval.retrieve_MPDS.MPDSDataRetrieval.default_properties"]], "endpoint (matminer.data_retrieval.retrieve_mpds.mpdsdataretrieval attribute)": [[9, "matminer.data_retrieval.retrieve_MPDS.MPDSDataRetrieval.endpoint"]], "from_pymongo() (matminer.data_retrieval.retrieve_aflow.retrievalquery class method)": [[9, "matminer.data_retrieval.retrieve_AFLOW.RetrievalQuery.from_pymongo"]], "get_data() (matminer.data_retrieval.retrieve_citrine.citrinedataretrieval method)": [[9, "matminer.data_retrieval.retrieve_Citrine.CitrineDataRetrieval.get_data"]], "get_data() (matminer.data_retrieval.retrieve_mdf.mdfdataretrieval method)": [[9, "matminer.data_retrieval.retrieve_MDF.MDFDataRetrieval.get_data"]], "get_data() (matminer.data_retrieval.retrieve_mp.mpdataretrieval method)": [[9, "matminer.data_retrieval.retrieve_MP.MPDataRetrieval.get_data"]], "get_data() (matminer.data_retrieval.retrieve_mpds.mpdsdataretrieval method)": [[9, "matminer.data_retrieval.retrieve_MPDS.MPDSDataRetrieval.get_data"]], "get_dataframe() (matminer.data_retrieval.retrieve_aflow.aflowdataretrieval method)": [[9, "matminer.data_retrieval.retrieve_AFLOW.AFLOWDataRetrieval.get_dataframe"]], "get_dataframe() (matminer.data_retrieval.retrieve_citrine.citrinedataretrieval method)": [[9, "matminer.data_retrieval.retrieve_Citrine.CitrineDataRetrieval.get_dataframe"]], "get_dataframe() (matminer.data_retrieval.retrieve_mdf.mdfdataretrieval method)": [[9, "matminer.data_retrieval.retrieve_MDF.MDFDataRetrieval.get_dataframe"]], "get_dataframe() (matminer.data_retrieval.retrieve_mp.mpdataretrieval method)": [[9, "matminer.data_retrieval.retrieve_MP.MPDataRetrieval.get_dataframe"]], "get_dataframe() (matminer.data_retrieval.retrieve_mpds.mpdsdataretrieval method)": [[9, "matminer.data_retrieval.retrieve_MPDS.MPDSDataRetrieval.get_dataframe"]], "get_dataframe() (matminer.data_retrieval.retrieve_mongodb.mongodataretrieval method)": [[9, "matminer.data_retrieval.retrieve_MongoDB.MongoDataRetrieval.get_dataframe"]], "get_dataframe() (matminer.data_retrieval.retrieve_base.basedataretrieval method)": [[9, "matminer.data_retrieval.retrieve_base.BaseDataRetrieval.get_dataframe"]], "get_relaxed_structure() (matminer.data_retrieval.retrieve_aflow.aflowdataretrieval static method)": [[9, "matminer.data_retrieval.retrieve_AFLOW.AFLOWDataRetrieval.get_relaxed_structure"]], "get_value() (in module matminer.data_retrieval.retrieve_citrine)": [[9, "matminer.data_retrieval.retrieve_Citrine.get_value"]], "is_int() (in module matminer.data_retrieval.retrieve_mongodb)": [[9, "matminer.data_retrieval.retrieve_MongoDB.is_int"]], "make_dataframe() (in module matminer.data_retrieval.retrieve_mdf)": [[9, "matminer.data_retrieval.retrieve_MDF.make_dataframe"]], "matminer.data_retrieval": [[9, "module-matminer.data_retrieval"]], "matminer.data_retrieval.retrieve_aflow": [[9, "module-matminer.data_retrieval.retrieve_AFLOW"]], "matminer.data_retrieval.retrieve_citrine": [[9, "module-matminer.data_retrieval.retrieve_Citrine"]], "matminer.data_retrieval.retrieve_mdf": [[9, "module-matminer.data_retrieval.retrieve_MDF"]], "matminer.data_retrieval.retrieve_mp": [[9, "module-matminer.data_retrieval.retrieve_MP"]], "matminer.data_retrieval.retrieve_mpds": [[9, "module-matminer.data_retrieval.retrieve_MPDS"]], "matminer.data_retrieval.retrieve_mongodb": [[9, "module-matminer.data_retrieval.retrieve_MongoDB"]], "matminer.data_retrieval.retrieve_base": [[9, "module-matminer.data_retrieval.retrieve_base"]], "maxnpages (matminer.data_retrieval.retrieve_mpds.mpdsdataretrieval attribute)": [[9, "matminer.data_retrieval.retrieve_MPDS.MPDSDataRetrieval.maxnpages"]], "pagesize (matminer.data_retrieval.retrieve_mpds.mpdsdataretrieval attribute)": [[9, "matminer.data_retrieval.retrieve_MPDS.MPDSDataRetrieval.pagesize"]], "parse_scalars() (in module matminer.data_retrieval.retrieve_citrine)": [[9, "matminer.data_retrieval.retrieve_Citrine.parse_scalars"]], "remove_ints() (in module matminer.data_retrieval.retrieve_mongodb)": [[9, "matminer.data_retrieval.retrieve_MongoDB.remove_ints"]], "try_get_prop_by_material_id() (matminer.data_retrieval.retrieve_mp.mpdataretrieval method)": [[9, "matminer.data_retrieval.retrieve_MP.MPDataRetrieval.try_get_prop_by_material_id"]], "aflowdataretrievaltest (class in matminer.data_retrieval.tests.test_retrieve_aflow)": [[10, "matminer.data_retrieval.tests.test_retrieve_AFLOW.AFLOWDataRetrievalTest"]], "citrinedataretrievaltest (class in matminer.data_retrieval.tests.test_retrieve_citrine)": [[10, "matminer.data_retrieval.tests.test_retrieve_Citrine.CitrineDataRetrievalTest"]], "mdfdataretrievaltest (class in matminer.data_retrieval.tests.test_retrieve_mdf)": [[10, "matminer.data_retrieval.tests.test_retrieve_MDF.MDFDataRetrievalTest"]], "mpdsdataretrievaltest (class in matminer.data_retrieval.tests.test_retrieve_mpds)": [[10, "matminer.data_retrieval.tests.test_retrieve_MPDS.MPDSDataRetrievalTest"]], "mpdataretrievaltest (class in matminer.data_retrieval.tests.test_retrieve_mp)": [[10, "matminer.data_retrieval.tests.test_retrieve_MP.MPDataRetrievalTest"]], "mongodataretrievaltest (class in matminer.data_retrieval.tests.test_retrieve_mongodb)": [[10, "matminer.data_retrieval.tests.test_retrieve_MongoDB.MongoDataRetrievalTest"]], "matminer.data_retrieval.tests": [[10, "module-matminer.data_retrieval.tests"]], "matminer.data_retrieval.tests.base": [[10, "module-matminer.data_retrieval.tests.base"]], "matminer.data_retrieval.tests.test_retrieve_aflow": [[10, "module-matminer.data_retrieval.tests.test_retrieve_AFLOW"]], "matminer.data_retrieval.tests.test_retrieve_citrine": [[10, "module-matminer.data_retrieval.tests.test_retrieve_Citrine"]], "matminer.data_retrieval.tests.test_retrieve_mdf": [[10, "module-matminer.data_retrieval.tests.test_retrieve_MDF"]], "matminer.data_retrieval.tests.test_retrieve_mp": [[10, "module-matminer.data_retrieval.tests.test_retrieve_MP"]], "matminer.data_retrieval.tests.test_retrieve_mpds": [[10, "module-matminer.data_retrieval.tests.test_retrieve_MPDS"]], "matminer.data_retrieval.tests.test_retrieve_mongodb": [[10, "module-matminer.data_retrieval.tests.test_retrieve_MongoDB"]], "setup() (matminer.data_retrieval.tests.test_retrieve_aflow.aflowdataretrievaltest method)": [[10, "matminer.data_retrieval.tests.test_retrieve_AFLOW.AFLOWDataRetrievalTest.setUp"]], "setup() (matminer.data_retrieval.tests.test_retrieve_citrine.citrinedataretrievaltest method)": [[10, "matminer.data_retrieval.tests.test_retrieve_Citrine.CitrineDataRetrievalTest.setUp"]], "setup() (matminer.data_retrieval.tests.test_retrieve_mp.mpdataretrievaltest method)": [[10, "matminer.data_retrieval.tests.test_retrieve_MP.MPDataRetrievalTest.setUp"]], "setup() (matminer.data_retrieval.tests.test_retrieve_mpds.mpdsdataretrievaltest method)": [[10, "matminer.data_retrieval.tests.test_retrieve_MPDS.MPDSDataRetrievalTest.setUp"]], "setupclass() (matminer.data_retrieval.tests.test_retrieve_mdf.mdfdataretrievaltest class method)": [[10, "matminer.data_retrieval.tests.test_retrieve_MDF.MDFDataRetrievalTest.setUpClass"]], "test_cleaned_projection() (matminer.data_retrieval.tests.test_retrieve_mongodb.mongodataretrievaltest method)": [[10, "matminer.data_retrieval.tests.test_retrieve_MongoDB.MongoDataRetrievalTest.test_cleaned_projection"]], "test_get_data() (matminer.data_retrieval.tests.test_retrieve_aflow.aflowdataretrievaltest method)": [[10, "matminer.data_retrieval.tests.test_retrieve_AFLOW.AFLOWDataRetrievalTest.test_get_data"]], "test_get_data() (matminer.data_retrieval.tests.test_retrieve_citrine.citrinedataretrievaltest method)": [[10, "matminer.data_retrieval.tests.test_retrieve_Citrine.CitrineDataRetrievalTest.test_get_data"]], "test_get_data() (matminer.data_retrieval.tests.test_retrieve_mp.mpdataretrievaltest method)": [[10, "matminer.data_retrieval.tests.test_retrieve_MP.MPDataRetrievalTest.test_get_data"]], "test_get_dataframe() (matminer.data_retrieval.tests.test_retrieve_mdf.mdfdataretrievaltest method)": [[10, "matminer.data_retrieval.tests.test_retrieve_MDF.MDFDataRetrievalTest.test_get_dataframe"]], "test_get_dataframe() (matminer.data_retrieval.tests.test_retrieve_mongodb.mongodataretrievaltest method)": [[10, "matminer.data_retrieval.tests.test_retrieve_MongoDB.MongoDataRetrievalTest.test_get_dataframe"]], "test_get_dataframe_by_query() (matminer.data_retrieval.tests.test_retrieve_mdf.mdfdataretrievaltest method)": [[10, "matminer.data_retrieval.tests.test_retrieve_MDF.MDFDataRetrievalTest.test_get_dataframe_by_query"]], "test_make_dataframe() (matminer.data_retrieval.tests.test_retrieve_mdf.mdfdataretrievaltest method)": [[10, "matminer.data_retrieval.tests.test_retrieve_MDF.MDFDataRetrievalTest.test_make_dataframe"]], "test_multiple_items_in_list() (matminer.data_retrieval.tests.test_retrieve_citrine.citrinedataretrievaltest method)": [[10, "matminer.data_retrieval.tests.test_retrieve_Citrine.CitrineDataRetrievalTest.test_multiple_items_in_list"]], "test_remove_ints() (matminer.data_retrieval.tests.test_retrieve_mongodb.mongodataretrievaltest method)": [[10, "matminer.data_retrieval.tests.test_retrieve_MongoDB.MongoDataRetrievalTest.test_remove_ints"]], "test_valid_answer() (matminer.data_retrieval.tests.test_retrieve_mpds.mpdsdataretrievaltest method)": [[10, "matminer.data_retrieval.tests.test_retrieve_MPDS.MPDSDataRetrievalTest.test_valid_answer"]], "get_all_dataset_info() (in module matminer.datasets.dataset_retrieval)": [[11, "matminer.datasets.dataset_retrieval.get_all_dataset_info"]], "get_available_datasets() (in module matminer.datasets.dataset_retrieval)": [[11, "matminer.datasets.dataset_retrieval.get_available_datasets"]], "get_dataset_attribute() (in module matminer.datasets.dataset_retrieval)": [[11, "matminer.datasets.dataset_retrieval.get_dataset_attribute"]], "get_dataset_citations() (in module matminer.datasets.dataset_retrieval)": [[11, "matminer.datasets.dataset_retrieval.get_dataset_citations"]], "get_dataset_column_description() (in module matminer.datasets.dataset_retrieval)": [[11, "matminer.datasets.dataset_retrieval.get_dataset_column_description"]], "get_dataset_columns() (in module matminer.datasets.dataset_retrieval)": [[11, "matminer.datasets.dataset_retrieval.get_dataset_columns"]], "get_dataset_description() (in module matminer.datasets.dataset_retrieval)": [[11, "matminer.datasets.dataset_retrieval.get_dataset_description"]], "get_dataset_num_entries() (in module matminer.datasets.dataset_retrieval)": [[11, "matminer.datasets.dataset_retrieval.get_dataset_num_entries"]], "get_dataset_reference() (in module matminer.datasets.dataset_retrieval)": [[11, "matminer.datasets.dataset_retrieval.get_dataset_reference"]], "load_boltztrap_mp() (in module matminer.datasets.convenience_loaders)": [[11, "matminer.datasets.convenience_loaders.load_boltztrap_mp"]], "load_brgoch_superhard_training() (in module matminer.datasets.convenience_loaders)": [[11, "matminer.datasets.convenience_loaders.load_brgoch_superhard_training"]], "load_castelli_perovskites() (in module matminer.datasets.convenience_loaders)": [[11, "matminer.datasets.convenience_loaders.load_castelli_perovskites"]], "load_citrine_thermal_conductivity() (in module matminer.datasets.convenience_loaders)": [[11, "matminer.datasets.convenience_loaders.load_citrine_thermal_conductivity"]], "load_dataset() (in module matminer.datasets.dataset_retrieval)": [[11, "matminer.datasets.dataset_retrieval.load_dataset"]], "load_dielectric_constant() (in module matminer.datasets.convenience_loaders)": [[11, "matminer.datasets.convenience_loaders.load_dielectric_constant"]], "load_double_perovskites_gap() (in module matminer.datasets.convenience_loaders)": [[11, "matminer.datasets.convenience_loaders.load_double_perovskites_gap"]], "load_double_perovskites_gap_lumo() (in module matminer.datasets.convenience_loaders)": [[11, "matminer.datasets.convenience_loaders.load_double_perovskites_gap_lumo"]], "load_elastic_tensor() (in module matminer.datasets.convenience_loaders)": [[11, "matminer.datasets.convenience_loaders.load_elastic_tensor"]], "load_expt_formation_enthalpy() (in module matminer.datasets.convenience_loaders)": [[11, "matminer.datasets.convenience_loaders.load_expt_formation_enthalpy"]], "load_expt_gap() (in module matminer.datasets.convenience_loaders)": [[11, "matminer.datasets.convenience_loaders.load_expt_gap"]], "load_flla() (in module matminer.datasets.convenience_loaders)": [[11, "matminer.datasets.convenience_loaders.load_flla"]], "load_glass_binary() (in module matminer.datasets.convenience_loaders)": [[11, "matminer.datasets.convenience_loaders.load_glass_binary"]], "load_glass_ternary_hipt() (in module matminer.datasets.convenience_loaders)": [[11, "matminer.datasets.convenience_loaders.load_glass_ternary_hipt"]], "load_glass_ternary_landolt() (in module matminer.datasets.convenience_loaders)": [[11, "matminer.datasets.convenience_loaders.load_glass_ternary_landolt"]], "load_heusler_magnetic() (in module matminer.datasets.convenience_loaders)": [[11, "matminer.datasets.convenience_loaders.load_heusler_magnetic"]], "load_jarvis_dft_2d() (in module matminer.datasets.convenience_loaders)": [[11, "matminer.datasets.convenience_loaders.load_jarvis_dft_2d"]], "load_jarvis_dft_3d() (in module matminer.datasets.convenience_loaders)": [[11, "matminer.datasets.convenience_loaders.load_jarvis_dft_3d"]], "load_jarvis_ml_dft_training() (in module matminer.datasets.convenience_loaders)": [[11, "matminer.datasets.convenience_loaders.load_jarvis_ml_dft_training"]], "load_m2ax() (in module matminer.datasets.convenience_loaders)": [[11, "matminer.datasets.convenience_loaders.load_m2ax"]], "load_mp() (in module matminer.datasets.convenience_loaders)": [[11, "matminer.datasets.convenience_loaders.load_mp"]], "load_phonon_dielectric_mp() (in module matminer.datasets.convenience_loaders)": [[11, "matminer.datasets.convenience_loaders.load_phonon_dielectric_mp"]], "load_piezoelectric_tensor() (in module matminer.datasets.convenience_loaders)": [[11, "matminer.datasets.convenience_loaders.load_piezoelectric_tensor"]], "load_steel_strength() (in module matminer.datasets.convenience_loaders)": [[11, "matminer.datasets.convenience_loaders.load_steel_strength"]], "load_wolverton_oxides() (in module matminer.datasets.convenience_loaders)": [[11, "matminer.datasets.convenience_loaders.load_wolverton_oxides"]], "matminer.datasets": [[11, "module-matminer.datasets"]], "matminer.datasets.convenience_loaders": [[11, "module-matminer.datasets.convenience_loaders"]], "matminer.datasets.dataset_retrieval": [[11, "module-matminer.datasets.dataset_retrieval"]], "matminer.datasets.utils": [[11, "module-matminer.datasets.utils"]], "dataretrievaltest (class in matminer.datasets.tests.test_dataset_retrieval)": [[12, "matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest"]], "datasetstest (class in matminer.datasets.tests.test_datasets)": [[12, "matminer.datasets.tests.test_datasets.DataSetsTest"]], "datasettest (class in matminer.datasets.tests.base)": [[12, "matminer.datasets.tests.base.DatasetTest"]], "matbenchdatasetstest (class in matminer.datasets.tests.test_datasets)": [[12, "matminer.datasets.tests.test_datasets.MatbenchDatasetsTest"]], "matminerdatasetstest (class in matminer.datasets.tests.test_datasets)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest"]], "utilstest (class in matminer.datasets.tests.test_utils)": [[12, "matminer.datasets.tests.test_utils.UtilsTest"]], "matminer.datasets.tests": [[12, "module-matminer.datasets.tests"]], "matminer.datasets.tests.base": [[12, "module-matminer.datasets.tests.base"]], "matminer.datasets.tests.test_convenience_loaders": [[12, "module-matminer.datasets.tests.test_convenience_loaders"]], "matminer.datasets.tests.test_dataset_retrieval": [[12, "module-matminer.datasets.tests.test_dataset_retrieval"]], "matminer.datasets.tests.test_datasets": [[12, "module-matminer.datasets.tests.test_datasets"]], "matminer.datasets.tests.test_utils": [[12, "module-matminer.datasets.tests.test_utils"]], "setup() (matminer.datasets.tests.base.datasettest method)": [[12, "matminer.datasets.tests.base.DatasetTest.setUp"]], "test_boltztrap_mp() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_boltztrap_mp"]], "test_brgoch_superhard_training() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_brgoch_superhard_training"]], "test_castelli_perovskites() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_castelli_perovskites"]], "test_citrine_thermal_conductivity() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_citrine_thermal_conductivity"]], "test_dielectric_constant() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_dielectric_constant"]], "test_double_perovskites_gap() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_double_perovskites_gap"]], "test_double_perovskites_gap_lumo() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_double_perovskites_gap_lumo"]], "test_elastic_tensor_2015() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_elastic_tensor_2015"]], "test_expt_formation_enthalpy() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_expt_formation_enthalpy"]], "test_expt_formation_enthalpy_kingsbury() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_expt_formation_enthalpy_kingsbury"]], "test_expt_gap() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_expt_gap"]], "test_expt_gap_kingsbury() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_expt_gap_kingsbury"]], "test_fetch_external_dataset() (matminer.datasets.tests.test_utils.utilstest method)": [[12, "matminer.datasets.tests.test_utils.UtilsTest.test_fetch_external_dataset"]], "test_flla() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_flla"]], "test_get_all_dataset_info() (matminer.datasets.tests.test_dataset_retrieval.dataretrievaltest method)": [[12, "matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_all_dataset_info"]], "test_get_data_home() (matminer.datasets.tests.test_utils.utilstest method)": [[12, "matminer.datasets.tests.test_utils.UtilsTest.test_get_data_home"]], "test_get_dataset_attribute() (matminer.datasets.tests.test_dataset_retrieval.dataretrievaltest method)": [[12, "matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_attribute"]], "test_get_dataset_citations() (matminer.datasets.tests.test_dataset_retrieval.dataretrievaltest method)": [[12, "matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_citations"]], "test_get_dataset_column_descriptions() (matminer.datasets.tests.test_dataset_retrieval.dataretrievaltest method)": [[12, "matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_column_descriptions"]], "test_get_dataset_columns() (matminer.datasets.tests.test_dataset_retrieval.dataretrievaltest method)": [[12, "matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_columns"]], "test_get_dataset_description() (matminer.datasets.tests.test_dataset_retrieval.dataretrievaltest method)": [[12, "matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_description"]], "test_get_dataset_num_entries() (matminer.datasets.tests.test_dataset_retrieval.dataretrievaltest method)": [[12, "matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_num_entries"]], "test_get_dataset_reference() (matminer.datasets.tests.test_dataset_retrieval.dataretrievaltest method)": [[12, "matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_get_dataset_reference"]], "test_get_file_sha256_hash() (matminer.datasets.tests.test_utils.utilstest method)": [[12, "matminer.datasets.tests.test_utils.UtilsTest.test_get_file_sha256_hash"]], "test_glass_binary() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_glass_binary"]], "test_glass_binary_v2() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_glass_binary_v2"]], "test_glass_ternary_hipt() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_glass_ternary_hipt"]], "test_glass_ternary_landolt() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_glass_ternary_landolt"]], "test_heusler_magnetic() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_heusler_magnetic"]], "test_jarvis_dft_2d() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_jarvis_dft_2d"]], "test_jarvis_dft_3d() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_jarvis_dft_3d"]], "test_jarvis_ml_dft_training() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_jarvis_ml_dft_training"]], "test_load_dataset() (matminer.datasets.tests.test_dataset_retrieval.dataretrievaltest method)": [[12, "matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_load_dataset"]], "test_load_dataset_dict() (matminer.datasets.tests.test_utils.utilstest method)": [[12, "matminer.datasets.tests.test_utils.UtilsTest.test_load_dataset_dict"]], "test_m2ax() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_m2ax"]], "test_matbench_v0_1() (matminer.datasets.tests.test_datasets.matbenchdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatbenchDatasetsTest.test_matbench_v0_1"]], "test_mp_all_20181018() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_mp_all_20181018"]], "test_mp_nostruct_20181018() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_mp_nostruct_20181018"]], "test_phonon_dielectric_mp() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_phonon_dielectric_mp"]], "test_piezoelectric_tensor() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_piezoelectric_tensor"]], "test_print_available_datasets() (matminer.datasets.tests.test_dataset_retrieval.dataretrievaltest method)": [[12, "matminer.datasets.tests.test_dataset_retrieval.DataRetrievalTest.test_print_available_datasets"]], "test_read_dataframe_from_file() (matminer.datasets.tests.test_utils.utilstest method)": [[12, "matminer.datasets.tests.test_utils.UtilsTest.test_read_dataframe_from_file"]], "test_ricci_boltztrap_mp_tabular() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_ricci_boltztrap_mp_tabular"]], "test_steel_strength() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_steel_strength"]], "test_superconductivity2018() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_superconductivity2018"]], "test_tholander_nitrides_e_form() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_tholander_nitrides_e_form"]], "test_ucsb_thermoelectrics() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_ucsb_thermoelectrics"]], "test_validate_dataset() (matminer.datasets.tests.test_utils.utilstest method)": [[12, "matminer.datasets.tests.test_utils.UtilsTest.test_validate_dataset"]], "test_wolverton_oxides() (matminer.datasets.tests.test_datasets.matminerdatasetstest method)": [[12, "matminer.datasets.tests.test_datasets.MatminerDatasetsTest.test_wolverton_oxides"]], "universal_dataset_check() (matminer.datasets.tests.test_datasets.datasetstest method)": [[12, "matminer.datasets.tests.test_datasets.DataSetsTest.universal_dataset_check"]], "aseatomstostructure (class in matminer.featurizers.conversions)": [[13, "matminer.featurizers.conversions.ASEAtomstoStructure"]], "bandfeaturizer (class in matminer.featurizers.bandstructure)": [[13, "matminer.featurizers.bandstructure.BandFeaturizer"]], "basefeaturizer (class in matminer.featurizers.base)": [[13, "matminer.featurizers.base.BaseFeaturizer"]], "branchpointenergy (class in matminer.featurizers.bandstructure)": [[13, "matminer.featurizers.bandstructure.BranchPointEnergy"]], "compositiontooxidcomposition (class in matminer.featurizers.conversions)": [[13, "matminer.featurizers.conversions.CompositionToOxidComposition"]], "compositiontostructurefrommp (class in matminer.featurizers.conversions)": [[13, "matminer.featurizers.conversions.CompositionToStructureFromMP"]], "conversionfeaturizer (class in matminer.featurizers.conversions)": [[13, "matminer.featurizers.conversions.ConversionFeaturizer"]], "dosfeaturizer (class in matminer.featurizers.dos)": [[13, "matminer.featurizers.dos.DOSFeaturizer"]], "dicttoobject (class in matminer.featurizers.conversions)": [[13, "matminer.featurizers.conversions.DictToObject"]], "dopingfermi (class in matminer.featurizers.dos)": [[13, "matminer.featurizers.dos.DopingFermi"]], "dosasymmetry (class in matminer.featurizers.dos)": [[13, "matminer.featurizers.dos.DosAsymmetry"]], "functionfeaturizer (class in matminer.featurizers.function)": [[13, "matminer.featurizers.function.FunctionFeaturizer"]], "hybridization (class in matminer.featurizers.dos)": [[13, "matminer.featurizers.dos.Hybridization"]], "illegal_characters (matminer.featurizers.function.functionfeaturizer attribute)": [[13, "matminer.featurizers.function.FunctionFeaturizer.ILLEGAL_CHARACTERS"]], "jsontoobject (class in matminer.featurizers.conversions)": [[13, "matminer.featurizers.conversions.JsonToObject"]], "multiplefeaturizer (class in matminer.featurizers.base)": [[13, "matminer.featurizers.base.MultipleFeaturizer"]], "pymatgenfunctionapplicator (class in matminer.featurizers.conversions)": [[13, "matminer.featurizers.conversions.PymatgenFunctionApplicator"]], "sitedos (class in matminer.featurizers.dos)": [[13, "matminer.featurizers.dos.SiteDOS"]], "stackedfeaturizer (class in matminer.featurizers.base)": [[13, "matminer.featurizers.base.StackedFeaturizer"]], "strtocomposition (class in matminer.featurizers.conversions)": [[13, "matminer.featurizers.conversions.StrToComposition"]], "structuretocomposition (class in matminer.featurizers.conversions)": [[13, "matminer.featurizers.conversions.StructureToComposition"]], "structuretoistructure (class in matminer.featurizers.conversions)": [[13, "matminer.featurizers.conversions.StructureToIStructure"]], "structuretooxidstructure (class in matminer.featurizers.conversions)": [[13, "matminer.featurizers.conversions.StructureToOxidStructure"]], "__init__() (matminer.featurizers.bandstructure.bandfeaturizer method)": [[13, "matminer.featurizers.bandstructure.BandFeaturizer.__init__"]], "__init__() (matminer.featurizers.bandstructure.branchpointenergy method)": [[13, "matminer.featurizers.bandstructure.BranchPointEnergy.__init__"]], "__init__() (matminer.featurizers.base.multiplefeaturizer method)": [[13, "matminer.featurizers.base.MultipleFeaturizer.__init__"]], "__init__() (matminer.featurizers.base.stackedfeaturizer method)": [[13, "matminer.featurizers.base.StackedFeaturizer.__init__"]], "__init__() (matminer.featurizers.conversions.aseatomstostructure method)": [[13, "matminer.featurizers.conversions.ASEAtomstoStructure.__init__"]], "__init__() (matminer.featurizers.conversions.compositiontooxidcomposition method)": [[13, "matminer.featurizers.conversions.CompositionToOxidComposition.__init__"]], "__init__() (matminer.featurizers.conversions.compositiontostructurefrommp method)": [[13, "matminer.featurizers.conversions.CompositionToStructureFromMP.__init__"]], "__init__() (matminer.featurizers.conversions.conversionfeaturizer method)": [[13, "matminer.featurizers.conversions.ConversionFeaturizer.__init__"]], "__init__() (matminer.featurizers.conversions.dicttoobject method)": [[13, "matminer.featurizers.conversions.DictToObject.__init__"]], "__init__() (matminer.featurizers.conversions.jsontoobject method)": [[13, "matminer.featurizers.conversions.JsonToObject.__init__"]], "__init__() (matminer.featurizers.conversions.pymatgenfunctionapplicator method)": [[13, "matminer.featurizers.conversions.PymatgenFunctionApplicator.__init__"]], "__init__() (matminer.featurizers.conversions.strtocomposition method)": [[13, "matminer.featurizers.conversions.StrToComposition.__init__"]], "__init__() (matminer.featurizers.conversions.structuretocomposition method)": [[13, "matminer.featurizers.conversions.StructureToComposition.__init__"]], "__init__() (matminer.featurizers.conversions.structuretoistructure method)": [[13, "matminer.featurizers.conversions.StructureToIStructure.__init__"]], "__init__() (matminer.featurizers.conversions.structuretooxidstructure method)": [[13, "matminer.featurizers.conversions.StructureToOxidStructure.__init__"]], "__init__() (matminer.featurizers.dos.dosfeaturizer method)": [[13, "matminer.featurizers.dos.DOSFeaturizer.__init__"]], "__init__() (matminer.featurizers.dos.dopingfermi method)": [[13, "matminer.featurizers.dos.DopingFermi.__init__"]], "__init__() (matminer.featurizers.dos.dosasymmetry method)": [[13, "matminer.featurizers.dos.DosAsymmetry.__init__"]], "__init__() (matminer.featurizers.dos.hybridization method)": [[13, "matminer.featurizers.dos.Hybridization.__init__"]], "__init__() (matminer.featurizers.dos.sitedos method)": [[13, "matminer.featurizers.dos.SiteDOS.__init__"]], "__init__() (matminer.featurizers.function.functionfeaturizer method)": [[13, "matminer.featurizers.function.FunctionFeaturizer.__init__"]], "chunksize (matminer.featurizers.base.basefeaturizer property)": [[13, "matminer.featurizers.base.BaseFeaturizer.chunksize"]], "citations() (matminer.featurizers.bandstructure.bandfeaturizer method)": [[13, "matminer.featurizers.bandstructure.BandFeaturizer.citations"]], "citations() (matminer.featurizers.bandstructure.branchpointenergy method)": [[13, "matminer.featurizers.bandstructure.BranchPointEnergy.citations"]], "citations() (matminer.featurizers.base.basefeaturizer method)": [[13, "matminer.featurizers.base.BaseFeaturizer.citations"]], "citations() (matminer.featurizers.base.multiplefeaturizer method)": [[13, "matminer.featurizers.base.MultipleFeaturizer.citations"]], "citations() (matminer.featurizers.base.stackedfeaturizer method)": [[13, "matminer.featurizers.base.StackedFeaturizer.citations"]], "citations() (matminer.featurizers.conversions.compositiontooxidcomposition method)": [[13, "matminer.featurizers.conversions.CompositionToOxidComposition.citations"]], "citations() (matminer.featurizers.conversions.compositiontostructurefrommp method)": [[13, "matminer.featurizers.conversions.CompositionToStructureFromMP.citations"]], "citations() (matminer.featurizers.conversions.conversionfeaturizer method)": [[13, "matminer.featurizers.conversions.ConversionFeaturizer.citations"]], "citations() (matminer.featurizers.conversions.dicttoobject method)": [[13, "matminer.featurizers.conversions.DictToObject.citations"]], "citations() (matminer.featurizers.conversions.jsontoobject method)": [[13, "matminer.featurizers.conversions.JsonToObject.citations"]], "citations() (matminer.featurizers.conversions.strtocomposition method)": [[13, "matminer.featurizers.conversions.StrToComposition.citations"]], "citations() (matminer.featurizers.conversions.structuretocomposition method)": [[13, "matminer.featurizers.conversions.StructureToComposition.citations"]], "citations() (matminer.featurizers.conversions.structuretoistructure method)": [[13, "matminer.featurizers.conversions.StructureToIStructure.citations"]], "citations() (matminer.featurizers.conversions.structuretooxidstructure method)": [[13, "matminer.featurizers.conversions.StructureToOxidStructure.citations"]], "citations() (matminer.featurizers.dos.dosfeaturizer method)": [[13, "matminer.featurizers.dos.DOSFeaturizer.citations"]], "citations() (matminer.featurizers.dos.dopingfermi method)": [[13, "matminer.featurizers.dos.DopingFermi.citations"]], "citations() (matminer.featurizers.dos.dosasymmetry method)": [[13, "matminer.featurizers.dos.DosAsymmetry.citations"]], "citations() (matminer.featurizers.dos.hybridization method)": [[13, "matminer.featurizers.dos.Hybridization.citations"]], "citations() (matminer.featurizers.dos.sitedos method)": [[13, "matminer.featurizers.dos.SiteDOS.citations"]], "citations() (matminer.featurizers.function.functionfeaturizer method)": [[13, "matminer.featurizers.function.FunctionFeaturizer.citations"]], "exp_dict (matminer.featurizers.function.functionfeaturizer property)": [[13, "matminer.featurizers.function.FunctionFeaturizer.exp_dict"]], "feature_labels() (matminer.featurizers.bandstructure.bandfeaturizer method)": [[13, "matminer.featurizers.bandstructure.BandFeaturizer.feature_labels"]], "feature_labels() (matminer.featurizers.bandstructure.branchpointenergy method)": [[13, "matminer.featurizers.bandstructure.BranchPointEnergy.feature_labels"]], "feature_labels() (matminer.featurizers.base.basefeaturizer method)": [[13, "matminer.featurizers.base.BaseFeaturizer.feature_labels"]], "feature_labels() (matminer.featurizers.base.multiplefeaturizer method)": [[13, "matminer.featurizers.base.MultipleFeaturizer.feature_labels"]], "feature_labels() (matminer.featurizers.base.stackedfeaturizer method)": [[13, "matminer.featurizers.base.StackedFeaturizer.feature_labels"]], "feature_labels() (matminer.featurizers.conversions.conversionfeaturizer method)": [[13, "matminer.featurizers.conversions.ConversionFeaturizer.feature_labels"]], "feature_labels() (matminer.featurizers.dos.dosfeaturizer method)": [[13, "matminer.featurizers.dos.DOSFeaturizer.feature_labels"]], "feature_labels() (matminer.featurizers.dos.dopingfermi method)": [[13, "matminer.featurizers.dos.DopingFermi.feature_labels"]], "feature_labels() (matminer.featurizers.dos.dosasymmetry method)": [[13, "matminer.featurizers.dos.DosAsymmetry.feature_labels"]], "feature_labels() (matminer.featurizers.dos.hybridization method)": [[13, "matminer.featurizers.dos.Hybridization.feature_labels"]], "feature_labels() (matminer.featurizers.dos.sitedos method)": [[13, "matminer.featurizers.dos.SiteDOS.feature_labels"]], "feature_labels() (matminer.featurizers.function.functionfeaturizer method)": [[13, "matminer.featurizers.function.FunctionFeaturizer.feature_labels"]], "featurize() (matminer.featurizers.bandstructure.bandfeaturizer method)": [[13, "matminer.featurizers.bandstructure.BandFeaturizer.featurize"]], "featurize() (matminer.featurizers.bandstructure.branchpointenergy method)": [[13, "matminer.featurizers.bandstructure.BranchPointEnergy.featurize"]], "featurize() (matminer.featurizers.base.basefeaturizer method)": [[13, "matminer.featurizers.base.BaseFeaturizer.featurize"]], "featurize() (matminer.featurizers.base.multiplefeaturizer method)": [[13, "matminer.featurizers.base.MultipleFeaturizer.featurize"]], "featurize() (matminer.featurizers.base.stackedfeaturizer method)": [[13, "matminer.featurizers.base.StackedFeaturizer.featurize"]], "featurize() (matminer.featurizers.conversions.aseatomstostructure method)": [[13, "matminer.featurizers.conversions.ASEAtomstoStructure.featurize"]], "featurize() (matminer.featurizers.conversions.compositiontooxidcomposition method)": [[13, "matminer.featurizers.conversions.CompositionToOxidComposition.featurize"]], "featurize() (matminer.featurizers.conversions.compositiontostructurefrommp method)": [[13, "matminer.featurizers.conversions.CompositionToStructureFromMP.featurize"]], "featurize() (matminer.featurizers.conversions.conversionfeaturizer method)": [[13, "matminer.featurizers.conversions.ConversionFeaturizer.featurize"]], "featurize() (matminer.featurizers.conversions.dicttoobject method)": [[13, "matminer.featurizers.conversions.DictToObject.featurize"]], "featurize() (matminer.featurizers.conversions.jsontoobject method)": [[13, "matminer.featurizers.conversions.JsonToObject.featurize"]], "featurize() (matminer.featurizers.conversions.pymatgenfunctionapplicator method)": [[13, "matminer.featurizers.conversions.PymatgenFunctionApplicator.featurize"]], "featurize() (matminer.featurizers.conversions.strtocomposition method)": [[13, "matminer.featurizers.conversions.StrToComposition.featurize"]], "featurize() (matminer.featurizers.conversions.structuretocomposition method)": [[13, "matminer.featurizers.conversions.StructureToComposition.featurize"]], "featurize() (matminer.featurizers.conversions.structuretoistructure method)": [[13, "matminer.featurizers.conversions.StructureToIStructure.featurize"]], "featurize() (matminer.featurizers.conversions.structuretooxidstructure method)": [[13, "matminer.featurizers.conversions.StructureToOxidStructure.featurize"]], "featurize() (matminer.featurizers.dos.dosfeaturizer method)": [[13, "matminer.featurizers.dos.DOSFeaturizer.featurize"]], "featurize() (matminer.featurizers.dos.dopingfermi method)": [[13, "matminer.featurizers.dos.DopingFermi.featurize"]], "featurize() (matminer.featurizers.dos.dosasymmetry method)": [[13, "matminer.featurizers.dos.DosAsymmetry.featurize"]], "featurize() (matminer.featurizers.dos.hybridization method)": [[13, "matminer.featurizers.dos.Hybridization.featurize"]], "featurize() (matminer.featurizers.dos.sitedos method)": [[13, "matminer.featurizers.dos.SiteDOS.featurize"]], "featurize() (matminer.featurizers.function.functionfeaturizer method)": [[13, "matminer.featurizers.function.FunctionFeaturizer.featurize"]], "featurize_dataframe() (matminer.featurizers.base.basefeaturizer method)": [[13, "matminer.featurizers.base.BaseFeaturizer.featurize_dataframe"]], "featurize_dataframe() (matminer.featurizers.conversions.conversionfeaturizer method)": [[13, "matminer.featurizers.conversions.ConversionFeaturizer.featurize_dataframe"]], "featurize_many() (matminer.featurizers.base.basefeaturizer method)": [[13, "matminer.featurizers.base.BaseFeaturizer.featurize_many"]], "featurize_many() (matminer.featurizers.base.multiplefeaturizer method)": [[13, "matminer.featurizers.base.MultipleFeaturizer.featurize_many"]], "featurize_wrapper() (matminer.featurizers.base.basefeaturizer method)": [[13, "matminer.featurizers.base.BaseFeaturizer.featurize_wrapper"]], "featurize_wrapper() (matminer.featurizers.base.multiplefeaturizer method)": [[13, "matminer.featurizers.base.MultipleFeaturizer.featurize_wrapper"]], "fit() (matminer.featurizers.base.basefeaturizer method)": [[13, "matminer.featurizers.base.BaseFeaturizer.fit"]], "fit() (matminer.featurizers.base.multiplefeaturizer method)": [[13, "matminer.featurizers.base.MultipleFeaturizer.fit"]], "fit() (matminer.featurizers.function.functionfeaturizer method)": [[13, "matminer.featurizers.function.FunctionFeaturizer.fit"]], "fit_featurize_dataframe() (matminer.featurizers.base.basefeaturizer method)": [[13, "matminer.featurizers.base.BaseFeaturizer.fit_featurize_dataframe"]], "generate_expressions_combinations() (in module matminer.featurizers.function)": [[13, "matminer.featurizers.function.generate_expressions_combinations"]], "generate_string_expressions() (matminer.featurizers.function.functionfeaturizer method)": [[13, "matminer.featurizers.function.FunctionFeaturizer.generate_string_expressions"]], "get_bindex_bspin() (matminer.featurizers.bandstructure.bandfeaturizer static method)": [[13, "matminer.featurizers.bandstructure.BandFeaturizer.get_bindex_bspin"]], "get_cbm_vbm_scores() (in module matminer.featurizers.dos)": [[13, "matminer.featurizers.dos.get_cbm_vbm_scores"]], "get_site_dos_scores() (in module matminer.featurizers.dos)": [[13, "matminer.featurizers.dos.get_site_dos_scores"]], "implementors() (matminer.featurizers.bandstructure.bandfeaturizer method)": [[13, "matminer.featurizers.bandstructure.BandFeaturizer.implementors"]], "implementors() (matminer.featurizers.bandstructure.branchpointenergy method)": [[13, "matminer.featurizers.bandstructure.BranchPointEnergy.implementors"]], "implementors() (matminer.featurizers.base.basefeaturizer method)": [[13, "matminer.featurizers.base.BaseFeaturizer.implementors"]], "implementors() (matminer.featurizers.base.multiplefeaturizer method)": [[13, "matminer.featurizers.base.MultipleFeaturizer.implementors"]], "implementors() (matminer.featurizers.base.stackedfeaturizer method)": [[13, "matminer.featurizers.base.StackedFeaturizer.implementors"]], "implementors() (matminer.featurizers.conversions.aseatomstostructure method)": [[13, "matminer.featurizers.conversions.ASEAtomstoStructure.implementors"]], "implementors() (matminer.featurizers.conversions.compositiontooxidcomposition method)": [[13, "matminer.featurizers.conversions.CompositionToOxidComposition.implementors"]], "implementors() (matminer.featurizers.conversions.compositiontostructurefrommp method)": [[13, "matminer.featurizers.conversions.CompositionToStructureFromMP.implementors"]], "implementors() (matminer.featurizers.conversions.conversionfeaturizer method)": [[13, "matminer.featurizers.conversions.ConversionFeaturizer.implementors"]], "implementors() (matminer.featurizers.conversions.dicttoobject method)": [[13, "matminer.featurizers.conversions.DictToObject.implementors"]], "implementors() (matminer.featurizers.conversions.jsontoobject method)": [[13, "matminer.featurizers.conversions.JsonToObject.implementors"]], "implementors() (matminer.featurizers.conversions.pymatgenfunctionapplicator method)": [[13, "matminer.featurizers.conversions.PymatgenFunctionApplicator.implementors"]], "implementors() (matminer.featurizers.conversions.strtocomposition method)": [[13, "matminer.featurizers.conversions.StrToComposition.implementors"]], "implementors() (matminer.featurizers.conversions.structuretocomposition method)": [[13, "matminer.featurizers.conversions.StructureToComposition.implementors"]], "implementors() (matminer.featurizers.conversions.structuretoistructure method)": [[13, "matminer.featurizers.conversions.StructureToIStructure.implementors"]], "implementors() (matminer.featurizers.conversions.structuretooxidstructure method)": [[13, "matminer.featurizers.conversions.StructureToOxidStructure.implementors"]], "implementors() (matminer.featurizers.dos.dosfeaturizer method)": [[13, "matminer.featurizers.dos.DOSFeaturizer.implementors"]], "implementors() (matminer.featurizers.dos.dopingfermi method)": [[13, "matminer.featurizers.dos.DopingFermi.implementors"]], "implementors() (matminer.featurizers.dos.dosasymmetry method)": [[13, "matminer.featurizers.dos.DosAsymmetry.implementors"]], "implementors() (matminer.featurizers.dos.hybridization method)": [[13, "matminer.featurizers.dos.Hybridization.implementors"]], "implementors() (matminer.featurizers.dos.sitedos method)": [[13, "matminer.featurizers.dos.SiteDOS.implementors"]], "implementors() (matminer.featurizers.function.functionfeaturizer method)": [[13, "matminer.featurizers.function.FunctionFeaturizer.implementors"]], "matminer.featurizers": [[13, "module-matminer.featurizers"]], "matminer.featurizers.bandstructure": [[13, "module-matminer.featurizers.bandstructure"]], "matminer.featurizers.base": [[13, "module-matminer.featurizers.base"]], "matminer.featurizers.conversions": [[13, "module-matminer.featurizers.conversions"]], "matminer.featurizers.dos": [[13, "module-matminer.featurizers.dos"]], "matminer.featurizers.function": [[13, "module-matminer.featurizers.function"]], "n_jobs (matminer.featurizers.base.basefeaturizer property)": [[13, "matminer.featurizers.base.BaseFeaturizer.n_jobs"]], "precheck() (matminer.featurizers.base.basefeaturizer method)": [[13, "matminer.featurizers.base.BaseFeaturizer.precheck"]], "precheck_dataframe() (matminer.featurizers.base.basefeaturizer method)": [[13, "matminer.featurizers.base.BaseFeaturizer.precheck_dataframe"]], "set_chunksize() (matminer.featurizers.base.basefeaturizer method)": [[13, "matminer.featurizers.base.BaseFeaturizer.set_chunksize"]], "set_n_jobs() (matminer.featurizers.base.basefeaturizer method)": [[13, "matminer.featurizers.base.BaseFeaturizer.set_n_jobs"]], "set_n_jobs() (matminer.featurizers.base.multiplefeaturizer method)": [[13, "matminer.featurizers.base.MultipleFeaturizer.set_n_jobs"]], "transform() (matminer.featurizers.base.basefeaturizer method)": [[13, "matminer.featurizers.base.BaseFeaturizer.transform"]], "atomicorbitals (class in matminer.featurizers.composition.orbital)": [[14, "matminer.featurizers.composition.orbital.AtomicOrbitals"]], "atomicpackingefficiency (class in matminer.featurizers.composition.packing)": [[14, "matminer.featurizers.composition.packing.AtomicPackingEfficiency"]], "bandcenter (class in matminer.featurizers.composition.element)": [[14, "matminer.featurizers.composition.element.BandCenter"]], "cationproperty (class in matminer.featurizers.composition.ion)": [[14, "matminer.featurizers.composition.ion.CationProperty"]], "cohesiveenergy (class in matminer.featurizers.composition.thermo)": [[14, "matminer.featurizers.composition.thermo.CohesiveEnergy"]], "cohesiveenergymp (class in matminer.featurizers.composition.thermo)": [[14, "matminer.featurizers.composition.thermo.CohesiveEnergyMP"]], "electronaffinity (class in matminer.featurizers.composition.ion)": [[14, "matminer.featurizers.composition.ion.ElectronAffinity"]], "electronegativitydiff (class in matminer.featurizers.composition.ion)": [[14, "matminer.featurizers.composition.ion.ElectronegativityDiff"]], "elementfraction (class in matminer.featurizers.composition.element)": [[14, "matminer.featurizers.composition.element.ElementFraction"]], "elementproperty (class in matminer.featurizers.composition.composite)": [[14, "matminer.featurizers.composition.composite.ElementProperty"]], "ionproperty (class in matminer.featurizers.composition.ion)": [[14, "matminer.featurizers.composition.ion.IonProperty"]], "meredig (class in matminer.featurizers.composition.composite)": [[14, "matminer.featurizers.composition.composite.Meredig"]], "miedema (class in matminer.featurizers.composition.alloy)": [[14, "matminer.featurizers.composition.alloy.Miedema"]], "oxidationstates (class in matminer.featurizers.composition.ion)": [[14, "matminer.featurizers.composition.ion.OxidationStates"]], "stoichiometry (class in matminer.featurizers.composition.element)": [[14, "matminer.featurizers.composition.element.Stoichiometry"]], "tmetalfraction (class in matminer.featurizers.composition.element)": [[14, "matminer.featurizers.composition.element.TMetalFraction"]], "valenceorbital (class in matminer.featurizers.composition.orbital)": [[14, "matminer.featurizers.composition.orbital.ValenceOrbital"]], "wenalloys (class in matminer.featurizers.composition.alloy)": [[14, "matminer.featurizers.composition.alloy.WenAlloys"]], "yangsolidsolution (class in matminer.featurizers.composition.alloy)": [[14, "matminer.featurizers.composition.alloy.YangSolidSolution"]], "__init__() (matminer.featurizers.composition.alloy.miedema method)": [[14, "matminer.featurizers.composition.alloy.Miedema.__init__"]], "__init__() (matminer.featurizers.composition.alloy.wenalloys method)": [[14, "matminer.featurizers.composition.alloy.WenAlloys.__init__"]], "__init__() (matminer.featurizers.composition.alloy.yangsolidsolution method)": [[14, "matminer.featurizers.composition.alloy.YangSolidSolution.__init__"]], "__init__() (matminer.featurizers.composition.composite.elementproperty method)": [[14, "matminer.featurizers.composition.composite.ElementProperty.__init__"]], "__init__() (matminer.featurizers.composition.composite.meredig method)": [[14, "matminer.featurizers.composition.composite.Meredig.__init__"]], "__init__() (matminer.featurizers.composition.element.elementfraction method)": [[14, "matminer.featurizers.composition.element.ElementFraction.__init__"]], "__init__() (matminer.featurizers.composition.element.stoichiometry method)": [[14, "matminer.featurizers.composition.element.Stoichiometry.__init__"]], "__init__() (matminer.featurizers.composition.element.tmetalfraction method)": [[14, "matminer.featurizers.composition.element.TMetalFraction.__init__"]], "__init__() (matminer.featurizers.composition.ion.electronaffinity method)": [[14, "matminer.featurizers.composition.ion.ElectronAffinity.__init__"]], "__init__() (matminer.featurizers.composition.ion.electronegativitydiff method)": [[14, "matminer.featurizers.composition.ion.ElectronegativityDiff.__init__"]], "__init__() (matminer.featurizers.composition.ion.ionproperty method)": [[14, "matminer.featurizers.composition.ion.IonProperty.__init__"]], "__init__() (matminer.featurizers.composition.ion.oxidationstates method)": [[14, "matminer.featurizers.composition.ion.OxidationStates.__init__"]], "__init__() (matminer.featurizers.composition.orbital.valenceorbital method)": [[14, "matminer.featurizers.composition.orbital.ValenceOrbital.__init__"]], "__init__() (matminer.featurizers.composition.packing.atomicpackingefficiency method)": [[14, "matminer.featurizers.composition.packing.AtomicPackingEfficiency.__init__"]], "__init__() (matminer.featurizers.composition.thermo.cohesiveenergy method)": [[14, "matminer.featurizers.composition.thermo.CohesiveEnergy.__init__"]], "__init__() (matminer.featurizers.composition.thermo.cohesiveenergymp method)": [[14, "matminer.featurizers.composition.thermo.CohesiveEnergyMP.__init__"]], "citations() (matminer.featurizers.composition.alloy.miedema method)": [[14, "matminer.featurizers.composition.alloy.Miedema.citations"]], "citations() (matminer.featurizers.composition.alloy.wenalloys method)": [[14, "matminer.featurizers.composition.alloy.WenAlloys.citations"]], "citations() (matminer.featurizers.composition.alloy.yangsolidsolution method)": [[14, "matminer.featurizers.composition.alloy.YangSolidSolution.citations"]], "citations() (matminer.featurizers.composition.composite.elementproperty method)": [[14, "matminer.featurizers.composition.composite.ElementProperty.citations"]], "citations() (matminer.featurizers.composition.composite.meredig method)": [[14, "matminer.featurizers.composition.composite.Meredig.citations"]], "citations() (matminer.featurizers.composition.element.bandcenter method)": [[14, "matminer.featurizers.composition.element.BandCenter.citations"]], "citations() (matminer.featurizers.composition.element.elementfraction method)": [[14, "matminer.featurizers.composition.element.ElementFraction.citations"]], "citations() (matminer.featurizers.composition.element.stoichiometry method)": [[14, "matminer.featurizers.composition.element.Stoichiometry.citations"]], "citations() (matminer.featurizers.composition.element.tmetalfraction method)": [[14, "matminer.featurizers.composition.element.TMetalFraction.citations"]], "citations() (matminer.featurizers.composition.ion.cationproperty method)": [[14, "matminer.featurizers.composition.ion.CationProperty.citations"]], "citations() (matminer.featurizers.composition.ion.electronaffinity method)": [[14, "matminer.featurizers.composition.ion.ElectronAffinity.citations"]], "citations() (matminer.featurizers.composition.ion.electronegativitydiff method)": [[14, "matminer.featurizers.composition.ion.ElectronegativityDiff.citations"]], "citations() (matminer.featurizers.composition.ion.ionproperty method)": [[14, "matminer.featurizers.composition.ion.IonProperty.citations"]], "citations() (matminer.featurizers.composition.ion.oxidationstates method)": [[14, "matminer.featurizers.composition.ion.OxidationStates.citations"]], "citations() (matminer.featurizers.composition.orbital.atomicorbitals method)": [[14, "matminer.featurizers.composition.orbital.AtomicOrbitals.citations"]], "citations() (matminer.featurizers.composition.orbital.valenceorbital method)": [[14, "matminer.featurizers.composition.orbital.ValenceOrbital.citations"]], "citations() (matminer.featurizers.composition.packing.atomicpackingefficiency method)": [[14, "matminer.featurizers.composition.packing.AtomicPackingEfficiency.citations"]], "citations() (matminer.featurizers.composition.thermo.cohesiveenergy method)": [[14, "matminer.featurizers.composition.thermo.CohesiveEnergy.citations"]], "citations() (matminer.featurizers.composition.thermo.cohesiveenergymp method)": [[14, "matminer.featurizers.composition.thermo.CohesiveEnergyMP.citations"]], "compute_atomic_fraction() (matminer.featurizers.composition.alloy.wenalloys static method)": [[14, "matminer.featurizers.composition.alloy.WenAlloys.compute_atomic_fraction"]], "compute_configuration_entropy() (matminer.featurizers.composition.alloy.wenalloys static method)": [[14, "matminer.featurizers.composition.alloy.WenAlloys.compute_configuration_entropy"]], "compute_delta() (matminer.featurizers.composition.alloy.wenalloys static method)": [[14, "matminer.featurizers.composition.alloy.WenAlloys.compute_delta"]], "compute_delta() (matminer.featurizers.composition.alloy.yangsolidsolution method)": [[14, "matminer.featurizers.composition.alloy.YangSolidSolution.compute_delta"]], "compute_enthalpy() (matminer.featurizers.composition.alloy.wenalloys method)": [[14, "matminer.featurizers.composition.alloy.WenAlloys.compute_enthalpy"]], "compute_gamma_radii() (matminer.featurizers.composition.alloy.wenalloys static method)": [[14, "matminer.featurizers.composition.alloy.WenAlloys.compute_gamma_radii"]], "compute_lambda() (matminer.featurizers.composition.alloy.wenalloys static method)": [[14, "matminer.featurizers.composition.alloy.WenAlloys.compute_lambda"]], "compute_local_mismatch() (matminer.featurizers.composition.alloy.wenalloys static method)": [[14, "matminer.featurizers.composition.alloy.WenAlloys.compute_local_mismatch"]], "compute_magpie_summary() (matminer.featurizers.composition.alloy.wenalloys method)": [[14, "matminer.featurizers.composition.alloy.WenAlloys.compute_magpie_summary"]], "compute_nearest_cluster_distance() (matminer.featurizers.composition.packing.atomicpackingefficiency method)": [[14, "matminer.featurizers.composition.packing.AtomicPackingEfficiency.compute_nearest_cluster_distance"]], "compute_omega() (matminer.featurizers.composition.alloy.yangsolidsolution method)": [[14, "matminer.featurizers.composition.alloy.YangSolidSolution.compute_omega"]], "compute_simultaneous_packing_efficiency() (matminer.featurizers.composition.packing.atomicpackingefficiency method)": [[14, "matminer.featurizers.composition.packing.AtomicPackingEfficiency.compute_simultaneous_packing_efficiency"]], "compute_strength_local_mismatch_shear() (matminer.featurizers.composition.alloy.wenalloys static method)": [[14, "matminer.featurizers.composition.alloy.WenAlloys.compute_strength_local_mismatch_shear"]], "compute_weight_fraction() (matminer.featurizers.composition.alloy.wenalloys static method)": [[14, "matminer.featurizers.composition.alloy.WenAlloys.compute_weight_fraction"]], "create_cluster_lookup_tool() (matminer.featurizers.composition.packing.atomicpackingefficiency method)": [[14, "matminer.featurizers.composition.packing.AtomicPackingEfficiency.create_cluster_lookup_tool"]], "deltah_chem() (matminer.featurizers.composition.alloy.miedema method)": [[14, "matminer.featurizers.composition.alloy.Miedema.deltaH_chem"]], "deltah_elast() (matminer.featurizers.composition.alloy.miedema method)": [[14, "matminer.featurizers.composition.alloy.Miedema.deltaH_elast"]], "deltah_struct() (matminer.featurizers.composition.alloy.miedema method)": [[14, "matminer.featurizers.composition.alloy.Miedema.deltaH_struct"]], "deltah_topo() (matminer.featurizers.composition.alloy.miedema method)": [[14, "matminer.featurizers.composition.alloy.Miedema.deltaH_topo"]], "deml_data (matminer.featurizers.composition.element.bandcenter attribute)": [[14, "matminer.featurizers.composition.element.BandCenter.deml_data"]], "feature_labels() (matminer.featurizers.composition.alloy.miedema method)": [[14, "matminer.featurizers.composition.alloy.Miedema.feature_labels"]], "feature_labels() (matminer.featurizers.composition.alloy.wenalloys method)": [[14, "matminer.featurizers.composition.alloy.WenAlloys.feature_labels"]], "feature_labels() (matminer.featurizers.composition.alloy.yangsolidsolution method)": [[14, "matminer.featurizers.composition.alloy.YangSolidSolution.feature_labels"]], "feature_labels() (matminer.featurizers.composition.composite.elementproperty method)": [[14, "matminer.featurizers.composition.composite.ElementProperty.feature_labels"]], "feature_labels() (matminer.featurizers.composition.composite.meredig method)": [[14, "matminer.featurizers.composition.composite.Meredig.feature_labels"]], "feature_labels() (matminer.featurizers.composition.element.bandcenter method)": [[14, "matminer.featurizers.composition.element.BandCenter.feature_labels"]], "feature_labels() (matminer.featurizers.composition.element.elementfraction method)": [[14, "matminer.featurizers.composition.element.ElementFraction.feature_labels"]], "feature_labels() (matminer.featurizers.composition.element.stoichiometry method)": [[14, "matminer.featurizers.composition.element.Stoichiometry.feature_labels"]], "feature_labels() (matminer.featurizers.composition.element.tmetalfraction method)": [[14, "matminer.featurizers.composition.element.TMetalFraction.feature_labels"]], "feature_labels() (matminer.featurizers.composition.ion.cationproperty method)": [[14, "matminer.featurizers.composition.ion.CationProperty.feature_labels"]], "feature_labels() (matminer.featurizers.composition.ion.electronaffinity method)": [[14, "matminer.featurizers.composition.ion.ElectronAffinity.feature_labels"]], "feature_labels() (matminer.featurizers.composition.ion.electronegativitydiff method)": [[14, "matminer.featurizers.composition.ion.ElectronegativityDiff.feature_labels"]], "feature_labels() (matminer.featurizers.composition.ion.ionproperty method)": [[14, "matminer.featurizers.composition.ion.IonProperty.feature_labels"]], "feature_labels() (matminer.featurizers.composition.ion.oxidationstates method)": [[14, "matminer.featurizers.composition.ion.OxidationStates.feature_labels"]], "feature_labels() (matminer.featurizers.composition.orbital.atomicorbitals method)": [[14, "matminer.featurizers.composition.orbital.AtomicOrbitals.feature_labels"]], "feature_labels() (matminer.featurizers.composition.orbital.valenceorbital method)": [[14, "matminer.featurizers.composition.orbital.ValenceOrbital.feature_labels"]], "feature_labels() (matminer.featurizers.composition.packing.atomicpackingefficiency method)": [[14, "matminer.featurizers.composition.packing.AtomicPackingEfficiency.feature_labels"]], "feature_labels() (matminer.featurizers.composition.thermo.cohesiveenergy method)": [[14, "matminer.featurizers.composition.thermo.CohesiveEnergy.feature_labels"]], "feature_labels() (matminer.featurizers.composition.thermo.cohesiveenergymp method)": [[14, "matminer.featurizers.composition.thermo.CohesiveEnergyMP.feature_labels"]], "featurize() (matminer.featurizers.composition.alloy.miedema method)": [[14, "matminer.featurizers.composition.alloy.Miedema.featurize"]], "featurize() (matminer.featurizers.composition.alloy.wenalloys method)": [[14, "matminer.featurizers.composition.alloy.WenAlloys.featurize"]], "featurize() (matminer.featurizers.composition.alloy.yangsolidsolution method)": [[14, "matminer.featurizers.composition.alloy.YangSolidSolution.featurize"]], "featurize() (matminer.featurizers.composition.composite.elementproperty method)": [[14, "matminer.featurizers.composition.composite.ElementProperty.featurize"]], "featurize() (matminer.featurizers.composition.composite.meredig method)": [[14, "matminer.featurizers.composition.composite.Meredig.featurize"]], "featurize() (matminer.featurizers.composition.element.bandcenter method)": [[14, "matminer.featurizers.composition.element.BandCenter.featurize"]], "featurize() (matminer.featurizers.composition.element.elementfraction method)": [[14, "matminer.featurizers.composition.element.ElementFraction.featurize"]], "featurize() (matminer.featurizers.composition.element.stoichiometry method)": [[14, "matminer.featurizers.composition.element.Stoichiometry.featurize"]], "featurize() (matminer.featurizers.composition.element.tmetalfraction method)": [[14, "matminer.featurizers.composition.element.TMetalFraction.featurize"]], "featurize() (matminer.featurizers.composition.ion.cationproperty method)": [[14, "matminer.featurizers.composition.ion.CationProperty.featurize"]], "featurize() (matminer.featurizers.composition.ion.electronaffinity method)": [[14, "matminer.featurizers.composition.ion.ElectronAffinity.featurize"]], "featurize() (matminer.featurizers.composition.ion.electronegativitydiff method)": [[14, "matminer.featurizers.composition.ion.ElectronegativityDiff.featurize"]], "featurize() (matminer.featurizers.composition.ion.ionproperty method)": [[14, "matminer.featurizers.composition.ion.IonProperty.featurize"]], "featurize() (matminer.featurizers.composition.ion.oxidationstates method)": [[14, "matminer.featurizers.composition.ion.OxidationStates.featurize"]], "featurize() (matminer.featurizers.composition.orbital.atomicorbitals method)": [[14, "matminer.featurizers.composition.orbital.AtomicOrbitals.featurize"]], "featurize() (matminer.featurizers.composition.orbital.valenceorbital method)": [[14, "matminer.featurizers.composition.orbital.ValenceOrbital.featurize"]], "featurize() (matminer.featurizers.composition.packing.atomicpackingefficiency method)": [[14, "matminer.featurizers.composition.packing.AtomicPackingEfficiency.featurize"]], "featurize() (matminer.featurizers.composition.thermo.cohesiveenergy method)": [[14, "matminer.featurizers.composition.thermo.CohesiveEnergy.featurize"]], "featurize() (matminer.featurizers.composition.thermo.cohesiveenergymp method)": [[14, "matminer.featurizers.composition.thermo.CohesiveEnergyMP.featurize"]], "find_ideal_cluster_size() (matminer.featurizers.composition.packing.atomicpackingefficiency method)": [[14, "matminer.featurizers.composition.packing.AtomicPackingEfficiency.find_ideal_cluster_size"]], "from_preset() (matminer.featurizers.composition.composite.elementproperty class method)": [[14, "matminer.featurizers.composition.composite.ElementProperty.from_preset"]], "from_preset() (matminer.featurizers.composition.ion.cationproperty class method)": [[14, "matminer.featurizers.composition.ion.CationProperty.from_preset"]], "from_preset() (matminer.featurizers.composition.ion.oxidationstates class method)": [[14, "matminer.featurizers.composition.ion.OxidationStates.from_preset"]], "get_ideal_radius_ratio() (matminer.featurizers.composition.packing.atomicpackingefficiency method)": [[14, "matminer.featurizers.composition.packing.AtomicPackingEfficiency.get_ideal_radius_ratio"]], "implementors() (matminer.featurizers.composition.alloy.miedema method)": [[14, "matminer.featurizers.composition.alloy.Miedema.implementors"]], "implementors() (matminer.featurizers.composition.alloy.wenalloys method)": [[14, "matminer.featurizers.composition.alloy.WenAlloys.implementors"]], "implementors() (matminer.featurizers.composition.alloy.yangsolidsolution method)": [[14, "matminer.featurizers.composition.alloy.YangSolidSolution.implementors"]], "implementors() (matminer.featurizers.composition.composite.elementproperty method)": [[14, "matminer.featurizers.composition.composite.ElementProperty.implementors"]], "implementors() (matminer.featurizers.composition.composite.meredig method)": [[14, "matminer.featurizers.composition.composite.Meredig.implementors"]], "implementors() (matminer.featurizers.composition.element.bandcenter method)": [[14, "matminer.featurizers.composition.element.BandCenter.implementors"]], "implementors() (matminer.featurizers.composition.element.elementfraction method)": [[14, "matminer.featurizers.composition.element.ElementFraction.implementors"]], "implementors() (matminer.featurizers.composition.element.stoichiometry method)": [[14, "matminer.featurizers.composition.element.Stoichiometry.implementors"]], "implementors() (matminer.featurizers.composition.element.tmetalfraction method)": [[14, "matminer.featurizers.composition.element.TMetalFraction.implementors"]], "implementors() (matminer.featurizers.composition.ion.electronaffinity method)": [[14, "matminer.featurizers.composition.ion.ElectronAffinity.implementors"]], "implementors() (matminer.featurizers.composition.ion.electronegativitydiff method)": [[14, "matminer.featurizers.composition.ion.ElectronegativityDiff.implementors"]], "implementors() (matminer.featurizers.composition.ion.ionproperty method)": [[14, "matminer.featurizers.composition.ion.IonProperty.implementors"]], "implementors() (matminer.featurizers.composition.ion.oxidationstates method)": [[14, "matminer.featurizers.composition.ion.OxidationStates.implementors"]], "implementors() (matminer.featurizers.composition.orbital.atomicorbitals method)": [[14, "matminer.featurizers.composition.orbital.AtomicOrbitals.implementors"]], "implementors() (matminer.featurizers.composition.orbital.valenceorbital method)": [[14, "matminer.featurizers.composition.orbital.ValenceOrbital.implementors"]], "implementors() (matminer.featurizers.composition.packing.atomicpackingefficiency method)": [[14, "matminer.featurizers.composition.packing.AtomicPackingEfficiency.implementors"]], "implementors() (matminer.featurizers.composition.thermo.cohesiveenergy method)": [[14, "matminer.featurizers.composition.thermo.CohesiveEnergy.implementors"]], "implementors() (matminer.featurizers.composition.thermo.cohesiveenergymp method)": [[14, "matminer.featurizers.composition.thermo.CohesiveEnergyMP.implementors"]], "is_ionic() (in module matminer.featurizers.composition.ion)": [[14, "matminer.featurizers.composition.ion.is_ionic"]], "magpie_data (matminer.featurizers.composition.element.bandcenter attribute)": [[14, "matminer.featurizers.composition.element.BandCenter.magpie_data"]], "matminer.featurizers.composition": [[14, "module-matminer.featurizers.composition"]], "matminer.featurizers.composition.alloy": [[14, "module-matminer.featurizers.composition.alloy"]], "matminer.featurizers.composition.composite": [[14, "module-matminer.featurizers.composition.composite"]], "matminer.featurizers.composition.element": [[14, "module-matminer.featurizers.composition.element"]], "matminer.featurizers.composition.ion": [[14, "module-matminer.featurizers.composition.ion"]], "matminer.featurizers.composition.orbital": [[14, "module-matminer.featurizers.composition.orbital"]], "matminer.featurizers.composition.packing": [[14, "module-matminer.featurizers.composition.packing"]], "matminer.featurizers.composition.thermo": [[14, "module-matminer.featurizers.composition.thermo"]], "precheck() (matminer.featurizers.composition.alloy.miedema method)": [[14, "matminer.featurizers.composition.alloy.Miedema.precheck"]], "precheck() (matminer.featurizers.composition.alloy.wenalloys method)": [[14, "matminer.featurizers.composition.alloy.WenAlloys.precheck"]], "precheck() (matminer.featurizers.composition.alloy.yangsolidsolution method)": [[14, "matminer.featurizers.composition.alloy.YangSolidSolution.precheck"]], "alloyfeaturizerstest (class in matminer.featurizers.composition.tests.test_alloy)": [[15, "matminer.featurizers.composition.tests.test_alloy.AlloyFeaturizersTest"]], "compositefeaturestest (class in matminer.featurizers.composition.tests.test_composite)": [[15, "matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest"]], "compositionfeaturestest (class in matminer.featurizers.composition.tests.base)": [[15, "matminer.featurizers.composition.tests.base.CompositionFeaturesTest"]], "elementfeaturestest (class in matminer.featurizers.composition.tests.test_element)": [[15, "matminer.featurizers.composition.tests.test_element.ElementFeaturesTest"]], "ionfeaturestest (class in matminer.featurizers.composition.tests.test_ion)": [[15, "matminer.featurizers.composition.tests.test_ion.IonFeaturesTest"]], "orbitalfeaturestest (class in matminer.featurizers.composition.tests.test_orbital)": [[15, "matminer.featurizers.composition.tests.test_orbital.OrbitalFeaturesTest"]], "packingfeaturestest (class in matminer.featurizers.composition.tests.test_packing)": [[15, "matminer.featurizers.composition.tests.test_packing.PackingFeaturesTest"]], "thermofeaturestest (class in matminer.featurizers.composition.tests.test_thermo)": [[15, "matminer.featurizers.composition.tests.test_thermo.ThermoFeaturesTest"]], "matminer.featurizers.composition.tests": [[15, "module-matminer.featurizers.composition.tests"]], "matminer.featurizers.composition.tests.base": [[15, "module-matminer.featurizers.composition.tests.base"]], "matminer.featurizers.composition.tests.test_alloy": [[15, "module-matminer.featurizers.composition.tests.test_alloy"]], "matminer.featurizers.composition.tests.test_composite": [[15, "module-matminer.featurizers.composition.tests.test_composite"]], "matminer.featurizers.composition.tests.test_element": [[15, "module-matminer.featurizers.composition.tests.test_element"]], "matminer.featurizers.composition.tests.test_ion": [[15, "module-matminer.featurizers.composition.tests.test_ion"]], "matminer.featurizers.composition.tests.test_orbital": [[15, "module-matminer.featurizers.composition.tests.test_orbital"]], "matminer.featurizers.composition.tests.test_packing": [[15, "module-matminer.featurizers.composition.tests.test_packing"]], "matminer.featurizers.composition.tests.test_thermo": [[15, "module-matminer.featurizers.composition.tests.test_thermo"]], "setup() (matminer.featurizers.composition.tests.base.compositionfeaturestest method)": [[15, "matminer.featurizers.composition.tests.base.CompositionFeaturesTest.setUp"]], "test_wenalloys() (matminer.featurizers.composition.tests.test_alloy.alloyfeaturizerstest method)": [[15, "matminer.featurizers.composition.tests.test_alloy.AlloyFeaturizersTest.test_WenAlloys"]], "test_ape() (matminer.featurizers.composition.tests.test_packing.packingfeaturestest method)": [[15, "matminer.featurizers.composition.tests.test_packing.PackingFeaturesTest.test_ape"]], "test_atomic_orbitals() (matminer.featurizers.composition.tests.test_orbital.orbitalfeaturestest method)": [[15, "matminer.featurizers.composition.tests.test_orbital.OrbitalFeaturesTest.test_atomic_orbitals"]], "test_band_center() (matminer.featurizers.composition.tests.test_element.elementfeaturestest method)": [[15, "matminer.featurizers.composition.tests.test_element.ElementFeaturesTest.test_band_center"]], "test_cation_properties() (matminer.featurizers.composition.tests.test_ion.ionfeaturestest method)": [[15, "matminer.featurizers.composition.tests.test_ion.IonFeaturesTest.test_cation_properties"]], "test_cohesive_energy() (matminer.featurizers.composition.tests.test_thermo.thermofeaturestest method)": [[15, "matminer.featurizers.composition.tests.test_thermo.ThermoFeaturesTest.test_cohesive_energy"]], "test_cohesive_energy_mp() (matminer.featurizers.composition.tests.test_thermo.thermofeaturestest method)": [[15, "matminer.featurizers.composition.tests.test_thermo.ThermoFeaturesTest.test_cohesive_energy_mp"]], "test_elec_affin() (matminer.featurizers.composition.tests.test_ion.ionfeaturestest method)": [[15, "matminer.featurizers.composition.tests.test_ion.IonFeaturesTest.test_elec_affin"]], "test_elem() (matminer.featurizers.composition.tests.test_composite.compositefeaturestest method)": [[15, "matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest.test_elem"]], "test_elem_deml() (matminer.featurizers.composition.tests.test_composite.compositefeaturestest method)": [[15, "matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest.test_elem_deml"]], "test_elem_matminer() (matminer.featurizers.composition.tests.test_composite.compositefeaturestest method)": [[15, "matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest.test_elem_matminer"]], "test_elem_matscholar_el() (matminer.featurizers.composition.tests.test_composite.compositefeaturestest method)": [[15, "matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest.test_elem_matscholar_el"]], "test_elem_megnet_el() (matminer.featurizers.composition.tests.test_composite.compositefeaturestest method)": [[15, "matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest.test_elem_megnet_el"]], "test_en_diff() (matminer.featurizers.composition.tests.test_ion.ionfeaturestest method)": [[15, "matminer.featurizers.composition.tests.test_ion.IonFeaturesTest.test_en_diff"]], "test_fere_corr() (matminer.featurizers.composition.tests.test_composite.compositefeaturestest method)": [[15, "matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest.test_fere_corr"]], "test_fraction() (matminer.featurizers.composition.tests.test_element.elementfeaturestest method)": [[15, "matminer.featurizers.composition.tests.test_element.ElementFeaturesTest.test_fraction"]], "test_ionic() (matminer.featurizers.composition.tests.test_ion.ionfeaturestest method)": [[15, "matminer.featurizers.composition.tests.test_ion.IonFeaturesTest.test_ionic"]], "test_is_ionic() (matminer.featurizers.composition.tests.test_ion.ionfeaturestest method)": [[15, "matminer.featurizers.composition.tests.test_ion.IonFeaturesTest.test_is_ionic"]], "test_meredig() (matminer.featurizers.composition.tests.test_composite.compositefeaturestest method)": [[15, "matminer.featurizers.composition.tests.test_composite.CompositeFeaturesTest.test_meredig"]], "test_miedema_all() (matminer.featurizers.composition.tests.test_alloy.alloyfeaturizerstest method)": [[15, "matminer.featurizers.composition.tests.test_alloy.AlloyFeaturizersTest.test_miedema_all"]], "test_miedema_ss() (matminer.featurizers.composition.tests.test_alloy.alloyfeaturizerstest method)": [[15, "matminer.featurizers.composition.tests.test_alloy.AlloyFeaturizersTest.test_miedema_ss"]], "test_oxidation_states() (matminer.featurizers.composition.tests.test_ion.ionfeaturestest method)": [[15, "matminer.featurizers.composition.tests.test_ion.IonFeaturesTest.test_oxidation_states"]], "test_stoich() (matminer.featurizers.composition.tests.test_element.elementfeaturestest method)": [[15, "matminer.featurizers.composition.tests.test_element.ElementFeaturesTest.test_stoich"]], "test_tm_fraction() (matminer.featurizers.composition.tests.test_element.elementfeaturestest method)": [[15, "matminer.featurizers.composition.tests.test_element.ElementFeaturesTest.test_tm_fraction"]], "test_valence() (matminer.featurizers.composition.tests.test_orbital.orbitalfeaturestest method)": [[15, "matminer.featurizers.composition.tests.test_orbital.OrbitalFeaturesTest.test_valence"]], "test_yang() (matminer.featurizers.composition.tests.test_alloy.alloyfeaturizerstest method)": [[15, "matminer.featurizers.composition.tests.test_alloy.AlloyFeaturizersTest.test_yang"]], "agnifingerprints (class in matminer.featurizers.site.fingerprint)": [[16, "matminer.featurizers.site.fingerprint.AGNIFingerprints"]], "angularfourierseries (class in matminer.featurizers.site.rdf)": [[16, "matminer.featurizers.site.rdf.AngularFourierSeries"]], "averagebondangle (class in matminer.featurizers.site.bonding)": [[16, "matminer.featurizers.site.bonding.AverageBondAngle"]], "averagebondlength (class in matminer.featurizers.site.bonding)": [[16, "matminer.featurizers.site.bonding.AverageBondLength"]], "bondorientationalparameter (class in matminer.featurizers.site.bonding)": [[16, "matminer.featurizers.site.bonding.BondOrientationalParameter"]], "chemenvsitefingerprint (class in matminer.featurizers.site.fingerprint)": [[16, "matminer.featurizers.site.fingerprint.ChemEnvSiteFingerprint"]], "chemicalsro (class in matminer.featurizers.site.chemical)": [[16, "matminer.featurizers.site.chemical.ChemicalSRO"]], "coordinationnumber (class in matminer.featurizers.site.misc)": [[16, "matminer.featurizers.site.misc.CoordinationNumber"]], "crystalnnfingerprint (class in matminer.featurizers.site.fingerprint)": [[16, "matminer.featurizers.site.fingerprint.CrystalNNFingerprint"]], "ewaldsiteenergy (class in matminer.featurizers.site.chemical)": [[16, "matminer.featurizers.site.chemical.EwaldSiteEnergy"]], "gaussiansymmfunc (class in matminer.featurizers.site.rdf)": [[16, "matminer.featurizers.site.rdf.GaussianSymmFunc"]], "generalizedradialdistributionfunction (class in matminer.featurizers.site.rdf)": [[16, "matminer.featurizers.site.rdf.GeneralizedRadialDistributionFunction"]], "intersticedistribution (class in matminer.featurizers.site.misc)": [[16, "matminer.featurizers.site.misc.IntersticeDistribution"]], "localpropertydifference (class in matminer.featurizers.site.chemical)": [[16, "matminer.featurizers.site.chemical.LocalPropertyDifference"]], "opsitefingerprint (class in matminer.featurizers.site.fingerprint)": [[16, "matminer.featurizers.site.fingerprint.OPSiteFingerprint"]], "soap (class in matminer.featurizers.site.external)": [[16, "matminer.featurizers.site.external.SOAP"]], "siteelementalproperty (class in matminer.featurizers.site.chemical)": [[16, "matminer.featurizers.site.chemical.SiteElementalProperty"]], "voronoifingerprint (class in matminer.featurizers.site.fingerprint)": [[16, "matminer.featurizers.site.fingerprint.VoronoiFingerprint"]], "__init__() (matminer.featurizers.site.bonding.averagebondangle method)": [[16, "matminer.featurizers.site.bonding.AverageBondAngle.__init__"]], "__init__() (matminer.featurizers.site.bonding.averagebondlength method)": [[16, "matminer.featurizers.site.bonding.AverageBondLength.__init__"]], "__init__() (matminer.featurizers.site.bonding.bondorientationalparameter method)": [[16, "matminer.featurizers.site.bonding.BondOrientationalParameter.__init__"]], "__init__() (matminer.featurizers.site.chemical.chemicalsro method)": [[16, "matminer.featurizers.site.chemical.ChemicalSRO.__init__"]], "__init__() (matminer.featurizers.site.chemical.ewaldsiteenergy method)": [[16, "matminer.featurizers.site.chemical.EwaldSiteEnergy.__init__"]], "__init__() (matminer.featurizers.site.chemical.localpropertydifference method)": [[16, "matminer.featurizers.site.chemical.LocalPropertyDifference.__init__"]], "__init__() (matminer.featurizers.site.chemical.siteelementalproperty method)": [[16, "matminer.featurizers.site.chemical.SiteElementalProperty.__init__"]], "__init__() (matminer.featurizers.site.external.soap method)": [[16, "matminer.featurizers.site.external.SOAP.__init__"]], "__init__() (matminer.featurizers.site.fingerprint.agnifingerprints method)": [[16, "matminer.featurizers.site.fingerprint.AGNIFingerprints.__init__"]], "__init__() (matminer.featurizers.site.fingerprint.chemenvsitefingerprint method)": [[16, "matminer.featurizers.site.fingerprint.ChemEnvSiteFingerprint.__init__"]], "__init__() (matminer.featurizers.site.fingerprint.crystalnnfingerprint method)": [[16, "matminer.featurizers.site.fingerprint.CrystalNNFingerprint.__init__"]], "__init__() (matminer.featurizers.site.fingerprint.opsitefingerprint method)": [[16, "matminer.featurizers.site.fingerprint.OPSiteFingerprint.__init__"]], "__init__() (matminer.featurizers.site.fingerprint.voronoifingerprint method)": [[16, "matminer.featurizers.site.fingerprint.VoronoiFingerprint.__init__"]], "__init__() (matminer.featurizers.site.misc.coordinationnumber method)": [[16, "matminer.featurizers.site.misc.CoordinationNumber.__init__"]], "__init__() (matminer.featurizers.site.misc.intersticedistribution method)": [[16, "matminer.featurizers.site.misc.IntersticeDistribution.__init__"]], "__init__() (matminer.featurizers.site.rdf.angularfourierseries method)": [[16, "matminer.featurizers.site.rdf.AngularFourierSeries.__init__"]], "__init__() (matminer.featurizers.site.rdf.gaussiansymmfunc method)": [[16, "matminer.featurizers.site.rdf.GaussianSymmFunc.__init__"]], "__init__() (matminer.featurizers.site.rdf.generalizedradialdistributionfunction method)": [[16, "matminer.featurizers.site.rdf.GeneralizedRadialDistributionFunction.__init__"]], "analyze_area_interstice() (matminer.featurizers.site.misc.intersticedistribution static method)": [[16, "matminer.featurizers.site.misc.IntersticeDistribution.analyze_area_interstice"]], "analyze_dist_interstices() (matminer.featurizers.site.misc.intersticedistribution static method)": [[16, "matminer.featurizers.site.misc.IntersticeDistribution.analyze_dist_interstices"]], "analyze_vol_interstice() (matminer.featurizers.site.misc.intersticedistribution static method)": [[16, "matminer.featurizers.site.misc.IntersticeDistribution.analyze_vol_interstice"]], "citations() (matminer.featurizers.site.bonding.averagebondangle method)": [[16, "matminer.featurizers.site.bonding.AverageBondAngle.citations"]], "citations() (matminer.featurizers.site.bonding.averagebondlength method)": [[16, "matminer.featurizers.site.bonding.AverageBondLength.citations"]], "citations() (matminer.featurizers.site.bonding.bondorientationalparameter method)": [[16, "matminer.featurizers.site.bonding.BondOrientationalParameter.citations"]], "citations() (matminer.featurizers.site.chemical.chemicalsro method)": [[16, "matminer.featurizers.site.chemical.ChemicalSRO.citations"]], "citations() (matminer.featurizers.site.chemical.ewaldsiteenergy method)": [[16, "matminer.featurizers.site.chemical.EwaldSiteEnergy.citations"]], "citations() (matminer.featurizers.site.chemical.localpropertydifference method)": [[16, "matminer.featurizers.site.chemical.LocalPropertyDifference.citations"]], "citations() (matminer.featurizers.site.chemical.siteelementalproperty method)": [[16, "matminer.featurizers.site.chemical.SiteElementalProperty.citations"]], "citations() (matminer.featurizers.site.external.soap method)": [[16, "matminer.featurizers.site.external.SOAP.citations"]], "citations() (matminer.featurizers.site.fingerprint.agnifingerprints method)": [[16, "matminer.featurizers.site.fingerprint.AGNIFingerprints.citations"]], "citations() (matminer.featurizers.site.fingerprint.chemenvsitefingerprint method)": [[16, "matminer.featurizers.site.fingerprint.ChemEnvSiteFingerprint.citations"]], "citations() (matminer.featurizers.site.fingerprint.crystalnnfingerprint method)": [[16, "matminer.featurizers.site.fingerprint.CrystalNNFingerprint.citations"]], "citations() (matminer.featurizers.site.fingerprint.opsitefingerprint method)": [[16, "matminer.featurizers.site.fingerprint.OPSiteFingerprint.citations"]], "citations() (matminer.featurizers.site.fingerprint.voronoifingerprint method)": [[16, "matminer.featurizers.site.fingerprint.VoronoiFingerprint.citations"]], "citations() (matminer.featurizers.site.misc.coordinationnumber method)": [[16, "matminer.featurizers.site.misc.CoordinationNumber.citations"]], "citations() (matminer.featurizers.site.misc.intersticedistribution method)": [[16, "matminer.featurizers.site.misc.IntersticeDistribution.citations"]], "citations() (matminer.featurizers.site.rdf.angularfourierseries method)": [[16, "matminer.featurizers.site.rdf.AngularFourierSeries.citations"]], "citations() (matminer.featurizers.site.rdf.gaussiansymmfunc method)": [[16, "matminer.featurizers.site.rdf.GaussianSymmFunc.citations"]], "citations() (matminer.featurizers.site.rdf.generalizedradialdistributionfunction method)": [[16, "matminer.featurizers.site.rdf.GeneralizedRadialDistributionFunction.citations"]], "cosine_cutoff() (matminer.featurizers.site.rdf.gaussiansymmfunc static method)": [[16, "matminer.featurizers.site.rdf.GaussianSymmFunc.cosine_cutoff"]], "feature_labels() (matminer.featurizers.site.bonding.averagebondangle method)": [[16, "matminer.featurizers.site.bonding.AverageBondAngle.feature_labels"]], "feature_labels() (matminer.featurizers.site.bonding.averagebondlength method)": [[16, "matminer.featurizers.site.bonding.AverageBondLength.feature_labels"]], "feature_labels() (matminer.featurizers.site.bonding.bondorientationalparameter method)": [[16, "matminer.featurizers.site.bonding.BondOrientationalParameter.feature_labels"]], "feature_labels() (matminer.featurizers.site.chemical.chemicalsro method)": [[16, "matminer.featurizers.site.chemical.ChemicalSRO.feature_labels"]], "feature_labels() (matminer.featurizers.site.chemical.ewaldsiteenergy method)": [[16, "matminer.featurizers.site.chemical.EwaldSiteEnergy.feature_labels"]], "feature_labels() (matminer.featurizers.site.chemical.localpropertydifference method)": [[16, "matminer.featurizers.site.chemical.LocalPropertyDifference.feature_labels"]], "feature_labels() (matminer.featurizers.site.chemical.siteelementalproperty method)": [[16, "matminer.featurizers.site.chemical.SiteElementalProperty.feature_labels"]], "feature_labels() (matminer.featurizers.site.external.soap method)": [[16, "matminer.featurizers.site.external.SOAP.feature_labels"]], "feature_labels() (matminer.featurizers.site.fingerprint.agnifingerprints method)": [[16, "matminer.featurizers.site.fingerprint.AGNIFingerprints.feature_labels"]], "feature_labels() (matminer.featurizers.site.fingerprint.chemenvsitefingerprint method)": [[16, "matminer.featurizers.site.fingerprint.ChemEnvSiteFingerprint.feature_labels"]], "feature_labels() (matminer.featurizers.site.fingerprint.crystalnnfingerprint method)": [[16, "matminer.featurizers.site.fingerprint.CrystalNNFingerprint.feature_labels"]], "feature_labels() (matminer.featurizers.site.fingerprint.opsitefingerprint method)": [[16, "matminer.featurizers.site.fingerprint.OPSiteFingerprint.feature_labels"]], "feature_labels() (matminer.featurizers.site.fingerprint.voronoifingerprint method)": [[16, "matminer.featurizers.site.fingerprint.VoronoiFingerprint.feature_labels"]], "feature_labels() (matminer.featurizers.site.misc.coordinationnumber method)": [[16, "matminer.featurizers.site.misc.CoordinationNumber.feature_labels"]], "feature_labels() (matminer.featurizers.site.misc.intersticedistribution method)": [[16, "matminer.featurizers.site.misc.IntersticeDistribution.feature_labels"]], "feature_labels() (matminer.featurizers.site.rdf.angularfourierseries method)": [[16, "matminer.featurizers.site.rdf.AngularFourierSeries.feature_labels"]], "feature_labels() (matminer.featurizers.site.rdf.gaussiansymmfunc method)": [[16, "matminer.featurizers.site.rdf.GaussianSymmFunc.feature_labels"]], "feature_labels() (matminer.featurizers.site.rdf.generalizedradialdistributionfunction method)": [[16, "matminer.featurizers.site.rdf.GeneralizedRadialDistributionFunction.feature_labels"]], "featurize() (matminer.featurizers.site.bonding.averagebondangle method)": [[16, "matminer.featurizers.site.bonding.AverageBondAngle.featurize"]], "featurize() (matminer.featurizers.site.bonding.averagebondlength method)": [[16, "matminer.featurizers.site.bonding.AverageBondLength.featurize"]], "featurize() (matminer.featurizers.site.bonding.bondorientationalparameter method)": [[16, "matminer.featurizers.site.bonding.BondOrientationalParameter.featurize"]], "featurize() (matminer.featurizers.site.chemical.chemicalsro method)": [[16, "matminer.featurizers.site.chemical.ChemicalSRO.featurize"]], "featurize() (matminer.featurizers.site.chemical.ewaldsiteenergy method)": [[16, "matminer.featurizers.site.chemical.EwaldSiteEnergy.featurize"]], "featurize() (matminer.featurizers.site.chemical.localpropertydifference method)": [[16, "matminer.featurizers.site.chemical.LocalPropertyDifference.featurize"]], "featurize() (matminer.featurizers.site.chemical.siteelementalproperty method)": [[16, "matminer.featurizers.site.chemical.SiteElementalProperty.featurize"]], "featurize() (matminer.featurizers.site.external.soap method)": [[16, "matminer.featurizers.site.external.SOAP.featurize"]], "featurize() (matminer.featurizers.site.fingerprint.agnifingerprints method)": [[16, "matminer.featurizers.site.fingerprint.AGNIFingerprints.featurize"]], "featurize() (matminer.featurizers.site.fingerprint.chemenvsitefingerprint method)": [[16, "matminer.featurizers.site.fingerprint.ChemEnvSiteFingerprint.featurize"]], "featurize() (matminer.featurizers.site.fingerprint.crystalnnfingerprint method)": [[16, "matminer.featurizers.site.fingerprint.CrystalNNFingerprint.featurize"]], "featurize() (matminer.featurizers.site.fingerprint.opsitefingerprint method)": [[16, "matminer.featurizers.site.fingerprint.OPSiteFingerprint.featurize"]], "featurize() (matminer.featurizers.site.fingerprint.voronoifingerprint method)": [[16, "matminer.featurizers.site.fingerprint.VoronoiFingerprint.featurize"]], "featurize() (matminer.featurizers.site.misc.coordinationnumber method)": [[16, "matminer.featurizers.site.misc.CoordinationNumber.featurize"]], "featurize() (matminer.featurizers.site.misc.intersticedistribution method)": [[16, "matminer.featurizers.site.misc.IntersticeDistribution.featurize"]], "featurize() (matminer.featurizers.site.rdf.angularfourierseries method)": [[16, "matminer.featurizers.site.rdf.AngularFourierSeries.featurize"]], "featurize() (matminer.featurizers.site.rdf.gaussiansymmfunc method)": [[16, "matminer.featurizers.site.rdf.GaussianSymmFunc.featurize"]], "featurize() (matminer.featurizers.site.rdf.generalizedradialdistributionfunction method)": [[16, "matminer.featurizers.site.rdf.GeneralizedRadialDistributionFunction.featurize"]], "fit() (matminer.featurizers.site.chemical.chemicalsro method)": [[16, "matminer.featurizers.site.chemical.ChemicalSRO.fit"]], "fit() (matminer.featurizers.site.external.soap method)": [[16, "matminer.featurizers.site.external.SOAP.fit"]], "fit() (matminer.featurizers.site.rdf.generalizedradialdistributionfunction method)": [[16, "matminer.featurizers.site.rdf.GeneralizedRadialDistributionFunction.fit"]], "from_preset() (matminer.featurizers.site.chemical.chemicalsro static method)": [[16, "matminer.featurizers.site.chemical.ChemicalSRO.from_preset"]], "from_preset() (matminer.featurizers.site.chemical.localpropertydifference static method)": [[16, "matminer.featurizers.site.chemical.LocalPropertyDifference.from_preset"]], "from_preset() (matminer.featurizers.site.chemical.siteelementalproperty static method)": [[16, "matminer.featurizers.site.chemical.SiteElementalProperty.from_preset"]], "from_preset() (matminer.featurizers.site.external.soap class method)": [[16, "matminer.featurizers.site.external.SOAP.from_preset"]], "from_preset() (matminer.featurizers.site.fingerprint.chemenvsitefingerprint static method)": [[16, "matminer.featurizers.site.fingerprint.ChemEnvSiteFingerprint.from_preset"]], "from_preset() (matminer.featurizers.site.fingerprint.crystalnnfingerprint static method)": [[16, "matminer.featurizers.site.fingerprint.CrystalNNFingerprint.from_preset"]], "from_preset() (matminer.featurizers.site.misc.coordinationnumber static method)": [[16, "matminer.featurizers.site.misc.CoordinationNumber.from_preset"]], "from_preset() (matminer.featurizers.site.rdf.angularfourierseries static method)": [[16, "matminer.featurizers.site.rdf.AngularFourierSeries.from_preset"]], "from_preset() (matminer.featurizers.site.rdf.generalizedradialdistributionfunction static method)": [[16, "matminer.featurizers.site.rdf.GeneralizedRadialDistributionFunction.from_preset"]], "g2() (matminer.featurizers.site.rdf.gaussiansymmfunc static method)": [[16, "matminer.featurizers.site.rdf.GaussianSymmFunc.g2"]], "g4() (matminer.featurizers.site.rdf.gaussiansymmfunc static method)": [[16, "matminer.featurizers.site.rdf.GaussianSymmFunc.g4"]], "get_wigner_coeffs() (in module matminer.featurizers.site.bonding)": [[16, "matminer.featurizers.site.bonding.get_wigner_coeffs"]], "implementors() (matminer.featurizers.site.bonding.averagebondangle method)": [[16, "matminer.featurizers.site.bonding.AverageBondAngle.implementors"]], "implementors() (matminer.featurizers.site.bonding.averagebondlength method)": [[16, "matminer.featurizers.site.bonding.AverageBondLength.implementors"]], "implementors() (matminer.featurizers.site.bonding.bondorientationalparameter method)": [[16, "matminer.featurizers.site.bonding.BondOrientationalParameter.implementors"]], "implementors() (matminer.featurizers.site.chemical.chemicalsro method)": [[16, "matminer.featurizers.site.chemical.ChemicalSRO.implementors"]], "implementors() (matminer.featurizers.site.chemical.ewaldsiteenergy method)": [[16, "matminer.featurizers.site.chemical.EwaldSiteEnergy.implementors"]], "implementors() (matminer.featurizers.site.chemical.localpropertydifference method)": [[16, "matminer.featurizers.site.chemical.LocalPropertyDifference.implementors"]], "implementors() (matminer.featurizers.site.chemical.siteelementalproperty method)": [[16, "matminer.featurizers.site.chemical.SiteElementalProperty.implementors"]], "implementors() (matminer.featurizers.site.external.soap method)": [[16, "matminer.featurizers.site.external.SOAP.implementors"]], "implementors() (matminer.featurizers.site.fingerprint.agnifingerprints method)": [[16, "matminer.featurizers.site.fingerprint.AGNIFingerprints.implementors"]], "implementors() (matminer.featurizers.site.fingerprint.chemenvsitefingerprint method)": [[16, "matminer.featurizers.site.fingerprint.ChemEnvSiteFingerprint.implementors"]], "implementors() (matminer.featurizers.site.fingerprint.crystalnnfingerprint method)": [[16, "matminer.featurizers.site.fingerprint.CrystalNNFingerprint.implementors"]], "implementors() (matminer.featurizers.site.fingerprint.opsitefingerprint method)": [[16, "matminer.featurizers.site.fingerprint.OPSiteFingerprint.implementors"]], "implementors() (matminer.featurizers.site.fingerprint.voronoifingerprint method)": [[16, "matminer.featurizers.site.fingerprint.VoronoiFingerprint.implementors"]], "implementors() (matminer.featurizers.site.misc.coordinationnumber method)": [[16, "matminer.featurizers.site.misc.CoordinationNumber.implementors"]], "implementors() (matminer.featurizers.site.misc.intersticedistribution method)": [[16, "matminer.featurizers.site.misc.IntersticeDistribution.implementors"]], "implementors() (matminer.featurizers.site.rdf.angularfourierseries method)": [[16, "matminer.featurizers.site.rdf.AngularFourierSeries.implementors"]], "implementors() (matminer.featurizers.site.rdf.gaussiansymmfunc method)": [[16, "matminer.featurizers.site.rdf.GaussianSymmFunc.implementors"]], "implementors() (matminer.featurizers.site.rdf.generalizedradialdistributionfunction method)": [[16, "matminer.featurizers.site.rdf.GeneralizedRadialDistributionFunction.implementors"]], "load_cn_motif_op_params() (in module matminer.featurizers.site.fingerprint)": [[16, "matminer.featurizers.site.fingerprint.load_cn_motif_op_params"]], "load_cn_target_motif_op() (in module matminer.featurizers.site.fingerprint)": [[16, "matminer.featurizers.site.fingerprint.load_cn_target_motif_op"]], "matminer.featurizers.site": [[16, "module-matminer.featurizers.site"]], "matminer.featurizers.site.bonding": [[16, "module-matminer.featurizers.site.bonding"]], "matminer.featurizers.site.chemical": [[16, "module-matminer.featurizers.site.chemical"]], "matminer.featurizers.site.external": [[16, "module-matminer.featurizers.site.external"]], "matminer.featurizers.site.fingerprint": [[16, "module-matminer.featurizers.site.fingerprint"]], "matminer.featurizers.site.misc": [[16, "module-matminer.featurizers.site.misc"]], "matminer.featurizers.site.rdf": [[16, "module-matminer.featurizers.site.rdf"]], "bondingtest (class in matminer.featurizers.site.tests.test_bonding)": [[17, "matminer.featurizers.site.tests.test_bonding.BondingTest"]], "chemicalsitetests (class in matminer.featurizers.site.tests.test_chemical)": [[17, "matminer.featurizers.site.tests.test_chemical.ChemicalSiteTests"]], "externalsitetests (class in matminer.featurizers.site.tests.test_external)": [[17, "matminer.featurizers.site.tests.test_external.ExternalSiteTests"]], "fingerprinttests (class in matminer.featurizers.site.tests.test_fingerprint)": [[17, "matminer.featurizers.site.tests.test_fingerprint.FingerprintTests"]], "miscsitetests (class in matminer.featurizers.site.tests.test_misc)": [[17, "matminer.featurizers.site.tests.test_misc.MiscSiteTests"]], "rdftests (class in matminer.featurizers.site.tests.test_rdf)": [[17, "matminer.featurizers.site.tests.test_rdf.RDFTests"]], "sitefeaturizertest (class in matminer.featurizers.site.tests.base)": [[17, "matminer.featurizers.site.tests.base.SiteFeaturizerTest"]], "matminer.featurizers.site.tests": [[17, "module-matminer.featurizers.site.tests"]], "matminer.featurizers.site.tests.base": [[17, "module-matminer.featurizers.site.tests.base"]], "matminer.featurizers.site.tests.test_bonding": [[17, "module-matminer.featurizers.site.tests.test_bonding"]], "matminer.featurizers.site.tests.test_chemical": [[17, "module-matminer.featurizers.site.tests.test_chemical"]], "matminer.featurizers.site.tests.test_external": [[17, "module-matminer.featurizers.site.tests.test_external"]], "matminer.featurizers.site.tests.test_fingerprint": [[17, "module-matminer.featurizers.site.tests.test_fingerprint"]], "matminer.featurizers.site.tests.test_misc": [[17, "module-matminer.featurizers.site.tests.test_misc"]], "matminer.featurizers.site.tests.test_rdf": [[17, "module-matminer.featurizers.site.tests.test_rdf"]], "setup() (matminer.featurizers.site.tests.base.sitefeaturizertest method)": [[17, "matminer.featurizers.site.tests.base.SiteFeaturizerTest.setUp"]], "teardown() (matminer.featurizers.site.tests.base.sitefeaturizertest method)": [[17, "matminer.featurizers.site.tests.base.SiteFeaturizerTest.tearDown"]], "test_averagebondangle() (matminer.featurizers.site.tests.test_bonding.bondingtest method)": [[17, "matminer.featurizers.site.tests.test_bonding.BondingTest.test_AverageBondAngle"]], "test_averagebondlength() (matminer.featurizers.site.tests.test_bonding.bondingtest method)": [[17, "matminer.featurizers.site.tests.test_bonding.BondingTest.test_AverageBondLength"]], "test_soap() (matminer.featurizers.site.tests.test_external.externalsitetests method)": [[17, "matminer.featurizers.site.tests.test_external.ExternalSiteTests.test_SOAP"]], "test_afs() (matminer.featurizers.site.tests.test_rdf.rdftests method)": [[17, "matminer.featurizers.site.tests.test_rdf.RDFTests.test_afs"]], "test_bop() (matminer.featurizers.site.tests.test_bonding.bondingtest method)": [[17, "matminer.featurizers.site.tests.test_bonding.BondingTest.test_bop"]], "test_chemenv_site_fingerprint() (matminer.featurizers.site.tests.test_fingerprint.fingerprinttests method)": [[17, "matminer.featurizers.site.tests.test_fingerprint.FingerprintTests.test_chemenv_site_fingerprint"]], "test_chemicalsro() (matminer.featurizers.site.tests.test_chemical.chemicalsitetests method)": [[17, "matminer.featurizers.site.tests.test_chemical.ChemicalSiteTests.test_chemicalSRO"]], "test_cns() (matminer.featurizers.site.tests.test_misc.miscsitetests method)": [[17, "matminer.featurizers.site.tests.test_misc.MiscSiteTests.test_cns"]], "test_crystal_nn_fingerprint() (matminer.featurizers.site.tests.test_fingerprint.fingerprinttests method)": [[17, "matminer.featurizers.site.tests.test_fingerprint.FingerprintTests.test_crystal_nn_fingerprint"]], "test_dataframe() (matminer.featurizers.site.tests.test_fingerprint.fingerprinttests method)": [[17, "matminer.featurizers.site.tests.test_fingerprint.FingerprintTests.test_dataframe"]], "test_ewald_site() (matminer.featurizers.site.tests.test_chemical.chemicalsitetests method)": [[17, "matminer.featurizers.site.tests.test_chemical.ChemicalSiteTests.test_ewald_site"]], "test_gaussiansymmfunc() (matminer.featurizers.site.tests.test_rdf.rdftests method)": [[17, "matminer.featurizers.site.tests.test_rdf.RDFTests.test_gaussiansymmfunc"]], "test_grdf() (matminer.featurizers.site.tests.test_rdf.rdftests method)": [[17, "matminer.featurizers.site.tests.test_rdf.RDFTests.test_grdf"]], "test_interstice_distribution_of_crystal() (matminer.featurizers.site.tests.test_misc.miscsitetests method)": [[17, "matminer.featurizers.site.tests.test_misc.MiscSiteTests.test_interstice_distribution_of_crystal"]], "test_interstice_distribution_of_glass() (matminer.featurizers.site.tests.test_misc.miscsitetests method)": [[17, "matminer.featurizers.site.tests.test_misc.MiscSiteTests.test_interstice_distribution_of_glass"]], "test_local_prop_diff() (matminer.featurizers.site.tests.test_chemical.chemicalsitetests method)": [[17, "matminer.featurizers.site.tests.test_chemical.ChemicalSiteTests.test_local_prop_diff"]], "test_off_center_cscl() (matminer.featurizers.site.tests.test_fingerprint.fingerprinttests method)": [[17, "matminer.featurizers.site.tests.test_fingerprint.FingerprintTests.test_off_center_cscl"]], "test_op_site_fingerprint() (matminer.featurizers.site.tests.test_fingerprint.fingerprinttests method)": [[17, "matminer.featurizers.site.tests.test_fingerprint.FingerprintTests.test_op_site_fingerprint"]], "test_simple_cubic() (matminer.featurizers.site.tests.test_fingerprint.fingerprinttests method)": [[17, "matminer.featurizers.site.tests.test_fingerprint.FingerprintTests.test_simple_cubic"]], "test_site_elem_prop() (matminer.featurizers.site.tests.test_chemical.chemicalsitetests method)": [[17, "matminer.featurizers.site.tests.test_chemical.ChemicalSiteTests.test_site_elem_prop"]], "test_voronoifingerprint() (matminer.featurizers.site.tests.test_fingerprint.fingerprinttests method)": [[17, "matminer.featurizers.site.tests.test_fingerprint.FingerprintTests.test_voronoifingerprint"]], "bagofbonds (class in matminer.featurizers.structure.bonding)": [[18, "matminer.featurizers.structure.bonding.BagofBonds"]], "bondfractions (class in matminer.featurizers.structure.bonding)": [[18, "matminer.featurizers.structure.bonding.BondFractions"]], "chemicalordering (class in matminer.featurizers.structure.order)": [[18, "matminer.featurizers.structure.order.ChemicalOrdering"]], "coulombmatrix (class in matminer.featurizers.structure.matrix)": [[18, "matminer.featurizers.structure.matrix.CoulombMatrix"]], "densityfeatures (class in matminer.featurizers.structure.order)": [[18, "matminer.featurizers.structure.order.DensityFeatures"]], "dimensionality (class in matminer.featurizers.structure.symmetry)": [[18, "matminer.featurizers.structure.symmetry.Dimensionality"]], "electronicradialdistributionfunction (class in matminer.featurizers.structure.rdf)": [[18, "matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction"]], "ewaldenergy (class in matminer.featurizers.structure.misc)": [[18, "matminer.featurizers.structure.misc.EwaldEnergy"]], "globalinstabilityindex (class in matminer.featurizers.structure.bonding)": [[18, "matminer.featurizers.structure.bonding.GlobalInstabilityIndex"]], "globalsymmetryfeatures (class in matminer.featurizers.structure.symmetry)": [[18, "matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures"]], "jarviscfid (class in matminer.featurizers.structure.composite)": [[18, "matminer.featurizers.structure.composite.JarvisCFID"]], "maximumpackingefficiency (class in matminer.featurizers.structure.order)": [[18, "matminer.featurizers.structure.order.MaximumPackingEfficiency"]], "minimumrelativedistances (class in matminer.featurizers.structure.bonding)": [[18, "matminer.featurizers.structure.bonding.MinimumRelativeDistances"]], "orbitalfieldmatrix (class in matminer.featurizers.structure.matrix)": [[18, "matminer.featurizers.structure.matrix.OrbitalFieldMatrix"]], "partialradialdistributionfunction (class in matminer.featurizers.structure.rdf)": [[18, "matminer.featurizers.structure.rdf.PartialRadialDistributionFunction"]], "partialssitestatsfingerprint (class in matminer.featurizers.structure.sites)": [[18, "matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint"]], "radialdistributionfunction (class in matminer.featurizers.structure.rdf)": [[18, "matminer.featurizers.structure.rdf.RadialDistributionFunction"]], "sinecoulombmatrix (class in matminer.featurizers.structure.matrix)": [[18, "matminer.featurizers.structure.matrix.SineCoulombMatrix"]], "sitestatsfingerprint (class in matminer.featurizers.structure.sites)": [[18, "matminer.featurizers.structure.sites.SiteStatsFingerprint"]], "structuralcomplexity (class in matminer.featurizers.structure.order)": [[18, "matminer.featurizers.structure.order.StructuralComplexity"]], "structuralheterogeneity (class in matminer.featurizers.structure.bonding)": [[18, "matminer.featurizers.structure.bonding.StructuralHeterogeneity"]], "structurecomposition (class in matminer.featurizers.structure.misc)": [[18, "matminer.featurizers.structure.misc.StructureComposition"]], "xrdpowderpattern (class in matminer.featurizers.structure.misc)": [[18, "matminer.featurizers.structure.misc.XRDPowderPattern"]], "__init__() (matminer.featurizers.structure.bonding.bagofbonds method)": [[18, "matminer.featurizers.structure.bonding.BagofBonds.__init__"]], "__init__() (matminer.featurizers.structure.bonding.bondfractions method)": [[18, "matminer.featurizers.structure.bonding.BondFractions.__init__"]], "__init__() (matminer.featurizers.structure.bonding.globalinstabilityindex method)": [[18, "matminer.featurizers.structure.bonding.GlobalInstabilityIndex.__init__"]], "__init__() (matminer.featurizers.structure.bonding.minimumrelativedistances method)": [[18, "matminer.featurizers.structure.bonding.MinimumRelativeDistances.__init__"]], "__init__() (matminer.featurizers.structure.bonding.structuralheterogeneity method)": [[18, "matminer.featurizers.structure.bonding.StructuralHeterogeneity.__init__"]], "__init__() (matminer.featurizers.structure.composite.jarviscfid method)": [[18, "matminer.featurizers.structure.composite.JarvisCFID.__init__"]], "__init__() (matminer.featurizers.structure.matrix.coulombmatrix method)": [[18, "matminer.featurizers.structure.matrix.CoulombMatrix.__init__"]], "__init__() (matminer.featurizers.structure.matrix.orbitalfieldmatrix method)": [[18, "matminer.featurizers.structure.matrix.OrbitalFieldMatrix.__init__"]], "__init__() (matminer.featurizers.structure.matrix.sinecoulombmatrix method)": [[18, "matminer.featurizers.structure.matrix.SineCoulombMatrix.__init__"]], "__init__() (matminer.featurizers.structure.misc.ewaldenergy method)": [[18, "matminer.featurizers.structure.misc.EwaldEnergy.__init__"]], "__init__() (matminer.featurizers.structure.misc.structurecomposition method)": [[18, "matminer.featurizers.structure.misc.StructureComposition.__init__"]], "__init__() (matminer.featurizers.structure.misc.xrdpowderpattern method)": [[18, "matminer.featurizers.structure.misc.XRDPowderPattern.__init__"]], "__init__() (matminer.featurizers.structure.order.chemicalordering method)": [[18, "matminer.featurizers.structure.order.ChemicalOrdering.__init__"]], "__init__() (matminer.featurizers.structure.order.densityfeatures method)": [[18, "matminer.featurizers.structure.order.DensityFeatures.__init__"]], "__init__() (matminer.featurizers.structure.order.structuralcomplexity method)": [[18, "matminer.featurizers.structure.order.StructuralComplexity.__init__"]], "__init__() (matminer.featurizers.structure.rdf.electronicradialdistributionfunction method)": [[18, "matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.__init__"]], "__init__() (matminer.featurizers.structure.rdf.partialradialdistributionfunction method)": [[18, "matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.__init__"]], "__init__() (matminer.featurizers.structure.rdf.radialdistributionfunction method)": [[18, "matminer.featurizers.structure.rdf.RadialDistributionFunction.__init__"]], "__init__() (matminer.featurizers.structure.sites.partialssitestatsfingerprint method)": [[18, "matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.__init__"]], "__init__() (matminer.featurizers.structure.sites.sitestatsfingerprint method)": [[18, "matminer.featurizers.structure.sites.SiteStatsFingerprint.__init__"]], "__init__() (matminer.featurizers.structure.symmetry.dimensionality method)": [[18, "matminer.featurizers.structure.symmetry.Dimensionality.__init__"]], "__init__() (matminer.featurizers.structure.symmetry.globalsymmetryfeatures method)": [[18, "matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.__init__"]], "all_features (matminer.featurizers.structure.symmetry.globalsymmetryfeatures attribute)": [[18, "matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.all_features"]], "bag() (matminer.featurizers.structure.bonding.bagofbonds method)": [[18, "matminer.featurizers.structure.bonding.BagofBonds.bag"]], "calc_bv_sum() (matminer.featurizers.structure.bonding.globalinstabilityindex method)": [[18, "matminer.featurizers.structure.bonding.GlobalInstabilityIndex.calc_bv_sum"]], "calc_gii_iucr() (matminer.featurizers.structure.bonding.globalinstabilityindex method)": [[18, "matminer.featurizers.structure.bonding.GlobalInstabilityIndex.calc_gii_iucr"]], "calc_gii_pymatgen() (matminer.featurizers.structure.bonding.globalinstabilityindex method)": [[18, "matminer.featurizers.structure.bonding.GlobalInstabilityIndex.calc_gii_pymatgen"]], "citations() (matminer.featurizers.structure.bonding.bagofbonds method)": [[18, "matminer.featurizers.structure.bonding.BagofBonds.citations"]], "citations() (matminer.featurizers.structure.bonding.bondfractions method)": [[18, "matminer.featurizers.structure.bonding.BondFractions.citations"]], "citations() (matminer.featurizers.structure.bonding.globalinstabilityindex method)": [[18, "matminer.featurizers.structure.bonding.GlobalInstabilityIndex.citations"]], "citations() (matminer.featurizers.structure.bonding.minimumrelativedistances method)": [[18, "matminer.featurizers.structure.bonding.MinimumRelativeDistances.citations"]], "citations() (matminer.featurizers.structure.bonding.structuralheterogeneity method)": [[18, "matminer.featurizers.structure.bonding.StructuralHeterogeneity.citations"]], "citations() (matminer.featurizers.structure.composite.jarviscfid method)": [[18, "matminer.featurizers.structure.composite.JarvisCFID.citations"]], "citations() (matminer.featurizers.structure.matrix.coulombmatrix method)": [[18, "matminer.featurizers.structure.matrix.CoulombMatrix.citations"]], "citations() (matminer.featurizers.structure.matrix.orbitalfieldmatrix method)": [[18, "matminer.featurizers.structure.matrix.OrbitalFieldMatrix.citations"]], "citations() (matminer.featurizers.structure.matrix.sinecoulombmatrix method)": [[18, "matminer.featurizers.structure.matrix.SineCoulombMatrix.citations"]], "citations() (matminer.featurizers.structure.misc.ewaldenergy method)": [[18, "matminer.featurizers.structure.misc.EwaldEnergy.citations"]], "citations() (matminer.featurizers.structure.misc.structurecomposition method)": [[18, "matminer.featurizers.structure.misc.StructureComposition.citations"]], "citations() (matminer.featurizers.structure.misc.xrdpowderpattern method)": [[18, "matminer.featurizers.structure.misc.XRDPowderPattern.citations"]], "citations() (matminer.featurizers.structure.order.chemicalordering method)": [[18, "matminer.featurizers.structure.order.ChemicalOrdering.citations"]], "citations() (matminer.featurizers.structure.order.densityfeatures method)": [[18, "matminer.featurizers.structure.order.DensityFeatures.citations"]], "citations() (matminer.featurizers.structure.order.maximumpackingefficiency method)": [[18, "matminer.featurizers.structure.order.MaximumPackingEfficiency.citations"]], "citations() (matminer.featurizers.structure.order.structuralcomplexity method)": [[18, "matminer.featurizers.structure.order.StructuralComplexity.citations"]], "citations() (matminer.featurizers.structure.rdf.electronicradialdistributionfunction method)": [[18, "matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.citations"]], "citations() (matminer.featurizers.structure.rdf.partialradialdistributionfunction method)": [[18, "matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.citations"]], "citations() (matminer.featurizers.structure.rdf.radialdistributionfunction method)": [[18, "matminer.featurizers.structure.rdf.RadialDistributionFunction.citations"]], "citations() (matminer.featurizers.structure.sites.sitestatsfingerprint method)": [[18, "matminer.featurizers.structure.sites.SiteStatsFingerprint.citations"]], "citations() (matminer.featurizers.structure.symmetry.dimensionality method)": [[18, "matminer.featurizers.structure.symmetry.Dimensionality.citations"]], "citations() (matminer.featurizers.structure.symmetry.globalsymmetryfeatures method)": [[18, "matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.citations"]], "compute_bv() (matminer.featurizers.structure.bonding.globalinstabilityindex static method)": [[18, "matminer.featurizers.structure.bonding.GlobalInstabilityIndex.compute_bv"]], "compute_prdf() (matminer.featurizers.structure.rdf.partialradialdistributionfunction method)": [[18, "matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.compute_prdf"]], "compute_pssf() (matminer.featurizers.structure.sites.partialssitestatsfingerprint method)": [[18, "matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.compute_pssf"]], "crystal_idx (matminer.featurizers.structure.symmetry.globalsymmetryfeatures attribute)": [[18, "matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.crystal_idx"]], "enumerate_all_bonds() (matminer.featurizers.structure.bonding.bondfractions method)": [[18, "matminer.featurizers.structure.bonding.BondFractions.enumerate_all_bonds"]], "enumerate_bonds() (matminer.featurizers.structure.bonding.bondfractions method)": [[18, "matminer.featurizers.structure.bonding.BondFractions.enumerate_bonds"]], "feature_labels() (matminer.featurizers.structure.bonding.bagofbonds method)": [[18, "matminer.featurizers.structure.bonding.BagofBonds.feature_labels"]], "feature_labels() (matminer.featurizers.structure.bonding.bondfractions method)": [[18, "matminer.featurizers.structure.bonding.BondFractions.feature_labels"]], "feature_labels() (matminer.featurizers.structure.bonding.globalinstabilityindex method)": [[18, "matminer.featurizers.structure.bonding.GlobalInstabilityIndex.feature_labels"]], "feature_labels() (matminer.featurizers.structure.bonding.minimumrelativedistances method)": [[18, "matminer.featurizers.structure.bonding.MinimumRelativeDistances.feature_labels"]], "feature_labels() (matminer.featurizers.structure.bonding.structuralheterogeneity method)": [[18, "matminer.featurizers.structure.bonding.StructuralHeterogeneity.feature_labels"]], "feature_labels() (matminer.featurizers.structure.composite.jarviscfid method)": [[18, "matminer.featurizers.structure.composite.JarvisCFID.feature_labels"]], "feature_labels() (matminer.featurizers.structure.matrix.coulombmatrix method)": [[18, "matminer.featurizers.structure.matrix.CoulombMatrix.feature_labels"]], "feature_labels() (matminer.featurizers.structure.matrix.orbitalfieldmatrix method)": [[18, "matminer.featurizers.structure.matrix.OrbitalFieldMatrix.feature_labels"]], "feature_labels() (matminer.featurizers.structure.matrix.sinecoulombmatrix method)": [[18, "matminer.featurizers.structure.matrix.SineCoulombMatrix.feature_labels"]], "feature_labels() (matminer.featurizers.structure.misc.ewaldenergy method)": [[18, "matminer.featurizers.structure.misc.EwaldEnergy.feature_labels"]], "feature_labels() (matminer.featurizers.structure.misc.structurecomposition method)": [[18, "matminer.featurizers.structure.misc.StructureComposition.feature_labels"]], "feature_labels() (matminer.featurizers.structure.misc.xrdpowderpattern method)": [[18, "matminer.featurizers.structure.misc.XRDPowderPattern.feature_labels"]], "feature_labels() (matminer.featurizers.structure.order.chemicalordering method)": [[18, "matminer.featurizers.structure.order.ChemicalOrdering.feature_labels"]], "feature_labels() (matminer.featurizers.structure.order.densityfeatures method)": [[18, "matminer.featurizers.structure.order.DensityFeatures.feature_labels"]], "feature_labels() (matminer.featurizers.structure.order.maximumpackingefficiency method)": [[18, "matminer.featurizers.structure.order.MaximumPackingEfficiency.feature_labels"]], "feature_labels() (matminer.featurizers.structure.order.structuralcomplexity method)": [[18, "matminer.featurizers.structure.order.StructuralComplexity.feature_labels"]], "feature_labels() (matminer.featurizers.structure.rdf.electronicradialdistributionfunction method)": [[18, "matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.feature_labels"]], "feature_labels() (matminer.featurizers.structure.rdf.partialradialdistributionfunction method)": [[18, "matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.feature_labels"]], "feature_labels() (matminer.featurizers.structure.rdf.radialdistributionfunction method)": [[18, "matminer.featurizers.structure.rdf.RadialDistributionFunction.feature_labels"]], "feature_labels() (matminer.featurizers.structure.sites.partialssitestatsfingerprint method)": [[18, "matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.feature_labels"]], "feature_labels() (matminer.featurizers.structure.sites.sitestatsfingerprint method)": [[18, "matminer.featurizers.structure.sites.SiteStatsFingerprint.feature_labels"]], "feature_labels() (matminer.featurizers.structure.symmetry.dimensionality method)": [[18, "matminer.featurizers.structure.symmetry.Dimensionality.feature_labels"]], "feature_labels() (matminer.featurizers.structure.symmetry.globalsymmetryfeatures method)": [[18, "matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.feature_labels"]], "featurize() (matminer.featurizers.structure.bonding.bagofbonds method)": [[18, "matminer.featurizers.structure.bonding.BagofBonds.featurize"]], "featurize() (matminer.featurizers.structure.bonding.bondfractions method)": [[18, "matminer.featurizers.structure.bonding.BondFractions.featurize"]], "featurize() (matminer.featurizers.structure.bonding.globalinstabilityindex method)": [[18, "matminer.featurizers.structure.bonding.GlobalInstabilityIndex.featurize"]], "featurize() (matminer.featurizers.structure.bonding.minimumrelativedistances method)": [[18, "matminer.featurizers.structure.bonding.MinimumRelativeDistances.featurize"]], "featurize() (matminer.featurizers.structure.bonding.structuralheterogeneity method)": [[18, "matminer.featurizers.structure.bonding.StructuralHeterogeneity.featurize"]], "featurize() (matminer.featurizers.structure.composite.jarviscfid method)": [[18, "matminer.featurizers.structure.composite.JarvisCFID.featurize"]], "featurize() (matminer.featurizers.structure.matrix.coulombmatrix method)": [[18, "matminer.featurizers.structure.matrix.CoulombMatrix.featurize"]], "featurize() (matminer.featurizers.structure.matrix.orbitalfieldmatrix method)": [[18, "matminer.featurizers.structure.matrix.OrbitalFieldMatrix.featurize"]], "featurize() (matminer.featurizers.structure.matrix.sinecoulombmatrix method)": [[18, "matminer.featurizers.structure.matrix.SineCoulombMatrix.featurize"]], "featurize() (matminer.featurizers.structure.misc.ewaldenergy method)": [[18, "matminer.featurizers.structure.misc.EwaldEnergy.featurize"]], "featurize() (matminer.featurizers.structure.misc.structurecomposition method)": [[18, "matminer.featurizers.structure.misc.StructureComposition.featurize"]], "featurize() (matminer.featurizers.structure.misc.xrdpowderpattern method)": [[18, "matminer.featurizers.structure.misc.XRDPowderPattern.featurize"]], "featurize() (matminer.featurizers.structure.order.chemicalordering method)": [[18, "matminer.featurizers.structure.order.ChemicalOrdering.featurize"]], "featurize() (matminer.featurizers.structure.order.densityfeatures method)": [[18, "matminer.featurizers.structure.order.DensityFeatures.featurize"]], "featurize() (matminer.featurizers.structure.order.maximumpackingefficiency method)": [[18, "matminer.featurizers.structure.order.MaximumPackingEfficiency.featurize"]], "featurize() (matminer.featurizers.structure.order.structuralcomplexity method)": [[18, "matminer.featurizers.structure.order.StructuralComplexity.featurize"]], "featurize() (matminer.featurizers.structure.rdf.electronicradialdistributionfunction method)": [[18, "matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.featurize"]], "featurize() (matminer.featurizers.structure.rdf.partialradialdistributionfunction method)": [[18, "matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.featurize"]], "featurize() (matminer.featurizers.structure.rdf.radialdistributionfunction method)": [[18, "matminer.featurizers.structure.rdf.RadialDistributionFunction.featurize"]], "featurize() (matminer.featurizers.structure.sites.partialssitestatsfingerprint method)": [[18, "matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.featurize"]], "featurize() (matminer.featurizers.structure.sites.sitestatsfingerprint method)": [[18, "matminer.featurizers.structure.sites.SiteStatsFingerprint.featurize"]], "featurize() (matminer.featurizers.structure.symmetry.dimensionality method)": [[18, "matminer.featurizers.structure.symmetry.Dimensionality.featurize"]], "featurize() (matminer.featurizers.structure.symmetry.globalsymmetryfeatures method)": [[18, "matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.featurize"]], "fit() (matminer.featurizers.structure.bonding.bagofbonds method)": [[18, "matminer.featurizers.structure.bonding.BagofBonds.fit"]], "fit() (matminer.featurizers.structure.bonding.bondfractions method)": [[18, "matminer.featurizers.structure.bonding.BondFractions.fit"]], "fit() (matminer.featurizers.structure.bonding.minimumrelativedistances method)": [[18, "matminer.featurizers.structure.bonding.MinimumRelativeDistances.fit"]], "fit() (matminer.featurizers.structure.matrix.coulombmatrix method)": [[18, "matminer.featurizers.structure.matrix.CoulombMatrix.fit"]], "fit() (matminer.featurizers.structure.matrix.sinecoulombmatrix method)": [[18, "matminer.featurizers.structure.matrix.SineCoulombMatrix.fit"]], "fit() (matminer.featurizers.structure.misc.structurecomposition method)": [[18, "matminer.featurizers.structure.misc.StructureComposition.fit"]], "fit() (matminer.featurizers.structure.rdf.partialradialdistributionfunction method)": [[18, "matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.fit"]], "fit() (matminer.featurizers.structure.sites.partialssitestatsfingerprint method)": [[18, "matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.fit"]], "fit() (matminer.featurizers.structure.sites.sitestatsfingerprint method)": [[18, "matminer.featurizers.structure.sites.SiteStatsFingerprint.fit"]], "from_preset() (matminer.featurizers.structure.bonding.bondfractions static method)": [[18, "matminer.featurizers.structure.bonding.BondFractions.from_preset"]], "from_preset() (matminer.featurizers.structure.sites.sitestatsfingerprint class method)": [[18, "matminer.featurizers.structure.sites.SiteStatsFingerprint.from_preset"]], "get_atom_ofms() (matminer.featurizers.structure.matrix.orbitalfieldmatrix method)": [[18, "matminer.featurizers.structure.matrix.OrbitalFieldMatrix.get_atom_ofms"]], "get_bv_params() (matminer.featurizers.structure.bonding.globalinstabilityindex method)": [[18, "matminer.featurizers.structure.bonding.GlobalInstabilityIndex.get_bv_params"]], "get_chem() (matminer.featurizers.structure.composite.jarviscfid method)": [[18, "matminer.featurizers.structure.composite.JarvisCFID.get_chem"]], "get_chg() (matminer.featurizers.structure.composite.jarviscfid method)": [[18, "matminer.featurizers.structure.composite.JarvisCFID.get_chg"]], "get_distributions() (matminer.featurizers.structure.composite.jarviscfid method)": [[18, "matminer.featurizers.structure.composite.JarvisCFID.get_distributions"]], "get_equiv_sites() (matminer.featurizers.structure.bonding.globalinstabilityindex method)": [[18, "matminer.featurizers.structure.bonding.GlobalInstabilityIndex.get_equiv_sites"]], "get_mean_ofm() (matminer.featurizers.structure.matrix.orbitalfieldmatrix method)": [[18, "matminer.featurizers.structure.matrix.OrbitalFieldMatrix.get_mean_ofm"]], "get_ohv() (matminer.featurizers.structure.matrix.orbitalfieldmatrix method)": [[18, "matminer.featurizers.structure.matrix.OrbitalFieldMatrix.get_ohv"]], "get_rdf_bin_labels() (in module matminer.featurizers.structure.rdf)": [[18, "matminer.featurizers.structure.rdf.get_rdf_bin_labels"]], "get_single_ofm() (matminer.featurizers.structure.matrix.orbitalfieldmatrix method)": [[18, "matminer.featurizers.structure.matrix.OrbitalFieldMatrix.get_single_ofm"]], "get_structure_ofm() (matminer.featurizers.structure.matrix.orbitalfieldmatrix method)": [[18, "matminer.featurizers.structure.matrix.OrbitalFieldMatrix.get_structure_ofm"]], "implementors() (matminer.featurizers.structure.bonding.bagofbonds method)": [[18, "matminer.featurizers.structure.bonding.BagofBonds.implementors"]], "implementors() (matminer.featurizers.structure.bonding.bondfractions method)": [[18, "matminer.featurizers.structure.bonding.BondFractions.implementors"]], "implementors() (matminer.featurizers.structure.bonding.globalinstabilityindex method)": [[18, "matminer.featurizers.structure.bonding.GlobalInstabilityIndex.implementors"]], "implementors() (matminer.featurizers.structure.bonding.minimumrelativedistances method)": [[18, "matminer.featurizers.structure.bonding.MinimumRelativeDistances.implementors"]], "implementors() (matminer.featurizers.structure.bonding.structuralheterogeneity method)": [[18, "matminer.featurizers.structure.bonding.StructuralHeterogeneity.implementors"]], "implementors() (matminer.featurizers.structure.composite.jarviscfid method)": [[18, "matminer.featurizers.structure.composite.JarvisCFID.implementors"]], "implementors() (matminer.featurizers.structure.matrix.coulombmatrix method)": [[18, "matminer.featurizers.structure.matrix.CoulombMatrix.implementors"]], "implementors() (matminer.featurizers.structure.matrix.orbitalfieldmatrix method)": [[18, "matminer.featurizers.structure.matrix.OrbitalFieldMatrix.implementors"]], "implementors() (matminer.featurizers.structure.matrix.sinecoulombmatrix method)": [[18, "matminer.featurizers.structure.matrix.SineCoulombMatrix.implementors"]], "implementors() (matminer.featurizers.structure.misc.ewaldenergy method)": [[18, "matminer.featurizers.structure.misc.EwaldEnergy.implementors"]], "implementors() (matminer.featurizers.structure.misc.structurecomposition method)": [[18, "matminer.featurizers.structure.misc.StructureComposition.implementors"]], "implementors() (matminer.featurizers.structure.misc.xrdpowderpattern method)": [[18, "matminer.featurizers.structure.misc.XRDPowderPattern.implementors"]], "implementors() (matminer.featurizers.structure.order.chemicalordering method)": [[18, "matminer.featurizers.structure.order.ChemicalOrdering.implementors"]], "implementors() (matminer.featurizers.structure.order.densityfeatures method)": [[18, "matminer.featurizers.structure.order.DensityFeatures.implementors"]], "implementors() (matminer.featurizers.structure.order.maximumpackingefficiency method)": [[18, "matminer.featurizers.structure.order.MaximumPackingEfficiency.implementors"]], "implementors() (matminer.featurizers.structure.order.structuralcomplexity method)": [[18, "matminer.featurizers.structure.order.StructuralComplexity.implementors"]], "implementors() (matminer.featurizers.structure.rdf.electronicradialdistributionfunction method)": [[18, "matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.implementors"]], "implementors() (matminer.featurizers.structure.rdf.partialradialdistributionfunction method)": [[18, "matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.implementors"]], "implementors() (matminer.featurizers.structure.rdf.radialdistributionfunction method)": [[18, "matminer.featurizers.structure.rdf.RadialDistributionFunction.implementors"]], "implementors() (matminer.featurizers.structure.sites.partialssitestatsfingerprint method)": [[18, "matminer.featurizers.structure.sites.PartialsSiteStatsFingerprint.implementors"]], "implementors() (matminer.featurizers.structure.sites.sitestatsfingerprint method)": [[18, "matminer.featurizers.structure.sites.SiteStatsFingerprint.implementors"]], "implementors() (matminer.featurizers.structure.symmetry.dimensionality method)": [[18, "matminer.featurizers.structure.symmetry.Dimensionality.implementors"]], "implementors() (matminer.featurizers.structure.symmetry.globalsymmetryfeatures method)": [[18, "matminer.featurizers.structure.symmetry.GlobalSymmetryFeatures.implementors"]], "matminer.featurizers.structure": [[18, "module-matminer.featurizers.structure"]], "matminer.featurizers.structure.bonding": [[18, "module-matminer.featurizers.structure.bonding"]], "matminer.featurizers.structure.composite": [[18, "module-matminer.featurizers.structure.composite"]], "matminer.featurizers.structure.matrix": [[18, "module-matminer.featurizers.structure.matrix"]], "matminer.featurizers.structure.misc": [[18, "module-matminer.featurizers.structure.misc"]], "matminer.featurizers.structure.order": [[18, "module-matminer.featurizers.structure.order"]], "matminer.featurizers.structure.rdf": [[18, "module-matminer.featurizers.structure.rdf"]], "matminer.featurizers.structure.sites": [[18, "module-matminer.featurizers.structure.sites"]], "matminer.featurizers.structure.symmetry": [[18, "module-matminer.featurizers.structure.symmetry"]], "precheck() (matminer.featurizers.structure.bonding.globalinstabilityindex method)": [[18, "matminer.featurizers.structure.bonding.GlobalInstabilityIndex.precheck"]], "precheck() (matminer.featurizers.structure.order.densityfeatures method)": [[18, "matminer.featurizers.structure.order.DensityFeatures.precheck"]], "precheck() (matminer.featurizers.structure.rdf.electronicradialdistributionfunction method)": [[18, "matminer.featurizers.structure.rdf.ElectronicRadialDistributionFunction.precheck"]], "precheck() (matminer.featurizers.structure.rdf.partialradialdistributionfunction method)": [[18, "matminer.featurizers.structure.rdf.PartialRadialDistributionFunction.precheck"]], "precheck() (matminer.featurizers.structure.rdf.radialdistributionfunction method)": [[18, "matminer.featurizers.structure.rdf.RadialDistributionFunction.precheck"]], "bondingstructuretest (class in matminer.featurizers.structure.tests.test_bonding)": [[19, "matminer.featurizers.structure.tests.test_bonding.BondingStructureTest"]], "compositestructurefeaturestest (class in matminer.featurizers.structure.tests.test_composite)": [[19, "matminer.featurizers.structure.tests.test_composite.CompositeStructureFeaturesTest"]], "matrixstructurefeaturestest (class in matminer.featurizers.structure.tests.test_matrix)": [[19, "matminer.featurizers.structure.tests.test_matrix.MatrixStructureFeaturesTest"]], "miscstructurefeaturestest (class in matminer.featurizers.structure.tests.test_misc)": [[19, "matminer.featurizers.structure.tests.test_misc.MiscStructureFeaturesTest"]], "orderstructurefeaturestest (class in matminer.featurizers.structure.tests.test_order)": [[19, "matminer.featurizers.structure.tests.test_order.OrderStructureFeaturesTest"]], "partialstructuresitesfeaturestest (class in matminer.featurizers.structure.tests.test_sites)": [[19, "matminer.featurizers.structure.tests.test_sites.PartialStructureSitesFeaturesTest"]], "structurefeaturestest (class in matminer.featurizers.structure.tests.base)": [[19, "matminer.featurizers.structure.tests.base.StructureFeaturesTest"]], "structurerdftest (class in matminer.featurizers.structure.tests.test_rdf)": [[19, "matminer.featurizers.structure.tests.test_rdf.StructureRDFTest"]], "structuresitesfeaturestest (class in matminer.featurizers.structure.tests.test_sites)": [[19, "matminer.featurizers.structure.tests.test_sites.StructureSitesFeaturesTest"]], "structuresymmetryfeaturestest (class in matminer.featurizers.structure.tests.test_symmetry)": [[19, "matminer.featurizers.structure.tests.test_symmetry.StructureSymmetryFeaturesTest"]], "matminer.featurizers.structure.tests": [[19, "module-matminer.featurizers.structure.tests"]], "matminer.featurizers.structure.tests.base": [[19, "module-matminer.featurizers.structure.tests.base"]], "matminer.featurizers.structure.tests.test_bonding": [[19, "module-matminer.featurizers.structure.tests.test_bonding"]], "matminer.featurizers.structure.tests.test_composite": [[19, "module-matminer.featurizers.structure.tests.test_composite"]], "matminer.featurizers.structure.tests.test_matrix": [[19, "module-matminer.featurizers.structure.tests.test_matrix"]], "matminer.featurizers.structure.tests.test_misc": [[19, "module-matminer.featurizers.structure.tests.test_misc"]], "matminer.featurizers.structure.tests.test_order": [[19, "module-matminer.featurizers.structure.tests.test_order"]], "matminer.featurizers.structure.tests.test_rdf": [[19, "module-matminer.featurizers.structure.tests.test_rdf"]], "matminer.featurizers.structure.tests.test_sites": [[19, "module-matminer.featurizers.structure.tests.test_sites"]], "matminer.featurizers.structure.tests.test_symmetry": [[19, "module-matminer.featurizers.structure.tests.test_symmetry"]], "setup() (matminer.featurizers.structure.tests.base.structurefeaturestest method)": [[19, "matminer.featurizers.structure.tests.base.StructureFeaturesTest.setUp"]], "test_globalinstabilityindex() (matminer.featurizers.structure.tests.test_bonding.bondingstructuretest method)": [[19, "matminer.featurizers.structure.tests.test_bonding.BondingStructureTest.test_GlobalInstabilityIndex"]], "test_bob() (matminer.featurizers.structure.tests.test_bonding.bondingstructuretest method)": [[19, "matminer.featurizers.structure.tests.test_bonding.BondingStructureTest.test_bob"]], "test_bondfractions() (matminer.featurizers.structure.tests.test_bonding.bondingstructuretest method)": [[19, "matminer.featurizers.structure.tests.test_bonding.BondingStructureTest.test_bondfractions"]], "test_composition_features() (matminer.featurizers.structure.tests.test_misc.miscstructurefeaturestest method)": [[19, "matminer.featurizers.structure.tests.test_misc.MiscStructureFeaturesTest.test_composition_features"]], "test_coulomb_matrix() (matminer.featurizers.structure.tests.test_matrix.matrixstructurefeaturestest method)": [[19, "matminer.featurizers.structure.tests.test_matrix.MatrixStructureFeaturesTest.test_coulomb_matrix"]], "test_density_features() (matminer.featurizers.structure.tests.test_order.orderstructurefeaturestest method)": [[19, "matminer.featurizers.structure.tests.test_order.OrderStructureFeaturesTest.test_density_features"]], "test_dimensionality() (matminer.featurizers.structure.tests.test_symmetry.structuresymmetryfeaturestest method)": [[19, "matminer.featurizers.structure.tests.test_symmetry.StructureSymmetryFeaturesTest.test_dimensionality"]], "test_ewald() (matminer.featurizers.structure.tests.test_misc.miscstructurefeaturestest method)": [[19, "matminer.featurizers.structure.tests.test_misc.MiscStructureFeaturesTest.test_ewald"]], "test_get_rdf_bin_labels() (matminer.featurizers.structure.tests.test_rdf.structurerdftest method)": [[19, "matminer.featurizers.structure.tests.test_rdf.StructureRDFTest.test_get_rdf_bin_labels"]], "test_global_symmetry() (matminer.featurizers.structure.tests.test_symmetry.structuresymmetryfeaturestest method)": [[19, "matminer.featurizers.structure.tests.test_symmetry.StructureSymmetryFeaturesTest.test_global_symmetry"]], "test_jarviscfid() (matminer.featurizers.structure.tests.test_composite.compositestructurefeaturestest method)": [[19, "matminer.featurizers.structure.tests.test_composite.CompositeStructureFeaturesTest.test_jarvisCFID"]], "test_min_relative_distances() (matminer.featurizers.structure.tests.test_bonding.bondingstructuretest method)": [[19, "matminer.featurizers.structure.tests.test_bonding.BondingStructureTest.test_min_relative_distances"]], "test_orbital_field_matrix() (matminer.featurizers.structure.tests.test_matrix.matrixstructurefeaturestest method)": [[19, "matminer.featurizers.structure.tests.test_matrix.MatrixStructureFeaturesTest.test_orbital_field_matrix"]], "test_ordering_param() (matminer.featurizers.structure.tests.test_order.orderstructurefeaturestest method)": [[19, "matminer.featurizers.structure.tests.test_order.OrderStructureFeaturesTest.test_ordering_param"]], "test_packing_efficiency() (matminer.featurizers.structure.tests.test_order.orderstructurefeaturestest method)": [[19, "matminer.featurizers.structure.tests.test_order.OrderStructureFeaturesTest.test_packing_efficiency"]], "test_partialsitestatsfingerprint() (matminer.featurizers.structure.tests.test_sites.partialstructuresitesfeaturestest method)": [[19, "matminer.featurizers.structure.tests.test_sites.PartialStructureSitesFeaturesTest.test_partialsitestatsfingerprint"]], "test_prdf() (matminer.featurizers.structure.tests.test_rdf.structurerdftest method)": [[19, "matminer.featurizers.structure.tests.test_rdf.StructureRDFTest.test_prdf"]], "test_rdf_and_peaks() (matminer.featurizers.structure.tests.test_rdf.structurerdftest method)": [[19, "matminer.featurizers.structure.tests.test_rdf.StructureRDFTest.test_rdf_and_peaks"]], "test_redf() (matminer.featurizers.structure.tests.test_rdf.structurerdftest method)": [[19, "matminer.featurizers.structure.tests.test_rdf.StructureRDFTest.test_redf"]], "test_sine_coulomb_matrix() (matminer.featurizers.structure.tests.test_matrix.matrixstructurefeaturestest method)": [[19, "matminer.featurizers.structure.tests.test_matrix.MatrixStructureFeaturesTest.test_sine_coulomb_matrix"]], "test_sitestatsfingerprint() (matminer.featurizers.structure.tests.test_sites.structuresitesfeaturestest method)": [[19, "matminer.featurizers.structure.tests.test_sites.StructureSitesFeaturesTest.test_sitestatsfingerprint"]], "test_structural_complexity() (matminer.featurizers.structure.tests.test_order.orderstructurefeaturestest method)": [[19, "matminer.featurizers.structure.tests.test_order.OrderStructureFeaturesTest.test_structural_complexity"]], "test_ward_prb_2017_efftcn() (matminer.featurizers.structure.tests.test_sites.partialstructuresitesfeaturestest method)": [[19, "matminer.featurizers.structure.tests.test_sites.PartialStructureSitesFeaturesTest.test_ward_prb_2017_efftcn"]], "test_ward_prb_2017_efftcn() (matminer.featurizers.structure.tests.test_sites.structuresitesfeaturestest method)": [[19, "matminer.featurizers.structure.tests.test_sites.StructureSitesFeaturesTest.test_ward_prb_2017_efftcn"]], "test_ward_prb_2017_lpd() (matminer.featurizers.structure.tests.test_sites.partialstructuresitesfeaturestest method)": [[19, "matminer.featurizers.structure.tests.test_sites.PartialStructureSitesFeaturesTest.test_ward_prb_2017_lpd"]], "test_ward_prb_2017_lpd() (matminer.featurizers.structure.tests.test_sites.structuresitesfeaturestest method)": [[19, "matminer.featurizers.structure.tests.test_sites.StructureSitesFeaturesTest.test_ward_prb_2017_lpd"]], "test_ward_prb_2017_strhet() (matminer.featurizers.structure.tests.test_bonding.bondingstructuretest method)": [[19, "matminer.featurizers.structure.tests.test_bonding.BondingStructureTest.test_ward_prb_2017_strhet"]], "test_xrd_powderpattern() (matminer.featurizers.structure.tests.test_misc.miscstructurefeaturestest method)": [[19, "matminer.featurizers.structure.tests.test_misc.MiscStructureFeaturesTest.test_xrd_powderPattern"]], "bandstructurefeaturestest (class in matminer.featurizers.tests.test_bandstructure)": [[20, "matminer.featurizers.tests.test_bandstructure.BandstructureFeaturesTest"]], "dosfeaturestest (class in matminer.featurizers.tests.test_dos)": [[20, "matminer.featurizers.tests.test_dos.DOSFeaturesTest"]], "fittablefeaturizer (class in matminer.featurizers.tests.test_base)": [[20, "matminer.featurizers.tests.test_base.FittableFeaturizer"]], "matrixfeaturizer (class in matminer.featurizers.tests.test_base)": [[20, "matminer.featurizers.tests.test_base.MatrixFeaturizer"]], "multiargs2 (class in matminer.featurizers.tests.test_base)": [[20, "matminer.featurizers.tests.test_base.MultiArgs2"]], "multitypefeaturizer (class in matminer.featurizers.tests.test_base)": [[20, "matminer.featurizers.tests.test_base.MultiTypeFeaturizer"]], "multiplefeaturefeaturizer (class in matminer.featurizers.tests.test_base)": [[20, "matminer.featurizers.tests.test_base.MultipleFeatureFeaturizer"]], "singlefeaturizer (class in matminer.featurizers.tests.test_base)": [[20, "matminer.featurizers.tests.test_base.SingleFeaturizer"]], "singlefeaturizermultiargs (class in matminer.featurizers.tests.test_base)": [[20, "matminer.featurizers.tests.test_base.SingleFeaturizerMultiArgs"]], "singlefeaturizermultiargswithprecheck (class in matminer.featurizers.tests.test_base)": [[20, "matminer.featurizers.tests.test_base.SingleFeaturizerMultiArgsWithPrecheck"]], "singlefeaturizerwithprecheck (class in matminer.featurizers.tests.test_base)": [[20, "matminer.featurizers.tests.test_base.SingleFeaturizerWithPrecheck"]], "testbaseclass (class in matminer.featurizers.tests.test_base)": [[20, "matminer.featurizers.tests.test_base.TestBaseClass"]], "testconversions (class in matminer.featurizers.tests.test_conversions)": [[20, "matminer.featurizers.tests.test_conversions.TestConversions"]], "testfunctionfeaturizer (class in matminer.featurizers.tests.test_function)": [[20, "matminer.featurizers.tests.test_function.TestFunctionFeaturizer"]], "__init__() (matminer.featurizers.tests.test_base.multiargs2 method)": [[20, "matminer.featurizers.tests.test_base.MultiArgs2.__init__"]], "citations() (matminer.featurizers.tests.test_base.fittablefeaturizer method)": [[20, "matminer.featurizers.tests.test_base.FittableFeaturizer.citations"]], "citations() (matminer.featurizers.tests.test_base.matrixfeaturizer method)": [[20, "matminer.featurizers.tests.test_base.MatrixFeaturizer.citations"]], "citations() (matminer.featurizers.tests.test_base.multiargs2 method)": [[20, "matminer.featurizers.tests.test_base.MultiArgs2.citations"]], "citations() (matminer.featurizers.tests.test_base.multitypefeaturizer method)": [[20, "matminer.featurizers.tests.test_base.MultiTypeFeaturizer.citations"]], "citations() (matminer.featurizers.tests.test_base.multiplefeaturefeaturizer method)": [[20, "matminer.featurizers.tests.test_base.MultipleFeatureFeaturizer.citations"]], "citations() (matminer.featurizers.tests.test_base.singlefeaturizer method)": [[20, "matminer.featurizers.tests.test_base.SingleFeaturizer.citations"]], "feature_labels() (matminer.featurizers.tests.test_base.fittablefeaturizer method)": [[20, "matminer.featurizers.tests.test_base.FittableFeaturizer.feature_labels"]], "feature_labels() (matminer.featurizers.tests.test_base.matrixfeaturizer method)": [[20, "matminer.featurizers.tests.test_base.MatrixFeaturizer.feature_labels"]], "feature_labels() (matminer.featurizers.tests.test_base.multiargs2 method)": [[20, "matminer.featurizers.tests.test_base.MultiArgs2.feature_labels"]], "feature_labels() (matminer.featurizers.tests.test_base.multitypefeaturizer method)": [[20, "matminer.featurizers.tests.test_base.MultiTypeFeaturizer.feature_labels"]], "feature_labels() (matminer.featurizers.tests.test_base.multiplefeaturefeaturizer method)": [[20, "matminer.featurizers.tests.test_base.MultipleFeatureFeaturizer.feature_labels"]], "feature_labels() (matminer.featurizers.tests.test_base.singlefeaturizer method)": [[20, "matminer.featurizers.tests.test_base.SingleFeaturizer.feature_labels"]], "featurize() (matminer.featurizers.tests.test_base.fittablefeaturizer method)": [[20, "matminer.featurizers.tests.test_base.FittableFeaturizer.featurize"]], "featurize() (matminer.featurizers.tests.test_base.matrixfeaturizer method)": [[20, "matminer.featurizers.tests.test_base.MatrixFeaturizer.featurize"]], "featurize() (matminer.featurizers.tests.test_base.multiargs2 method)": [[20, "matminer.featurizers.tests.test_base.MultiArgs2.featurize"]], "featurize() (matminer.featurizers.tests.test_base.multitypefeaturizer method)": [[20, "matminer.featurizers.tests.test_base.MultiTypeFeaturizer.featurize"]], "featurize() (matminer.featurizers.tests.test_base.multiplefeaturefeaturizer method)": [[20, "matminer.featurizers.tests.test_base.MultipleFeatureFeaturizer.featurize"]], "featurize() (matminer.featurizers.tests.test_base.singlefeaturizer method)": [[20, "matminer.featurizers.tests.test_base.SingleFeaturizer.featurize"]], "featurize() (matminer.featurizers.tests.test_base.singlefeaturizermultiargs method)": [[20, "matminer.featurizers.tests.test_base.SingleFeaturizerMultiArgs.featurize"]], "fit() (matminer.featurizers.tests.test_base.fittablefeaturizer method)": [[20, "matminer.featurizers.tests.test_base.FittableFeaturizer.fit"]], "implementors() (matminer.featurizers.tests.test_base.fittablefeaturizer method)": [[20, "matminer.featurizers.tests.test_base.FittableFeaturizer.implementors"]], "implementors() (matminer.featurizers.tests.test_base.matrixfeaturizer method)": [[20, "matminer.featurizers.tests.test_base.MatrixFeaturizer.implementors"]], "implementors() (matminer.featurizers.tests.test_base.multiargs2 method)": [[20, "matminer.featurizers.tests.test_base.MultiArgs2.implementors"]], "implementors() (matminer.featurizers.tests.test_base.multitypefeaturizer method)": [[20, "matminer.featurizers.tests.test_base.MultiTypeFeaturizer.implementors"]], "implementors() (matminer.featurizers.tests.test_base.multiplefeaturefeaturizer method)": [[20, "matminer.featurizers.tests.test_base.MultipleFeatureFeaturizer.implementors"]], "implementors() (matminer.featurizers.tests.test_base.singlefeaturizer method)": [[20, "matminer.featurizers.tests.test_base.SingleFeaturizer.implementors"]], "make_test_data() (matminer.featurizers.tests.test_base.testbaseclass static method)": [[20, "matminer.featurizers.tests.test_base.TestBaseClass.make_test_data"]], "matminer.featurizers.tests": [[20, "module-matminer.featurizers.tests"]], "matminer.featurizers.tests.test_bandstructure": [[20, "module-matminer.featurizers.tests.test_bandstructure"]], "matminer.featurizers.tests.test_base": [[20, "module-matminer.featurizers.tests.test_base"]], "matminer.featurizers.tests.test_conversions": [[20, "module-matminer.featurizers.tests.test_conversions"]], "matminer.featurizers.tests.test_dos": [[20, "module-matminer.featurizers.tests.test_dos"]], "matminer.featurizers.tests.test_function": [[20, "module-matminer.featurizers.tests.test_function"]], "precheck() (matminer.featurizers.tests.test_base.singlefeaturizermultiargswithprecheck method)": [[20, "matminer.featurizers.tests.test_base.SingleFeaturizerMultiArgsWithPrecheck.precheck"]], "precheck() (matminer.featurizers.tests.test_base.singlefeaturizerwithprecheck method)": [[20, "matminer.featurizers.tests.test_base.SingleFeaturizerWithPrecheck.precheck"]], "setup() (matminer.featurizers.tests.test_bandstructure.bandstructurefeaturestest method)": [[20, "matminer.featurizers.tests.test_bandstructure.BandstructureFeaturesTest.setUp"]], "setup() (matminer.featurizers.tests.test_base.testbaseclass method)": [[20, "matminer.featurizers.tests.test_base.TestBaseClass.setUp"]], "setup() (matminer.featurizers.tests.test_dos.dosfeaturestest method)": [[20, "matminer.featurizers.tests.test_dos.DOSFeaturesTest.setUp"]], "setup() (matminer.featurizers.tests.test_function.testfunctionfeaturizer method)": [[20, "matminer.featurizers.tests.test_function.TestFunctionFeaturizer.setUp"]], "test_bandfeaturizer() (matminer.featurizers.tests.test_bandstructure.bandstructurefeaturestest method)": [[20, "matminer.featurizers.tests.test_bandstructure.BandstructureFeaturesTest.test_BandFeaturizer"]], "test_branchpointenergy() (matminer.featurizers.tests.test_bandstructure.bandstructurefeaturestest method)": [[20, "matminer.featurizers.tests.test_bandstructure.BandstructureFeaturesTest.test_BranchPointEnergy"]], "test_dosfeaturizer() (matminer.featurizers.tests.test_dos.dosfeaturestest method)": [[20, "matminer.featurizers.tests.test_dos.DOSFeaturesTest.test_DOSFeaturizer"]], "test_dopingfermi() (matminer.featurizers.tests.test_dos.dosfeaturestest method)": [[20, "matminer.featurizers.tests.test_dos.DOSFeaturesTest.test_DopingFermi"]], "test_dosasymmetry() (matminer.featurizers.tests.test_dos.dosfeaturestest method)": [[20, "matminer.featurizers.tests.test_dos.DOSFeaturesTest.test_DosAsymmetry"]], "test_hybridization() (matminer.featurizers.tests.test_dos.dosfeaturestest method)": [[20, "matminer.featurizers.tests.test_dos.DOSFeaturesTest.test_Hybridization"]], "test_sitedos() (matminer.featurizers.tests.test_dos.dosfeaturestest method)": [[20, "matminer.featurizers.tests.test_dos.DOSFeaturesTest.test_SiteDOS"]], "test_ase_conversion() (matminer.featurizers.tests.test_conversions.testconversions method)": [[20, "matminer.featurizers.tests.test_conversions.TestConversions.test_ase_conversion"]], "test_caching() (matminer.featurizers.tests.test_base.testbaseclass method)": [[20, "matminer.featurizers.tests.test_base.TestBaseClass.test_caching"]], "test_composition_to_oxidcomposition() (matminer.featurizers.tests.test_conversions.testconversions method)": [[20, "matminer.featurizers.tests.test_conversions.TestConversions.test_composition_to_oxidcomposition"]], "test_composition_to_structurefrommp() (matminer.featurizers.tests.test_conversions.testconversions method)": [[20, "matminer.featurizers.tests.test_conversions.TestConversions.test_composition_to_structurefromMP"]], "test_conversion_multiindex() (matminer.featurizers.tests.test_conversions.testconversions method)": [[20, "matminer.featurizers.tests.test_conversions.TestConversions.test_conversion_multiindex"]], "test_conversion_multiindex_dynamic() (matminer.featurizers.tests.test_conversions.testconversions method)": [[20, "matminer.featurizers.tests.test_conversions.TestConversions.test_conversion_multiindex_dynamic"]], "test_conversion_overwrite() (matminer.featurizers.tests.test_conversions.testconversions method)": [[20, "matminer.featurizers.tests.test_conversions.TestConversions.test_conversion_overwrite"]], "test_dataframe() (matminer.featurizers.tests.test_base.testbaseclass method)": [[20, "matminer.featurizers.tests.test_base.TestBaseClass.test_dataframe"]], "test_dict_to_object() (matminer.featurizers.tests.test_conversions.testconversions method)": [[20, "matminer.featurizers.tests.test_conversions.TestConversions.test_dict_to_object"]], "test_featurize() (matminer.featurizers.tests.test_function.testfunctionfeaturizer method)": [[20, "matminer.featurizers.tests.test_function.TestFunctionFeaturizer.test_featurize"]], "test_featurize_labels() (matminer.featurizers.tests.test_function.testfunctionfeaturizer method)": [[20, "matminer.featurizers.tests.test_function.TestFunctionFeaturizer.test_featurize_labels"]], "test_featurize_many() (matminer.featurizers.tests.test_base.testbaseclass method)": [[20, "matminer.featurizers.tests.test_base.TestBaseClass.test_featurize_many"]], "test_fittable() (matminer.featurizers.tests.test_base.testbaseclass method)": [[20, "matminer.featurizers.tests.test_base.TestBaseClass.test_fittable"]], "test_helper_functions() (matminer.featurizers.tests.test_function.testfunctionfeaturizer method)": [[20, "matminer.featurizers.tests.test_function.TestFunctionFeaturizer.test_helper_functions"]], "test_ignore_errors() (matminer.featurizers.tests.test_base.testbaseclass method)": [[20, "matminer.featurizers.tests.test_base.TestBaseClass.test_ignore_errors"]], "test_indices() (matminer.featurizers.tests.test_base.testbaseclass method)": [[20, "matminer.featurizers.tests.test_base.TestBaseClass.test_indices"]], "test_inplace() (matminer.featurizers.tests.test_base.testbaseclass method)": [[20, "matminer.featurizers.tests.test_base.TestBaseClass.test_inplace"]], "test_json_to_object() (matminer.featurizers.tests.test_conversions.testconversions method)": [[20, "matminer.featurizers.tests.test_conversions.TestConversions.test_json_to_object"]], "test_matrix() (matminer.featurizers.tests.test_base.testbaseclass method)": [[20, "matminer.featurizers.tests.test_base.TestBaseClass.test_matrix"]], "test_multi_featurizer() (matminer.featurizers.tests.test_function.testfunctionfeaturizer method)": [[20, "matminer.featurizers.tests.test_function.TestFunctionFeaturizer.test_multi_featurizer"]], "test_multifeature_no_zero_index() (matminer.featurizers.tests.test_base.testbaseclass method)": [[20, "matminer.featurizers.tests.test_base.TestBaseClass.test_multifeature_no_zero_index"]], "test_multifeatures_multiargs() (matminer.featurizers.tests.test_base.testbaseclass method)": [[20, "matminer.featurizers.tests.test_base.TestBaseClass.test_multifeatures_multiargs"]], "test_multiindex_in_multifeaturizer() (matminer.featurizers.tests.test_base.testbaseclass method)": [[20, "matminer.featurizers.tests.test_base.TestBaseClass.test_multiindex_in_multifeaturizer"]], "test_multiindex_inplace() (matminer.featurizers.tests.test_base.testbaseclass method)": [[20, "matminer.featurizers.tests.test_base.TestBaseClass.test_multiindex_inplace"]], "test_multiindex_return() (matminer.featurizers.tests.test_base.testbaseclass method)": [[20, "matminer.featurizers.tests.test_base.TestBaseClass.test_multiindex_return"]], "test_multiple() (matminer.featurizers.tests.test_base.testbaseclass method)": [[20, "matminer.featurizers.tests.test_base.TestBaseClass.test_multiple"]], "test_multiprocessing_df() (matminer.featurizers.tests.test_base.testbaseclass method)": [[20, "matminer.featurizers.tests.test_base.TestBaseClass.test_multiprocessing_df"]], "test_multitype_multifeat() (matminer.featurizers.tests.test_base.testbaseclass method)": [[20, "matminer.featurizers.tests.test_base.TestBaseClass.test_multitype_multifeat"]], "test_precheck() (matminer.featurizers.tests.test_base.testbaseclass method)": [[20, "matminer.featurizers.tests.test_base.TestBaseClass.test_precheck"]], "test_pymatgen_general_converter() (matminer.featurizers.tests.test_conversions.testconversions method)": [[20, "matminer.featurizers.tests.test_conversions.TestConversions.test_pymatgen_general_converter"]], "test_stacked_featurizer() (matminer.featurizers.tests.test_base.testbaseclass method)": [[20, "matminer.featurizers.tests.test_base.TestBaseClass.test_stacked_featurizer"]], "test_str_to_composition() (matminer.featurizers.tests.test_conversions.testconversions method)": [[20, "matminer.featurizers.tests.test_conversions.TestConversions.test_str_to_composition"]], "test_structure_to_composition() (matminer.featurizers.tests.test_conversions.testconversions method)": [[20, "matminer.featurizers.tests.test_conversions.TestConversions.test_structure_to_composition"]], "test_structure_to_oxidstructure() (matminer.featurizers.tests.test_conversions.testconversions method)": [[20, "matminer.featurizers.tests.test_conversions.TestConversions.test_structure_to_oxidstructure"]], "test_to_istructure() (matminer.featurizers.tests.test_conversions.testconversions method)": [[20, "matminer.featurizers.tests.test_conversions.TestConversions.test_to_istructure"]], "abstractpairwise (class in matminer.featurizers.utils.grdf)": [[21, "matminer.featurizers.utils.grdf.AbstractPairwise"]], "bessel (class in matminer.featurizers.utils.grdf)": [[21, "matminer.featurizers.utils.grdf.Bessel"]], "cosine (class in matminer.featurizers.utils.grdf)": [[21, "matminer.featurizers.utils.grdf.Cosine"]], "gaussian (class in matminer.featurizers.utils.grdf)": [[21, "matminer.featurizers.utils.grdf.Gaussian"]], "histogram (class in matminer.featurizers.utils.grdf)": [[21, "matminer.featurizers.utils.grdf.Histogram"]], "propertystats (class in matminer.featurizers.utils.stats)": [[21, "matminer.featurizers.utils.stats.PropertyStats"]], "sine (class in matminer.featurizers.utils.grdf)": [[21, "matminer.featurizers.utils.grdf.Sine"]], "__init__() (matminer.featurizers.utils.grdf.bessel method)": [[21, "matminer.featurizers.utils.grdf.Bessel.__init__"]], "__init__() (matminer.featurizers.utils.grdf.cosine method)": [[21, "matminer.featurizers.utils.grdf.Cosine.__init__"]], "__init__() (matminer.featurizers.utils.grdf.gaussian method)": [[21, "matminer.featurizers.utils.grdf.Gaussian.__init__"]], "__init__() (matminer.featurizers.utils.grdf.histogram method)": [[21, "matminer.featurizers.utils.grdf.Histogram.__init__"]], "__init__() (matminer.featurizers.utils.grdf.sine method)": [[21, "matminer.featurizers.utils.grdf.Sine.__init__"]], "avg_dev() (matminer.featurizers.utils.stats.propertystats static method)": [[21, "matminer.featurizers.utils.stats.PropertyStats.avg_dev"]], "calc_stat() (matminer.featurizers.utils.stats.propertystats static method)": [[21, "matminer.featurizers.utils.stats.PropertyStats.calc_stat"]], "eigenvalues() (matminer.featurizers.utils.stats.propertystats static method)": [[21, "matminer.featurizers.utils.stats.PropertyStats.eigenvalues"]], "flatten() (matminer.featurizers.utils.stats.propertystats static method)": [[21, "matminer.featurizers.utils.stats.PropertyStats.flatten"]], "geom_std_dev() (matminer.featurizers.utils.stats.propertystats static method)": [[21, "matminer.featurizers.utils.stats.PropertyStats.geom_std_dev"]], "has_oxidation_states() (in module matminer.featurizers.utils.oxidation)": [[21, "matminer.featurizers.utils.oxidation.has_oxidation_states"]], "holder_mean() (matminer.featurizers.utils.stats.propertystats static method)": [[21, "matminer.featurizers.utils.stats.PropertyStats.holder_mean"]], "initialize_pairwise_function() (in module matminer.featurizers.utils.grdf)": [[21, "matminer.featurizers.utils.grdf.initialize_pairwise_function"]], "inverse_mean() (matminer.featurizers.utils.stats.propertystats static method)": [[21, "matminer.featurizers.utils.stats.PropertyStats.inverse_mean"]], "kurtosis() (matminer.featurizers.utils.stats.propertystats static method)": [[21, "matminer.featurizers.utils.stats.PropertyStats.kurtosis"]], "matminer.featurizers.utils": [[21, "module-matminer.featurizers.utils"]], "matminer.featurizers.utils.grdf": [[21, "module-matminer.featurizers.utils.grdf"]], "matminer.featurizers.utils.oxidation": [[21, "module-matminer.featurizers.utils.oxidation"]], "matminer.featurizers.utils.stats": [[21, "module-matminer.featurizers.utils.stats"]], "maximum() (matminer.featurizers.utils.stats.propertystats static method)": [[21, "matminer.featurizers.utils.stats.PropertyStats.maximum"]], "mean() (matminer.featurizers.utils.stats.propertystats static method)": [[21, "matminer.featurizers.utils.stats.PropertyStats.mean"]], "minimum() (matminer.featurizers.utils.stats.propertystats static method)": [[21, "matminer.featurizers.utils.stats.PropertyStats.minimum"]], "mode() (matminer.featurizers.utils.stats.propertystats static method)": [[21, "matminer.featurizers.utils.stats.PropertyStats.mode"]], "name() (matminer.featurizers.utils.grdf.abstractpairwise method)": [[21, "matminer.featurizers.utils.grdf.AbstractPairwise.name"]], "quantile() (matminer.featurizers.utils.stats.propertystats static method)": [[21, "matminer.featurizers.utils.stats.PropertyStats.quantile"]], "range() (matminer.featurizers.utils.stats.propertystats static method)": [[21, "matminer.featurizers.utils.stats.PropertyStats.range"]], "skewness() (matminer.featurizers.utils.stats.propertystats static method)": [[21, "matminer.featurizers.utils.stats.PropertyStats.skewness"]], "sorted() (matminer.featurizers.utils.stats.propertystats static method)": [[21, "matminer.featurizers.utils.stats.PropertyStats.sorted"]], "std_dev() (matminer.featurizers.utils.stats.propertystats static method)": [[21, "matminer.featurizers.utils.stats.PropertyStats.std_dev"]], "volume() (matminer.featurizers.utils.grdf.abstractpairwise method)": [[21, "matminer.featurizers.utils.grdf.AbstractPairwise.volume"]], "volume() (matminer.featurizers.utils.grdf.cosine method)": [[21, "matminer.featurizers.utils.grdf.Cosine.volume"]], "volume() (matminer.featurizers.utils.grdf.gaussian method)": [[21, "matminer.featurizers.utils.grdf.Gaussian.volume"]], "volume() (matminer.featurizers.utils.grdf.histogram method)": [[21, "matminer.featurizers.utils.grdf.Histogram.volume"]], "volume() (matminer.featurizers.utils.grdf.sine method)": [[21, "matminer.featurizers.utils.grdf.Sine.volume"]], "grdftests (class in matminer.featurizers.utils.tests.test_grdf)": [[22, "matminer.featurizers.utils.tests.test_grdf.GRDFTests"]], "oxidationtest (class in matminer.featurizers.utils.tests.test_oxidation)": [[22, "matminer.featurizers.utils.tests.test_oxidation.OxidationTest"]], "testpropertystats (class in matminer.featurizers.utils.tests.test_stats)": [[22, "matminer.featurizers.utils.tests.test_stats.TestPropertyStats"]], "matminer.featurizers.utils.tests": [[22, "module-matminer.featurizers.utils.tests"]], "matminer.featurizers.utils.tests.test_grdf": [[22, "module-matminer.featurizers.utils.tests.test_grdf"]], "matminer.featurizers.utils.tests.test_oxidation": [[22, "module-matminer.featurizers.utils.tests.test_oxidation"]], "matminer.featurizers.utils.tests.test_stats": [[22, "module-matminer.featurizers.utils.tests.test_stats"]], "setup() (matminer.featurizers.utils.tests.test_stats.testpropertystats method)": [[22, "matminer.featurizers.utils.tests.test_stats.TestPropertyStats.setUp"]], "test_avg_dev() (matminer.featurizers.utils.tests.test_stats.testpropertystats method)": [[22, "matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_avg_dev"]], "test_bessel() (matminer.featurizers.utils.tests.test_grdf.grdftests method)": [[22, "matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_bessel"]], "test_cosine() (matminer.featurizers.utils.tests.test_grdf.grdftests method)": [[22, "matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_cosine"]], "test_gaussian() (matminer.featurizers.utils.tests.test_grdf.grdftests method)": [[22, "matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_gaussian"]], "test_geom_std_dev() (matminer.featurizers.utils.tests.test_stats.testpropertystats method)": [[22, "matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_geom_std_dev"]], "test_has_oxidation_states() (matminer.featurizers.utils.tests.test_oxidation.oxidationtest method)": [[22, "matminer.featurizers.utils.tests.test_oxidation.OxidationTest.test_has_oxidation_states"]], "test_histogram() (matminer.featurizers.utils.tests.test_grdf.grdftests method)": [[22, "matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_histogram"]], "test_holder_mean() (matminer.featurizers.utils.tests.test_stats.testpropertystats method)": [[22, "matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_holder_mean"]], "test_kurtosis() (matminer.featurizers.utils.tests.test_stats.testpropertystats method)": [[22, "matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_kurtosis"]], "test_load_class() (matminer.featurizers.utils.tests.test_grdf.grdftests method)": [[22, "matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_load_class"]], "test_maximum() (matminer.featurizers.utils.tests.test_stats.testpropertystats method)": [[22, "matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_maximum"]], "test_mean() (matminer.featurizers.utils.tests.test_stats.testpropertystats method)": [[22, "matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_mean"]], "test_minimum() (matminer.featurizers.utils.tests.test_stats.testpropertystats method)": [[22, "matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_minimum"]], "test_mode() (matminer.featurizers.utils.tests.test_stats.testpropertystats method)": [[22, "matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_mode"]], "test_quantile() (matminer.featurizers.utils.tests.test_stats.testpropertystats method)": [[22, "matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_quantile"]], "test_range() (matminer.featurizers.utils.tests.test_stats.testpropertystats method)": [[22, "matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_range"]], "test_sin() (matminer.featurizers.utils.tests.test_grdf.grdftests method)": [[22, "matminer.featurizers.utils.tests.test_grdf.GRDFTests.test_sin"]], "test_skewness() (matminer.featurizers.utils.tests.test_stats.testpropertystats method)": [[22, "matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_skewness"]], "test_std_dev() (matminer.featurizers.utils.tests.test_stats.testpropertystats method)": [[22, "matminer.featurizers.utils.tests.test_stats.TestPropertyStats.test_std_dev"]], "abstractdata (class in matminer.utils.data)": [[25, "matminer.utils.data.AbstractData"]], "cohesiveenergydata (class in matminer.utils.data)": [[25, "matminer.utils.data.CohesiveEnergyData"]], "demldata (class in matminer.utils.data)": [[25, "matminer.utils.data.DemlData"]], "dropexcluded (class in matminer.utils.pipeline)": [[25, "matminer.utils.pipeline.DropExcluded"]], "iucrbondvalencedata (class in matminer.utils.data)": [[25, "matminer.utils.data.IUCrBondValenceData"]], "itemselector (class in matminer.utils.pipeline)": [[25, "matminer.utils.pipeline.ItemSelector"]], "megnetelementdata (class in matminer.utils.data)": [[25, "matminer.utils.data.MEGNetElementData"]], "magpiedata (class in matminer.utils.data)": [[25, "matminer.utils.data.MagpieData"]], "matscholarelementdata (class in matminer.utils.data)": [[25, "matminer.utils.data.MatscholarElementData"]], "mixingenthalpy (class in matminer.utils.data)": [[25, "matminer.utils.data.MixingEnthalpy"]], "oxidationstatedependentdata (class in matminer.utils.data)": [[25, "matminer.utils.data.OxidationStateDependentData"]], "oxidationstatesmixin (class in matminer.utils.data)": [[25, "matminer.utils.data.OxidationStatesMixin"]], "pymatgendata (class in matminer.utils.data)": [[25, "matminer.utils.data.PymatgenData"]], "__init__() (matminer.utils.data.cohesiveenergydata method)": [[25, "matminer.utils.data.CohesiveEnergyData.__init__"]], "__init__() (matminer.utils.data.demldata method)": [[25, "matminer.utils.data.DemlData.__init__"]], "__init__() (matminer.utils.data.iucrbondvalencedata method)": [[25, "matminer.utils.data.IUCrBondValenceData.__init__"]], "__init__() (matminer.utils.data.megnetelementdata method)": [[25, "matminer.utils.data.MEGNetElementData.__init__"]], "__init__() (matminer.utils.data.magpiedata method)": [[25, "matminer.utils.data.MagpieData.__init__"]], "__init__() (matminer.utils.data.matscholarelementdata method)": [[25, "matminer.utils.data.MatscholarElementData.__init__"]], "__init__() (matminer.utils.data.mixingenthalpy method)": [[25, "matminer.utils.data.MixingEnthalpy.__init__"]], "__init__() (matminer.utils.data.pymatgendata method)": [[25, "matminer.utils.data.PymatgenData.__init__"]], "__init__() (matminer.utils.pipeline.dropexcluded method)": [[25, "matminer.utils.pipeline.DropExcluded.__init__"]], "__init__() (matminer.utils.pipeline.itemselector method)": [[25, "matminer.utils.pipeline.ItemSelector.__init__"]], "fit() (matminer.utils.pipeline.dropexcluded method)": [[25, "matminer.utils.pipeline.DropExcluded.fit"]], "fit() (matminer.utils.pipeline.itemselector method)": [[25, "matminer.utils.pipeline.ItemSelector.fit"]], "flatten_dict() (in module matminer.utils.flatten_dict)": [[25, "matminer.utils.flatten_dict.flatten_dict"]], "gaussian_kernel() (in module matminer.utils.kernels)": [[25, "matminer.utils.kernels.gaussian_kernel"]], "get_all_nearest_neighbors() (in module matminer.utils.caching)": [[25, "matminer.utils.caching.get_all_nearest_neighbors"]], "get_bv_params() (matminer.utils.data.iucrbondvalencedata method)": [[25, "matminer.utils.data.IUCrBondValenceData.get_bv_params"]], "get_charge_dependent_property() (matminer.utils.data.demldata method)": [[25, "matminer.utils.data.DemlData.get_charge_dependent_property"]], "get_charge_dependent_property() (matminer.utils.data.oxidationstatedependentdata method)": [[25, "matminer.utils.data.OxidationStateDependentData.get_charge_dependent_property"]], "get_charge_dependent_property() (matminer.utils.data.pymatgendata method)": [[25, "matminer.utils.data.PymatgenData.get_charge_dependent_property"]], "get_charge_dependent_property_from_specie() (matminer.utils.data.oxidationstatedependentdata method)": [[25, "matminer.utils.data.OxidationStateDependentData.get_charge_dependent_property_from_specie"]], "get_elemental_properties() (matminer.utils.data.abstractdata method)": [[25, "matminer.utils.data.AbstractData.get_elemental_properties"]], "get_elemental_property() (matminer.utils.data.abstractdata method)": [[25, "matminer.utils.data.AbstractData.get_elemental_property"]], "get_elemental_property() (matminer.utils.data.cohesiveenergydata method)": [[25, "matminer.utils.data.CohesiveEnergyData.get_elemental_property"]], "get_elemental_property() (matminer.utils.data.demldata method)": [[25, "matminer.utils.data.DemlData.get_elemental_property"]], "get_elemental_property() (matminer.utils.data.megnetelementdata method)": [[25, "matminer.utils.data.MEGNetElementData.get_elemental_property"]], "get_elemental_property() (matminer.utils.data.magpiedata method)": [[25, "matminer.utils.data.MagpieData.get_elemental_property"]], "get_elemental_property() (matminer.utils.data.matscholarelementdata method)": [[25, "matminer.utils.data.MatscholarElementData.get_elemental_property"]], "get_elemental_property() (matminer.utils.data.pymatgendata method)": [[25, "matminer.utils.data.PymatgenData.get_elemental_property"]], "get_mixing_enthalpy() (matminer.utils.data.mixingenthalpy method)": [[25, "matminer.utils.data.MixingEnthalpy.get_mixing_enthalpy"]], "get_nearest_neighbors() (in module matminer.utils.caching)": [[25, "matminer.utils.caching.get_nearest_neighbors"]], "get_oxidation_states() (matminer.utils.data.demldata method)": [[25, "matminer.utils.data.DemlData.get_oxidation_states"]], "get_oxidation_states() (matminer.utils.data.magpiedata method)": [[25, "matminer.utils.data.MagpieData.get_oxidation_states"]], "get_oxidation_states() (matminer.utils.data.oxidationstatesmixin method)": [[25, "matminer.utils.data.OxidationStatesMixin.get_oxidation_states"]], "get_oxidation_states() (matminer.utils.data.pymatgendata method)": [[25, "matminer.utils.data.PymatgenData.get_oxidation_states"]], "homogenize_multiindex() (in module matminer.utils.utils)": [[25, "matminer.utils.utils.homogenize_multiindex"]], "interpolate_soft_anions() (matminer.utils.data.iucrbondvalencedata method)": [[25, "matminer.utils.data.IUCrBondValenceData.interpolate_soft_anions"]], "laplacian_kernel() (in module matminer.utils.kernels)": [[25, "matminer.utils.kernels.laplacian_kernel"]], "load_dataframe_from_json() (in module matminer.utils.io)": [[25, "matminer.utils.io.load_dataframe_from_json"]], "matminer.utils": [[25, "module-matminer.utils"]], "matminer.utils.caching": [[25, "module-matminer.utils.caching"]], "matminer.utils.data": [[25, "module-matminer.utils.data"]], "matminer.utils.flatten_dict": [[25, "module-matminer.utils.flatten_dict"]], "matminer.utils.io": [[25, "module-matminer.utils.io"]], "matminer.utils.kernels": [[25, "module-matminer.utils.kernels"]], "matminer.utils.pipeline": [[25, "module-matminer.utils.pipeline"]], "matminer.utils.utils": [[25, "module-matminer.utils.utils"]], "store_dataframe_as_json() (in module matminer.utils.io)": [[25, "matminer.utils.io.store_dataframe_as_json"]], "transform() (matminer.utils.pipeline.dropexcluded method)": [[25, "matminer.utils.pipeline.DropExcluded.transform"]], "transform() (matminer.utils.pipeline.itemselector method)": [[25, "matminer.utils.pipeline.ItemSelector.transform"]], "matminer.utils.data_files": [[26, "module-matminer.utils.data_files"]], "matminer.utils.data_files.deml_elementdata": [[26, "module-matminer.utils.data_files.deml_elementdata"]], "flattendicttest (class in matminer.utils.tests.test_flatten_dict)": [[27, "matminer.utils.tests.test_flatten_dict.FlattenDictTest"]], "iotest (class in matminer.utils.tests.test_io)": [[27, "matminer.utils.tests.test_io.IOTest"]], "testcaching (class in matminer.utils.tests.test_caching)": [[27, "matminer.utils.tests.test_caching.TestCaching"]], "testdemldata (class in matminer.utils.tests.test_data)": [[27, "matminer.utils.tests.test_data.TestDemlData"]], "testiucrbondvalencedata (class in matminer.utils.tests.test_data)": [[27, "matminer.utils.tests.test_data.TestIUCrBondValenceData"]], "testmegnetdata (class in matminer.utils.tests.test_data)": [[27, "matminer.utils.tests.test_data.TestMEGNetData"]], "testmagpiedata (class in matminer.utils.tests.test_data)": [[27, "matminer.utils.tests.test_data.TestMagpieData"]], "testmatscholardata (class in matminer.utils.tests.test_data)": [[27, "matminer.utils.tests.test_data.TestMatScholarData"]], "testmixingenthalpy (class in matminer.utils.tests.test_data)": [[27, "matminer.utils.tests.test_data.TestMixingEnthalpy"]], "testpymatgendata (class in matminer.utils.tests.test_data)": [[27, "matminer.utils.tests.test_data.TestPymatgenData"]], "generate_json_files() (in module matminer.utils.tests.test_io)": [[27, "matminer.utils.tests.test_io.generate_json_files"]], "matminer.utils.tests": [[27, "module-matminer.utils.tests"]], "matminer.utils.tests.test_caching": [[27, "module-matminer.utils.tests.test_caching"]], "matminer.utils.tests.test_data": [[27, "module-matminer.utils.tests.test_data"]], "matminer.utils.tests.test_flatten_dict": [[27, "module-matminer.utils.tests.test_flatten_dict"]], "matminer.utils.tests.test_io": [[27, "module-matminer.utils.tests.test_io"]], "setup() (matminer.utils.tests.test_data.testdemldata method)": [[27, "matminer.utils.tests.test_data.TestDemlData.setUp"]], "setup() (matminer.utils.tests.test_data.testiucrbondvalencedata method)": [[27, "matminer.utils.tests.test_data.TestIUCrBondValenceData.setUp"]], "setup() (matminer.utils.tests.test_data.testmegnetdata method)": [[27, "matminer.utils.tests.test_data.TestMEGNetData.setUp"]], "setup() (matminer.utils.tests.test_data.testmagpiedata method)": [[27, "matminer.utils.tests.test_data.TestMagpieData.setUp"]], "setup() (matminer.utils.tests.test_data.testmatscholardata method)": [[27, "matminer.utils.tests.test_data.TestMatScholarData.setUp"]], "setup() (matminer.utils.tests.test_data.testmixingenthalpy method)": [[27, "matminer.utils.tests.test_data.TestMixingEnthalpy.setUp"]], "setup() (matminer.utils.tests.test_data.testpymatgendata method)": [[27, "matminer.utils.tests.test_data.TestPymatgenData.setUp"]], "setup() (matminer.utils.tests.test_io.iotest method)": [[27, "matminer.utils.tests.test_io.IOTest.setUp"]], "teardown() (matminer.utils.tests.test_io.iotest method)": [[27, "matminer.utils.tests.test_io.IOTest.tearDown"]], "test_cache() (matminer.utils.tests.test_caching.testcaching method)": [[27, "matminer.utils.tests.test_caching.TestCaching.test_cache"]], "test_flatten_nested_dict() (matminer.utils.tests.test_flatten_dict.flattendicttest method)": [[27, "matminer.utils.tests.test_flatten_dict.FlattenDictTest.test_flatten_nested_dict"]], "test_get_data() (matminer.utils.tests.test_data.testiucrbondvalencedata method)": [[27, "matminer.utils.tests.test_data.TestIUCrBondValenceData.test_get_data"]], "test_get_data() (matminer.utils.tests.test_data.testmixingenthalpy method)": [[27, "matminer.utils.tests.test_data.TestMixingEnthalpy.test_get_data"]], "test_get_oxidation() (matminer.utils.tests.test_data.testdemldata method)": [[27, "matminer.utils.tests.test_data.TestDemlData.test_get_oxidation"]], "test_get_oxidation() (matminer.utils.tests.test_data.testmagpiedata method)": [[27, "matminer.utils.tests.test_data.TestMagpieData.test_get_oxidation"]], "test_get_oxidation() (matminer.utils.tests.test_data.testpymatgendata method)": [[27, "matminer.utils.tests.test_data.TestPymatgenData.test_get_oxidation"]], "test_get_property() (matminer.utils.tests.test_data.testdemldata method)": [[27, "matminer.utils.tests.test_data.TestDemlData.test_get_property"]], "test_get_property() (matminer.utils.tests.test_data.testmegnetdata method)": [[27, "matminer.utils.tests.test_data.TestMEGNetData.test_get_property"]], "test_get_property() (matminer.utils.tests.test_data.testmagpiedata method)": [[27, "matminer.utils.tests.test_data.TestMagpieData.test_get_property"]], "test_get_property() (matminer.utils.tests.test_data.testmatscholardata method)": [[27, "matminer.utils.tests.test_data.TestMatScholarData.test_get_property"]], "test_get_property() (matminer.utils.tests.test_data.testpymatgendata method)": [[27, "matminer.utils.tests.test_data.TestPymatgenData.test_get_property"]], "test_load_dataframe_from_json() (matminer.utils.tests.test_io.iotest method)": [[27, "matminer.utils.tests.test_io.IOTest.test_load_dataframe_from_json"]], "test_store_dataframe_as_json() (matminer.utils.tests.test_io.iotest method)": [[27, "matminer.utils.tests.test_io.IOTest.test_store_dataframe_as_json"]]}})