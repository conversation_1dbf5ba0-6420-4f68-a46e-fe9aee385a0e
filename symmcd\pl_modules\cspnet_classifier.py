import torch
import torch.nn as nn
import torch.nn.functional as F

import math
from torch_scatter import scatter
from torch_scatter.composite import scatter_softmax
from torch_geometric.utils import to_dense_adj, dense_to_sparse
from einops import rearrange, repeat

from symmcd.common.data_utils import lattice_params_to_matrix_torch, get_pbc_distances, radius_graph_pbc, frac_to_cart_coords, repeat_blocks

MAX_ATOMIC_NUM=100

class SinusoidsEmbedding(nn.Module):
    def __init__(self, n_frequencies = 10, n_space = 3):
        super().__init__()
        self.n_frequencies = n_frequencies
        self.n_space = n_space
        self.frequencies = 2 * math.pi * torch.arange(self.n_frequencies)
        self.dim = self.n_frequencies * 2 * self.n_space

    def forward(self, x):
        emb = x.unsqueeze(-1) * self.frequencies[None, None, :].to(x.device)
        emb = emb.reshape(-1, self.n_frequencies * self.n_space)
        emb = torch.cat((emb.sin(), emb.cos()), dim=-1)
        return emb.detach()



class CSPLayer(nn.Module):
    """ Message passing layer for cspnet."""

    def __init__(
        self,
        hidden_dim=128,
        act_fn=nn.SiLU(),
        dis_emb=None,
        ln=False,
        ip=True
    ):
        super(CSPLayer, self).__init__()

        self.dis_dim = 3
        self.dis_emb = dis_emb
        self.ip = True
        if dis_emb is not None:
            self.dis_dim = dis_emb.dim
        self.edge_mlp = nn.Sequential(
            nn.Linear(hidden_dim * 2 + 6 + self.dis_dim, hidden_dim),
            act_fn,
            nn.Linear(hidden_dim, hidden_dim),
            act_fn)
        self.node_mlp = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            act_fn,
            nn.Linear(hidden_dim, hidden_dim),
            act_fn)
        self.ln = ln
        if self.ln:
            self.layer_norm = nn.LayerNorm(hidden_dim)

    def node_model(self, node_features):

        out = self.node_mlp(node_features)
        return out

    def forward(self, node_features):

        node_input = node_features
        if self.ln:
            node_features = self.layer_norm(node_input)
        node_output = self.node_model(node_features)
        return node_input + node_output


class CSPNet(nn.Module):

    def __init__(
        self,
        hidden_dim = 128,
        latent_dim = 256,
        num_layers = 4,
        max_atoms = 118,
        act_fn = 'silu',
        dis_emb = 'sin',
        num_freqs = 10,
        edge_style = 'fc',
        coord_style = 'node',
        cutoff = 6.0,
        max_neighbors = 20,
        ln = False,
        attn = False,
        pred_type = False,
        coord_cart = False,
        dense = False,
        smooth = False,
        ip = True,
        gate = False,
        pred_scalar=False,
        pooling='mean',
        num_cond=1,
    ):
        super(CSPNet, self).__init__()

        self.smooth = smooth
        self.ip = ip
        if self.smooth:
            self.node_embedding = nn.Linear(max_atoms, hidden_dim)
        else:
            self.node_embedding = nn.Embedding(max_atoms, hidden_dim)

        self.atom_latent_emb = nn.Linear(max_atoms + latent_dim, hidden_dim)
        if act_fn == 'silu':
            self.act_fn = nn.SiLU()
        if dis_emb == 'sin':
            self.dis_emb = SinusoidsEmbedding(n_frequencies = num_freqs)
        elif dis_emb == 'none':
            self.dis_emb = None
        self.coord_style = coord_style
        for i in range(0, num_layers):
            self.add_module(
                "csp_layer_%d" % i, CSPLayer(hidden_dim, self.act_fn, self.dis_emb, ln=ln, ip=ip)
            )            
        self.num_layers = num_layers
        self.sigmoid = torch.nn.Sigmoid()
        self.dense = dense

        hidden_dim_before_out = hidden_dim

        self.cond_adapt_layers = nn.ModuleDict()
        # self.cond_mixin_layers = nn.ModuleDict()
        for i in range(0, num_cond):
            adapt_layers = []
            # mixin_layers = []            
            for _ in range(0, num_layers):
                adapt_layers.append(
                    nn.Sequential(
                        nn.Linear(hidden_dim, hidden_dim),
                        nn.ReLU(),
                        nn.Linear(hidden_dim, hidden_dim),
                    )
                )
                # mixin_layers.append(nn.Linear(hidden_dim, hidden_dim, bias=False))
                # nn.init.zeros_(mixin_layers[-1].weight)

            self.cond_adapt_layers[str(i)] = torch.nn.ModuleList(adapt_layers)
            # self.cond_mixin_layers[str(i)] = torch.nn.ModuleList(mixin_layers)

        if self.dense:
            hidden_dim_before_out = hidden_dim_before_out * (num_layers + 1)

        self.coord_out = nn.Linear(hidden_dim_before_out, 3, bias = False)
        self.lattice_out = nn.Linear(hidden_dim_before_out, 6, bias = False)
        self.classifier_out = nn.Linear(hidden_dim_before_out, 230, bias = False)

        self.edge_style = edge_style
        self.cutoff = cutoff
        self.max_neighbors = max_neighbors
        self.ln = ln
        if self.ln:
            self.final_layer_norm = nn.LayerNorm(hidden_dim)
        self.pred_type = pred_type
        if self.pred_type:
            self.type_out = nn.Linear(hidden_dim, MAX_ATOMIC_NUM)

        self.pred_scalar = pred_scalar
        if self.pred_scalar:
            self.scalar_out = nn.Linear(hidden_dim_before_out, 1)

        self.pooling = pooling

    def forward(self, conditions_adapt_dict, conditions_adapt_mask_dict, node2graph):

        # print('atom_types:', atom_types.shape)        
        # print('condition_emb:', condition_emb.shape) 
        # if self.smooth:
        #     node_features = self.node_embedding(atom_types)
        # else:
        #     node_features = self.node_embedding(atom_types - 1)
        # print('conditions_adapt_dict:', conditions_adapt_dict)
        # print('conditions_adapt_mask_dict:', conditions_adapt_mask_dict)
        # node_features = torch.cat([atom_types, condition_emb], dim=1)
        # # print(node_features.shape)
        # node_features = self.atom_latent_emb(node_features)

        for i in range(0, self.num_layers):
            for index, cond in enumerate(conditions_adapt_dict):
                if index == 0 and i == 0:
                    node_features = torch.zeros_like(conditions_adapt_dict[cond])
                #print('node_features_adapt_shape:', node_features_adapt.shape)
                node_features_adapt_cond = self.cond_adapt_layers[str(index)][i](
                conditions_adapt_dict[cond]
            )
                node_features += conditions_adapt_mask_dict[cond] * node_features_adapt_cond
            node_features = self._modules["csp_layer_%d" % i](node_features)

        if self.ln:
            node_features = self.final_layer_norm(node_features)

        class_out = self.sigmoid(self.classifier_out(node_features))
        # print(class_out.shape)

        return class_out